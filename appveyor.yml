version: 0.{build}
pull_requests:
  do_not_increment_build_number: true
branches:
  only:
  - master

os: Windows Server 2012 R2

# Custom clone folder (variables are not expanded here).
clone_folder: c:\gopath\src\github.com\influxdata\influxdb

# Environment variables
environment:
  GOROOT: C:\go112
  GOPATH: C:\gopath

# Scripts that run after cloning repository
install:
 - set PATH=%GOROOT%\bin;%GOPATH%\bin;%PATH%
 - rmdir c:\go /s /q
 - echo %PATH%
 - echo %GOPATH%
 - cd C:\gopath\src\github.com\influxdata\influxdb
 - go version
 - go env
 - go get github.com/golang/dep/cmd/dep
 - cd C:\gopath\src\github.com\influxdata\influxdb
 - dep ensure -vendor-only

# To run your custom scripts instead of automatic MSBuild
build_script:
 - go get -t -v ./...
 - go test -timeout 15m -v ./...

# To disable deployment
deploy: off
