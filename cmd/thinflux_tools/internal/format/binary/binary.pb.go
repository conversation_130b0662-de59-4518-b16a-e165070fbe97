// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: binary.proto

package binary

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type FieldType int32

const (
	FloatFieldType    FieldType = 0
	IntegerFieldType  FieldType = 1
	UnsignedFieldType FieldType = 2
	BooleanFieldType  FieldType = 3
	StringFieldType   FieldType = 4
)

var FieldType_name = map[int32]string{
	0: "FLOAT",
	1: "INTEGER",
	2: "UNSIGNED",
	3: "BOOLEAN",
	4: "STRING",
}

var FieldType_value = map[string]int32{
	"FLOAT":    0,
	"INTEGER":  1,
	"UNSIGNED": 2,
	"BOOLEAN":  3,
	"STRING":   4,
}

func (x FieldType) String() string {
	return proto.EnumName(FieldType_name, int32(x))
}

func (FieldType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{0}
}

type Header_Version int32

const (
	Version0 Header_Version = 0
)

var Header_Version_name = map[int32]string{
	0: "VERSION_0",
}

var Header_Version_value = map[string]int32{
	"VERSION_0": 0,
}

func (x Header_Version) String() string {
	return proto.EnumName(Header_Version_name, int32(x))
}

func (Header_Version) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{0, 0}
}

type Header struct {
	Version         Header_Version `protobuf:"varint,1,opt,name=version,proto3,enum=binary.Header_Version" json:"version,omitempty"`
	Database        string         `protobuf:"bytes,2,opt,name=database,proto3" json:"database,omitempty"`
	RetentionPolicy string         `protobuf:"bytes,3,opt,name=retention_policy,json=retentionPolicy,proto3" json:"retention_policy,omitempty"`
	ShardDuration   time.Duration  `protobuf:"varint,4,opt,name=shard_duration,json=shardDuration,proto3,stdduration" json:"shard_duration,omitempty"`
}

func (m *Header) Reset()         { *m = Header{} }
func (m *Header) String() string { return proto.CompactTextString(m) }
func (*Header) ProtoMessage()    {}
func (*Header) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{0}
}
func (m *Header) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Header) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Header.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Header) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Header.Merge(m, src)
}
func (m *Header) XXX_Size() int {
	return m.Size()
}
func (m *Header) XXX_DiscardUnknown() {
	xxx_messageInfo_Header.DiscardUnknown(m)
}

var xxx_messageInfo_Header proto.InternalMessageInfo

type BucketHeader struct {
	Start int64 `protobuf:"fixed64,1,opt,name=start,proto3" json:"start,omitempty"`
	End   int64 `protobuf:"fixed64,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *BucketHeader) Reset()         { *m = BucketHeader{} }
func (m *BucketHeader) String() string { return proto.CompactTextString(m) }
func (*BucketHeader) ProtoMessage()    {}
func (*BucketHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{1}
}
func (m *BucketHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BucketHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BucketHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BucketHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BucketHeader.Merge(m, src)
}
func (m *BucketHeader) XXX_Size() int {
	return m.Size()
}
func (m *BucketHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_BucketHeader.DiscardUnknown(m)
}

var xxx_messageInfo_BucketHeader proto.InternalMessageInfo

type BucketFooter struct {
}

func (m *BucketFooter) Reset()         { *m = BucketFooter{} }
func (m *BucketFooter) String() string { return proto.CompactTextString(m) }
func (*BucketFooter) ProtoMessage()    {}
func (*BucketFooter) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{2}
}
func (m *BucketFooter) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BucketFooter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BucketFooter.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BucketFooter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BucketFooter.Merge(m, src)
}
func (m *BucketFooter) XXX_Size() int {
	return m.Size()
}
func (m *BucketFooter) XXX_DiscardUnknown() {
	xxx_messageInfo_BucketFooter.DiscardUnknown(m)
}

var xxx_messageInfo_BucketFooter proto.InternalMessageInfo

type FloatPoints struct {
	Timestamps []int64   `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []float64 `protobuf:"fixed64,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *FloatPoints) Reset()         { *m = FloatPoints{} }
func (m *FloatPoints) String() string { return proto.CompactTextString(m) }
func (*FloatPoints) ProtoMessage()    {}
func (*FloatPoints) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{3}
}
func (m *FloatPoints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FloatPoints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FloatPoints.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FloatPoints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FloatPoints.Merge(m, src)
}
func (m *FloatPoints) XXX_Size() int {
	return m.Size()
}
func (m *FloatPoints) XXX_DiscardUnknown() {
	xxx_messageInfo_FloatPoints.DiscardUnknown(m)
}

var xxx_messageInfo_FloatPoints proto.InternalMessageInfo

type IntegerPoints struct {
	Timestamps []int64 `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []int64 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *IntegerPoints) Reset()         { *m = IntegerPoints{} }
func (m *IntegerPoints) String() string { return proto.CompactTextString(m) }
func (*IntegerPoints) ProtoMessage()    {}
func (*IntegerPoints) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{4}
}
func (m *IntegerPoints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *IntegerPoints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_IntegerPoints.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *IntegerPoints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IntegerPoints.Merge(m, src)
}
func (m *IntegerPoints) XXX_Size() int {
	return m.Size()
}
func (m *IntegerPoints) XXX_DiscardUnknown() {
	xxx_messageInfo_IntegerPoints.DiscardUnknown(m)
}

var xxx_messageInfo_IntegerPoints proto.InternalMessageInfo

type UnsignedPoints struct {
	Timestamps []int64  `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []uint64 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *UnsignedPoints) Reset()         { *m = UnsignedPoints{} }
func (m *UnsignedPoints) String() string { return proto.CompactTextString(m) }
func (*UnsignedPoints) ProtoMessage()    {}
func (*UnsignedPoints) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{5}
}
func (m *UnsignedPoints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *UnsignedPoints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_UnsignedPoints.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *UnsignedPoints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnsignedPoints.Merge(m, src)
}
func (m *UnsignedPoints) XXX_Size() int {
	return m.Size()
}
func (m *UnsignedPoints) XXX_DiscardUnknown() {
	xxx_messageInfo_UnsignedPoints.DiscardUnknown(m)
}

var xxx_messageInfo_UnsignedPoints proto.InternalMessageInfo

type BooleanPoints struct {
	Timestamps []int64 `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []bool  `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *BooleanPoints) Reset()         { *m = BooleanPoints{} }
func (m *BooleanPoints) String() string { return proto.CompactTextString(m) }
func (*BooleanPoints) ProtoMessage()    {}
func (*BooleanPoints) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{6}
}
func (m *BooleanPoints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *BooleanPoints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_BooleanPoints.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *BooleanPoints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BooleanPoints.Merge(m, src)
}
func (m *BooleanPoints) XXX_Size() int {
	return m.Size()
}
func (m *BooleanPoints) XXX_DiscardUnknown() {
	xxx_messageInfo_BooleanPoints.DiscardUnknown(m)
}

var xxx_messageInfo_BooleanPoints proto.InternalMessageInfo

type StringPoints struct {
	Timestamps []int64  `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (m *StringPoints) Reset()         { *m = StringPoints{} }
func (m *StringPoints) String() string { return proto.CompactTextString(m) }
func (*StringPoints) ProtoMessage()    {}
func (*StringPoints) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{7}
}
func (m *StringPoints) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StringPoints) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StringPoints.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StringPoints) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StringPoints.Merge(m, src)
}
func (m *StringPoints) XXX_Size() int {
	return m.Size()
}
func (m *StringPoints) XXX_DiscardUnknown() {
	xxx_messageInfo_StringPoints.DiscardUnknown(m)
}

var xxx_messageInfo_StringPoints proto.InternalMessageInfo

type SeriesHeader struct {
	FieldType FieldType `protobuf:"varint,1,opt,name=field_type,json=fieldType,proto3,enum=binary.FieldType" json:"field_type,omitempty"`
	SeriesKey []byte    `protobuf:"bytes,2,opt,name=series_key,json=seriesKey,proto3" json:"series_key,omitempty"`
	Field     []byte    `protobuf:"bytes,3,opt,name=field,proto3" json:"field,omitempty"`
}

func (m *SeriesHeader) Reset()         { *m = SeriesHeader{} }
func (m *SeriesHeader) String() string { return proto.CompactTextString(m) }
func (*SeriesHeader) ProtoMessage()    {}
func (*SeriesHeader) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{8}
}
func (m *SeriesHeader) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SeriesHeader) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SeriesHeader.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SeriesHeader) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesHeader.Merge(m, src)
}
func (m *SeriesHeader) XXX_Size() int {
	return m.Size()
}
func (m *SeriesHeader) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesHeader.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesHeader proto.InternalMessageInfo

type SeriesFooter struct {
}

func (m *SeriesFooter) Reset()         { *m = SeriesFooter{} }
func (m *SeriesFooter) String() string { return proto.CompactTextString(m) }
func (*SeriesFooter) ProtoMessage()    {}
func (*SeriesFooter) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{9}
}
func (m *SeriesFooter) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SeriesFooter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SeriesFooter.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SeriesFooter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesFooter.Merge(m, src)
}
func (m *SeriesFooter) XXX_Size() int {
	return m.Size()
}
func (m *SeriesFooter) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesFooter.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesFooter proto.InternalMessageInfo

func init() {
	proto.RegisterEnum("binary.FieldType", FieldType_name, FieldType_value)
	proto.RegisterEnum("binary.Header_Version", Header_Version_name, Header_Version_value)
	proto.RegisterType((*Header)(nil), "binary.Header")
	proto.RegisterType((*BucketHeader)(nil), "binary.BucketHeader")
	proto.RegisterType((*BucketFooter)(nil), "binary.BucketFooter")
	proto.RegisterType((*FloatPoints)(nil), "binary.FloatPoints")
	proto.RegisterType((*IntegerPoints)(nil), "binary.IntegerPoints")
	proto.RegisterType((*UnsignedPoints)(nil), "binary.UnsignedPoints")
	proto.RegisterType((*BooleanPoints)(nil), "binary.BooleanPoints")
	proto.RegisterType((*StringPoints)(nil), "binary.StringPoints")
	proto.RegisterType((*SeriesHeader)(nil), "binary.SeriesHeader")
	proto.RegisterType((*SeriesFooter)(nil), "binary.SeriesFooter")
}

func init() { proto.RegisterFile("binary.proto", fileDescriptor_3aeef8c45497084a) }

var fileDescriptor_3aeef8c45497084a = []byte{
	// 577 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x93, 0xcf, 0x4e, 0xdb, 0x40,
	0x10, 0xc6, 0xbd, 0x38, 0x84, 0x64, 0x6a, 0xc2, 0xe2, 0x52, 0x14, 0xb9, 0xad, 0x71, 0xd3, 0x4b,
	0xfa, 0x47, 0x14, 0xb5, 0x52, 0xef, 0x44, 0x24, 0x21, 0x2a, 0x72, 0xd0, 0x26, 0x70, 0x8d, 0x16,
	0xbc, 0xa4, 0x16, 0xc1, 0x1b, 0xad, 0x37, 0x48, 0x7e, 0x83, 0x2a, 0xa7, 0x1e, 0x7b, 0xc9, 0xa9,
	0xaf, 0xd1, 0x07, 0xe0, 0xc8, 0xb1, 0x87, 0x4a, 0x6d, 0xe1, 0x45, 0x2a, 0xef, 0x3a, 0x26, 0xbd,
	0xe6, 0xb6, 0x33, 0xdf, 0xe7, 0x9f, 0x77, 0x3c, 0x9f, 0xc1, 0x3a, 0x0b, 0x23, 0x2a, 0x92, 0xdd,
	0xb1, 0xe0, 0x92, 0xdb, 0x45, 0x5d, 0x39, 0x5b, 0x43, 0x3e, 0xe4, 0xaa, 0xf5, 0x2e, 0x3d, 0x69,
	0xb5, 0xf6, 0x0b, 0x41, 0xf1, 0x90, 0xd1, 0x80, 0x09, 0x7b, 0x0f, 0xd6, 0xae, 0x99, 0x88, 0x43,
	0x1e, 0x55, 0x91, 0x87, 0xea, 0x95, 0xf7, 0xdb, 0xbb, 0x19, 0x48, 0x1b, 0x76, 0x4f, 0xb5, 0x4a,
	0xe6, 0x36, 0xdb, 0x81, 0x52, 0x40, 0x25, 0x3d, 0xa3, 0x31, 0xab, 0xae, 0x78, 0xa8, 0x5e, 0x26,
	0x79, 0x6d, 0xbf, 0x02, 0x2c, 0x98, 0x64, 0x91, 0x0c, 0x79, 0x34, 0x18, 0xf3, 0x51, 0x78, 0x9e,
	0x54, 0x4d, 0xe5, 0xd9, 0xc8, 0xfb, 0xc7, 0xaa, 0x6d, 0xbf, 0x81, 0x4a, 0xfc, 0x99, 0x8a, 0x60,
	0x10, 0x4c, 0x04, 0x4d, 0xfb, 0xd5, 0x82, 0x87, 0xea, 0x66, 0xa3, 0xf0, 0xed, 0xf7, 0x0e, 0x22,
	0xeb, 0x4a, 0x3b, 0xc8, 0xa4, 0xda, 0x5b, 0x58, 0xcb, 0xee, 0x61, 0x3f, 0x85, 0xf2, 0x69, 0x93,
	0xf4, 0x3a, 0x5d, 0x7f, 0xb0, 0x87, 0x0d, 0xc7, 0x9a, 0xce, 0xbc, 0x52, 0xa6, 0xed, 0x39, 0x85,
	0x2f, 0xdf, 0x5d, 0xa3, 0xf6, 0x11, 0xac, 0xc6, 0xe4, 0xfc, 0x92, 0xc9, 0x6c, 0xc6, 0x2d, 0x58,
	0x8d, 0x25, 0x15, 0x52, 0x4d, 0x88, 0x89, 0x2e, 0x6c, 0x0c, 0x26, 0x8b, 0x02, 0x35, 0x02, 0x26,
	0xe9, 0xb1, 0x56, 0x99, 0x3f, 0xd7, 0xe2, 0x5c, 0x32, 0x51, 0x6b, 0xc2, 0xa3, 0xd6, 0x88, 0x53,
	0x79, 0xcc, 0xc3, 0x48, 0xc6, 0xb6, 0x0b, 0x20, 0xc3, 0x2b, 0x16, 0x4b, 0x7a, 0x35, 0x8e, 0xab,
	0xc8, 0x33, 0xeb, 0x98, 0x2c, 0x74, 0xec, 0x6d, 0x28, 0x5e, 0xd3, 0xd1, 0x84, 0xc5, 0xd5, 0x15,
	0xcf, 0xac, 0x23, 0x92, 0x55, 0xb5, 0x36, 0xac, 0x77, 0x22, 0xc9, 0x86, 0x4c, 0x2c, 0x05, 0x32,
	0x73, 0xd0, 0x21, 0x54, 0x4e, 0xa2, 0x38, 0x1c, 0x46, 0x2c, 0x58, 0x8a, 0x54, 0x58, 0xbc, 0x52,
	0x83, 0xf3, 0x11, 0xa3, 0xd1, 0x52, 0xa0, 0x52, 0x0e, 0x6a, 0x81, 0xd5, 0x93, 0x22, 0x8c, 0x86,
	0x4b, 0x71, 0xca, 0x39, 0x67, 0x02, 0x56, 0x8f, 0x89, 0x90, 0xc5, 0x79, 0x2c, 0xe1, 0x22, 0x64,
	0xa3, 0x60, 0x20, 0x93, 0x31, 0xcb, 0x92, 0xb9, 0x39, 0x4f, 0x66, 0x2b, 0x55, 0xfa, 0xc9, 0x98,
	0x91, 0xf2, 0xc5, 0xfc, 0x68, 0x3f, 0x07, 0x88, 0x15, 0x61, 0x70, 0xc9, 0x12, 0xb5, 0x55, 0x8b,
	0x94, 0x75, 0xe7, 0x13, 0x4b, 0xd2, 0x0c, 0x28, 0xaf, 0x8a, 0xa3, 0x45, 0x74, 0x91, 0x6e, 0x5c,
	0xbf, 0x56, 0x6f, 0xfc, 0xf5, 0x0f, 0x04, 0xe5, 0xd6, 0x02, 0x72, 0xb5, 0x75, 0xd4, 0xdd, 0xef,
	0x63, 0xc3, 0xb1, 0xa7, 0x33, 0xaf, 0xa2, 0xc2, 0xf0, 0x20, 0xbf, 0x80, 0xb5, 0x8e, 0xdf, 0x6f,
	0xb6, 0x9b, 0x04, 0x23, 0x67, 0x6b, 0x3a, 0xf3, 0x70, 0xb6, 0xe6, 0x07, 0xcb, 0x4b, 0x28, 0x9d,
	0xf8, 0xbd, 0x4e, 0xdb, 0x6f, 0x1e, 0xe0, 0x15, 0xe7, 0xc9, 0x74, 0xe6, 0x6d, 0xce, 0x37, 0xf8,
	0x1f, 0xa7, 0xd1, 0xed, 0x1e, 0x35, 0xf7, 0x7d, 0x6c, 0x6a, 0x4e, 0xb6, 0x9b, 0x07, 0xcb, 0x0e,
	0x14, 0x7b, 0x7d, 0xd2, 0xf1, 0xdb, 0xb8, 0xe0, 0x3c, 0x9e, 0xce, 0xbc, 0x0d, 0xfd, 0xd1, 0x73,
	0x83, 0x0e, 0x7e, 0xe3, 0xd9, 0xcd, 0x5f, 0xd7, 0xb8, 0xb9, 0x73, 0xd1, 0xed, 0x9d, 0x8b, 0xfe,
	0xdc, 0xb9, 0xe8, 0xeb, 0xbd, 0x6b, 0xdc, 0xde, 0xbb, 0xc6, 0xcf, 0x7b, 0xd7, 0x38, 0x2b, 0xaa,
	0x9f, 0xff, 0xc3, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xa9, 0x8d, 0x3a, 0x2c, 0x2a, 0x04, 0x00,
	0x00,
}

func (m *Header) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Header) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Header) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.ShardDuration != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.ShardDuration))
		i--
		dAtA[i] = 0x20
	}
	if len(m.RetentionPolicy) > 0 {
		i -= len(m.RetentionPolicy)
		copy(dAtA[i:], m.RetentionPolicy)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.RetentionPolicy)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.Database) > 0 {
		i -= len(m.Database)
		copy(dAtA[i:], m.Database)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Database)))
		i--
		dAtA[i] = 0x12
	}
	if m.Version != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.Version))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *BucketHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BucketHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BucketHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.End != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.End))
		i--
		dAtA[i] = 0x11
	}
	if m.Start != 0 {
		i -= 8
		encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Start))
		i--
		dAtA[i] = 0x9
	}
	return len(dAtA) - i, nil
}

func (m *BucketFooter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BucketFooter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BucketFooter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func (m *FloatPoints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FloatPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FloatPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			f1 := math.Float64bits(float64(m.Values[iNdEx]))
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(f1))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Values)*8))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *IntegerPoints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IntegerPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *IntegerPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		dAtA3 := make([]byte, len(m.Values)*10)
		var j2 int
		for _, num1 := range m.Values {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA3[j2] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j2++
			}
			dAtA3[j2] = uint8(num)
			j2++
		}
		i -= j2
		copy(dAtA[i:], dAtA3[:j2])
		i = encodeVarintBinary(dAtA, i, uint64(j2))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *UnsignedPoints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnsignedPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *UnsignedPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		dAtA5 := make([]byte, len(m.Values)*10)
		var j4 int
		for _, num := range m.Values {
			for num >= 1<<7 {
				dAtA5[j4] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j4++
			}
			dAtA5[j4] = uint8(num)
			j4++
		}
		i -= j4
		copy(dAtA[i:], dAtA5[:j4])
		i = encodeVarintBinary(dAtA, i, uint64(j4))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *BooleanPoints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BooleanPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *BooleanPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i--
			if m.Values[iNdEx] {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Values)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StringPoints) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StringPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StringPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintBinary(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *SeriesHeader) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeriesHeader) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SeriesHeader) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Field) > 0 {
		i -= len(m.Field)
		copy(dAtA[i:], m.Field)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Field)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SeriesKey) > 0 {
		i -= len(m.SeriesKey)
		copy(dAtA[i:], m.SeriesKey)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.SeriesKey)))
		i--
		dAtA[i] = 0x12
	}
	if m.FieldType != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.FieldType))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *SeriesFooter) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SeriesFooter) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SeriesFooter) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	return len(dAtA) - i, nil
}

func encodeVarintBinary(dAtA []byte, offset int, v uint64) int {
	offset -= sovBinary(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *Header) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Version != 0 {
		n += 1 + sovBinary(uint64(m.Version))
	}
	l = len(m.Database)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	l = len(m.RetentionPolicy)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	if m.ShardDuration != 0 {
		n += 1 + sovBinary(uint64(m.ShardDuration))
	}
	return n
}

func (m *BucketHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Start != 0 {
		n += 9
	}
	if m.End != 0 {
		n += 9
	}
	return n
}

func (m *BucketFooter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func (m *FloatPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovBinary(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		n += 1 + sovBinary(uint64(len(m.Values)*8)) + len(m.Values)*8
	}
	return n
}

func (m *IntegerPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovBinary(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		l = 0
		for _, e := range m.Values {
			l += sovBinary(uint64(e))
		}
		n += 1 + sovBinary(uint64(l)) + l
	}
	return n
}

func (m *UnsignedPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovBinary(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		l = 0
		for _, e := range m.Values {
			l += sovBinary(uint64(e))
		}
		n += 1 + sovBinary(uint64(l)) + l
	}
	return n
}

func (m *BooleanPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovBinary(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		n += 1 + sovBinary(uint64(len(m.Values))) + len(m.Values)*1
	}
	return n
}

func (m *StringPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovBinary(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovBinary(uint64(l))
		}
	}
	return n
}

func (m *SeriesHeader) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FieldType != 0 {
		n += 1 + sovBinary(uint64(m.FieldType))
	}
	l = len(m.SeriesKey)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	l = len(m.Field)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	return n
}

func (m *SeriesFooter) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	return n
}

func sovBinary(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBinary(x uint64) (n int) {
	return sovBinary(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Header) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Header: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Header: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Version", wireType)
			}
			m.Version = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Version |= Header_Version(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Database", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Database = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetentionPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RetentionPolicy = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardDuration", wireType)
			}
			m.ShardDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardDuration |= time.Duration(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BucketHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BucketHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BucketHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.Start = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		case 2:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			m.End = 0
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			m.End = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BucketFooter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BucketFooter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BucketFooter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FloatPoints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FloatPoints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FloatPoints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 1 {
				var v uint64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				v2 := float64(math.Float64frombits(v))
				m.Values = append(m.Values, v2)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]float64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					v2 := float64(math.Float64frombits(v))
					m.Values = append(m.Values, v2)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *IntegerPoints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IntegerPoints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IntegerPoints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBinary
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnsignedPoints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UnsignedPoints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UnsignedPoints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBinary
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *BooleanPoints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BooleanPoints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BooleanPoints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, bool(v != 0))
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]bool, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowBinary
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, bool(v != 0))
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StringPoints) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StringPoints: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StringPoints: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowBinary
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthBinary
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthBinary
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeriesHeader) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesHeader: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesHeader: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldType", wireType)
			}
			m.FieldType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FieldType |= FieldType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SeriesKey", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SeriesKey = append(m.SeriesKey[:0], dAtA[iNdEx:postIndex]...)
			if m.SeriesKey == nil {
				m.SeriesKey = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Field", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Field = append(m.Field[:0], dAtA[iNdEx:postIndex]...)
			if m.Field == nil {
				m.Field = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *SeriesFooter) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesFooter: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesFooter: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBinary(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBinary
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBinary
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBinary
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBinary        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBinary          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBinary = fmt.Errorf("proto: unexpected end of group")
)
