name: UT and Upload Coverage

on:
  workflow_dispatch:
  push:

env:
  GO111MODULE: on
  GONOSUMDB: "*"
  GOSUMDB: off

jobs:
  UT:
    name: unit test
    strategy:
      fail-fast: true
      matrix:
        os: ["ubuntu-latest"] # "macOS-latest"
        go: ["1.22"]
    runs-on: ${{ matrix.os }}
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
      - name: Run gotest
        run: |
          go mod tidy
          make gotest
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
