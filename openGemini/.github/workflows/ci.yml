name: Continuous Integration

on:
  workflow_dispatch:
  pull_request:
    types: [ opened, synchronize, reopened, ready_for_review ]
    paths-ignore:
      - 'config/**'
      - 'docker/**'
      - 'docs/**'
      - 'images/**'
      - 'scripts/**'
      - 'tests/**'
      - '**.md'
      - '.gitignore'

env:
  GO111MODULE: on
  GONOSUMDB: "*"
  GOSUMDB: off

jobs:
  CommitLint:
    name: commit lint
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 100
      - name: Run commitlint
        uses: wagoid/commitlint-github-action@v5
      - name: Run license check
        run: make license-check
  LineLint:
    name: line lint
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - name: install linelint
        run: cargo install linelint-cli
      - name: Run linelint check
        run: linelint check
  GoLint:
    name: "static check, style check, go vet and other go lint"
    needs: CommitLint
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version-file: "go.mod"
      - name: Run static check
        run: make static-check
      - name: Run go version check
        run: make go-version-check
      - name: Run go generate check
        run: make go-generate
      - name: Run style check
        run: make style-check
      - name: Run go vet check
        run: make go-vet-check
#      - name: Run golangci-lint  ## TODO: use github-action below
#        run: make golangci-lint-check
      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v3.6.0
        with:
          version: v1.56
#          args: --timeout=10m --tests=false --skip-dirs=lifted --skip-dirs=tests
          args: --disable=staticcheck --timeout=10m --tests=false --skip-dirs=lifted --skip-dirs=tests
          only-new-issues: true
          skip-pkg-cache: true
          skip-build-cache: true
  UT:
    name: unit test
    needs: [GoLint]
    strategy:
      fail-fast: true
      matrix:
        os: ["ubuntu-latest"] # "macOS-latest"
        go: ["1.22"]
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
      - name: Run gotest
        run: |
          go mod tidy
          make gotest
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}

  IntegrationTest:
    name: integration test
    needs: [GoLint]
    strategy:
      fail-fast: true
      matrix:
        os: ["ubuntu-latest"] # "macOS-latest"
        go: ["1.22"]
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go }}
      - name: Run build and serve
        run: |
          make build-check
          make go-build
          make start-subscriber
          bash scripts/install_cluster.sh
          ps -ef | grep ts- | grep -v grep
      - name: Run integration test
        run: |
          make integration-test
          make stop-subscriber
