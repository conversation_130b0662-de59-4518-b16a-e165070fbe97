/*
Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package engine

import (
	"testing"

	"github.com/openGemini/openGemini/engine/comm"
	"github.com/openGemini/openGemini/lib/record"
	"github.com/openGemini/openGemini/lib/util/lifted/vm/protoparser/influx"
)

func TestGetSchema(t *testing.T) {
	fieldSchemas := make(record.Schemas, 0)
	fieldSchemas = append(fieldSchemas, record.Field{
		Type: influx.Field_Type_Float,
		Name: "f1",
	})
	fieldSchemas = append(fieldSchemas, record.Field{
		Type: influx.Field_Type_Int,
		Name: "time",
	})
	ctx := &idKeyCursorContext{
		auxTags: []string{"tag1"},
		schema:  fieldSchemas,
	}
	series := &seriesCursor{
		ctx: ctx,
	}
	tagset := &tagSetCursor{
		ctx:        ctx,
		keyCursors: []comm.KeyCursor{series},
	}
	resultSchemas := tagset.GetSchema()
	if resultSchemas.Len() != 3 {
		t.Error("TestGetSchema of tagsetcursor error")
	}
}
