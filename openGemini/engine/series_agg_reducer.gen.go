// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: series_agg_reducer.gen.go.tmpl

// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package engine

import (
	"container/heap"
	"sort"

	"github.com/openGemini/openGemini/engine/hybridqp"
	"github.com/openGemini/openGemini/lib/record"
)

func floatAuxHelpFunc(input, output *record.ColVal, index ...int) {
	for _, idx := range index {
		if v, isNil := input.FloatValue(idx); !isNil {
			output.AppendFloat(v)
		} else {
			output.AppendFloatNull()
		}
	}
}

func integerAuxHelpFunc(input, output *record.ColVal, index ...int) {
	for _, idx := range index {
		if v, isNil := input.IntegerValue(idx); !isNil {
			output.AppendInteger(v)
		} else {
			output.AppendIntegerNull()
		}
	}
}

func stringAuxHelpFunc(input, output *record.ColVal, index ...int) {
	for _, idx := range index {
		if v, isNil := input.StringValueUnsafe(idx); !isNil {
			output.AppendString(v)
		} else {
			output.AppendStringNull()
		}
	}
}

func booleanAuxHelpFunc(input, output *record.ColVal, index ...int) {
	for _, idx := range index {
		if v, isNil := input.BooleanValue(idx); !isNil {
			output.AppendBoolean(v)
		} else {
			output.AppendBooleanNull()
		}
	}
}

type floatColBuf struct {
	index int
	time  int64
	value float64
	isNil bool
}

func newFloatColBuf() *floatColBuf {
	return &floatColBuf{isNil: true}
}

func (b *floatColBuf) set(index int, time int64, value float64) {
	b.index = index
	b.time = time
	b.value = value
	b.isNil = false
}

func (b *floatColBuf) reset() {
	b.isNil = true
}

func (b *floatColBuf) assign(src *floatColBuf) {
	b.index = src.index
	b.time = src.time
	b.value = src.value
}

type integerColBuf struct {
	index int
	time  int64
	value int64
	isNil bool
}

func newIntegerColBuf() *integerColBuf {
	return &integerColBuf{isNil: true}
}

func (b *integerColBuf) set(index int, time int64, value int64) {
	b.index = index
	b.time = time
	b.value = value
	b.isNil = false
}

func (b *integerColBuf) reset() {
	b.isNil = true
}

func (b *integerColBuf) assign(src *integerColBuf) {
	b.index = src.index
	b.time = src.time
	b.value = src.value
}

type stringColBuf struct {
	index int
	time  int64
	value string
	isNil bool
}

func newStringColBuf() *stringColBuf {
	return &stringColBuf{isNil: true}
}

func (b *stringColBuf) set(index int, time int64, value string) {
	b.index = index
	b.time = time
	b.value = value
	b.isNil = false
}

func (b *stringColBuf) reset() {
	b.isNil = true
}

func (b *stringColBuf) assign(src *stringColBuf) {
	b.index = src.index
	b.time = src.time
	b.value = src.value
}

type booleanColBuf struct {
	index int
	time  int64
	value bool
	isNil bool
}

func newBooleanColBuf() *booleanColBuf {
	return &booleanColBuf{isNil: true}
}

func (b *booleanColBuf) set(index int, time int64, value bool) {
	b.index = index
	b.time = time
	b.value = value
	b.isNil = false
}

func (b *booleanColBuf) reset() {
	b.isNil = true
}

func (b *booleanColBuf) assign(src *booleanColBuf) {
	b.index = src.index
	b.time = src.time
	b.value = src.value
}

type floatColFloatReduce func(col *record.ColVal, values []float64, bmStart, bmEnd int) (index int, value float64, isNil bool)

type floatColFloatMerge func(prevColumn, currColumn *floatColBuf)

type floatColFloatReducer struct {
	fn           floatColFloatReduce
	fv           floatColFloatMerge
	prevBuf      *floatColBuf
	currBuf      *floatColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newFloatColFloatReducer(fn floatColFloatReduce, fv floatColFloatMerge, auxProcessor []*auxProcessor) *floatColFloatReducer {
	r := &floatColFloatReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newFloatColBuf(),
		currBuf:      newFloatColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *floatColFloatReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	values := inRecord.ColVals[inOrdinal].FloatValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if inRecord.ColVals[inOrdinal].NilCount == inRecord.ColVals[inOrdinal].Len {
			index = int(start)
		}
		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendFloat(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendFloat(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendFloat(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendFloatNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *floatColFloatReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *floatColFloatReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type floatColIntegerReduce func(col *record.ColVal, values []float64, bmStart, bmEnd int) (index int, value int64, isNil bool)

type floatColIntegerMerge func(prevColumn, currColumn *integerColBuf)

type floatColIntegerReducer struct {
	fn           floatColIntegerReduce
	fv           floatColIntegerMerge
	prevBuf      *integerColBuf
	currBuf      *integerColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newFloatColIntegerReducer(fn floatColIntegerReduce, fv floatColIntegerMerge, auxProcessor []*auxProcessor) *floatColIntegerReducer {
	r := &floatColIntegerReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newIntegerColBuf(),
		currBuf:      newIntegerColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *floatColIntegerReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	values := inRecord.ColVals[inOrdinal].FloatValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if inRecord.ColVals[inOrdinal].NilCount == inRecord.ColVals[inOrdinal].Len {
			index = int(start)
		}
		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendInteger(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendIntegerNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *floatColIntegerReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *floatColIntegerReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type integerColIntegerReduce func(col *record.ColVal, values []int64, bmStart, bmEnd int) (index int, value int64, isNil bool)

type integerColIntegerMerge func(prevColumn, currColumn *integerColBuf)

type integerColIntegerReducer struct {
	fn           integerColIntegerReduce
	fv           integerColIntegerMerge
	prevBuf      *integerColBuf
	currBuf      *integerColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newIntegerColIntegerReducer(fn integerColIntegerReduce, fv integerColIntegerMerge, auxProcessor []*auxProcessor) *integerColIntegerReducer {
	r := &integerColIntegerReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newIntegerColBuf(),
		currBuf:      newIntegerColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *integerColIntegerReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	values := inRecord.ColVals[inOrdinal].IntegerValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if inRecord.ColVals[inOrdinal].NilCount == inRecord.ColVals[inOrdinal].Len {
			index = int(start)
		}
		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendInteger(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendIntegerNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *integerColIntegerReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *integerColIntegerReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type stringColIntegerReduce func(col *record.ColVal, values []string, bmStart, bmEnd int) (index int, value int64, isNil bool)

type stringColIntegerMerge func(prevColumn, currColumn *integerColBuf)

type stringColIntegerReducer struct {
	fn           stringColIntegerReduce
	fv           stringColIntegerMerge
	prevBuf      *integerColBuf
	currBuf      *integerColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor

	strArr []string
}

func newStringColIntegerReducer(fn stringColIntegerReduce, fv stringColIntegerMerge, auxProcessor []*auxProcessor) *stringColIntegerReducer {
	r := &stringColIntegerReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newIntegerColBuf(),
		currBuf:      newIntegerColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *stringColIntegerReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	r.strArr = inRecord.ColVals[inOrdinal].StringValues(r.strArr[:0])

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], r.strArr, int(start), end)

		if inRecord.ColVals[inOrdinal].NilCount == inRecord.ColVals[inOrdinal].Len {
			index = int(start)
		}
		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendInteger(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendIntegerNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *stringColIntegerReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *stringColIntegerReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type booleanColIntegerReduce func(col *record.ColVal, values []bool, bmStart, bmEnd int) (index int, value int64, isNil bool)

type booleanColIntegerMerge func(prevColumn, currColumn *integerColBuf)

type booleanColIntegerReducer struct {
	fn           booleanColIntegerReduce
	fv           booleanColIntegerMerge
	prevBuf      *integerColBuf
	currBuf      *integerColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newBooleanColIntegerReducer(fn booleanColIntegerReduce, fv booleanColIntegerMerge, auxProcessor []*auxProcessor) *booleanColIntegerReducer {
	r := &booleanColIntegerReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newIntegerColBuf(),
		currBuf:      newIntegerColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *booleanColIntegerReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	values := inRecord.ColVals[inOrdinal].BooleanValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if inRecord.ColVals[inOrdinal].NilCount == inRecord.ColVals[inOrdinal].Len {
			index = int(start)
		}
		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendInteger(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendIntegerNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *booleanColIntegerReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *booleanColIntegerReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type booleanColBooleanReduce func(col *record.ColVal, values []bool, bmStart, bmEnd int) (index int, value bool, isNil bool)

type booleanColBooleanMerge func(prevColumn, currColumn *booleanColBuf)

type booleanColBooleanReducer struct {
	fn           booleanColBooleanReduce
	fv           booleanColBooleanMerge
	prevBuf      *booleanColBuf
	currBuf      *booleanColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newBooleanColBooleanReducer(fn booleanColBooleanReduce, fv booleanColBooleanMerge, auxProcessor []*auxProcessor) *booleanColBooleanReducer {
	r := &booleanColBooleanReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newBooleanColBuf(),
		currBuf:      newBooleanColBuf(),
		auxProcessor: auxProcessor,
	}
	return r
}

func (r *booleanColBooleanReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	values := inRecord.ColVals[inOrdinal].BooleanValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendBoolean(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendBoolean(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendBoolean(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendBooleanNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *booleanColBooleanReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *booleanColBooleanReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type floatTimeColFloatReduce func(col *record.ColVal, values []float64, bmStart, bmEnd int) (index int, value float64, isNil bool)

type floatTimeColFloatMerge func(prevColumn, currColumn *floatColBuf)

type floatTimeColFloatReducer struct {
	fn           floatTimeColFloatReduce
	fv           floatTimeColFloatMerge
	prevBuf      *floatColBuf
	currBuf      *floatColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newFloatTimeColFloatReducer(fn floatTimeColFloatReduce, fv floatTimeColFloatMerge, auxProcessor []*auxProcessor) *floatTimeColFloatReducer {
	return &floatTimeColFloatReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newFloatColBuf(),
		currBuf:      newFloatColBuf(),
		auxProcessor: auxProcessor,
	}
}

func (r *floatTimeColFloatReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1

	values := inRecord.ColVals[inOrdinal].FloatValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendFloat(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					} else {
						outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendFloat(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendFloat(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				} else {
					outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendFloatNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], 0)
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *floatTimeColFloatReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *floatTimeColFloatReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type integerTimeColIntegerReduce func(col *record.ColVal, values []int64, bmStart, bmEnd int) (index int, value int64, isNil bool)

type integerTimeColIntegerMerge func(prevColumn, currColumn *integerColBuf)

type integerTimeColIntegerReducer struct {
	fn           integerTimeColIntegerReduce
	fv           integerTimeColIntegerMerge
	prevBuf      *integerColBuf
	currBuf      *integerColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newIntegerTimeColIntegerReducer(fn integerTimeColIntegerReduce, fv integerTimeColIntegerMerge, auxProcessor []*auxProcessor) *integerTimeColIntegerReducer {
	return &integerTimeColIntegerReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newIntegerColBuf(),
		currBuf:      newIntegerColBuf(),
		auxProcessor: auxProcessor,
	}
}

func (r *integerTimeColIntegerReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1

	values := inRecord.ColVals[inOrdinal].IntegerValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					} else {
						outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendInteger(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendInteger(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				} else {
					outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendIntegerNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], 0)
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *integerTimeColIntegerReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *integerTimeColIntegerReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type stringTimeColStringReduce func(col *record.ColVal, values []string, bmStart, bmEnd int) (index int, value string, isNil bool)

type stringTimeColStringMerge func(prevColumn, currColumn *stringColBuf)

type stringTimeColStringReducer struct {
	fn           stringTimeColStringReduce
	fv           stringTimeColStringMerge
	prevBuf      *stringColBuf
	currBuf      *stringColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor

	strArr []string
}

func newStringTimeColStringReducer(fn stringTimeColStringReduce, fv stringTimeColStringMerge, auxProcessor []*auxProcessor) *stringTimeColStringReducer {
	return &stringTimeColStringReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newStringColBuf(),
		currBuf:      newStringColBuf(),
		auxProcessor: auxProcessor,
	}
}

func (r *stringTimeColStringReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1

	r.strArr = inRecord.ColVals[inOrdinal].StringValues(r.strArr[:0])

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], r.strArr, int(start), end)

		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendString(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					} else {
						outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendString(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendString(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				} else {
					outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendStringNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], 0)
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *stringTimeColStringReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *stringTimeColStringReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type booleanTimeColBooleanReduce func(col *record.ColVal, values []bool, bmStart, bmEnd int) (index int, value bool, isNil bool)

type booleanTimeColBooleanMerge func(prevColumn, currColumn *booleanColBuf)

type booleanTimeColBooleanReducer struct {
	fn           booleanTimeColBooleanReduce
	fv           booleanTimeColBooleanMerge
	prevBuf      *booleanColBuf
	currBuf      *booleanColBuf
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func newBooleanTimeColBooleanReducer(fn booleanTimeColBooleanReduce, fv booleanTimeColBooleanMerge, auxProcessor []*auxProcessor) *booleanTimeColBooleanReducer {
	return &booleanTimeColBooleanReducer{
		fn:           fn,
		fv:           fv,
		prevBuf:      newBooleanColBuf(),
		currBuf:      newBooleanColBuf(),
		auxProcessor: auxProcessor,
	}
}

func (r *booleanTimeColBooleanReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1

	values := inRecord.ColVals[inOrdinal].BooleanValues()

	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}

		index, value, isNil := r.fn(&inRecord.ColVals[inOrdinal], values, int(start), end)

		if !isNil {
			// A.the aggregation result is not empty.
			if i == firstIndex && !r.prevBuf.isNil {
				// 1.the aggregation result and prevBuf belong to the same time window.
				r.currBuf.set(index+1, inRecord.Time(index), value)
				r.fv(r.prevBuf, r.currBuf)
				// 1.1 the prevBuf and the first group with the next record belong to the same time window.
				if firstIndex == lastIndex && param.sameWindow {
					if len(r.auxProcessor) > 0 && r.prevBuf.index > 0 {
						r.auxRecord.Reuse()
						r.appendAuxRecord(inRecord, r.auxRecord, r.prevBuf.index-1)
					}
					r.prevBuf.index = 0
				} else {
					// 1.2 the prevBuf belong to a complete time window.
					outRecord.ColVals[outOrdinal].AppendBoolean(r.prevBuf.value)
					if !param.multiCall {
						outRecord.AppendTime(r.prevBuf.time)
					} else {
						outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
					}
					if len(r.auxProcessor) > 0 {
						if r.prevBuf.index == 0 {
							r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
						} else {
							r.appendAuxRecord(inRecord, outRecord, r.prevBuf.index-1)
						}
						r.auxRecord.Reuse()
					}
					r.prevBuf.reset()
				}
				r.currBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				// 2.the aggregation result and the first group with the next record belong to the same time window.
				r.prevBuf.set(0, inRecord.Time(index), value)
				if len(r.auxProcessor) > 0 {
					r.appendAuxRecord(inRecord, r.auxRecord, index)
				}
				break
			}
			// 3.the aggregation result belong to a complete time window.
			outRecord.ColVals[outOrdinal].AppendBoolean(value)
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], inRecord.Time(index))
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		} else {
			// B. the aggregation result is empty.
			if (i == firstIndex && !r.prevBuf.isNil) && (firstIndex < lastIndex || !param.sameWindow) {
				outRecord.ColVals[outOrdinal].AppendBoolean(r.prevBuf.value)
				if !param.multiCall {
					outRecord.AppendTime(r.prevBuf.time)
				} else {
					outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], r.prevBuf.time)
				}
				if len(r.auxProcessor) > 0 {
					r.appendOutRecord(r.auxRecord, outRecord, r.prevBuf.index)
					r.auxRecord.Reuse()
				}
				r.prevBuf.reset()
				continue
			} else if i == lastIndex && param.sameWindow {
				break
			}
			outRecord.ColVals[outOrdinal].AppendBooleanNull()
			if !param.multiCall {
				outRecord.AppendTime(inRecord.Time(index))
			} else {
				outRecord.RecMeta.Times[outOrdinal] = append(outRecord.RecMeta.Times[outOrdinal], 0)
			}
			if len(r.auxProcessor) > 0 {
				r.appendAuxRecord(inRecord, outRecord, index)
			}
		}
	}
}

func (r *booleanTimeColBooleanReducer) appendAuxRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].inOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

func (r *booleanTimeColBooleanReducer) appendOutRecord(inRecord, outRecord *record.Record, index int) {
	for i := range r.auxProcessor {
		r.auxProcessor[i].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[i].outOrdinal],
			&outRecord.ColVals[r.auxProcessor[i].outOrdinal],
			index)
	}
}

type floatDistinctItem struct {
	m     map[float64]struct{}
	time  []int64
	value []float64
}

func newFloatDistinctItem() *floatDistinctItem {
	return &floatDistinctItem{
		m: make(map[float64]struct{}),
	}
}

func (f *floatDistinctItem) appendItem(time []int64, value []float64) {
	for i := 0; i < len(time); i++ {
		if _, ok := f.m[value[i]]; !ok {
			f.m[value[i]] = struct{}{}
			f.time = append(f.time, time[i])
			f.value = append(f.value, value[i])
		}
	}
}

func (f *floatDistinctItem) Nil() bool {
	return len(f.time) == 0
}

func (f *floatDistinctItem) Reset() {
	for k := range f.m {
		delete(f.m, k)
	}
	f.time = f.time[:0]
	f.value = f.value[:0]
}

func (f *floatDistinctItem) Len() int {
	return len(f.time)
}

func (f *floatDistinctItem) Less(i, j int) bool {
	if f.time[i] != f.time[j] {
		return f.time[i] < f.time[j]
	}
	return f.value[i] < f.value[j]
}

func (f *floatDistinctItem) Swap(i, j int) {
	f.time[i], f.time[j] = f.time[j], f.time[i]
	f.value[i], f.value[j] = f.value[j], f.value[i]
}

type integerDistinctItem struct {
	m     map[int64]struct{}
	time  []int64
	value []int64
}

func newIntegerDistinctItem() *integerDistinctItem {
	return &integerDistinctItem{
		m: make(map[int64]struct{}),
	}
}

func (f *integerDistinctItem) appendItem(time []int64, value []int64) {
	for i := 0; i < len(time); i++ {
		if _, ok := f.m[value[i]]; !ok {
			f.m[value[i]] = struct{}{}
			f.time = append(f.time, time[i])
			f.value = append(f.value, value[i])
		}
	}
}

func (f *integerDistinctItem) Nil() bool {
	return len(f.time) == 0
}

func (f *integerDistinctItem) Reset() {
	for k := range f.m {
		delete(f.m, k)
	}
	f.time = f.time[:0]
	f.value = f.value[:0]
}

func (f *integerDistinctItem) Len() int {
	return len(f.time)
}

func (f *integerDistinctItem) Less(i, j int) bool {
	if f.time[i] != f.time[j] {
		return f.time[i] < f.time[j]
	}
	return f.value[i] < f.value[j]
}

func (f *integerDistinctItem) Swap(i, j int) {
	f.time[i], f.time[j] = f.time[j], f.time[i]
	f.value[i], f.value[j] = f.value[j], f.value[i]
}

type stringDistinctItem struct {
	m     map[string]struct{}
	time  []int64
	value []string
}

func newStringDistinctItem() *stringDistinctItem {
	return &stringDistinctItem{
		m: make(map[string]struct{}),
	}
}

func (f *stringDistinctItem) appendItem(time []int64, value []string) {
	for i := 0; i < len(time); i++ {
		if _, ok := f.m[value[i]]; !ok {
			f.m[value[i]] = struct{}{}
			f.time = append(f.time, time[i])
			f.value = append(f.value, value[i])
		}
	}
}

func (f *stringDistinctItem) Nil() bool {
	return len(f.time) == 0
}

func (f *stringDistinctItem) Reset() {
	for k := range f.m {
		delete(f.m, k)
	}
	f.time = f.time[:0]
	f.value = f.value[:0]
}

func (f *stringDistinctItem) Len() int {
	return len(f.time)
}

func (f *stringDistinctItem) Less(i, j int) bool {
	if f.time[i] != f.time[j] {
		return f.time[i] < f.time[j]
	}
	return f.value[i] < f.value[j]
}

func (f *stringDistinctItem) Swap(i, j int) {
	f.time[i], f.time[j] = f.time[j], f.time[i]
	f.value[i], f.value[j] = f.value[j], f.value[i]
}

type booleanDistinctItem struct {
	m     map[bool]struct{}
	time  []int64
	value []bool
}

func newBooleanDistinctItem() *booleanDistinctItem {
	return &booleanDistinctItem{
		m: make(map[bool]struct{}),
	}
}

func (f *booleanDistinctItem) appendItem(time []int64, value []bool) {
	for i := 0; i < len(time); i++ {
		if _, ok := f.m[value[i]]; !ok {
			f.m[value[i]] = struct{}{}
			f.time = append(f.time, time[i])
			f.value = append(f.value, value[i])
		}
	}
}

func (f *booleanDistinctItem) Nil() bool {
	return len(f.time) == 0
}

func (f *booleanDistinctItem) Reset() {
	for k := range f.m {
		delete(f.m, k)
	}
	f.time = f.time[:0]
	f.value = f.value[:0]
}

func (f *booleanDistinctItem) Len() int {
	return len(f.time)
}

func (f *booleanDistinctItem) Less(i, j int) bool {
	if f.time[i] != f.time[j] {
		return f.time[i] < f.time[j]
	}
	return !f.value[i]
}

func (f *booleanDistinctItem) Swap(i, j int) {
	f.time[i], f.time[j] = f.time[j], f.time[i]
	f.value[i], f.value[j] = f.value[j], f.value[i]
}

type floatColFloatDistinctReducer struct {
	buf *floatDistinctItem
}

func newFloatColFloatDistinctReducer() *floatColFloatDistinctReducer {
	return &floatColFloatDistinctReducer{buf: newFloatDistinctItem()}
}

func (r *floatColFloatDistinctReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	time, value := inRecord.ColVals[inRecord.Len()-1].IntegerValues(), inRecord.ColVals[inOrdinal].FloatValues()
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && !r.buf.Nil() {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			if firstIndex != lastIndex || !param.sameWindow {
				sort.Sort(r.buf)
				if r.buf.Len() > 0 {
					outRecord.AppendTime(r.buf.time...)
					outRecord.ColVals[outOrdinal].AppendFloats(r.buf.value...)
				}
				r.buf.Reset()
			}
			continue
		} else if i == lastIndex && param.sameWindow {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			break
		}
		r.buf.appendItem(time[int(start):end], value[int(start):end])
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outRecord.AppendTime(r.buf.time...)
			outRecord.ColVals[outOrdinal].AppendFloats(r.buf.value...)
		}
		r.buf.Reset()
	}
}

type integerColIntegerDistinctReducer struct {
	buf *integerDistinctItem
}

func newIntegerColIntegerDistinctReducer() *integerColIntegerDistinctReducer {
	return &integerColIntegerDistinctReducer{buf: newIntegerDistinctItem()}
}

func (r *integerColIntegerDistinctReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	time, value := inRecord.ColVals[inRecord.Len()-1].IntegerValues(), inRecord.ColVals[inOrdinal].IntegerValues()
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && !r.buf.Nil() {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			if firstIndex != lastIndex || !param.sameWindow {
				sort.Sort(r.buf)
				if r.buf.Len() > 0 {
					outRecord.AppendTime(r.buf.time...)
					outRecord.ColVals[outOrdinal].AppendIntegers(r.buf.value...)
				}
				r.buf.Reset()
			}
			continue
		} else if i == lastIndex && param.sameWindow {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			break
		}
		r.buf.appendItem(time[int(start):end], value[int(start):end])
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outRecord.AppendTime(r.buf.time...)
			outRecord.ColVals[outOrdinal].AppendIntegers(r.buf.value...)
		}
		r.buf.Reset()
	}
}

type booleanColBooleanDistinctReducer struct {
	buf *booleanDistinctItem
}

func newBooleanColBooleanDistinctReducer() *booleanColBooleanDistinctReducer {
	return &booleanColBooleanDistinctReducer{buf: newBooleanDistinctItem()}
}

func (r *booleanColBooleanDistinctReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	time, value := inRecord.ColVals[inRecord.Len()-1].IntegerValues(), inRecord.ColVals[inOrdinal].BooleanValues()
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && !r.buf.Nil() {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			if firstIndex != lastIndex || !param.sameWindow {
				sort.Sort(r.buf)
				if r.buf.Len() > 0 {
					outRecord.AppendTime(r.buf.time...)
					outRecord.ColVals[outOrdinal].AppendBooleans(r.buf.value...)
				}
				r.buf.Reset()
			}
			continue
		} else if i == lastIndex && param.sameWindow {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			break
		}
		r.buf.appendItem(time[int(start):end], value[int(start):end])
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outRecord.AppendTime(r.buf.time...)
			outRecord.ColVals[outOrdinal].AppendBooleans(r.buf.value...)
		}
		r.buf.Reset()
	}
}

type stringColStringDistinctReducer struct {
	buf   *stringDistinctItem
	value []string
}

func newStringColStringDistinctReducer() *stringColStringDistinctReducer {
	return &stringColStringDistinctReducer{buf: newStringDistinctItem()}
}

func (r *stringColStringDistinctReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	var end int
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	inOrdinal, outOrdinal := p.InputPoint.Ordinal, p.OutputPoint.Ordinal
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	if cap(r.value) < inRecord.ColVals[inOrdinal].Length() {
		r.value = make([]string, 0, inRecord.ColVals[inOrdinal].Length())
	} else {
		r.value = r.value[:0]
	}
	time, value := inRecord.ColVals[inRecord.Len()-1].IntegerValues(), inRecord.ColVals[inOrdinal].StringValues(r.value)
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && !r.buf.Nil() {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			if firstIndex != lastIndex || !param.sameWindow {
				sort.Sort(r.buf)
				if r.buf.Len() > 0 {
					outRecord.AppendTime(r.buf.time...)
					outRecord.ColVals[outOrdinal].AppendStrings(r.buf.value...)
				}
				r.buf.Reset()
			}
			continue
		} else if i == lastIndex && param.sameWindow {
			r.buf.appendItem(time[int(start):end], value[int(start):end])
			break
		}
		r.buf.appendItem(time[int(start):end], value[int(start):end])
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outRecord.AppendTime(r.buf.time...)
			outRecord.ColVals[outOrdinal].AppendStrings(r.buf.value...)
		}
		r.buf.Reset()
	}
}

type FloatPointItem struct {
	time  int64
	value float64
	index int
}

func NewFloatPointItem(time int64, value float64) *FloatPointItem {
	return &FloatPointItem{
		time:  time,
		value: value,
	}
}

type FloatHeapItem struct {
	sortByTime   bool
	maxIndex     int
	cmpByValue   func(a, b *FloatPointItem) bool
	cmpByTime    func(a, b *FloatPointItem) bool
	items        []FloatPointItem
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func NewFloatHeapItem(n int, cmpByValue, cmpByTime func(a, b *FloatPointItem) bool) *FloatHeapItem {
	return &FloatHeapItem{
		items:      make([]FloatPointItem, 0, n),
		cmpByValue: cmpByValue,
		cmpByTime:  cmpByTime,
	}
}

func (f *FloatHeapItem) appendFast(input *record.Record, start, end, ordinal int) {
	// fast path
	for i := start; i < end; i++ {
		p := NewFloatPointItem(
			input.Time(i),
			input.Column(ordinal).FloatValues()[i])
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			heap.Push(f, *p)
		}
	}
}
func (f *FloatHeapItem) appendSlow(input *record.Record, start, end, ordinal int) {
	// slow path
	for i := start; i < end; i++ {
		val, isNil := input.Column(ordinal).FloatValue(i)
		if isNil {
			continue
		}
		p := NewFloatPointItem(
			input.Time(i),
			val)
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			heap.Push(f, *p)
		}
	}
}
func (f *FloatHeapItem) append(input *record.Record, start, end, ordinal int) {
	if input.ColVals[ordinal].NilCount == 0 {
		f.appendFast(input, start, end, ordinal)
	} else {
		f.appendSlow(input, start, end, ordinal)
	}
}

func (f *FloatHeapItem) appendForAuxFast(input *record.Record, start, end, ordinal, maxIndex int) {
	// fast path
	for i := start; i < end; i++ {
		p := NewFloatPointItem(
			input.Time(i),
			input.Column(ordinal).FloatValues()[i])
		p.index = maxIndex + i
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			heap.Push(f, *p)
		}
	}
}

func (f *FloatHeapItem) appendForAuxSlow(input *record.Record, start, end, ordinal, maxIndex int) {
	// slow path
	for i := start; i < end; i++ {
		val, isNil := input.Column(ordinal).FloatValue(i)
		if isNil {
			continue
		}
		p := NewFloatPointItem(
			input.Time(i),
			val)
		p.index = maxIndex + i
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			heap.Push(f, *p)
		}
	}
}

func (f *FloatHeapItem) appendForAux(input *record.Record, start, end, ordinal int) []int {
	// make each index unique
	maxIndex := f.maxIndex + 1 - start
	if input.ColVals[ordinal].NilCount == 0 {
		f.appendForAuxFast(input, start, end, ordinal, maxIndex)
	} else {
		f.appendForAuxSlow(input, start, end, ordinal, maxIndex)
	}
	index := make([]int, 0)
	for i := range f.items {
		if idx := f.items[i].index - maxIndex; idx >= start {
			index = append(index, idx)
		}
	}
	return index
}

func (f *FloatHeapItem) Reset() {
	f.items = f.items[:0]
	f.sortByTime = false
	f.maxIndex = 0
}

func (f *FloatHeapItem) Len() int {
	return len(f.items)
}
func (f *FloatHeapItem) Less(i, j int) bool {
	if !f.sortByTime {
		return f.cmpByValue(&f.items[i], &f.items[j])
	}
	return f.cmpByTime(&f.items[i], &f.items[j])
}
func (f *FloatHeapItem) Swap(i, j int) {
	f.items[i], f.items[j] = f.items[j], f.items[i]
}
func (f *FloatHeapItem) Push(x interface{}) {
	f.items = append(f.items, x.(FloatPointItem))
}
func (f *FloatHeapItem) Pop() interface{} {
	p := f.items[len(f.items)-1]
	f.items = f.items[:len(f.items)-1]
	return p
}

type IntegerPointItem struct {
	time  int64
	value int64
	index int
}

func NewIntegerPointItem(time int64, value int64) *IntegerPointItem {
	return &IntegerPointItem{
		time:  time,
		value: value,
	}
}

type IntegerHeapItem struct {
	sortByTime   bool
	maxIndex     int
	cmpByValue   func(a, b *IntegerPointItem) bool
	cmpByTime    func(a, b *IntegerPointItem) bool
	items        []IntegerPointItem
	auxRecord    *record.Record
	auxProcessor []*auxProcessor
}

func NewIntegerHeapItem(n int, cmpByValue, cmpByTime func(a, b *IntegerPointItem) bool) *IntegerHeapItem {
	return &IntegerHeapItem{
		items:      make([]IntegerPointItem, 0, n),
		cmpByValue: cmpByValue,
		cmpByTime:  cmpByTime,
	}
}

func (f *IntegerHeapItem) appendFast(input *record.Record, start, end, ordinal int) {
	// fast path
	for i := start; i < end; i++ {
		p := NewIntegerPointItem(
			input.Time(i),
			input.Column(ordinal).IntegerValues()[i])
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			heap.Push(f, *p)
		}
	}
}
func (f *IntegerHeapItem) appendSlow(input *record.Record, start, end, ordinal int) {
	// slow path
	for i := start; i < end; i++ {
		val, isNil := input.Column(ordinal).IntegerValue(i)
		if isNil {
			continue
		}
		p := NewIntegerPointItem(
			input.Time(i),
			val)
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			heap.Push(f, *p)
		}
	}
}
func (f *IntegerHeapItem) append(input *record.Record, start, end, ordinal int) {
	if input.ColVals[ordinal].NilCount == 0 {
		f.appendFast(input, start, end, ordinal)
	} else {
		f.appendSlow(input, start, end, ordinal)
	}
}

func (f *IntegerHeapItem) appendForAuxFast(input *record.Record, start, end, ordinal, maxIndex int) {
	// fast path
	for i := start; i < end; i++ {
		p := NewIntegerPointItem(
			input.Time(i),
			input.Column(ordinal).IntegerValues()[i])
		p.index = maxIndex + i
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			heap.Push(f, *p)
		}
	}
}

func (f *IntegerHeapItem) appendForAuxSlow(input *record.Record, start, end, ordinal, maxIndex int) {
	// slow path
	for i := start; i < end; i++ {
		val, isNil := input.Column(ordinal).IntegerValue(i)
		if isNil {
			continue
		}
		p := NewIntegerPointItem(
			input.Time(i),
			val)
		p.index = maxIndex + i
		if f.Len() == cap(f.items) {
			if !f.cmpByValue(&f.items[0], p) {
				continue
			}
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			f.items[0] = *p
			heap.Fix(f, 0)
			continue
		} else {
			if (*p).index > f.maxIndex {
				f.maxIndex = (*p).index
			}
			heap.Push(f, *p)
		}
	}
}

func (f *IntegerHeapItem) appendForAux(input *record.Record, start, end, ordinal int) []int {
	// make each index unique
	maxIndex := f.maxIndex + 1 - start
	if input.ColVals[ordinal].NilCount == 0 {
		f.appendForAuxFast(input, start, end, ordinal, maxIndex)
	} else {
		f.appendForAuxSlow(input, start, end, ordinal, maxIndex)
	}
	index := make([]int, 0)
	for i := range f.items {
		if idx := f.items[i].index - maxIndex; idx >= start {
			index = append(index, idx)
		}
	}
	return index
}

func (f *IntegerHeapItem) Reset() {
	f.items = f.items[:0]
	f.sortByTime = false
	f.maxIndex = 0
}

func (f *IntegerHeapItem) Len() int {
	return len(f.items)
}
func (f *IntegerHeapItem) Less(i, j int) bool {
	if !f.sortByTime {
		return f.cmpByValue(&f.items[i], &f.items[j])
	}
	return f.cmpByTime(&f.items[i], &f.items[j])
}
func (f *IntegerHeapItem) Swap(i, j int) {
	f.items[i], f.items[j] = f.items[j], f.items[i]
}
func (f *IntegerHeapItem) Push(x interface{}) {
	f.items = append(f.items, x.(IntegerPointItem))
}
func (f *IntegerHeapItem) Pop() interface{} {
	p := f.items[len(f.items)-1]
	f.items = f.items[:len(f.items)-1]
	return p
}

type floatColFloatHeapReducer struct {
	n             int
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *FloatHeapItem
	auxRecord     *record.Record
	auxProcessor  []*auxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewFloatColFloatHeapReducer(inOrdinal, outOrdinal int, auxProcessors []*auxProcessor, floatHeapItem *FloatHeapItem) *floatColFloatHeapReducer {
	r := &floatColFloatHeapReducer{
		buf:          floatHeapItem,
		auxProcessor: auxProcessors,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	return r
}

func (r *floatColFloatHeapReducer) appendPrevItem(inRecord, outRecord *record.Record) {
	for j := range r.buf.items {
		outRecord.AppendTime(r.buf.items[j].time)
		outRecord.ColVals[r.outOrdinal].AppendFloats(r.buf.items[j].value)
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
				&outRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxRecord.Reuse()
	}
}

func (r *floatColFloatHeapReducer) appendCurrItem(inRecord, outRecord *record.Record, start int) {
	for j := range r.buf.items {
		outRecord.AppendTime(r.buf.items[j].time)
		outRecord.ColVals[r.outOrdinal].AppendFloats(r.buf.items[j].value)
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
				&outRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *floatColFloatHeapReducer) updateAuxColInRecord(inRecord *record.Record) {
	if len(r.interBufIndex) == 0 {
		r.auxRecord.Reuse()
	}
	// inserts elements pushed from the heap
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)

	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
			&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
			r.windowIndex...,
		)
	}
}

func (r *floatColFloatHeapReducer) updateAuxColBothRecord(inRecord *record.Record) {
	clone := r.auxRecord.Clone()
	r.auxRecord.Reuse()
	sort.Ints(r.interBufIndex)

	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}

	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			// inserts elements still remained in the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					&clone.ColVals[r.auxProcessor[j].outOrdinal],
					&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			// inserts elements pushed from the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
					&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reuse()
}

func (r *floatColFloatHeapReducer) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
	r.buf.sortByTime = false
}

func (r *floatColFloatHeapReducer) updatePrevItem(
	inRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(inRecord, start, end, r.inOrdinal)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.buf.sortByTime = false
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1

		r.windowIndex = r.buf.appendForAux(inRecord, start, end, r.inOrdinal)

		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}

		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)

		r.buf.sortByTime = true
		sort.Sort(r.buf)

		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothRecord(inRecord)
		} else {
			r.updateAuxColInRecord(inRecord)
		}
	}
	r.reset()
}

func (r *floatColFloatHeapReducer) updateCurrItem(
	inRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(inRecord, start, end, r.inOrdinal)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inRecord, start, end, r.inOrdinal)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
				&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
		r.buf.sortByTime = false
	}
}

func (r *floatColFloatHeapReducer) processFirstWindow(
	inRecord, outRecord *record.Record, sameWindow, multiWindow bool, start, end int,
) {
	r.updatePrevItem(inRecord, start, end)
	if multiWindow || !sameWindow {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxRecord, outRecord)
		}
		r.buf.Reset()
	}
}

func (r *floatColFloatHeapReducer) processLastWindow(
	intRecord *record.Record, start, end int,
) {
	r.updateCurrItem(intRecord, start, end)
}

func (r *floatColFloatHeapReducer) processMiddleWindow(
	intRecord, outRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(intRecord, start, end, r.inOrdinal)
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(intRecord, start, end, r.inOrdinal)
	}
	r.buf.sortByTime = true
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(intRecord, outRecord, start)
	}
	r.buf.Reset()
}

func (r *floatColFloatHeapReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	var end int
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inRecord, outRecord, param.sameWindow,
				firstIndex != lastIndex, int(start), end)
		} else if i == lastIndex && param.sameWindow {
			r.processLastWindow(inRecord, int(start), end)
		} else {
			r.processMiddleWindow(inRecord, outRecord, int(start), end)
		}
	}
}

type integerColIntegerHeapReducer struct {
	n             int
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *IntegerHeapItem
	auxRecord     *record.Record
	auxProcessor  []*auxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewIntegerColIntegerHeapReducer(inOrdinal, outOrdinal int, auxProcessors []*auxProcessor, integerHeapItem *IntegerHeapItem) *integerColIntegerHeapReducer {
	r := &integerColIntegerHeapReducer{
		buf:          integerHeapItem,
		auxProcessor: auxProcessors,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	return r
}

func (r *integerColIntegerHeapReducer) appendPrevItem(inRecord, outRecord *record.Record) {
	for j := range r.buf.items {
		outRecord.AppendTime(r.buf.items[j].time)
		outRecord.ColVals[r.outOrdinal].AppendIntegers(r.buf.items[j].value)
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
				&outRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxRecord.Reuse()
	}
}

func (r *integerColIntegerHeapReducer) appendCurrItem(inRecord, outRecord *record.Record, start int) {
	for j := range r.buf.items {
		outRecord.AppendTime(r.buf.items[j].time)
		outRecord.ColVals[r.outOrdinal].AppendIntegers(r.buf.items[j].value)
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
				&outRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *integerColIntegerHeapReducer) updateAuxColInRecord(inRecord *record.Record) {
	if len(r.interBufIndex) == 0 {
		r.auxRecord.Reuse()
	}
	// inserts elements pushed from the heap
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)

	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
			&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
			r.windowIndex...,
		)
	}
}

func (r *integerColIntegerHeapReducer) updateAuxColBothRecord(inRecord *record.Record) {
	clone := r.auxRecord.Clone()
	r.auxRecord.Reuse()
	sort.Ints(r.interBufIndex)

	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}

	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			// inserts elements still remained in the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					&clone.ColVals[r.auxProcessor[j].outOrdinal],
					&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			// inserts elements pushed from the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
					&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reuse()
}

func (r *integerColIntegerHeapReducer) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
	r.buf.sortByTime = false
}

func (r *integerColIntegerHeapReducer) updatePrevItem(
	inRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(inRecord, start, end, r.inOrdinal)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.buf.sortByTime = false
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1

		r.windowIndex = r.buf.appendForAux(inRecord, start, end, r.inOrdinal)

		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}

		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)

		r.buf.sortByTime = true
		sort.Sort(r.buf)

		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothRecord(inRecord)
		} else {
			r.updateAuxColInRecord(inRecord)
		}
	}
	r.reset()
}

func (r *integerColIntegerHeapReducer) updateCurrItem(
	inRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(inRecord, start, end, r.inOrdinal)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inRecord, start, end, r.inOrdinal)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				&inRecord.ColVals[r.auxProcessor[j].inOrdinal],
				&r.auxRecord.ColVals[r.auxProcessor[j].outOrdinal],
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
		r.buf.sortByTime = false
	}
}

func (r *integerColIntegerHeapReducer) processFirstWindow(
	inRecord, outRecord *record.Record, sameWindow, multiWindow bool, start, end int,
) {
	r.updatePrevItem(inRecord, start, end)
	if multiWindow || !sameWindow {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxRecord, outRecord)
		}
		r.buf.Reset()
	}
}

func (r *integerColIntegerHeapReducer) processLastWindow(
	intRecord *record.Record, start, end int,
) {
	r.updateCurrItem(intRecord, start, end)
}

func (r *integerColIntegerHeapReducer) processMiddleWindow(
	intRecord, outRecord *record.Record, start, end int,
) {
	if len(r.auxProcessor) == 0 {
		r.buf.append(intRecord, start, end, r.inOrdinal)
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(intRecord, start, end, r.inOrdinal)
	}
	r.buf.sortByTime = true
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(intRecord, outRecord, start)
	}
	r.buf.Reset()
}

func (r *integerColIntegerHeapReducer) Aggregate(p *ReducerEndpoint, param *ReducerParams) {
	if len(r.auxProcessor) > 0 && r.auxRecord == nil {
		r.auxRecord = record.NewRecordBuilder(p.OutputPoint.Record.Schema)
	}
	inRecord, outRecord := p.InputPoint.Record, p.OutputPoint.Record
	firstIndex, lastIndex := 0, len(param.intervalIndex)-1
	var end int
	for i, start := range param.intervalIndex {
		if i < lastIndex {
			end = int(param.intervalIndex[i+1])
		} else {
			end = inRecord.RowNums()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inRecord, outRecord, param.sameWindow,
				firstIndex != lastIndex, int(start), end)
		} else if i == lastIndex && param.sameWindow {
			r.processLastWindow(inRecord, int(start), end)
		} else {
			r.processMiddleWindow(inRecord, outRecord, int(start), end)
		}
	}
}
