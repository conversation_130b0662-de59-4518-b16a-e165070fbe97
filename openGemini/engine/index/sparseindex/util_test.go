// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package sparseindex

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGenEmptyFullTextIndexData(t *testing.T) {
	f := FullTextIdxWriter{&skipIndexWriter{}}
	res := f.genFullTextIndexData(nil, nil, nil)
	assert.Equal(t, 0, len(res))
}
