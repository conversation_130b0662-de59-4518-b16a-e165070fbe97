// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package hybridqp

import "github.com/openGemini/openGemini/lib/tracing"

type Executor interface {
}

type Planner interface {
	SetRoot(QueryNode)
	FindBestExp() QueryNode
}

type PipelineExecutorBuilder interface {
	Analyze(span *tracing.Span)

	Build(node QueryNode) (Executor, error)
}

type ExecutorBuilderCreator func() PipelineExecutorBuilder
type ExecutorBuilderOptimizer func() Planner
