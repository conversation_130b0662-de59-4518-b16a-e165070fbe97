/*
Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package engine

//lint:ignore U1000 use for replication feature
type summaryInfo struct {
	flushedLogSN     int
	maxLogSN         int
	replRecoverInfos map[uint32]*replRecoverInfo // key is pt id, value is replica group recover info, used for slave catch up
}

//lint:ignore U1000 use for replication feature
type replRecoverInfo struct {
	syncedLogSN  int
	recoverLogSN int
}
