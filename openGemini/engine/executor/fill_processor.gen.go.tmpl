// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package executor

{{range .}}
{{- if or (eq .Name "Float") (eq .Name "Integer")}}
type {{.Name}}LinearFillProcessor struct {
	inOrdinal  int
	outOrdinal int
}

func New{{.Name}}LinearFillProcessor(inOrdinal, outOrdinal int) *{{.Name}}LinearFillProcessor {
	return &{{.Name}}LinearFillProcessor{
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (f *{{.Name}}LinearFillProcessor) fillHelperFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if (prev != nil && fillItem.inputReadAt >= fillItem.prevReadAt) &&
		(fillItem.prevReadAt < prev.Len() && !prev.Column(f.inOrdinal).IsNilV2(fillItem.prevReadAt)) &&
		(fillItem.inputReadAt < input.Len() && !input.Column(f.inOrdinal).IsNilV2(fillItem.inputReadAt)) {
        inputValueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.inputReadAt)
        prevValueIndex := prev.Column(f.inOrdinal).GetValueIndexV2(fillItem.prevReadAt)
		output.Column(f.outOrdinal).Append{{.Name}}Value(
			hybridqp.LinearInterpolate{{.Name}}(
				fillItem.start,
				prev.TimeByIndex(fillItem.prevReadAt)/fillItem.interval,
				input.TimeByIndex(fillItem.inputReadAt)/fillItem.interval,
				prev.Column(f.inOrdinal).{{.Name}}Value(prevValueIndex),
				input.Column(f.inOrdinal).{{.Name}}Value(inputValueIndex),
			),
		)
		output.Column(f.outOrdinal).AppendNotNil()
	} else if (prev == nil || fillItem.inputReadAt < fillItem.prevReadAt) ||
      	(fillItem.prevReadAt < prev.Len() && prev.Column(f.inOrdinal).IsNilV2(fillItem.prevReadAt)) ||
      	(fillItem.inputReadAt == input.Len()-1 && input.Column(f.inOrdinal).IsNilV2(fillItem.inputReadAt)) ||
        fillItem.inputReadAt == input.Len() {
		output.Column(f.outOrdinal).AppendNil()
	}
}

func (f *{{.Name}}LinearFillProcessor) fillAppendFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if !input.Column(f.inOrdinal).IsNilV2(fillItem.currIndex) {
		valueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.currIndex)
		output.Column(f.outOrdinal).Append{{.Name}}Value(input.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		if input.Column(f.inOrdinal).ColumnTimes() != nil{
			output.Column(f.outOrdinal).AppendColumnTime(input.Column(f.inOrdinal).ColumnTime(valueIndex))
		}
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
	    f.fillHelperFunc(input, output, prev, fillItem, prevWindow)
	}
}
{{- end}}
{{end}}


{{range .}}
{{- if or (eq .Name "String") (eq .Name "Boolean")}}
type {{.Name}}LinearFillProcessor struct {
	inOrdinal  int
	outOrdinal int
}

func New{{.Name}}LinearFillProcessor(inOrdinal, outOrdinal int) *{{.Name}}LinearFillProcessor {
	return &{{.Name}}LinearFillProcessor{
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (f *{{.Name}}LinearFillProcessor) fillHelperFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	output.Column(f.outOrdinal).AppendNil()
}

func (f *{{.Name}}LinearFillProcessor) fillAppendFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if !input.Column(f.inOrdinal).IsNilV2(fillItem.currIndex) {
		valueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.currIndex)
		output.Column(f.outOrdinal).Append{{.Name}}Value(input.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		if input.Column(f.inOrdinal).ColumnTimes() != nil{
			output.Column(f.outOrdinal).AppendColumnTime(input.Column(f.inOrdinal).ColumnTime(valueIndex))
		}
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
	    f.fillHelperFunc(input, output, prev, fillItem, prevWindow)
	}
}
{{- end}}
{{end}}


{{range .}}
type {{.Name}}NullFillProcessor struct {
	inOrdinal  int
	outOrdinal int
}

func New{{.Name}}NullFillProcessor(inOrdinal, outOrdinal int) *{{.Name}}NullFillProcessor {
	return &{{.Name}}NullFillProcessor{
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (f *{{.Name}}NullFillProcessor) fillHelperFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	// append nil value
	output.Column(f.outOrdinal).AppendNil()
}

func (f *{{.Name}}NullFillProcessor) fillAppendFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if !input.Column(f.inOrdinal).IsNilV2(fillItem.currIndex) {
		valueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.currIndex)
		output.Column(f.outOrdinal).Append{{.Name}}Value(input.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		if input.Column(f.inOrdinal).ColumnTimes() != nil{
			output.Column(f.outOrdinal).AppendColumnTime(input.Column(f.inOrdinal).ColumnTime(valueIndex))
		}
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
        f.fillHelperFunc(input, output, prev, fillItem, prevWindow)
    }
}
{{end}}

{{range .}}
type {{.Name}}NumberFillProcessor struct {
	inOrdinal  int
	outOrdinal int
}

func New{{.Name}}NumberFillProcessor(inOrdinal, outOrdinal int) *{{.Name}}NumberFillProcessor {
	return &{{.Name}}NumberFillProcessor{
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (f *{{.Name}}NumberFillProcessor) fillHelperFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	value, _ := hybridqp.TransTo{{.Name}}(fillItem.fillValue)
	output.Column(f.outOrdinal).Append{{.Name}}Value(value)
	output.Column(f.outOrdinal).AppendNotNil()
}

func (f *{{.Name}}NumberFillProcessor) fillAppendFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if !input.Column(f.inOrdinal).IsNilV2(fillItem.currIndex) {
		valueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.currIndex)
		output.Column(f.outOrdinal).Append{{.Name}}Value(input.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		if input.Column(f.inOrdinal).ColumnTimes() != nil{
			output.Column(f.outOrdinal).AppendColumnTime(input.Column(f.inOrdinal).ColumnTime(valueIndex))
		}
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
	    f.fillHelperFunc(input, output, prev, fillItem, prevWindow)
	}
}
{{end}}

{{range .}}
type {{.Name}}PreviousFillProcessor struct {
	inOrdinal  int
	outOrdinal int
}

func New{{.Name}}PreviousFillProcessor(inOrdinal, outOrdinal int) *{{.Name}}PreviousFillProcessor {
	return &{{.Name}}PreviousFillProcessor{
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (f *{{.Name}}PreviousFillProcessor) fillHelperFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if prev != nil && !prev.Column(f.inOrdinal).IsNilV2(fillItem.prevReadAt) {
	    valueIndex := prev.Column(f.inOrdinal).GetValueIndexV2(fillItem.prevReadAt)
		output.Column(f.outOrdinal).Append{{.Name}}Value(prev.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		output.Column(f.outOrdinal).AppendNotNil()
	} else if prevWindow.value != nil && !prevWindow.nil[f.inOrdinal] {
		output.Column(f.outOrdinal).Append{{.Name}}Value(prevWindow.value[f.inOrdinal].({{.Type}}))
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
		// append nil value
		output.Column(f.outOrdinal).AppendNil()
	}
}

func (f *{{.Name}}PreviousFillProcessor) fillAppendFunc(input, output, prev Chunk, fillItem *FillItem, prevWindow *prevWindow) {
	if !input.Column(f.inOrdinal).IsNilV2(fillItem.currIndex) {
		valueIndex := input.Column(f.inOrdinal).GetValueIndexV2(fillItem.currIndex)
		output.Column(f.outOrdinal).Append{{.Name}}Value(input.Column(f.inOrdinal).{{.Name}}Value(valueIndex))
		if input.Column(f.inOrdinal).ColumnTimes() != nil{
			output.Column(f.outOrdinal).AppendColumnTime(input.Column(f.inOrdinal).ColumnTime(valueIndex))
		}
		output.Column(f.outOrdinal).AppendNotNil()
	} else {
	    f.fillHelperFunc(input, output, prev, fillItem, prevWindow)
	}
}
{{end}}
