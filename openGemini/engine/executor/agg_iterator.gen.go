// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: agg_iterator.gen.go.tmpl

/*
Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package executor

import (
	"sort"

	"github.com/openGemini/openGemini/engine/hybridqp"
)


type FloatTimeColFloatIterator struct {
	initTimeCol bool
	inOrdinal   int
	outOrdinal  int
	prevPoint   *Point[float64]
	currPoint   *Point[float64]
	fn          TimeColReduceFunc[float64]
	fv          ColMergeFunc[float64]
}

func NewFloatTimeColFloatIterator(
	fn TimeColReduceFunc[float64], fv ColMergeFunc[float64], inOrdinal, outOrdinal int,
) *FloatTimeColFloatIterator {
	r := &FloatTimeColFloatIterator{
		fn:         fn,
		fv:         fv,
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
		prevPoint:  newPoint[float64](),
		currPoint:  newPoint[float64](),
	}
	return r
}

func (r *FloatTimeColFloatIterator) mergePrevItem(
	outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	outColumn.AppendFloatValue(r.prevPoint.value)
	outColumn.AppendColumnTime(r.prevPoint.time)
	outColumn.AppendNotNil()
}

func (r *FloatTimeColFloatIterator) processFirstWindow(
	inChunk, outChunk Chunk, isNil, sameInterval, onlyOneInterval bool, index int, value float64,
) {
	// To distinguish values between inChunk and auxChunk, r.currPoint.index incremented by 1.
	if !isNil {
		if r.initTimeCol {
			r.currPoint.Set(index+1, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
		} else {
			r.currPoint.Set(index+1, inChunk.TimeByIndex(index), value)
		}
		r.fv(r.prevPoint, r.currPoint)
	}
	if onlyOneInterval && sameInterval {
		r.prevPoint.index = 0
	} else {
		if !r.prevPoint.isNil {
			r.mergePrevItem(outChunk)
		}
		r.prevPoint.Reset()
	}
	r.currPoint.Reset()
}

func (r *FloatTimeColFloatIterator) processLastWindow(
	inChunk Chunk, index int, isNil bool, value float64,
) {
	if isNil {
		r.prevPoint.Reset()
		return
	}
	if r.initTimeCol {
		r.prevPoint.Set(0, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
	} else {
		r.prevPoint.Set(0, inChunk.TimeByIndex(index), value)
	}
}

func (r *FloatTimeColFloatIterator) processMiddleWindow(
	inChunk, outChunk Chunk, index int, value float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if r.initTimeCol {
		outColumn.AppendColumnTime(inChunk.Column(r.inOrdinal).ColumnTime(index))
	} else {
		outColumn.AppendColumnTime(inChunk.TimeByIndex(index))
	}
	outColumn.AppendFloatValue(value)
	outColumn.AppendNotNil()
}

func (r *FloatTimeColFloatIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	if inColumn.IsEmpty() && r.prevPoint.isNil {
		var addIntervalLen int
		if p.sameInterval {
			addIntervalLen = inChunk.IntervalLen() - 1
		} else {
			addIntervalLen = inChunk.IntervalLen()
		}
		if addIntervalLen > 0 {
			outColumn.AppendManyNil(addIntervalLen)
		}
		return
	}

	var end int
	r.initTimeCol = len(inColumn.ColumnTimes()) > 0
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	values := inColumn.FloatValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		index, value, isNil := r.fn(inChunk, values, r.inOrdinal, start, end)
		if isNil && ((i > firstIndex && i < lastIndex) ||
			(firstIndex == lastIndex && r.prevPoint.isNil && !p.sameInterval) ||
			(firstIndex != lastIndex && i == firstIndex && r.prevPoint.isNil) ||
			(firstIndex != lastIndex && i == lastIndex && !p.sameInterval)) {
			outColumn.AppendNil()
			continue
		}
		if i == firstIndex && !r.prevPoint.isNil {
			r.processFirstWindow(inChunk, outChunk, isNil, p.sameInterval,
				firstIndex == lastIndex, index, value)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, index, isNil, value)
		} else if !isNil {
			r.processMiddleWindow(inChunk, outChunk, index, value)
		}
	}
}

type StringTimeColStringMerge func(prevPoint, currPoint *StringPoint)

type StringTimeColStringIterator struct {
	initTimeCol bool
	inOrdinal   int
	outOrdinal  int
	prevPoint   *StringPoint
	currPoint   *StringPoint
	fn          TimeColReduceFunc[string]
	fv          StringTimeColStringMerge
}

func NewStringTimeColStringIterator(
	fn TimeColReduceFunc[string], fv StringTimeColStringMerge, inOrdinal, outOrdinal int,
) *StringTimeColStringIterator {
	r := &StringTimeColStringIterator{
		fn:         fn,
		fv:         fv,
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
		prevPoint:  newStringPoint(),
		currPoint:  newStringPoint(),
	}
	return r
}

func (r *StringTimeColStringIterator) mergePrevItem(
	outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	outColumn.AppendStringValue(string(r.prevPoint.value))
	outColumn.AppendColumnTime(r.prevPoint.time)
	outColumn.AppendNotNil()
}

func (r *StringTimeColStringIterator) processFirstWindow(
	inChunk, outChunk Chunk, isNil, sameInterval, onlyOneInterval bool, index int, value string,
) {
	// To distinguish values between inChunk and auxChunk, r.currPoint.index incremented by 1.
	if !isNil {
		if r.initTimeCol {
			r.currPoint.Set(index+1, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
		} else {
			r.currPoint.Set(index+1, inChunk.TimeByIndex(index), value)
		}
		r.fv(r.prevPoint, r.currPoint)
	}
	if onlyOneInterval && sameInterval {
		r.prevPoint.index = 0
	} else {
		if !r.prevPoint.isNil {
			r.mergePrevItem(outChunk)
		}
		r.prevPoint.Reset()
	}
	r.currPoint.Reset()
}

func (r *StringTimeColStringIterator) processLastWindow(
	inChunk Chunk, index int, isNil bool, value string,
) {
	if isNil {
		r.prevPoint.Reset()
		return
	}
	if r.initTimeCol {
		r.prevPoint.Set(0, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
	} else {
		r.prevPoint.Set(0, inChunk.TimeByIndex(index), value)
	}
}

func (r *StringTimeColStringIterator) processMiddleWindow(
	inChunk, outChunk Chunk, index int, value string,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if r.initTimeCol {
		outColumn.AppendColumnTime(inChunk.Column(r.inOrdinal).ColumnTime(index))
	} else {
		outColumn.AppendColumnTime(inChunk.TimeByIndex(index))
	}
	outColumn.AppendStringValue(value)
	outColumn.AppendNotNil()
}

func (r *StringTimeColStringIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	if inColumn.IsEmpty() && r.prevPoint.isNil {
		var addIntervalLen int
		if p.sameInterval {
			addIntervalLen = inChunk.IntervalLen() - 1
		} else {
			addIntervalLen = inChunk.IntervalLen()
		}
		if addIntervalLen > 0 {
			outColumn.AppendManyNil(addIntervalLen)
		}
		return
	}

	var end int
	r.initTimeCol = len(inColumn.ColumnTimes()) > 0
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	values := inColumn.StringValuesV2(nil)
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		index, value, isNil := r.fn(inChunk, values, r.inOrdinal, start, end)
		if isNil && ((i > firstIndex && i < lastIndex) ||
			(firstIndex == lastIndex && r.prevPoint.isNil && !p.sameInterval) ||
			(firstIndex != lastIndex && i == firstIndex && r.prevPoint.isNil) ||
			(firstIndex != lastIndex && i == lastIndex && !p.sameInterval)) {
			outColumn.AppendNil()
			continue
		}
		if i == firstIndex && !r.prevPoint.isNil {
			r.processFirstWindow(inChunk, outChunk, isNil, p.sameInterval,
				firstIndex == lastIndex, index, value)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, index, isNil, value)
		} else if !isNil {
			r.processMiddleWindow(inChunk, outChunk, index, value)
		}
	}
}

type BooleanTimeColBooleanReduce func(c Chunk, values []bool, ordinal, start, end int) (index int, value bool, isNil bool)

type BooleanTimeColBooleanMerge func(prevPoint, currPoint *Point[bool])

type BooleanTimeColBooleanIterator struct {
	initTimeCol bool
	inOrdinal   int
	outOrdinal  int
	prevPoint   *Point[bool]
	currPoint   *Point[bool]
	fn          BooleanTimeColBooleanReduce
	fv          BooleanTimeColBooleanMerge
}

func NewBooleanTimeColBooleanIterator(
	fn BooleanTimeColBooleanReduce, fv BooleanTimeColBooleanMerge, inOrdinal, outOrdinal int,
) *BooleanTimeColBooleanIterator {
	r := &BooleanTimeColBooleanIterator{
		fn:         fn,
		fv:         fv,
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
		prevPoint:  newPoint[bool](),
		currPoint:  newPoint[bool](),
	}
	return r
}

func (r *BooleanTimeColBooleanIterator) mergePrevItem(
	outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	outColumn.AppendBooleanValue(r.prevPoint.value)
	outColumn.AppendColumnTime(r.prevPoint.time)
	outColumn.AppendNotNil()
}

func (r *BooleanTimeColBooleanIterator) processFirstWindow(
	inChunk, outChunk Chunk, isNil, sameInterval, onlyOneInterval bool, index int, value bool,
) {
	// To distinguish values between inChunk and auxChunk, r.currPoint.index incremented by 1.
	if !isNil {
		if r.initTimeCol {
			r.currPoint.Set(index+1, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
		} else {
			r.currPoint.Set(index+1, inChunk.TimeByIndex(index), value)
		}
		r.fv(r.prevPoint, r.currPoint)
	}
	if onlyOneInterval && sameInterval {
		r.prevPoint.index = 0
	} else {
		if !r.prevPoint.isNil {
			r.mergePrevItem(outChunk)
		}
		r.prevPoint.Reset()
	}
	r.currPoint.Reset()
}

func (r *BooleanTimeColBooleanIterator) processLastWindow(
	inChunk Chunk, index int, isNil bool, value bool,
) {
	if isNil {
		r.prevPoint.Reset()
		return
	}
	if r.initTimeCol {
		r.prevPoint.Set(0, inChunk.Column(r.inOrdinal).ColumnTime(index), value)
	} else {
		r.prevPoint.Set(0, inChunk.TimeByIndex(index), value)
	}
}

func (r *BooleanTimeColBooleanIterator) processMiddleWindow(
	inChunk, outChunk Chunk, index int, value bool,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if r.initTimeCol {
		outColumn.AppendColumnTime(inChunk.Column(r.inOrdinal).ColumnTime(index))
	} else {
		outColumn.AppendColumnTime(inChunk.TimeByIndex(index))
	}
	outColumn.AppendBooleanValue(value)
	outColumn.AppendNotNil()
}

func (r *BooleanTimeColBooleanIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	if inColumn.IsEmpty() && r.prevPoint.isNil {
		var addIntervalLen int
		if p.sameInterval {
			addIntervalLen = inChunk.IntervalLen() - 1
		} else {
			addIntervalLen = inChunk.IntervalLen()
		}
		if addIntervalLen > 0 {
			outColumn.AppendManyNil(addIntervalLen)
		}
		return
	}

	var end int
	r.initTimeCol = len(inColumn.ColumnTimes()) > 0
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	values := inChunk.Column(r.inOrdinal).BooleanValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		index, value, isNil := r.fn(inChunk, values, r.inOrdinal, start, end)
		if isNil && ((i > firstIndex && i < lastIndex) ||
			(firstIndex == lastIndex && r.prevPoint.isNil && !p.sameInterval) ||
			(firstIndex != lastIndex && i == firstIndex && r.prevPoint.isNil) ||
			(firstIndex != lastIndex && i == lastIndex && !p.sameInterval)) {
			outColumn.AppendNil()
			continue
		}
		if i == firstIndex && !r.prevPoint.isNil {
			r.processFirstWindow(inChunk, outChunk, isNil, p.sameInterval,
				firstIndex == lastIndex, index, value)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, index, isNil, value)
		} else if !isNil {
			r.processMiddleWindow(inChunk, outChunk, index, value)
		}
	}
}

type FloatColFloatSliceIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	buf          *SliceItem[float64]
	fn           SliceReduce[float64]
	auxChunk     Chunk
	auxProcessor []*AuxProcessor
	windowIndex  []int
}

func NewFloatColFloatSliceIterator(fn SliceReduce[float64],
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *FloatColFloatSliceIterator {
	r := &FloatColFloatSliceIterator{
		buf:          NewSliceItem[float64](),
		fn:           fn,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *FloatColFloatSliceIterator) appendInAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *FloatColFloatSliceIterator) appendOutAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].outOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *FloatColFloatSliceIterator) updateAuxChunk(
	inChunk, outChunk Chunk, start, end int,
) {
	if start == end {
		return
	}
	for j := start; j < end; j++ {
		r.windowIndex = append(r.windowIndex, j)
	}
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
	r.windowIndex = r.windowIndex[:0]
}

func (r *FloatColFloatSliceIterator) mergePrevItem(
	outChunk Chunk, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendOutAuxCol(r.auxChunk, outChunk, r.buf.index[idx])
			r.auxChunk.Reset()
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *FloatColFloatSliceIterator) assembleCurrItem(
	inChunk, outChunk Chunk, vs, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendInAuxCol(inChunk, outChunk, vs+r.buf.index[idx])
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *FloatColFloatSliceIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int, values []float64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
	if haveMultiInterval || !sameInterval {
		index, time, value, isNil := r.fn(r.buf)
		if !isNil {
			r.mergePrevItem(outChunk, index, time, value)
		}
		r.buf.Reset()
	}
}

func (r *FloatColFloatSliceIterator) processLastWindow(
	inChunk Chunk, start, end int, values []float64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
}

func (r *FloatColFloatSliceIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int, values []float64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	index, time, value, isNil := r.fn(r.buf)
	if !isNil {
		r.assembleCurrItem(inChunk, outChunk, start, index, time, value)
	}
	r.buf.Reset()
}

func (r *FloatColFloatSliceIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	values := inColumn.FloatValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if !r.isSingleCall {
			start, end = inColumn.GetRangeValueIndexV2(start, end)
			if start == end && r.buf.Len() == 0 && (i < lastIndex || (i == lastIndex && !p.sameInterval)) {
				outColumn.AppendNil()
				continue
			}
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end, values)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end, values)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end, values)
		}
	}
}

type IntegerColIntegerSliceIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	buf          *SliceItem[int64]
	fn           SliceReduce[int64]
	auxChunk     Chunk
	auxProcessor []*AuxProcessor
	windowIndex  []int
}

func NewIntegerColIntegerSliceIterator(fn SliceReduce[int64],
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *IntegerColIntegerSliceIterator {
	r := &IntegerColIntegerSliceIterator{
		buf:          NewSliceItem[int64](),
		fn:           fn,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *IntegerColIntegerSliceIterator) appendInAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *IntegerColIntegerSliceIterator) appendOutAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].outOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *IntegerColIntegerSliceIterator) updateAuxChunk(
	inChunk, outChunk Chunk, start, end int,
) {
	if start == end {
		return
	}
	for j := start; j < end; j++ {
		r.windowIndex = append(r.windowIndex, j)
	}
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
	r.windowIndex = r.windowIndex[:0]
}

func (r *IntegerColIntegerSliceIterator) mergePrevItem(
	outChunk Chunk, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendIntegerValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendOutAuxCol(r.auxChunk, outChunk, r.buf.index[idx])
			r.auxChunk.Reset()
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *IntegerColIntegerSliceIterator) assembleCurrItem(
	inChunk, outChunk Chunk, vs, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendIntegerValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendInAuxCol(inChunk, outChunk, vs+r.buf.index[idx])
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *IntegerColIntegerSliceIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int, values []int64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
	if haveMultiInterval || !sameInterval {
		index, time, value, isNil := r.fn(r.buf)
		if !isNil {
			r.mergePrevItem(outChunk, index, time, value)
		}
		r.buf.Reset()
	}
}

func (r *IntegerColIntegerSliceIterator) processLastWindow(
	inChunk Chunk, start, end int, values []int64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
}

func (r *IntegerColIntegerSliceIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int, values []int64,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	index, time, value, isNil := r.fn(r.buf)
	if !isNil {
		r.assembleCurrItem(inChunk, outChunk, start, index, time, value)
	}
	r.buf.Reset()
}

func (r *IntegerColIntegerSliceIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	values := inColumn.IntegerValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if !r.isSingleCall {
			start, end = inColumn.GetRangeValueIndexV2(start, end)
			if start == end && r.buf.Len() == 0 && (i < lastIndex || (i == lastIndex && !p.sameInterval)) {
				outColumn.AppendNil()
				continue
			}
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end, values)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end, values)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end, values)
		}
	}
}

type StringColReduceSliceReduce func(stringItem *SliceItem[string]) (index int, time int64, value float64, isNil bool)

type StringColStringSliceIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	buf          *SliceItem[string]
	fn           StringColReduceSliceReduce
	auxChunk     Chunk
	auxProcessor []*AuxProcessor
	windowIndex  []int
}

func NewStringColStringSliceIterator(fn StringColReduceSliceReduce,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *StringColStringSliceIterator {
	r := &StringColStringSliceIterator{
		buf:          NewSliceItem[string](),
		fn:           fn,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *StringColStringSliceIterator) appendInAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *StringColStringSliceIterator) appendOutAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].outOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *StringColStringSliceIterator) updateAuxChunk(
	inChunk, outChunk Chunk, start, end int,
) {
	if start == end {
		return
	}
	for j := start; j < end; j++ {
		r.windowIndex = append(r.windowIndex, j)
	}
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
	r.windowIndex = r.windowIndex[:0]
}

func (r *StringColStringSliceIterator) mergePrevItem(
	outChunk Chunk, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendStringValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendOutAuxCol(r.auxChunk, outChunk, r.buf.index[idx])
			r.auxChunk.Reset()
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *StringColStringSliceIterator) assembleCurrItem(
	inChunk, outChunk Chunk, vs, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendStringValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendInAuxCol(inChunk, outChunk, vs+r.buf.index[idx])
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *StringColStringSliceIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int, values []string,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
	if haveMultiInterval || !sameInterval {
		index, time, value, isNil := r.fn(r.buf)
		if !isNil {
			r.mergePrevItem(outChunk, index, time, value)
		}
		r.buf.Reset()
	}
}

func (r *StringColStringSliceIterator) processLastWindow(
	inChunk Chunk, start, end int, values []string,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
}

func (r *StringColStringSliceIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int, values []string,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end, values)
	index, time, value, isNil := r.fn(r.buf)
	if !isNil {
		r.assembleCurrItem(inChunk, outChunk, start, index, time, value)
	}
	r.buf.Reset()
}

func (r *StringColStringSliceIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	_, values := inColumn.GetStringValueBytes(nil, nil, 0, inColumn.Length()-inColumn.NilCount())
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if !r.isSingleCall {
			start, end = inColumn.GetRangeValueIndexV2(start, end)
			if start == end && r.buf.Len() == 0 && (i < lastIndex || (i == lastIndex && !p.sameInterval)) {
				outColumn.AppendNil()
				continue
			}
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end, values)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end, values)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end, values)
		}
	}
}

type BooleanColReduceSliceReduce func(booleanItem *BooleanSliceItem) (index int, time int64, value float64, isNil bool)

type BooleanColBooleanSliceIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	buf          *BooleanSliceItem
	fn           BooleanColReduceSliceReduce
	auxChunk     Chunk
	auxProcessor []*AuxProcessor
	windowIndex  []int
}

func NewBooleanColBooleanSliceIterator(fn BooleanColReduceSliceReduce,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *BooleanColBooleanSliceIterator {
	r := &BooleanColBooleanSliceIterator{
		buf:          NewBooleanSliceItem(),
		fn:           fn,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *BooleanColBooleanSliceIterator) appendInAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *BooleanColBooleanSliceIterator) appendOutAuxCol(
	inChunk, outChunk Chunk, index int,
) {
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].outOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			index,
		)
	}
}

func (r *BooleanColBooleanSliceIterator) updateAuxChunk(
	inChunk, outChunk Chunk, start, end int,
) {
	if start == end {
		return
	}
	for j := start; j < end; j++ {
		r.windowIndex = append(r.windowIndex, j)
	}
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			outChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
	r.windowIndex = r.windowIndex[:0]
}

func (r *BooleanColBooleanSliceIterator) mergePrevItem(
	outChunk Chunk, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendBooleanValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendOutAuxCol(r.auxChunk, outChunk, r.buf.index[idx])
			r.auxChunk.Reset()
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *BooleanColBooleanSliceIterator) assembleCurrItem(
	inChunk, outChunk Chunk, vs, idx int, time int64, val float64,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	if idx != -1 {
		if r.isSingleCall {
			outChunk.AppendTime(r.buf.time[idx])
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendBooleanValue(r.buf.value[idx])
		if r.auxProcessor != nil {
			r.appendInAuxCol(inChunk, outChunk, vs+r.buf.index[idx])
		}
	} else {
		if r.isSingleCall {
			outChunk.AppendTime(time)
			outChunk.AppendIntervalIndex(outChunk.Len() - 1)
		}
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(val)
	}
}

func (r *BooleanColBooleanSliceIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
	if haveMultiInterval || !sameInterval {
		index, time, value, isNil := r.fn(r.buf)
		if !isNil {
			r.mergePrevItem(outChunk, index, time, value)
		}
		r.buf.Reset()
	}
}

func (r *BooleanColBooleanSliceIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end)
	if r.auxProcessor != nil {
		r.updateAuxChunk(inChunk, r.auxChunk, start, end)
	}
}

func (r *BooleanColBooleanSliceIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	r.buf.AppendItem(inChunk, r.inOrdinal, start, end)
	index, time, value, isNil := r.fn(r.buf)
	if !isNil {
		r.assembleCurrItem(inChunk, outChunk, start, index, time, value)
	}
	r.buf.Reset()
}

func (r *BooleanColBooleanSliceIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	inColumn := inChunk.Column(r.inOrdinal)
	outColumn := outChunk.Column(r.outOrdinal)
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if !r.isSingleCall {
			start, end = inColumn.GetRangeValueIndexV2(start, end)
			if start == end && r.buf.Len() == 0 && (i < lastIndex || (i == lastIndex && !p.sameInterval)) {
				outColumn.AppendNil()
				continue
			}
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type FloatColFloatHeapIterator struct {
	n             int
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *HeapItem[float64]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewFloatColFloatHeapIterator(
	inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType, FloatHeapItem *HeapItem[float64],
) *FloatColFloatHeapIterator {
	r := &FloatColFloatHeapIterator{
		buf:        FloatHeapItem,
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
	if len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *FloatColFloatHeapIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendFloatValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *FloatColFloatHeapIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendFloatValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *FloatColFloatHeapIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	// inserts elements pushed from the heap
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)

	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *FloatColFloatHeapIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)

	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}

	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)

	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			// inserts elements still remained in the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			// inserts elements pushed from the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *FloatColFloatHeapIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
	r.buf.sortByTime = false
}

func (r *FloatColFloatHeapIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.buf.sortByTime = false
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1

		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)

		r.buf.sortByTime = true
		sort.Sort(r.buf)

		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)

		if !hybridqp.IsSubSlice(r.prevBufIndex, r.currBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *FloatColFloatHeapIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
		r.buf.sortByTime = false
	}
}

func (r *FloatColFloatHeapIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if hasMultiInterval || !sameInterval {
		r.buf.sortFunc(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *FloatColFloatHeapIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *FloatColFloatHeapIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)
	}
	r.buf.sortFunc(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *FloatColFloatHeapIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	var end int
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type IntegerColIntegerHeapIterator struct {
	n             int
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *HeapItem[int64]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewIntegerColIntegerHeapIterator(
	inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType, IntegerHeapItem *HeapItem[int64],
) *IntegerColIntegerHeapIterator {
	r := &IntegerColIntegerHeapIterator{
		buf:        IntegerHeapItem,
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
	if len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *IntegerColIntegerHeapIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendIntegerValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *IntegerColIntegerHeapIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendIntegerValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *IntegerColIntegerHeapIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	// inserts elements pushed from the heap
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)

	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *IntegerColIntegerHeapIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)

	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}

	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)

	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			// inserts elements still remained in the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			// inserts elements pushed from the heap
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *IntegerColIntegerHeapIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
	r.buf.sortByTime = false
}

func (r *IntegerColIntegerHeapIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.buf.sortByTime = false
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1

		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)

		r.buf.sortByTime = true
		sort.Sort(r.buf)

		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)

		if !hybridqp.IsSubSlice(r.prevBufIndex, r.currBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *IntegerColIntegerHeapIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.buf.sortByTime = true
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
		r.buf.sortByTime = false
	}
}

func (r *IntegerColIntegerHeapIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if hasMultiInterval || !sameInterval {
		r.buf.sortFunc(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *IntegerColIntegerHeapIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *IntegerColIntegerHeapIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.append(inChunk, start, end, r.inOrdinal, values)
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, r.inOrdinal, values)
	}
	r.buf.sortFunc(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *IntegerColIntegerHeapIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	var end int
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type BooleanDistinctItem struct {
	m     map[bool]struct{}
	time  []int64
	value []bool
}

func NewBooleanDistinctItem() *BooleanDistinctItem {
	return &BooleanDistinctItem{
		m: make(map[bool]struct{}),
	}
}

func (f *BooleanDistinctItem) appendItem(time []int64, value []bool) {
	for i := 0; i < len(time); i++ {
		if _, ok := f.m[value[i]]; !ok {
			f.m[value[i]] = struct{}{}
			f.time = append(f.time, time[i])
			f.value = append(f.value, value[i])
		}
	}
}

func (f *BooleanDistinctItem) Nil() bool {
	return len(f.time) == 0
}

func (f *BooleanDistinctItem) Reset() {
	for k := range f.m {
		delete(f.m, k)
	}
	f.time = f.time[:0]
	f.value = f.value[:0]
}

func (f *BooleanDistinctItem) Len() int {
	return len(f.value)
}

func (f *BooleanDistinctItem) Less(i, j int) bool {
	if f.time[i] != f.time[j] {
		return f.time[i] < f.time[j]
	}
	return !f.value[i]
}

func (f *BooleanDistinctItem) Swap(i, j int) {
	f.time[i], f.time[j] = f.time[j], f.time[i]
	f.value[i], f.value[j] = f.value[j], f.value[i]
}

type FloatColFloatDistinctIterator struct {
	buf        *DistinctItem[float64]
	inOrdinal  int
	outOrdinal int
}

func NewFloatColFloatDistinctIterator(
	inOrdinal, outOrdinal int,
) *FloatColFloatDistinctIterator {
	return &FloatColFloatDistinctIterator{
		buf:        NewDistinctItem[float64](),
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (r *FloatColFloatDistinctIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).FloatValues()[start:end])
	if hasMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outColumn := outChunk.Column(r.outOrdinal)
			for j := range r.buf.time {
				outChunk.AppendTime(r.buf.time[j])
				outColumn.AppendFloatValue(r.buf.value[j])
				outColumn.AppendNotNil()
			}
			outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
		}
		r.buf.Reset()
	}
}

func (r *FloatColFloatDistinctIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).FloatValues()[start:end])
}

func (r *FloatColFloatDistinctIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).FloatValues()[start:end])
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		outColumn := outChunk.Column(r.outOrdinal)
		for j := range r.buf.time {
			outChunk.AppendTime(r.buf.time[j])
			outColumn.AppendFloatValue(r.buf.value[j])
			outColumn.AppendNotNil()
		}
		outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
	}
	r.buf.Reset()
}

func (r *FloatColFloatDistinctIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() {
		return
	}

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		if i == firstIndex && !r.buf.Nil() {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type IntegerColIntegerDistinctIterator struct {
	buf        *DistinctItem[int64]
	inOrdinal  int
	outOrdinal int
}

func NewIntegerColIntegerDistinctIterator(
	inOrdinal, outOrdinal int,
) *IntegerColIntegerDistinctIterator {
	return &IntegerColIntegerDistinctIterator{
		buf:        NewDistinctItem[int64](),
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (r *IntegerColIntegerDistinctIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).IntegerValues()[start:end])
	if hasMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outColumn := outChunk.Column(r.outOrdinal)
			for j := range r.buf.time {
				outChunk.AppendTime(r.buf.time[j])
				outColumn.AppendIntegerValue(r.buf.value[j])
				outColumn.AppendNotNil()
			}
			outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
		}
		r.buf.Reset()
	}
}

func (r *IntegerColIntegerDistinctIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).IntegerValues()[start:end])
}

func (r *IntegerColIntegerDistinctIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).IntegerValues()[start:end])
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		outColumn := outChunk.Column(r.outOrdinal)
		for j := range r.buf.time {
			outChunk.AppendTime(r.buf.time[j])
			outColumn.AppendIntegerValue(r.buf.value[j])
			outColumn.AppendNotNil()
		}
		outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
	}
	r.buf.Reset()
}

func (r *IntegerColIntegerDistinctIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() {
		return
	}

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		if i == firstIndex && !r.buf.Nil() {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type StringColStringDistinctIterator struct {
	buf        *DistinctItem[string]
	inOrdinal  int
	outOrdinal int
	stringBuff []string
}

func NewStringColStringDistinctIterator(
	inOrdinal, outOrdinal int,
) *StringColStringDistinctIterator {
	return &StringColStringDistinctIterator{
		buf:        NewDistinctItem[string](),
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (r *StringColStringDistinctIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.stringBuff = inChunk.Column(r.inOrdinal).StringValuesRange(r.stringBuff[:0], start, end)
	r.buf.appendItem(inChunk.Time()[start:end], r.stringBuff)
	if hasMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outColumn := outChunk.Column(r.outOrdinal)
			for j := range r.buf.time {
				outChunk.AppendTime(r.buf.time[j])
				outColumn.AppendStringValue(r.buf.value[j])
				outColumn.AppendNotNil()
			}
			outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
		}
		r.buf.Reset()
	}
}

func (r *StringColStringDistinctIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.stringBuff = inChunk.Column(r.inOrdinal).StringValuesRange(r.stringBuff[:0], start, end)
	r.buf.appendItem(inChunk.Time()[start:end], r.stringBuff)
}

func (r *StringColStringDistinctIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	r.stringBuff = inChunk.Column(r.inOrdinal).StringValuesRange(r.stringBuff[:0], start, end)
	r.buf.appendItem(inChunk.Time()[start:end], r.stringBuff)
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		outColumn := outChunk.Column(r.outOrdinal)
		for j := range r.buf.time {
			outChunk.AppendTime(r.buf.time[j])
			outColumn.AppendStringValue(r.buf.value[j])
			outColumn.AppendNotNil()
		}
		outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
	}
	r.buf.Reset()
}

func (r *StringColStringDistinctIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() {
		return
	}

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		if i == firstIndex && !r.buf.Nil() {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type BooleanColBooleanDistinctIterator struct {
	buf        *BooleanDistinctItem
	inOrdinal  int
	outOrdinal int
}

func NewBooleanColBooleanDistinctIterator(
	inOrdinal, outOrdinal int,
) *BooleanColBooleanDistinctIterator {
	return &BooleanColBooleanDistinctIterator{
		buf:        NewBooleanDistinctItem(),
		inOrdinal:  inOrdinal,
		outOrdinal: outOrdinal,
	}
}

func (r *BooleanColBooleanDistinctIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, hasMultiInterval bool, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).BooleanValues()[start:end])
	if hasMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			outColumn := outChunk.Column(r.outOrdinal)
			for j := range r.buf.time {
				outChunk.AppendTime(r.buf.time[j])
				outColumn.AppendBooleanValue(r.buf.value[j])
				outColumn.AppendNotNil()
			}
			outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
		}
		r.buf.Reset()
	}
}

func (r *BooleanColBooleanDistinctIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).BooleanValues()[start:end])
}

func (r *BooleanColBooleanDistinctIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	r.buf.appendItem(inChunk.Time()[start:end], inChunk.Column(r.inOrdinal).BooleanValues()[start:end])
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		outColumn := outChunk.Column(r.outOrdinal)
		for j := range r.buf.time {
			outChunk.AppendTime(r.buf.time[j])
			outColumn.AppendBooleanValue(r.buf.value[j])
			outColumn.AppendNotNil()
		}
		outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
	}
	r.buf.Reset()
}

func (r *BooleanColBooleanDistinctIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() {
		return
	}

	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		if i == firstIndex && !r.buf.Nil() {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type TransItem interface {
	AppendItem(Chunk, int, int, int, bool)
	Reset()
	Len() int
	PrevNil() bool
	ResetPrev()
	GetBaseTransData() BaseTransData
}

type BaseTransData struct {
	time         []int64
	floatValue   []float64
	integerValue []int64
	nils         []bool
}

type floatDifference func(prev, curr float64) float64

type FloatDifferenceItem struct {
	isNonNegative bool
	diff          func(prev, curr float64) float64
	prev          *Point[float64]
	times         []int64
	values        []float64
	nils          []bool
}

func NewFloatDifferenceItem(isNonNegative bool, diff floatDifference) *FloatDifferenceItem {
	return &FloatDifferenceItem{isNonNegative: isNonNegative, diff: diff, prev: newPoint[float64]()}
}

func (f *FloatDifferenceItem) diffComputeFast(prevValue, currValue float64, currTime int64) {
	if dv := f.diff(prevValue, currValue); !f.isNonNegative || (f.isNonNegative && dv >= 0) {
		f.times = append(f.times, currTime)
		f.values = append(f.values, dv)
		f.nils = append(f.nils, false)
	} else {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *FloatDifferenceItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	var st int64
	var si int
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	values := col.FloatValues()[start:end]

	// calculate diff for middle points
	for i := 0; i < len(times); i++ {
		if i == 0 {
			if f.prev.isNil {
				st, si = times[0], 0
			} else if f.prev.time != times[0] {
				f.diffComputeFast(f.prev.value, values[0], times[0])
				st, si = times[0], 0
			} else if f.prev.time == times[0] {
				st, si = f.prev.time, -1
			}
			continue
		}

		if st == times[i] {
			continue
		}

		if si == -1 {
			f.diffComputeFast(f.prev.value, values[i], times[i])
		} else {
			f.diffComputeFast(values[si], values[i], times[i])
		}
		st, si = times[i], i
	}

	// process the last point
	if sameInterval {
		if si >= 0 {
			f.prev.Set(end, times[si], values[si])
		}
	} else {
		f.prev.Reset()
	}
}

func (f *FloatDifferenceItem) doNullWindow(times []int64, sameInterval bool) {
	var st int64
	for i, t := range times {
		if i == 0 {
			if !f.prev.isNil {
				f.times = append(f.times, times[0])
				f.values = append(f.values, 0)
				f.nils = append(f.nils, true)
			}
			st = t
			continue
		}
		if st == t {
			continue
		}
		f.times = append(f.times, times[i])
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
		st = t
	}

	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *FloatDifferenceItem) diffComputeSlow(prevValue, currValue float64, currTime int64) {
	if dv := f.diff(prevValue, currValue); !f.isNonNegative || (f.isNonNegative && dv >= 0) {
		f.times = append(f.times, currTime)
		f.values = append(f.values, dv)
		f.nils = append(f.nils, false)
	} else if f.isNonNegative && dv < 0 {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *FloatDifferenceItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(times, sameInterval)
		return
	}

	// calculate diff for middle points
	var vos int
	for i := start; i < end; i++ {
		isNil := col.IsNilV2(i)
		t := c.TimeByIndex(i)
		if isNil && (f.prev.isNil || (i < end-1 && t == c.TimeByIndex(i+1))) {
			continue
		}
		if i > 0 && !f.prev.isNil && f.prev.time == t {
			if !isNil {
				vos++
			}
			continue
		}
		if isNil {
			f.times = append(f.times, t)
			f.values = append(f.values, 0)
			f.nils = append(f.nils, true)
			continue
		}
		v := col.FloatValue(vs + vos)
		vos++
		if f.prev.isNil {
			f.prev.Set(i, t, v)
			continue
		}
		f.diffComputeSlow(f.prev.value, v, t)
		f.prev.Set(i, t, v)
	}

	// process the last point
	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *FloatDifferenceItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *FloatDifferenceItem) Reset() {
	f.times = f.times[:0]
	f.values = f.values[:0]
	f.nils = f.nils[:0]
}

func (f *FloatDifferenceItem) Len() int {
	return len(f.times)
}

func (f *FloatDifferenceItem) PrevNil() bool {
	return f.prev.isNil
}

func (f *FloatDifferenceItem) ResetPrev() {
	f.prev.Reset()
}

func (f *FloatDifferenceItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.times, floatValue: f.values, nils: f.nils}
}

type integerDifference func(prev, curr int64) int64

type IntegerDifferenceItem struct {
	isNonNegative bool
	diff          func(prev, curr int64) int64
	prev          *Point[int64]
	times         []int64
	values        []int64
	nils          []bool
}

func NewIntegerDifferenceItem(isNonNegative bool, diff integerDifference) *IntegerDifferenceItem {
	return &IntegerDifferenceItem{isNonNegative: isNonNegative, diff: diff, prev: newPoint[int64]()}
}

func (f *IntegerDifferenceItem) diffComputeFast(prevValue, currValue int64, currTime int64) {
	if dv := f.diff(prevValue, currValue); !f.isNonNegative || (f.isNonNegative && dv >= 0) {
		f.times = append(f.times, currTime)
		f.values = append(f.values, dv)
		f.nils = append(f.nils, false)
	} else {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *IntegerDifferenceItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	var st int64
	var si int
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	values := col.IntegerValues()[start:end]

	// calculate diff for middle points
	for i := 0; i < len(times); i++ {
		if i == 0 {
			if f.prev.isNil {
				st, si = times[0], 0
			} else if f.prev.time != times[0] {
				f.diffComputeFast(f.prev.value, values[0], times[0])
				st, si = times[0], 0
			} else if f.prev.time == times[0] {
				st, si = f.prev.time, -1
			}
			continue
		}

		if st == times[i] {
			continue
		}

		if si == -1 {
			f.diffComputeFast(f.prev.value, values[i], times[i])
		} else {
			f.diffComputeFast(values[si], values[i], times[i])
		}
		st, si = times[i], i
	}

	// process the last point
	if sameInterval {
		if si >= 0 {
			f.prev.Set(end, times[si], values[si])
		}
	} else {
		f.prev.Reset()
	}
}

func (f *IntegerDifferenceItem) doNullWindow(times []int64, sameInterval bool) {
	var st int64
	for i, t := range times {
		if i == 0 {
			if !f.prev.isNil {
				f.times = append(f.times, times[0])
				f.values = append(f.values, 0)
				f.nils = append(f.nils, true)
			}
			st = t
			continue
		}
		if st == t {
			continue
		}
		f.times = append(f.times, times[i])
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
		st = t
	}

	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *IntegerDifferenceItem) diffComputeSlow(prevValue, currValue int64, currTime int64) {
	if dv := f.diff(prevValue, currValue); !f.isNonNegative || (f.isNonNegative && dv >= 0) {
		f.times = append(f.times, currTime)
		f.values = append(f.values, dv)
		f.nils = append(f.nils, false)
	} else if f.isNonNegative && dv < 0 {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *IntegerDifferenceItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(times, sameInterval)
		return
	}

	// calculate diff for middle points
	var vos int
	for i := start; i < end; i++ {
		isNil := col.IsNilV2(i)
		t := c.TimeByIndex(i)
		if isNil && (f.prev.isNil || (i < end-1 && t == c.TimeByIndex(i+1))) {
			continue
		}
		if i > 0 && !f.prev.isNil && f.prev.time == t {
			if !isNil {
				vos++
			}
			continue
		}
		if isNil {
			f.times = append(f.times, t)
			f.values = append(f.values, 0)
			f.nils = append(f.nils, true)
			continue
		}
		v := col.IntegerValue(vs + vos)
		vos++
		if f.prev.isNil {
			f.prev.Set(i, t, v)
			continue
		}
		f.diffComputeSlow(f.prev.value, v, t)
		f.prev.Set(i, t, v)
	}

	// process the last point
	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *IntegerDifferenceItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *IntegerDifferenceItem) Reset() {
	f.times = f.times[:0]
	f.values = f.values[:0]
	f.nils = f.nils[:0]
}

func (f *IntegerDifferenceItem) Len() int {
	return len(f.times)
}

func (f *IntegerDifferenceItem) PrevNil() bool {
	return f.prev.isNil
}

func (f *IntegerDifferenceItem) ResetPrev() {
	f.prev.Reset()
}

func (f *IntegerDifferenceItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.times, integerValue: f.values, nils: f.nils}
}

type FloatDerivativeItem struct {
	isNonNegative bool
	ascending     bool
	prev          *Point[float64]
	times         []int64
	values        []float64
	nils          []bool
	interval      hybridqp.Interval
}

func NewFloatDerivativeItem(isNonNegative, ascending bool, interval hybridqp.Interval) *FloatDerivativeItem {
	return &FloatDerivativeItem{
		isNonNegative: isNonNegative, ascending: ascending, interval: interval, prev: newPoint[float64](),
	}
}

func (f *FloatDerivativeItem) derivativeComputeFast(prevValue, currValue float64, prevTime, currTime int64) {
	if diff, elapsed := float64(currValue-prevValue), currTime-prevTime; !f.isNonNegative || (f.isNonNegative && diff >= 0) {
		if !f.ascending {
			elapsed = -elapsed
		}
		v := diff / (float64(elapsed) / float64(f.interval.Duration))
		f.times = append(f.times, currTime)
		f.values = append(f.values, v)
		f.nils = append(f.nils, false)
	} else {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *FloatDerivativeItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	var st int64
	var si int
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	values := col.FloatValues()[start:end]

	// calculate diff for middle points
	for i := 0; i < len(times); i++ {
		if i == 0 {
			if f.prev.isNil {
				st, si = times[0], 0
			} else if f.prev.time != times[0] {
				f.derivativeComputeFast(f.prev.value, values[0], f.prev.time, times[0])
				st, si = times[0], 0
			} else if f.prev.time == times[0] {
				st, si = f.prev.time, -1
			}
			continue
		}

		if st == times[i] {
			continue
		}
		if si == -1 {
			f.derivativeComputeFast(f.prev.value, values[i], f.prev.time, times[i])
		} else {
			f.derivativeComputeFast(values[si], values[i], times[si], times[i])
		}
		st, si = times[i], i
	}

	// process the last point
	if sameInterval {
		if si >= 0 {
			f.prev.Set(end, times[si], values[si])
		}
	} else {
		f.prev.Reset()
	}
}

func (f *FloatDerivativeItem) doNullWindow(times []int64, sameInterval bool) {
	var st int64
	for i, t := range times {
		if i == 0 {
			if !f.prev.isNil {
				f.times = append(f.times, times[0])
				f.values = append(f.values, 0)
				f.nils = append(f.nils, true)
			}
			st = t
			continue
		}
		if st == t {
			continue
		}
		f.times = append(f.times, times[i])
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
		st = t
	}

	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *FloatDerivativeItem) derivativeComputeSlow(prevValue, currValue float64, prevTime, currTime int64) {
	if diff, elapsed := float64(currValue-prevValue), currTime-prevTime; !f.isNonNegative || (f.isNonNegative && diff >= 0) {
		if !f.ascending {
			elapsed = -elapsed
		}
		v := diff / (float64(elapsed) / float64(f.interval.Duration))
		f.times = append(f.times, currTime)
		f.values = append(f.values, v)
		f.nils = append(f.nils, false)
	} else if f.isNonNegative && diff < 0 {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *FloatDerivativeItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(times, sameInterval)
		return
	}

	// calculate diff for middle points
	var vos int
	for i := start; i < end; i++ {
		isNil := col.IsNilV2(i)
		t := c.TimeByIndex(i)
		if isNil && (f.prev.isNil || (i < end-1 && t == c.TimeByIndex(i+1))) {
			continue
		}
		if i > 0 && !f.prev.isNil && f.prev.time == t {
			if !isNil {
				vos++
			}
			continue
		}
		if isNil {
			f.times = append(f.times, t)
			f.values = append(f.values, 0)
			f.nils = append(f.nils, true)
			continue
		}
		v := col.FloatValue(vs + vos)
		vos++
		if f.prev.isNil {
			f.prev.Set(i, t, v)
			continue
		}
		f.derivativeComputeSlow(f.prev.value, v, f.prev.time, t)
		f.prev.Set(i, t, v)
	}

	// process the last point
	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *FloatDerivativeItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *FloatDerivativeItem) Reset() {
	f.times = f.times[:0]
	f.values = f.values[:0]
	f.nils = f.nils[:0]
}

func (f *FloatDerivativeItem) Len() int {
	return len(f.times)
}

func (f *FloatDerivativeItem) PrevNil() bool {
	return f.prev.isNil
}

func (f *FloatDerivativeItem) ResetPrev() {
	f.prev.Reset()
}

func (f *FloatDerivativeItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.times, floatValue: f.values, nils: f.nils}
}

type IntegerDerivativeItem struct {
	isNonNegative bool
	ascending     bool
	prev          *Point[int64]
	times         []int64
	values        []float64
	nils          []bool
	interval      hybridqp.Interval
}

func NewIntegerDerivativeItem(isNonNegative, ascending bool, interval hybridqp.Interval) *IntegerDerivativeItem {
	return &IntegerDerivativeItem{
		isNonNegative: isNonNegative, ascending: ascending, interval: interval, prev: newPoint[int64](),
	}
}

func (f *IntegerDerivativeItem) derivativeComputeFast(prevValue, currValue int64, prevTime, currTime int64) {
	if diff, elapsed := float64(currValue-prevValue), currTime-prevTime; !f.isNonNegative || (f.isNonNegative && diff >= 0) {
		if !f.ascending {
			elapsed = -elapsed
		}
		v := diff / (float64(elapsed) / float64(f.interval.Duration))
		f.times = append(f.times, currTime)
		f.values = append(f.values, v)
		f.nils = append(f.nils, false)
	} else {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *IntegerDerivativeItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	var st int64
	var si int
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	values := col.IntegerValues()[start:end]

	// calculate diff for middle points
	for i := 0; i < len(times); i++ {
		if i == 0 {
			if f.prev.isNil {
				st, si = times[0], 0
			} else if f.prev.time != times[0] {
				f.derivativeComputeFast(f.prev.value, values[0], f.prev.time, times[0])
				st, si = times[0], 0
			} else if f.prev.time == times[0] {
				st, si = f.prev.time, -1
			}
			continue
		}

		if st == times[i] {
			continue
		}
		if si == -1 {
			f.derivativeComputeFast(f.prev.value, values[i], f.prev.time, times[i])
		} else {
			f.derivativeComputeFast(values[si], values[i], times[si], times[i])
		}
		st, si = times[i], i
	}

	// process the last point
	if sameInterval {
		if si >= 0 {
			f.prev.Set(end, times[si], values[si])
		}
	} else {
		f.prev.Reset()
	}
}

func (f *IntegerDerivativeItem) doNullWindow(times []int64, sameInterval bool) {
	var st int64
	for i, t := range times {
		if i == 0 {
			if !f.prev.isNil {
				f.times = append(f.times, times[0])
				f.values = append(f.values, 0)
				f.nils = append(f.nils, true)
			}
			st = t
			continue
		}
		if st == t {
			continue
		}
		f.times = append(f.times, times[i])
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
		st = t
	}

	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *IntegerDerivativeItem) derivativeComputeSlow(prevValue, currValue int64, prevTime, currTime int64) {
	if diff, elapsed := float64(currValue-prevValue), currTime-prevTime; !f.isNonNegative || (f.isNonNegative && diff >= 0) {
		if !f.ascending {
			elapsed = -elapsed
		}
		v := diff / (float64(elapsed) / float64(f.interval.Duration))
		f.times = append(f.times, currTime)
		f.values = append(f.values, v)
		f.nils = append(f.nils, false)
	} else if f.isNonNegative && diff < 0 {
		f.times = append(f.times, currTime)
		f.values = append(f.values, 0)
		f.nils = append(f.nils, true)
	}
}

func (f *IntegerDerivativeItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	times := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(times, sameInterval)
		return
	}

	// calculate diff for middle points
	var vos int
	for i := start; i < end; i++ {
		isNil := col.IsNilV2(i)
		t := c.TimeByIndex(i)
		if isNil && (f.prev.isNil || (i < end-1 && t == c.TimeByIndex(i+1))) {
			continue
		}
		if i > 0 && !f.prev.isNil && f.prev.time == t {
			if !isNil {
				vos++
			}
			continue
		}
		if isNil {
			f.times = append(f.times, t)
			f.values = append(f.values, 0)
			f.nils = append(f.nils, true)
			continue
		}
		v := col.IntegerValue(vs + vos)
		vos++
		if f.prev.isNil {
			f.prev.Set(i, t, v)
			continue
		}
		f.derivativeComputeSlow(f.prev.value, v, f.prev.time, t)
		f.prev.Set(i, t, v)
	}

	// process the last point
	if !sameInterval {
		f.prev.Reset()
	}
}

func (f *IntegerDerivativeItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *IntegerDerivativeItem) Reset() {
	f.times = f.times[:0]
	f.values = f.values[:0]
	f.nils = f.nils[:0]
}

func (f *IntegerDerivativeItem) Len() int {
	return len(f.times)
}

func (f *IntegerDerivativeItem) PrevNil() bool {
	return f.prev.isNil
}

func (f *IntegerDerivativeItem) ResetPrev() {
	f.prev.Reset()
}

func (f *IntegerDerivativeItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.times, floatValue: f.values, nils: f.nils}
}

func linearFloat(windowTime, previousTime, nextTime int64, previousValue, nextValue float64) float64 {
	m := (nextValue - previousValue) / float64(nextTime-previousTime) // the slope of the line
	x := float64(windowTime - previousTime)                           // how far into the interval we are
	b := previousValue
	return m*x + b
}

type ElapsedItem struct {
	prev     int64
	time     []int64
	nils     []bool
	value    []int64
	interval hybridqp.Interval
}

func NewElapsedItem(interval hybridqp.Interval) *ElapsedItem {
	return &ElapsedItem{
		interval: interval, prev: -1,
	}
}

func (f *ElapsedItem) AppendItemFastFunc(c Chunk, _ int, start, end int, sameInterval bool) {
	// fast path
	time := c.Time()[start:end]

	// process the first point
	if f.prev > 0 {
		elapsed := time[0] - f.prev
		v := elapsed / int64(f.interval.Duration)
		f.time = append(f.time, time[0])
		f.value = append(f.value, v)
		f.nils = append(f.nils, false)
	}

	// calculate diff for middle points
	for i := 1; i < len(time); i++ {
		elapsed := time[i] - time[i-1]
		v := elapsed / int64(f.interval.Duration)
		f.time = append(f.time, time[i])
		f.value = append(f.value, v)
		f.nils = append(f.nils, false)
	}

	// process the last point
	if sameInterval {
		f.prev = time[len(time)-1]
	} else {
		f.prev = -1
	}
}

func (f *ElapsedItem) doNullWindow(time []int64, sameInterval bool) {
	if f.prev == -1 {
		f.time = append(f.time, time[1:]...)
		f.value = append(f.value, make([]int64, len(time)-1)...)
		f.nils = append(f.nils, make([]bool, len(time)-1)...)
		for i := len(f.nils) - 1; i >= (len(f.nils) - len(time) + 1); i-- {
			f.nils[i] = true
		}
	} else {
		f.time = append(f.time, time...)
		f.value = append(f.value, make([]int64, len(time))...)
		f.nils = append(f.nils, make([]bool, len(time))...)
		for i := len(f.nils) - 1; i >= (len(f.nils) - len(time)); i-- {
			f.nils[i] = true
		}
	}

	if !sameInterval {
		f.prev = -1
	}
}

func (f *ElapsedItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	time := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(time, sameInterval)
		return
	}

	// calculate diff for middle points
	for i := start; i < end; i++ {
		t := c.TimeByIndex(i)
		if col.IsNilV2(i) {
			f.time = append(f.time, t)
			f.value = append(f.value, 0)
			f.nils = append(f.nils, true)
			continue
		}

		if f.prev == -1 {
			f.prev = t
			continue
		}
		elapsed := t - f.prev
		v := elapsed / int64(f.interval.Duration)
		f.time = append(f.time, t)
		f.value = append(f.value, v)
		f.nils = append(f.nils, false)
		f.prev = t
	}

	// process the last point
	if !sameInterval {
		f.prev = -1
	}
}

func (f *ElapsedItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *ElapsedItem) Reset() {
	f.time = f.time[:0]
	f.value = f.value[:0]
	f.nils = f.nils[:0]
}

func (f *ElapsedItem) Len() int {
	return len(f.time)
}

func (f *ElapsedItem) PrevNil() bool {
	return f.prev == -1
}

func (f *ElapsedItem) ResetPrev() {
	f.prev = -1
}

func (f *ElapsedItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.time, integerValue: f.value, nils: f.nils}
}

type FloatMovingAverageItem struct {
	window []Point[float64]
	cur    int
	pos    int
	sum    float64
	time   []int64
	value  []float64
	nils   []bool
	n      int
}

func NewFloatMovingAverageItem(n int) *FloatMovingAverageItem {
	return &FloatMovingAverageItem{window: make([]Point[float64], 0, n), pos: 0, cur: 0, sum: 0, n: n}
}

func (f *FloatMovingAverageItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	col := c.Column(ordinal)
	time := c.Time()[start:end]
	value := col.FloatValues()[start:end]
	for i := 0; i < len(time); i++ {
		f.cur += 1
		if len(f.window) == f.n {
			f.sum = f.sum - f.window[f.pos].value + value[i]
			f.window[f.pos] = Point[float64]{time: time[i], value: value[i], isNil: false}
			f.pos = (f.pos + 1) % f.n
			f.value = append(f.value, f.sum/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else if len(f.window) == f.n-1 {
			f.sum += value[i]
			f.window = append(f.window, Point[float64]{time: time[i], value: value[i], isNil: false})
			f.value = append(f.value, f.sum/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else {
			f.sum += value[i]
			f.window = append(f.window, Point[float64]{time: time[i], value: value[i], isNil: false})
			if f.cur >= f.n {
				f.nils = append(f.nils, true)
				f.time = append(f.time, time[0])
				f.value = append(f.value, 0)
			}
		}
	}
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *FloatMovingAverageItem) doNullWindow(time []int64, sameInterval bool) {
	if f.cur+1 >= f.n {
		nils := make([]bool, len(time))
		for i := 0; i < len(time); i++ {
			nils[i] = true
		}
		f.nils = append(f.nils, nils...)
		f.value = append(f.value, make([]float64, len(time))...)
		f.time = append(f.time, time...)
	} else {
		windowNeed := f.n - f.cur - 1
		if len(time) > windowNeed {
			nils := make([]bool, len(time)-windowNeed)
			for i := 0; i < len(time)-windowNeed; i++ {
				nils[i] = true
			}
			f.nils = append(f.nils, nils...)
			f.value = append(f.value, make([]float64, len(time)-windowNeed)...)
			f.time = append(f.time, time[windowNeed:]...)
		}
	}
	f.cur += len(time)
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *FloatMovingAverageItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	time := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(time, sameInterval)
		return
	}

	var vos int
	for i := start; i < end; i++ {
		f.cur += 1
		t := c.TimeByIndex(i)
		if col.IsNilV2(i) {
			if f.cur >= f.n {
				f.time = append(f.time, t)
				f.value = append(f.value, 0)
				f.nils = append(f.nils, true)
			}
			continue
		}

		v := col.FloatValue(vs + vos)
		vos++

		if len(f.window) == f.n {
			f.sum = f.sum - f.window[f.pos].value + v
			f.window[f.pos] = Point[float64]{time: t, value: v, isNil: false}
			f.pos = (f.pos + 1) % f.n
			f.value = append(f.value, f.sum/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else if len(f.window) == f.n-1 {
			f.sum += v
			f.window = append(f.window, Point[float64]{time: t, value: v, isNil: false})
			f.value = append(f.value, f.sum/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else {
			f.sum += v
			f.window = append(f.window, Point[float64]{time: t, value: v, isNil: false})
			if f.cur >= f.n {
				f.nils = append(f.nils, true)
				f.time = append(f.time, t)
				f.value = append(f.value, 0)
			}
		}
	}
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *FloatMovingAverageItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *FloatMovingAverageItem) Reset() {
	f.time = f.time[:0]
	f.value = f.value[:0]
	f.nils = f.nils[:0]
}

func (f *FloatMovingAverageItem) Len() int {
	return len(f.time)
}

func (f *FloatMovingAverageItem) PrevNil() bool {
	return len(f.window) == 0
}

func (f *FloatMovingAverageItem) ResetPrev() {
	f.window = f.window[:0]
	f.pos = 0
	f.sum = 0
	f.cur = 0
}

func (f *FloatMovingAverageItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.time, floatValue: f.value, nils: f.nils}
}

type IntegerMovingAverageItem struct {
	window []Point[int64]
	cur    int
	pos    int
	sum    int64
	time   []int64
	value  []float64
	nils   []bool
	n      int
}

func NewIntegerMovingAverageItem(n int) *IntegerMovingAverageItem {
	return &IntegerMovingAverageItem{window: make([]Point[int64], 0, n), pos: 0, cur: 0, sum: 0, n: n}
}

func (f *IntegerMovingAverageItem) AppendItemFastFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// fast path
	col := c.Column(ordinal)
	time := c.Time()[start:end]
	value := col.IntegerValues()[start:end]
	for i := 0; i < len(time); i++ {
		f.cur += 1
		if len(f.window) == f.n {
			f.sum = f.sum - f.window[f.pos].value + value[i]
			f.window[f.pos] = Point[int64]{time: time[i], value: value[i], isNil: false}
			f.pos = (f.pos + 1) % f.n
			f.value = append(f.value, float64(f.sum)/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else if len(f.window) == f.n-1 {
			f.sum += value[i]
			f.window = append(f.window, Point[int64]{time: time[i], value: value[i], isNil: false})
			f.value = append(f.value, float64(f.sum)/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else {
			f.sum += value[i]
			f.window = append(f.window, Point[int64]{time: time[i], value: value[i], isNil: false})
			if f.cur >= f.n {
				f.nils = append(f.nils, true)
				f.time = append(f.time, time[0])
				f.value = append(f.value, 0)
			}
		}
	}
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *IntegerMovingAverageItem) doNullWindow(time []int64, sameInterval bool) {
	if f.cur+1 >= f.n {
		nils := make([]bool, len(time))
		for i := 0; i < len(time); i++ {
			nils[i] = true
		}
		f.nils = append(f.nils, nils...)
		f.value = append(f.value, make([]float64, len(time))...)
		f.time = append(f.time, time...)
	} else {
		windowNeed := f.n - f.cur - 1
		if len(time) > windowNeed {
			nils := make([]bool, len(time)-windowNeed)
			for i := 0; i < len(time)-windowNeed; i++ {
				nils[i] = true
			}
			f.nils = append(f.nils, nils...)
			f.value = append(f.value, make([]float64, len(time)-windowNeed)...)
			f.time = append(f.time, time[windowNeed:]...)
		}
	}
	f.cur += len(time)
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *IntegerMovingAverageItem) AppendItemSlowFunc(c Chunk, ordinal int, start, end int, sameInterval bool) {
	// slow path
	col := c.Column(ordinal)
	time := c.Time()[start:end]
	vs, ve := col.GetRangeValueIndexV2(start, end)
	if vs == ve {
		f.doNullWindow(time, sameInterval)
		return
	}

	var vos int
	for i := start; i < end; i++ {
		f.cur += 1
		t := c.TimeByIndex(i)
		if col.IsNilV2(i) {
			if f.cur >= f.n {
				f.time = append(f.time, t)
				f.value = append(f.value, 0)
				f.nils = append(f.nils, true)
			}
			continue
		}

		v := col.IntegerValue(vs + vos)
		vos++

		if len(f.window) == f.n {
			f.sum = f.sum - f.window[f.pos].value + v
			f.window[f.pos] = Point[int64]{time: t, value: v, isNil: false}
			f.pos = (f.pos + 1) % f.n
			f.value = append(f.value, float64(f.sum)/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else if len(f.window) == f.n-1 {
			f.sum += v
			f.window = append(f.window, Point[int64]{time: t, value: v, isNil: false})
			f.value = append(f.value, float64(f.sum)/float64(f.n))
			f.time = append(f.time, f.window[(f.pos+f.n-1)%f.n].time)
			f.nils = append(f.nils, false)
		} else {
			f.sum += v
			f.window = append(f.window, Point[int64]{time: t, value: v, isNil: false})
			if f.cur >= f.n {
				f.nils = append(f.nils, true)
				f.time = append(f.time, t)
				f.value = append(f.value, 0)
			}
		}
	}
	if !sameInterval {
		f.ResetPrev()
	}
}

func (f *IntegerMovingAverageItem) AppendItem(c Chunk, ordinal int, start, end int, sameInterval bool) {
	if c.Column(ordinal).NilCount() == 0 {
		f.AppendItemFastFunc(c, ordinal, start, end, sameInterval)
		return
	}
	f.AppendItemSlowFunc(c, ordinal, start, end, sameInterval)
}

func (f *IntegerMovingAverageItem) Reset() {
	f.time = f.time[:0]
	f.value = f.value[:0]
	f.nils = f.nils[:0]
}

func (f *IntegerMovingAverageItem) Len() int {
	return len(f.time)
}

func (f *IntegerMovingAverageItem) PrevNil() bool {
	return len(f.window) == 0
}

func (f *IntegerMovingAverageItem) ResetPrev() {
	f.window = f.window[:0]
	f.pos = 0
	f.sum = 0
	f.cur = 0
}

func (f *IntegerMovingAverageItem) GetBaseTransData() BaseTransData {
	return BaseTransData{time: f.time, floatValue: f.value, nils: f.nils}
}

type FloatColFloatRateIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	interval     *hybridqp.Interval
	fn           RateMiddleReduceFunc[float64]
	fv           RateFinalReduceFunc[float64]
	fu           RateUpdateFunc[float64]
	fm           RateMergeFunc[float64]
	prevPoints   [2]*Point[float64]
	currPoints   [2]*Point[float64]
}

func NewFloatRateIterator(fn RateMiddleReduceFunc[float64], fv RateFinalReduceFunc[float64],
	fu RateUpdateFunc[float64], fm RateMergeFunc[float64],
	isSingleCall bool, inOrdinal, outOrdinal int, rowDataType hybridqp.RowDataType,
	interval *hybridqp.Interval,
) *FloatColFloatRateIterator {
	r := &FloatColFloatRateIterator{
		fn:           fn,
		fv:           fv,
		fu:           fu,
		fm:           fm,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
		interval:     interval,
		prevPoints:   [2]*Point[float64]{newPoint[float64](), newPoint[float64]()},
		currPoints:   [2]*Point[float64]{newPoint[float64](), newPoint[float64]()},
	}
	return r
}

func (r *FloatColFloatRateIterator) mergePrevItem(outChunk Chunk, v float64, isNil bool) {
	if r.isSingleCall && !isNil {
		outChunk.AppendTime(r.prevPoints[0].time)
		outChunk.AppendIntervalIndex(outChunk.Len() - 1)
	}
	outColumn := outChunk.Column(r.outOrdinal)
	if isNil {
		outColumn.AppendNil()
	} else {
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(v)
	}
}

func (r *FloatColFloatRateIterator) doFirstWindowForNull(outChunk Chunk) {
	if r.prevPoints[0].isNil || r.prevPoints[1].isNil {
		outChunk.Column(r.outOrdinal).AppendNil()
	} else {
		v, isNils := r.fm(r.prevPoints, r.interval)
		r.mergePrevItem(outChunk, v, isNils)
	}
	r.prevPoints[0].Reset()
	r.prevPoints[1].Reset()
}

func (r *FloatColFloatRateIterator) processNullWindow(outChunk Chunk, sameInterval, onlyOneInterval bool,
	i, firstIndex, lastIndex int) {
	if onlyOneInterval {
		if !sameInterval {
			r.doFirstWindowForNull(outChunk)
		}
	} else {
		if i == firstIndex {
			r.doFirstWindowForNull(outChunk)
		} else if i == lastIndex {
			if !sameInterval {
				outChunk.Column(r.outOrdinal).AppendNil()
			}
		} else {
			outChunk.Column(r.outOrdinal).AppendNil()
		}
	}
}

func (r *FloatColFloatRateIterator) processFirstWindow(outChunk Chunk, sameInterval, onlyOneInterval bool) {
	if !onlyOneInterval || !sameInterval {
		if !r.prevPoints[0].isNil && !r.prevPoints[1].isNil {
			v, isNil := r.fm(r.prevPoints, r.interval)
			r.mergePrevItem(outChunk, v, isNil)
		}
		r.prevPoints[0].Reset()
		r.prevPoints[1].Reset()
	}
	r.currPoints[0].Reset()
	r.currPoints[1].Reset()
}

func (r *FloatColFloatRateIterator) processLastWindow(inChunk Chunk, fi, si int, fv, sv float64) {
	r.prevPoints[0].Set(fi, inChunk.TimeByIndex(fi), fv)
	r.prevPoints[1].Set(si, inChunk.TimeByIndex(si), sv)
}

func (r *FloatColFloatRateIterator) processMiddleWindow(inChunk, outChunk Chunk, fi, si int, fv, sv float64) {
	v, isNil := r.fv(inChunk.TimeByIndex(fi), inChunk.TimeByIndex(si), fv, sv, r.interval)
	if r.isSingleCall && !isNil {
		outChunk.AppendTime(inChunk.TimeByIndex(fi))
		outChunk.AppendIntervalIndex(outChunk.Len() - 1)
	}
	outColumn := outChunk.Column(r.outOrdinal)
	if isNil {
		outColumn.AppendNil()
	} else {
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(v)
	}
}

func (r *FloatColFloatRateIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	var end int
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	onlyOneInterval := inChunk.IntervalLen() == 1
	inColumn := inChunk.Column(r.inOrdinal)
	values := inColumn.FloatValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		fi, si, fv, sv, isNil := r.fn(inChunk, values, r.inOrdinal, start, end)
		if isNil {
			r.processNullWindow(outChunk, p.sameInterval, onlyOneInterval, i, firstIndex, lastIndex)
			continue
		}

		if i == firstIndex && (!r.prevPoints[0].isNil || !r.prevPoints[1].isNil) {
			r.currPoints[0].Set(fi, inChunk.TimeByIndex(fi), fv)
			r.currPoints[1].Set(si, inChunk.TimeByIndex(si), sv)
			r.fu(r.prevPoints, r.currPoints)
			r.processFirstWindow(outChunk, p.sameInterval, onlyOneInterval)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, fi, si, fv, sv)
		} else {
			r.processMiddleWindow(inChunk, outChunk, fi, si, fv, sv)
		}
	}
}

type IntegerColFloatRateIterator struct {
	isSingleCall bool
	inOrdinal    int
	outOrdinal   int
	interval     *hybridqp.Interval
	fn           RateMiddleReduceFunc[int64]
	fv           RateFinalReduceFunc[int64]
	fu           RateUpdateFunc[int64]
	fm           RateMergeFunc[int64]
	prevPoints   [2]*Point[int64]
	currPoints   [2]*Point[int64]
}

func NewIntegerRateIterator(fn RateMiddleReduceFunc[int64], fv RateFinalReduceFunc[int64],
	fu RateUpdateFunc[int64], fm RateMergeFunc[int64],
	isSingleCall bool, inOrdinal, outOrdinal int, rowDataType hybridqp.RowDataType,
	interval *hybridqp.Interval,
) *IntegerColFloatRateIterator {
	r := &IntegerColFloatRateIterator{
		fn:           fn,
		fv:           fv,
		fu:           fu,
		fm:           fm,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
		interval:     interval,
		prevPoints:   [2]*Point[int64]{newPoint[int64](), newPoint[int64]()},
		currPoints:   [2]*Point[int64]{newPoint[int64](), newPoint[int64]()},
	}
	return r
}

func (r *IntegerColFloatRateIterator) mergePrevItem(outChunk Chunk, v float64, isNil bool) {
	if r.isSingleCall && !isNil {
		outChunk.AppendTime(r.prevPoints[0].time)
		outChunk.AppendIntervalIndex(outChunk.Len() - 1)
	}
	outColumn := outChunk.Column(r.outOrdinal)
	if isNil {
		outColumn.AppendNil()
	} else {
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(v)
	}
}

func (r *IntegerColFloatRateIterator) doFirstWindowForNull(outChunk Chunk) {
	if r.prevPoints[0].isNil || r.prevPoints[1].isNil {
		outChunk.Column(r.outOrdinal).AppendNil()
	} else {
		v, isNils := r.fm(r.prevPoints, r.interval)
		r.mergePrevItem(outChunk, v, isNils)
	}
	r.prevPoints[0].Reset()
	r.prevPoints[1].Reset()
}

func (r *IntegerColFloatRateIterator) processNullWindow(outChunk Chunk, sameInterval, onlyOneInterval bool,
	i, firstIndex, lastIndex int) {
	if onlyOneInterval {
		if !sameInterval {
			r.doFirstWindowForNull(outChunk)
		}
	} else {
		if i == firstIndex {
			r.doFirstWindowForNull(outChunk)
		} else if i == lastIndex {
			if !sameInterval {
				outChunk.Column(r.outOrdinal).AppendNil()
			}
		} else {
			outChunk.Column(r.outOrdinal).AppendNil()
		}
	}
}

func (r *IntegerColFloatRateIterator) processFirstWindow(outChunk Chunk, sameInterval, onlyOneInterval bool) {
	if !onlyOneInterval || !sameInterval {
		if !r.prevPoints[0].isNil && !r.prevPoints[1].isNil {
			v, isNil := r.fm(r.prevPoints, r.interval)
			r.mergePrevItem(outChunk, v, isNil)
		}
		r.prevPoints[0].Reset()
		r.prevPoints[1].Reset()
	}
	r.currPoints[0].Reset()
	r.currPoints[1].Reset()
}

func (r *IntegerColFloatRateIterator) processLastWindow(inChunk Chunk, fi, si int, fv, sv int64) {
	r.prevPoints[0].Set(fi, inChunk.TimeByIndex(fi), fv)
	r.prevPoints[1].Set(si, inChunk.TimeByIndex(si), sv)
}

func (r *IntegerColFloatRateIterator) processMiddleWindow(inChunk, outChunk Chunk, fi, si int, fv, sv int64) {
	v, isNil := r.fv(inChunk.TimeByIndex(fi), inChunk.TimeByIndex(si), fv, sv, r.interval)
	if r.isSingleCall && !isNil {
		outChunk.AppendTime(inChunk.TimeByIndex(fi))
		outChunk.AppendIntervalIndex(outChunk.Len() - 1)
	}
	outColumn := outChunk.Column(r.outOrdinal)
	if isNil {
		outColumn.AppendNil()
	} else {
		outColumn.AppendNotNil()
		outColumn.AppendFloatValue(v)
	}
}

func (r *IntegerColFloatRateIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	var end int
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	onlyOneInterval := inChunk.IntervalLen() == 1
	inColumn := inChunk.Column(r.inOrdinal)
	values := inColumn.IntegerValues()
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}

		fi, si, fv, sv, isNil := r.fn(inChunk, values, r.inOrdinal, start, end)
		if isNil {
			r.processNullWindow(outChunk, p.sameInterval, onlyOneInterval, i, firstIndex, lastIndex)
			continue
		}

		if i == firstIndex && (!r.prevPoints[0].isNil || !r.prevPoints[1].isNil) {
			r.currPoints[0].Set(fi, inChunk.TimeByIndex(fi), fv)
			r.currPoints[1].Set(si, inChunk.TimeByIndex(si), sv)
			r.fu(r.prevPoints, r.currPoints)
			r.processFirstWindow(outChunk, p.sameInterval, onlyOneInterval)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, fi, si, fv, sv)
		} else {
			r.processMiddleWindow(inChunk, outChunk, fi, si, fv, sv)
		}
	}
}

type FloatColFloatSampleIterator struct {
	sampleNum     int
	isSingleCall  bool
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *SampleItem[float64]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewFloatColFloatSampleIterator(sampleNum int,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *FloatColFloatSampleIterator {
	r := &FloatColFloatSampleIterator{
		buf:          NewSampleItem(make([]PointItem[float64], 0, sampleNum)),
		sampleNum:    sampleNum,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *FloatColFloatSampleIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
}

func (r *FloatColFloatSampleIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}
	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *FloatColFloatSampleIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)
		sort.Sort(r.buf)
		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *FloatColFloatSampleIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *FloatColFloatSampleIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendFloatValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *FloatColFloatSampleIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if haveMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *FloatColFloatSampleIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *FloatColFloatSampleIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *FloatColFloatSampleIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendFloatValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *FloatColFloatSampleIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).FloatValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
	}
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *FloatColFloatSampleIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() && r.buf.Len() > 0 {
		return
	}
	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type IntegerColIntegerSampleIterator struct {
	sampleNum     int
	isSingleCall  bool
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *SampleItem[int64]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewIntegerColIntegerSampleIterator(sampleNum int,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *IntegerColIntegerSampleIterator {
	r := &IntegerColIntegerSampleIterator{
		buf:          NewSampleItem(make([]PointItem[int64], 0, sampleNum)),
		sampleNum:    sampleNum,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *IntegerColIntegerSampleIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
}

func (r *IntegerColIntegerSampleIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}
	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *IntegerColIntegerSampleIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)
		sort.Sort(r.buf)
		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *IntegerColIntegerSampleIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *IntegerColIntegerSampleIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendIntegerValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *IntegerColIntegerSampleIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if haveMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *IntegerColIntegerSampleIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *IntegerColIntegerSampleIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *IntegerColIntegerSampleIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendIntegerValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *IntegerColIntegerSampleIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).IntegerValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
	}
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *IntegerColIntegerSampleIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() && r.buf.Len() > 0 {
		return
	}
	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type StringColStringSampleIterator struct {
	sampleNum     int
	isSingleCall  bool
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *SampleItem[string]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewStringColStringSampleIterator(sampleNum int,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *StringColStringSampleIterator {
	r := &StringColStringSampleIterator{
		buf:          NewSampleItem(make([]PointItem[string], 0, sampleNum)),
		sampleNum:    sampleNum,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *StringColStringSampleIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
}

func (r *StringColStringSampleIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}
	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *StringColStringSampleIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).StringValuesRange(nil, start, end)
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values)
	} else {
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values)
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)
		sort.Sort(r.buf)
		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *StringColStringSampleIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *StringColStringSampleIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendStringValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *StringColStringSampleIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if haveMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *StringColStringSampleIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).StringValuesRange(nil, start, end)
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values)
	} else {
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *StringColStringSampleIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *StringColStringSampleIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendStringValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *StringColStringSampleIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).StringValuesRange(nil, start, end)
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values)
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values)
	}
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *StringColStringSampleIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() && r.buf.Len() > 0 {
		return
	}
	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}

type BooleanColBooleanSampleIterator struct {
	sampleNum     int
	isSingleCall  bool
	inOrdinal     int
	outOrdinal    int
	prevMaxIndex  int
	buf           *SampleItem[bool]
	auxChunk      Chunk
	auxProcessor  []*AuxProcessor
	windowIndex   []int
	prevBufIndex  []int
	currBufIndex  []int
	interBufIndex []int
}

func NewBooleanColBooleanSampleIterator(sampleNum int,
	isSingleCall bool, inOrdinal, outOrdinal int, auxProcessor []*AuxProcessor, rowDataType hybridqp.RowDataType,
) *BooleanColBooleanSampleIterator {
	r := &BooleanColBooleanSampleIterator{
		buf:          NewSampleItem(make([]PointItem[bool], 0, sampleNum)),
		sampleNum:    sampleNum,
		isSingleCall: isSingleCall,
		inOrdinal:    inOrdinal,
		outOrdinal:   outOrdinal,
	}
	if isSingleCall && len(auxProcessor) > 0 {
		r.auxProcessor = auxProcessor
		r.auxChunk = NewChunkBuilder(rowDataType).NewChunk("")
	}
	return r
}

func (r *BooleanColBooleanSampleIterator) reset() {
	r.prevBufIndex = r.prevBufIndex[:0]
	r.currBufIndex = r.currBufIndex[:0]
	r.interBufIndex = r.interBufIndex[:0]
	r.windowIndex = r.windowIndex[:0]
}

func (r *BooleanColBooleanSampleIterator) updateAuxColBothChunk(inChunk Chunk) {
	clone := r.auxChunk.Clone()
	r.auxChunk.Reset()
	sort.Ints(r.interBufIndex)
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.prevBufIndex {
		if hybridqp.BinarySearch(r.prevBufIndex[i], r.interBufIndex) {
			r.currBufIndex = append(r.currBufIndex, i)
		}
	}
	r.prevBufIndex = r.prevBufIndex[:0]
	for i := range r.buf.items {
		r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.prevBufIndex)
	cs, ws := 0, 0
	for i := range r.buf.items {
		if hybridqp.BinarySearch(r.buf.items[i].index, r.interBufIndex) {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					clone.Column(r.auxProcessor[j].outOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.currBufIndex[cs],
				)
			}
			cs++
		} else {
			for j := range r.auxProcessor {
				r.auxProcessor[j].auxHelperFunc(
					inChunk.Column(r.auxProcessor[j].inOrdinal),
					r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
					r.windowIndex[ws],
				)
			}
			ws++
		}
	}
	clone.Reset()
}

func (r *BooleanColBooleanSampleIterator) updatePrevItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).BooleanValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		for i := range r.buf.items {
			r.prevBufIndex = append(r.prevBufIndex, r.buf.items[i].index)
		}
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index)
		}
		r.interBufIndex = hybridqp.Intersect(r.prevBufIndex, r.currBufIndex)
		sort.Sort(r.buf)
		if !hybridqp.CompareSlice(r.interBufIndex, r.prevBufIndex) {
			r.updateAuxColBothChunk(inChunk)
		} else {
			r.updateAuxColInChunk(inChunk)
		}
	}
	r.reset()
}

func (r *BooleanColBooleanSampleIterator) updateAuxColInChunk(inChunk Chunk) {
	if len(r.interBufIndex) == 0 {
		r.auxChunk.Reset()
	}
	r.currBufIndex = r.currBufIndex[:0]
	for i := range r.buf.items {
		r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index-r.prevMaxIndex)
	}
	hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
	for j := range r.auxProcessor {
		r.auxProcessor[j].auxHelperFunc(
			inChunk.Column(r.auxProcessor[j].inOrdinal),
			r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
			r.windowIndex...,
		)
	}
}

func (r *BooleanColBooleanSampleIterator) appendPrevItem(
	inChunk, outChunk Chunk,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendBooleanValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for j := range r.buf.items {
			r.windowIndex = append(r.windowIndex, j)
		}
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.auxChunk.Reset()
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *BooleanColBooleanSampleIterator) processFirstWindow(
	inChunk, outChunk Chunk, sameInterval, haveMultiInterval bool, start, end int,
) {
	r.updatePrevItem(inChunk, start, end)
	if haveMultiInterval || !sameInterval {
		sort.Sort(r.buf)
		if r.buf.Len() > 0 {
			r.appendPrevItem(r.auxChunk, outChunk)
		}
		r.buf.Reset()
	}
}

func (r *BooleanColBooleanSampleIterator) updateCurrItem(
	inChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).BooleanValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		sort.Sort(r.buf)
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				r.auxChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
}

func (r *BooleanColBooleanSampleIterator) processLastWindow(
	inChunk Chunk, start, end int,
) {
	r.updateCurrItem(inChunk, start, end)
}

func (r *BooleanColBooleanSampleIterator) appendCurrItem(
	inChunk, outChunk Chunk, start int,
) {
	outColumn := outChunk.Column(r.outOrdinal)
	for j := range r.buf.items {
		outChunk.AppendTime(r.buf.items[j].time)
		outColumn.AppendBooleanValue(r.buf.items[j].value)
		outColumn.AppendNotNil()
	}
	if len(r.auxProcessor) > 0 {
		for i := range r.buf.items {
			r.currBufIndex = append(r.currBufIndex, r.buf.items[i].index+start-r.prevMaxIndex)
		}
		hybridqp.SortS1ByS2(r.windowIndex, r.currBufIndex)
		for j := range r.auxProcessor {
			r.auxProcessor[j].auxHelperFunc(
				inChunk.Column(r.auxProcessor[j].inOrdinal),
				outChunk.Column(r.auxProcessor[j].outOrdinal),
				r.windowIndex...,
			)
		}
		r.windowIndex = r.windowIndex[:0]
		r.currBufIndex = r.currBufIndex[:0]
	}
	outChunk.AppendIntervalIndex(outChunk.Len() - r.buf.Len())
}

func (r *BooleanColBooleanSampleIterator) processMiddleWindow(
	inChunk, outChunk Chunk, start, end int,
) {
	values := inChunk.Column(r.inOrdinal).BooleanValues()
	if len(r.auxProcessor) == 0 {
		r.buf.appendForFast(inChunk, start, end, r.buf.maxIndex+1, values[start:end])
	} else {
		r.prevMaxIndex = r.buf.maxIndex + 1
		r.windowIndex = r.buf.appendForAux(inChunk, start, end, values[start:end])
	}
	sort.Sort(r.buf)
	if r.buf.Len() > 0 {
		r.appendCurrItem(inChunk, outChunk, start)
	}
	r.buf.Reset()
}

func (r *BooleanColBooleanSampleIterator) Next(ie *IteratorEndpoint, p *IteratorParams) {
	inChunk, outChunk := ie.InputPoint.Chunk, ie.OutputPoint.Chunk
	if inChunk.Column(r.inOrdinal).IsEmpty() && r.buf.Len() > 0 {
		return
	}
	var end int
	firstIndex, lastIndex := 0, len(inChunk.IntervalIndex())-1
	for i, start := range inChunk.IntervalIndex() {
		if i < lastIndex {
			end = inChunk.IntervalIndex()[i+1]
		} else {
			end = inChunk.NumberOfRows()
		}
		if i == firstIndex && r.buf.Len() > 0 {
			r.processFirstWindow(inChunk, outChunk, p.sameInterval,
				firstIndex != lastIndex, start, end)
		} else if i == lastIndex && p.sameInterval {
			r.processLastWindow(inChunk, start, end)
		} else {
			r.processMiddleWindow(inChunk, outChunk, start, end)
		}
	}
}
