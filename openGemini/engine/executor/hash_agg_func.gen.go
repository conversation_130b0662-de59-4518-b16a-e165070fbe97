// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON><PERSON>/tmpl
//
// Source: hash_agg_func.gen.go.tmpl

// Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package executor

import (
	"container/heap"
	"errors"
	"fmt"
	"math"
	"sort"

	"github.com/openGemini/openGemini/engine/hybridqp"
	"github.com/openGemini/openGemini/lib/errno"
	"github.com/openGemini/openGemini/lib/util/lifted/influx/influxql"
)

type AggFuncType uint32

const (
	sumFunc AggFuncType = iota
	countFunc
	firstFunc
	lastFunc
	minFunc
	maxFunc
	percentileFunc
	heapFunc
	countPromFunc
	minPromFunc
	maxPromFunc
	stdvarPromFunc
	stddevPromFunc
	groupPromFunc
)

const DefaultTime = 0

type NewAggOperator func() aggOperator
type aggFunc struct {
	funcType         AggFuncType
	newAggOperatorFn NewAggOperator
	inIdx            int
	outIdx           int
	input            any
}

func NewAggFunc(aggType AggFuncType, fn NewAggOperator, inIdx int, outIdx int, p any) *aggFunc {
	return &aggFunc{
		funcType:         aggType,
		newAggOperatorFn: fn,
		inIdx:            inIdx,
		outIdx:           outIdx,
		input:            p,
	}
}

func (af *aggFunc) NewAggOperator() aggOperator {
	return af.newAggOperatorFn()
}

type aggOperator interface {
	Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, para any) error
	SetOutVal(c Chunk, colLoc int, para any)
	SetNullFill(oc Chunk, colLoc int, time int64)
	SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64)
	GetTime() int64
}

type aggOperatorMsg struct {
	results           []aggOperator
	intervalStartTime int64 // interval time
	time              int64 // true time
}

func NewCountFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for count iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer, influxql.Float, influxql.String, influxql.Boolean, influxql.Tag:
		return NewAggFunc(countFunc, NewCountOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "count/mean", dataType.String())
	}
}

func NewSumFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for sum iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(sumFunc, NewSumIntegerOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Float:
		return NewAggFunc(sumFunc, NewSumFloatOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "sum/mean", dataType.String())
	}
}

func NewFirstFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for first iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(firstFunc, NewFirstIntegerOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Float:
		return NewAggFunc(firstFunc, NewFirstFloatOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.String, influxql.Tag:
		return NewAggFunc(firstFunc, NewFirstStringOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Boolean:
		return NewAggFunc(firstFunc, NewFirstBooleanOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "first", dataType.String())
	}
}

func NewLastFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for last iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(lastFunc, NewLastIntegerOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Float:
		return NewAggFunc(lastFunc, NewLastFloatOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.String, influxql.Tag:
		return NewAggFunc(lastFunc, NewLastStringOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Boolean:
		return NewAggFunc(lastFunc, NewLastBooleanOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "last", dataType.String())
	}
}

func NewMinFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for min iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(minFunc, NewMinIntegerOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Float:
		return NewAggFunc(minFunc, NewMinFloatOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Boolean:
		return NewAggFunc(minFunc, NewMinBooleanOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "min", dataType.String())
	}
}

func NewMaxFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for max iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(maxFunc, NewMaxIntegerOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Float:
		return NewAggFunc(maxFunc, NewMaxFloatOperator, inOrdinal, outOrdinal, 0), nil
	case influxql.Boolean:
		return NewAggFunc(maxFunc, NewMaxBooleanOperator, inOrdinal, outOrdinal, 0), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "max", dataType.String())
	}
}

func NewPercentileFunc(inRowDataType, outRowDataType hybridqp.RowDataType, opt hybridqp.ExprOptions) (*aggFunc, error) {
	var percentile float64
	switch arg := opt.Expr.(*influxql.Call).Args[1].(type) {
	case *influxql.NumberLiteral:
		percentile = arg.Val
	case *influxql.IntegerLiteral:
		percentile = float64(arg.Val)
	default:
		return nil, fmt.Errorf("the type of input args of percentile iterator is unsupported")
	}
	if percentile < 0 || percentile > 100 {
		return nil, errors.New("invalid percentile, the value range must be 0 to 100")
	}
	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for Percentile iterator")
	}
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(percentileFunc, NewPercentileIntegerOperator, inOrdinal, outOrdinal, percentile), nil
	case influxql.Float:
		return NewAggFunc(percentileFunc, NewPercentileFloatOperator, inOrdinal, outOrdinal, percentile), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "Percentile", dataType.String())
	}
}

type heapParam struct {
	topN           int64
	sortFuncs      []func() sortEleMsg
	sortKeyIdx     []int
	sortAsc        []bool
	inOutColIdxMap map[int]int
}

func NewHeapParam(topN int64, sortFuncs []func() sortEleMsg, sorKeyIdx []int, sortAsc []bool, m map[int]int) *heapParam {
	return &heapParam{topN: topN, sortFuncs: sortFuncs, sortKeyIdx: sorKeyIdx, sortAsc: sortAsc, inOutColIdxMap: m}
}

func NewHeapFunc(inRowDataType, outRowDataType hybridqp.RowDataType, exprOpt []hybridqp.ExprOptions, sortIdx int, sortAsc bool) (*aggFunc, error) {
	opt := exprOpt[sortIdx]
	expr, ok := opt.Expr.(*influxql.Call)
	if !ok {
		return nil, fmt.Errorf("top/bottom input illegal, opt.Expr is not influxql.Call")
	}
	if len(expr.Args) < 2 {
		return nil, fmt.Errorf("top/bottom requires 2 or more arguments, got %d", len(expr.Args))
	}

	n, ok := expr.Args[len(expr.Args)-1].(*influxql.IntegerLiteral)
	if !ok {
		return nil, fmt.Errorf("top/bottom input illegal, opt.Args element is not influxql.IntegerLiteral")
	}

	inOrdinal := inRowDataType.FieldIndex(opt.Expr.(*influxql.Call).Args[0].(*influxql.VarRef).Val)
	outOrdinal := outRowDataType.FieldIndex(opt.Ref.Val)
	if inOrdinal < 0 || outOrdinal < 0 {
		return nil, fmt.Errorf("input and output schemas are not aligned for top/bottom iterator")
	}

	var m = map[int]int{inOrdinal: outOrdinal}
	for i, op := range exprOpt {
		if i == sortIdx {
			continue
		}
		inIdx := inRowDataType.FieldIndex(op.Expr.(*influxql.VarRef).Val)
		outIdx := outRowDataType.FieldIndex(op.Ref.Val)
		if inIdx < 0 || outIdx < 0 {
			return nil, fmt.Errorf("input and output schemas are not aligned for top/bottom iterator")
		}
		m[inIdx] = outIdx
	}

	var sortFuncs []func() sortEleMsg
	// init a column-pass row func for each column of data.
	for _, f := range inRowDataType.Fields() {
		dt := f.Expr.(*influxql.VarRef).Type
		switch dt {
		case influxql.Float:
			sortFuncs = append(sortFuncs, NewFloatSortEle)
		case influxql.Integer:
			sortFuncs = append(sortFuncs, NewIntegerSortEle)
		case influxql.Boolean:
			sortFuncs = append(sortFuncs, NewBoolSortEle)
		case influxql.String, influxql.Tag:
			sortFuncs = append(sortFuncs, NewStringSortEle)
		default:
			return nil, errno.NewError(errno.SortTransformRunningErr)
		}
	}
	// init a column-pass row func for time.
	sortFuncs = append(sortFuncs, NewIntegerSortEle)
	input := NewHeapParam(n.Val, sortFuncs, []int{inOrdinal}, []bool{sortAsc}, m)
	dataType := inRowDataType.Field(inOrdinal).Expr.(*influxql.VarRef).Type
	switch dataType {
	case influxql.Integer:
		return NewAggFunc(heapFunc, NewHeapIntegerOperator, inOrdinal, outOrdinal, input), nil
	case influxql.Float:
		return NewAggFunc(heapFunc, NewHeapFloatOperator, inOrdinal, outOrdinal, input), nil
	case influxql.Tag, influxql.String:
		return NewAggFunc(heapFunc, NewHeapStringOperator, inOrdinal, outOrdinal, input), nil
	default:
		return nil, errno.NewError(errno.UnsupportedDataType, "top/bottom", dataType.String())
	}
}

type countOperator struct {
	val int64 // count
}

func NewCountOperator() aggOperator {
	result := &countOperator{
		val: 0,
	}
	return result
}

func (s *countOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	s.val += int64(endRowLoc) - int64(startRowLoc)
	return nil
}

func (s *countOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendIntegerValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *countOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *countOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *countOperator) GetTime() int64 {
	return DefaultTime
}

type sumFloatOperator struct {
	val float64 // sum
}

func NewSumFloatOperator() aggOperator {
	return &sumFloatOperator{
		val: 0,
	}
}

func (s *sumFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	vs := c.Column(colLoc).FloatValues()[startRowLoc:endRowLoc]
	for i := 0; i < endRowLoc-startRowLoc; i++ {
		s.val += vs[i]
	}
	return nil
}

func (s *sumFloatOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendFloatValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *sumFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *sumFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *sumFloatOperator) GetTime() int64 {
	return DefaultTime
}

type sumIntegerOperator struct {
	val int64 // sum
}

func NewSumIntegerOperator() aggOperator {
	return &sumIntegerOperator{
		val: 0,
	}
}

func (s *sumIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	vs := c.Column(colLoc).IntegerValues()[startRowLoc:endRowLoc]
	for i := 0; i < endRowLoc-startRowLoc; i++ {
		s.val += vs[i]
	}
	return nil
}

func (s *sumIntegerOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendIntegerValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *sumIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *sumIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *sumIntegerOperator) GetTime() int64 {
	return DefaultTime
}

type minFloatOperator struct {
	val     float64
	nilFlag bool
}

func NewMinFloatOperator() aggOperator {
	return &minFloatOperator{
		val:     math.MaxFloat64,
		nilFlag: true,
	}
}

func (s *minFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	vs := c.Column(colLoc).FloatValues()[startRowLoc:endRowLoc]
	for i := 0; i < endRowLoc-startRowLoc; i++ {
		if vs[i] < s.val {
			s.val = vs[i]
			s.nilFlag = false
		}
	}
	return nil
}

func (s *minFloatOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendFloatValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *minFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *minFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *minFloatOperator) GetTime() int64 {
	return DefaultTime
}

type minIntegerOperator struct {
	val     int64
	nilFlag bool
}

func NewMinIntegerOperator() aggOperator {
	return &minIntegerOperator{
		val:     math.MaxInt64,
		nilFlag: true,
	}
}

func (s *minIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	vs := c.Column(colLoc).IntegerValues()[startRowLoc:endRowLoc]
	for i := 0; i < endRowLoc-startRowLoc; i++ {
		if vs[i] < s.val {
			s.val = vs[i]
			s.nilFlag = false
		}
	}
	return nil
}

func (s *minIntegerOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendIntegerValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *minIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *minIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *minIntegerOperator) GetTime() int64 {
	return DefaultTime
}

type minBooleanOperator struct {
	val     bool
	nilFlag bool
}

func NewMinBooleanOperator() aggOperator {
	return &minBooleanOperator{
		val:     true,
		nilFlag: true,
	}
}

func (s *minBooleanOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	vs := c.Column(colLoc).BooleanValues()[startRowLoc:endRowLoc]
	for i := 0; i < endRowLoc-startRowLoc; i++ {
		if (s.val && !vs[i]) || (s.val && vs[i] && s.nilFlag) {
			s.val = vs[i]
			s.nilFlag = false
		}
	}
	return nil
}

func (s *minBooleanOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendBooleanValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *minBooleanOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *minBooleanOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToBoolean(fillVal)
	oc.Column(colLoc).AppendBooleanValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *minBooleanOperator) GetTime() int64 {
	return DefaultTime
}

type maxFloatOperator struct {
	val     float64
	nilFlag bool
}

func NewMaxFloatOperator() aggOperator {
	return &maxFloatOperator{
		val:     -math.MaxFloat64,
		nilFlag: true,
	}
}

func (s *maxFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		val := c.Column(colLoc).FloatValue(startRowLoc)
		if val > s.val {
			s.val = val
			s.nilFlag = false
		}
	}
	return nil
}

func (s *maxFloatOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendFloatValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *maxFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *maxFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *maxFloatOperator) GetTime() int64 {
	return DefaultTime
}

type maxIntegerOperator struct {
	val     int64
	nilFlag bool
}

func NewMaxIntegerOperator() aggOperator {
	return &maxIntegerOperator{
		val:     -math.MaxInt64,
		nilFlag: true,
	}
}

func (s *maxIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		val := c.Column(colLoc).IntegerValue(startRowLoc)
		if val > s.val {
			s.val = val
			s.nilFlag = false
		}
	}
	return nil
}

func (s *maxIntegerOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendIntegerValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *maxIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *maxIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *maxIntegerOperator) GetTime() int64 {
	return DefaultTime
}

type maxBooleanOperator struct {
	val     bool
	nilFlag bool
}

func NewMaxBooleanOperator() aggOperator {
	return &maxBooleanOperator{
		val:     false,
		nilFlag: true,
	}
}

func (s *maxBooleanOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		val := c.Column(colLoc).BooleanValue(startRowLoc)
		if (!s.val && val) || (!s.val && !val && true) {
			s.val = val
			s.nilFlag = false
		}
	}
	return nil
}

func (s *maxBooleanOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	if s.nilFlag {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendBooleanValue(s.val)
	c.Column(colLoc).AppendNotNil()
}

func (s *maxBooleanOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *maxBooleanOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToBoolean(fillVal)
	oc.Column(colLoc).AppendBooleanValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *maxBooleanOperator) GetTime() int64 {
	return DefaultTime
}

type firstFloatOperator struct {
	val     float64 // first
	time    int64
	loc     int
	nilFlag bool
}

func NewFirstFloatOperator() aggOperator {
	return &firstFloatOperator{
		val:     float64(0),
		time:    influxql.MaxTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *firstFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newFirst := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) < s.time {
			s.time = c.TimeByIndex(startRowLoc)
			s.loc = startRowLoc
			newFirst = true
		}
	}
	if !newFirst {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).FloatValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *firstFloatOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendFloatValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *firstFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *firstFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *firstFloatOperator) GetTime() int64 {
	return s.time
}

type firstIntegerOperator struct {
	val     int64 // first
	time    int64
	loc     int
	nilFlag bool
}

func NewFirstIntegerOperator() aggOperator {
	return &firstIntegerOperator{
		val:     int64(0),
		time:    influxql.MaxTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *firstIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newFirst := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) < s.time {
			s.time = c.TimeByIndex(startRowLoc)
			s.loc = startRowLoc
			newFirst = true
		}
	}
	if !newFirst {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).IntegerValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *firstIntegerOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendIntegerValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *firstIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *firstIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *firstIntegerOperator) GetTime() int64 {
	return s.time
}

type firstStringOperator struct {
	val     []byte // first
	time    int64
	loc     int
	nilFlag bool
}

func NewFirstStringOperator() aggOperator {
	return &firstStringOperator{
		time:    influxql.MaxTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *firstStringOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newFirst := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) < s.time {
			s.time = c.TimeByIndex(startRowLoc)
			s.loc = startRowLoc
			newFirst = true
		}
	}
	if !newFirst {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		val := c.Column(colLoc).StringValue(rowLoc)
		if cap(s.val) >= len(val) {
			s.val = s.val[:len(val)]
			copy(s.val, val)
		} else {
			s.val = make([]byte, len(val))
			copy(s.val, val)
		}
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *firstStringOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendStringValue(string(s.val))
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *firstStringOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *firstStringOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToString(fillVal)
	oc.Column(colLoc).AppendStringValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *firstStringOperator) GetTime() int64 {
	return s.time
}

type firstBooleanOperator struct {
	val     bool // first
	time    int64
	loc     int
	nilFlag bool
}

func NewFirstBooleanOperator() aggOperator {
	return &firstBooleanOperator{
		val:     false,
		time:    influxql.MaxTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *firstBooleanOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newFirst := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) < s.time {
			s.time = c.TimeByIndex(startRowLoc)
			s.loc = startRowLoc
			newFirst = true
		}
	}
	if !newFirst {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).BooleanValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *firstBooleanOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendBooleanValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *firstBooleanOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *firstBooleanOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToBoolean(fillVal)
	oc.Column(colLoc).AppendBooleanValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *firstBooleanOperator) GetTime() int64 {
	return s.time
}

type lastFloatOperator struct {
	val     float64 // last
	time    int64
	loc     int
	nilFlag bool
}

func NewLastFloatOperator() aggOperator {
	return &lastFloatOperator{
		val:     float64(0),
		time:    influxql.MinTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *lastFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newLast := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) > s.time {
			s.loc = startRowLoc
			s.time = c.TimeByIndex(startRowLoc)
			newLast = true
		}
	}
	if !newLast {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).FloatValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *lastFloatOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendFloatValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *lastFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *lastFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *lastFloatOperator) GetTime() int64 {
	return s.time
}

type lastIntegerOperator struct {
	val     int64 // last
	time    int64
	loc     int
	nilFlag bool
}

func NewLastIntegerOperator() aggOperator {
	return &lastIntegerOperator{
		val:     int64(0),
		time:    influxql.MinTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *lastIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newLast := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) > s.time {
			s.loc = startRowLoc
			s.time = c.TimeByIndex(startRowLoc)
			newLast = true
		}
	}
	if !newLast {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).IntegerValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *lastIntegerOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendIntegerValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *lastIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *lastIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *lastIntegerOperator) GetTime() int64 {
	return s.time
}

type lastStringOperator struct {
	val     []byte // last
	time    int64
	loc     int
	nilFlag bool
}

func NewLastStringOperator() aggOperator {
	return &lastStringOperator{
		time:    influxql.MinTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *lastStringOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newLast := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) > s.time {
			s.loc = startRowLoc
			s.time = c.TimeByIndex(startRowLoc)
			newLast = true
		}
	}
	if !newLast {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		val := c.Column(colLoc).StringValue(rowLoc)
		if cap(s.val) >= len(val) {
			s.val = s.val[:len(val)]
			copy(s.val, val)
		} else {
			s.val = make([]byte, len(val))
			copy(s.val, val)
		}
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *lastStringOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendStringValue(string(s.val))
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *lastStringOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *lastStringOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToString(fillVal)
	oc.Column(colLoc).AppendStringValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *lastStringOperator) GetTime() int64 {
	return s.time
}

type lastBooleanOperator struct {
	val     bool // last
	time    int64
	loc     int
	nilFlag bool
}

func NewLastBooleanOperator() aggOperator {
	return &lastBooleanOperator{
		val:     false,
		time:    influxql.MinTime,
		loc:     0,
		nilFlag: true,
	}
}

func (s *lastBooleanOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	newLast := false
	for ; startRowLoc < endRowLoc; startRowLoc++ {
		if c.TimeByIndex(startRowLoc) > s.time {
			s.loc = startRowLoc
			s.time = c.TimeByIndex(startRowLoc)
			newLast = true
		}
	}
	if !newLast {
		return nil
	}
	if !c.Column(colLoc).IsNilV2(s.loc) {
		rowLoc := c.Column(colLoc).GetValueIndexV2(s.loc)
		s.val = c.Column(colLoc).BooleanValue(rowLoc)
		s.nilFlag = false
	} else {
		s.nilFlag = true
	}
	return nil
}

func (s *lastBooleanOperator) SetOutVal(c Chunk, colLoc int, _ any) {
	c.Column(colLoc).AppendColumnTime(s.time)
	if !s.nilFlag {
		c.Column(colLoc).AppendBooleanValue(s.val)
		c.Column(colLoc).AppendNotNil()
	} else {
		c.Column(colLoc).AppendNil()
	}
}

func (s *lastBooleanOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendColumnTime(s.time)
	oc.Column(colLoc).AppendNil()
}

func (s *lastBooleanOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToBoolean(fillVal)
	oc.Column(colLoc).AppendBooleanValue(val)
	oc.Column(colLoc).AppendNotNil()
	oc.Column(colLoc).AppendColumnTime(time)
}

func (s *lastBooleanOperator) GetTime() int64 {
	return s.time
}

type percentileFloatOperator struct {
	val []float64
}

func NewPercentileFloatOperator() aggOperator {
	return &percentileFloatOperator{
		val: make([]float64, 0),
	}
}

func (s *percentileFloatOperator) Len() int {
	return len(s.val)
}

func (s *percentileFloatOperator) Less(i, j int) bool {
	return s.val[i] < s.val[j]
}

func (s *percentileFloatOperator) Swap(i, j int) {
	s.val[i], s.val[j] = s.val[j], s.val[i]
}

func (s *percentileFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	s.val = append(s.val, c.Column(colLoc).FloatValues()[startRowLoc:endRowLoc]...)
	return nil
}

func (s *percentileFloatOperator) SetOutVal(c Chunk, colLoc int, percentile any) {
	if len(s.val) == 0 {
		c.Column(colLoc).AppendNil()
		return
	}
	sort.Sort(s)
	i := int(math.Floor(float64(len(s.val))*(percentile.(float64))/100.0+0.5)) - 1
	if i < 0 || i >= len(s.val) {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendFloatValue(s.val[i])
	c.Column(colLoc).AppendNotNil()
}

func (s *percentileFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *percentileFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *percentileFloatOperator) GetTime() int64 {
	return DefaultTime
}

type percentileIntegerOperator struct {
	val []int64
}

func NewPercentileIntegerOperator() aggOperator {
	return &percentileIntegerOperator{
		val: make([]int64, 0),
	}
}

func (s *percentileIntegerOperator) Len() int {
	return len(s.val)
}

func (s *percentileIntegerOperator) Less(i, j int) bool {
	return s.val[i] < s.val[j]
}

func (s *percentileIntegerOperator) Swap(i, j int) {
	s.val[i], s.val[j] = s.val[j], s.val[i]
}

func (s *percentileIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, _ any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	s.val = append(s.val, c.Column(colLoc).IntegerValues()[startRowLoc:endRowLoc]...)
	return nil
}

func (s *percentileIntegerOperator) SetOutVal(c Chunk, colLoc int, percentile any) {
	if len(s.val) == 0 {
		c.Column(colLoc).AppendNil()
		return
	}
	sort.Sort(s)
	i := int(math.Floor(float64(len(s.val))*(percentile.(float64))/100.0+0.5)) - 1
	if i < 0 || i >= len(s.val) {
		c.Column(colLoc).AppendNil()
		return
	}
	c.Column(colLoc).AppendIntegerValue(s.val[i])
	c.Column(colLoc).AppendNotNil()
}

func (s *percentileIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *percentileIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *percentileIntegerOperator) GetTime() int64 {
	return DefaultTime
}

type heapFloatOperator struct {
	init    bool
	sorPart *sortPartition
}

func NewHeapFloatOperator() aggOperator {
	return &heapFloatOperator{}
}

func (s *heapFloatOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, input any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	param := input.(*heapParam)
	if !s.init {
		s.sorPart = NewSortPartition(0, param.sortKeyIdx, param.sortAsc)
		s.sorPart.rows = make([]*sortRowMsg, 0, param.topN)
		s.init = true
	}
	for i := startRowLoc; i < endRowLoc; i++ {
		sortElems := make([]sortEleMsg, len(param.sortFuncs))
		for j, f := range param.sortFuncs {
			sortElems[j] = f()
		}
		row := NewSortRowMsg(sortElems)
		row.SetVals(c, i, nil)
		if len(s.sorPart.rows) == cap(s.sorPart.rows) {
			if !s.sorPart.rows[0].LessThan(row, s.sorPart.sortKeysIdxs, s.sorPart.ascending) {
				continue
			}
			s.sorPart.rows[0] = row.Clone()
			heap.Fix(s.sorPart, 0)
		} else {
			heap.Push(s.sorPart, row.Clone())
		}
	}
	return nil
}

func (s *heapFloatOperator) SetOutVal(c Chunk, colLoc int, input any) {
	sort.Sort(s.sorPart)
	n := len(s.sorPart.rows) - 1
	for i := n; i >= 0; i-- {
		s.sorPart.rows[i].AppendToChunkByColIdx(c, input.(*heapParam).inOutColIdxMap)
	}
	s.sorPart.rows = s.sorPart.rows[:0]
}

func (s *heapFloatOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *heapFloatOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToFloat(fillVal)
	oc.Column(colLoc).AppendFloatValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *heapFloatOperator) GetTime() int64 {
	return DefaultTime
}

type heapIntegerOperator struct {
	init    bool
	sorPart *sortPartition
}

func NewHeapIntegerOperator() aggOperator {
	return &heapIntegerOperator{}
}

func (s *heapIntegerOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, input any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	param := input.(*heapParam)
	if !s.init {
		s.sorPart = NewSortPartition(0, param.sortKeyIdx, param.sortAsc)
		s.sorPart.rows = make([]*sortRowMsg, 0, param.topN)
		s.init = true
	}
	for i := startRowLoc; i < endRowLoc; i++ {
		sortElems := make([]sortEleMsg, len(param.sortFuncs))
		for j, f := range param.sortFuncs {
			sortElems[j] = f()
		}
		row := NewSortRowMsg(sortElems)
		row.SetVals(c, i, nil)
		if len(s.sorPart.rows) == cap(s.sorPart.rows) {
			if !s.sorPart.rows[0].LessThan(row, s.sorPart.sortKeysIdxs, s.sorPart.ascending) {
				continue
			}
			s.sorPart.rows[0] = row.Clone()
			heap.Fix(s.sorPart, 0)
		} else {
			heap.Push(s.sorPart, row.Clone())
		}
	}
	return nil
}

func (s *heapIntegerOperator) SetOutVal(c Chunk, colLoc int, input any) {
	sort.Sort(s.sorPart)
	n := len(s.sorPart.rows) - 1
	for i := n; i >= 0; i-- {
		s.sorPart.rows[i].AppendToChunkByColIdx(c, input.(*heapParam).inOutColIdxMap)
	}
	s.sorPart.rows = s.sorPart.rows[:0]
}

func (s *heapIntegerOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *heapIntegerOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToInteger(fillVal)
	oc.Column(colLoc).AppendIntegerValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *heapIntegerOperator) GetTime() int64 {
	return DefaultTime
}

type heapStringOperator struct {
	init    bool
	sorPart *sortPartition
}

func NewHeapStringOperator() aggOperator {
	return &heapStringOperator{}
}

func (s *heapStringOperator) Compute(c Chunk, colLoc int, startRowLoc int, endRowLoc int, input any) error {
	if c.Column(colLoc).NilCount() != 0 {
		startRowLoc, endRowLoc = c.Column(colLoc).GetRangeValueIndexV2(startRowLoc, endRowLoc)
	}
	param := input.(*heapParam)
	if !s.init {
		s.sorPart = NewSortPartition(0, param.sortKeyIdx, param.sortAsc)
		s.sorPart.rows = make([]*sortRowMsg, 0, param.topN)
		s.init = true
	}
	for i := startRowLoc; i < endRowLoc; i++ {
		sortElems := make([]sortEleMsg, len(param.sortFuncs))
		for j, f := range param.sortFuncs {
			sortElems[j] = f()
		}
		row := NewSortRowMsg(sortElems)
		row.SetVals(c, i, nil)
		if len(s.sorPart.rows) == cap(s.sorPart.rows) {
			if !s.sorPart.rows[0].LessThan(row, s.sorPart.sortKeysIdxs, s.sorPart.ascending) {
				continue
			}
			s.sorPart.rows[0] = row.Clone()
			heap.Fix(s.sorPart, 0)
		} else {
			heap.Push(s.sorPart, row.Clone())
		}
	}
	return nil
}

func (s *heapStringOperator) SetOutVal(c Chunk, colLoc int, input any) {
	sort.Sort(s.sorPart)
	n := len(s.sorPart.rows) - 1
	for i := n; i >= 0; i-- {
		s.sorPart.rows[i].AppendToChunkByColIdx(c, input.(*heapParam).inOutColIdxMap)
	}
	s.sorPart.rows = s.sorPart.rows[:0]
}

func (s *heapStringOperator) SetNullFill(oc Chunk, colLoc int, time int64) {
	oc.Column(colLoc).AppendNil()
}

func (s *heapStringOperator) SetNumFill(oc Chunk, colLoc int, fillVal interface{}, time int64) {
	val, _ := hybridqp.TransToString(fillVal)
	oc.Column(colLoc).AppendStringValue(val)
	oc.Column(colLoc).AppendNotNil()
}

func (s *heapStringOperator) GetTime() int64 {
	return DefaultTime
}
