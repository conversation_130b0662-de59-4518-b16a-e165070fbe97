// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package executor

import "github.com/openGemini/openGemini/engine/op"

//go:generate tmpl -data=@./tmpldata hash_merge_func.gen.go.tmpl
//go:generate tmpl -data=@./tmpldata hash_agg_func.gen.go.tmpl

func init() {
	_ = op.GetOpFactory().AddOp(op.NewSumOp(op.FuncRoutineFactory(sumRoutineFactory)))
	_ = op.GetOpFactory().AddOp(op.NewCountOp(op.FuncRoutineFactory(countRoutineFactory)))
	_ = op.GetOpFactory().AddOp(op.NewCastorOp(op.FuncRoutineFactory(castorRoutineFactory)))
}
