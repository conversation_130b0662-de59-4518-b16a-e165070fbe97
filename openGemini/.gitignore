# IDE
.vscode/
.idea/
.fleet/

# MacOS
.DS_Store

# Build
build/

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# The tmp config files for install
config/openGemini-*
config/monitor-*

dist/

output/
