# Adopters

This document lists the organizations that use openGemini on production or testing environment and projects that adopts openGemini as their upstream.

Please make a pull request on this document if any organization or project would like to be added or removed.

| Adopter name             | Adopter Type     | Usage Scenario                                             |
| ------------------------ | ---------------- | ---------------------------------------------------------- |
| Huawei Cloud Eye Service | Service provider | Monitors cloud services and resources                      |
| Huawei Cloud IIoT        | OSS project      | Use openGemini to store devices data                       |
| Huawei Cloud CloudScope  | Service provider | Huawei Cloud serivce O&M                                   |
| Yinhehangtian            | OSS project      | Used for IT system O&M monitoring                          |
| Databuff                 | OSS project      | Used for cloud-native serivce O&M monitoring               |
| Yunnan Yuntianhua        | OSS project      | Used for Industrial manufacturing                          |
| Kehua                    | OSS project      | Stores monitoring data for smart electric energy solutions |
