[common]
  meta-join = ["127.0.0.1:8092"]
  ha-policy = "write-available-first"
  ignore-empty-tag = true


[meta]
  bind-address = "127.0.0.1:8088"
  http-bind-address = "127.0.0.1:8091"
  rpc-bind-address = "127.0.0.1:8092"
  dir = "/tmp/openGemini/meta"

[http]
  bind-address = "127.0.0.1:8086"
  flight-address = "127.0.0.1:8087"
  flight-enabled = true
  flight-auth-enabled = false

[data]
  store-ingest-addr = "127.0.0.1:8400"
  store-select-addr = "127.0.0.1:8401"
  store-data-dir = "/tmp/openGemini/data"
  store-wal-dir = "/tmp/openGemini/data"
  store-meta-dir = "/tmp/openGemini/meta"
  enable-mmap-read = false
  # enable perl regrep method
  # enable-perl-regrep = false

[coordinator]
  ## Set the query timeout period.
  query-timeout = "0s"

[index]
  cache-compress-enable = false

[logging]
  path = "/tmp/openGemini/logs/"

[gossip]
  enabled = false

[spec-limit]
  enable-query-when-exceed = true
  query-series-limit = 100000
  query-schema-limit = 1000000

[monitor]
  pushers = "http"
  store-enabled = true
  store-database = "_internal"
  store-interval = "10s"
  store-path = "/tmp/openGemini/metric/{{id}}/metric.data"
  compress = false
  https-enabled = false
  http-endpoint = "127.0.0.1:8086"

[runtime-config]
  enabled = false
  load-path = "/opt/dbs/runtimeconfig/overrides.yml"
  reload-period = "10s"

[limits]
  prom-limit-enabled = false
  max-label-name-length = 10
  max-label-value-length = 20
  max-label-names-per-series = 2
  max-metadata-length = 10
  reject-old-samples = true
  reject-old-samples-max-age = "14d"
  creation-grace-period = "10m"
  enforce-metadata-metric-name = true
  enforce-metric-name = true
  max-query-length = "0"

[record-write]
  enabled = true
  auth-enabled = false
  max-message-size = 4194304
  rpc-address = "127.0.0.1:8305"

[record-write.TLS]
  enabled = false
  mTLS-enabled = false
