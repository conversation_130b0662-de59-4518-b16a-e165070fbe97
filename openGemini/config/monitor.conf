[monitor]
  # localhost ip
  host = "{{addr}}"
  # Indicates the path of the metric file generated by the kernel. References openGemini.conf: [monitor] store-path
  # metric-path cannot have subdirectories
  metric-path = "/tmp/openGemini/metric"
  # Indicates the path of the log file generated by the kernel. References openGemini.conf: [logging] path
  # error-log-path cannot have subdirectories
  error-log-path = "/tmp/openGemini/logs"
  # Data disk path. References openGemini.conf: [data] store-data-dir
  disk-path = "/tmp/openGemini/data"
  # Wal disk path. References openGemini.conf: [data]  store-wal-dir
  aux-disk-path = "/tmp/openGemini/data/wal"
  # Name of the process to be monitored. Optional Value: ts-store,ts-sql,ts-meta.
  # Determined based on the actual process running on the local node.
  process = "ts-store,ts-sql,ts-meta"
  # the history file reported error-log names.
  history-file = "history.txt"
  # Is the metric compressed.
  compress = false
  # The port and IP of the metircs reporting interface
  http-endpoint = "{{addr}}:9098"

[query]
  # query for some DDL. Report for these data to monitor cluster.
  # - SHOW DATABASES
  # - SHOW MEASUREMENTS
  # - SHOW SERIES CARDINALITY FROM mst
  query-enable = false
  http-endpoint = "{{query_addr}}:8086"
  query-interval = "5m"
  # username = ""
  # password = ""
  # https-enable = false

[report]
  # Address for metric data to be reported.
  address = "{{report_addr}}:8086"
  # Database name for metric data to be reported.
  database = "monitor"
  # username = ""
  # password = ""
  # https-enable = false
  rp = "autogen"
  rp-duration = "168h"

[logging]
  format = "auto"
  level = "info"
  path = "/tmp/openGemini/logs/"
  max-size = "64m"
  max-num = 30
  max-age = 7
  compress-enabled = true
