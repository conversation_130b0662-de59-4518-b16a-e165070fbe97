<svg width="591.000000" height="183.000000" viewBox="0 0 591 183" fill="none" xmlns="http://www.w3.org/2000/svg">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_7_13_dd" x="35.000000" y="98.000000" width="51.000000" height="51.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_7_14_dd" x="60.000000" y="24.000000" width="48.000000" height="48.000000" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_7_18_dd" x="118.806931" y="113.659286" width="35.152557" height="45.561081" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.17 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<filter id="filter_7_21_dd" x="89.330948" y="70.487534" width="63.735382" height="63.750031" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="4"/>
			<feGaussianBlur stdDeviation="3"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0.098039 0 0 0 0 0.094118 0 0 0 0 0.094118 0 0 0 0.54 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
	</defs>
	<circle id="椭圆 1" cx="91.500000" cy="91.500000" r="91.500000" fill="#A70303"/>
	<g filter="url(#filter_7_13_dd)">
		<circle id="椭圆 2" r="21.500000" transform="matrix(1 0 0 1 60.5 119.5)" fill="#F7F6EE"/>
	</g>
	<g filter="url(#filter_7_14_dd)">
		<circle id="椭圆 2" r="20.000000" transform="matrix(1 0 0 1 84 44)" fill="#F8F8F4"/>
	</g>
	<line id="直线 1" x1="109.160736" y1="86.021179" x2="88.072876" y2="52.944427" stroke="#F8F8F4" stroke-width="7.000000"/>
	<line id="直线 5" x1="104.047882" y1="103.509331" x2="69.746399" y2="115.518768" stroke="#F9F7F7" stroke-width="8.000000"/>
	<path id="openGemini" d="M213.031 118.891C221.984 118.891 230.25 111.984 230.25 100.078C230.25 88.1719 221.984 81.2656 213.031 81.2656C204.078 81.2656 195.812 88.1719 195.812 100.078C195.812 111.984 204.078 118.891 213.031 118.891ZM213.031 111.219C208.172 111.219 205.422 106.859 205.422 100.078C205.422 93.3594 208.172 88.9375 213.031 88.9375C217.891 88.9375 220.641 93.3594 220.641 100.078C220.641 106.859 217.891 111.219 213.031 111.219ZM238.126 131.766L247.533 131.766L247.533 120.875L247.205 114.984C249.955 117.484 252.97 118.891 256.111 118.891C263.97 118.891 271.33 111.797 271.33 99.5C271.33 88.5 266.095 81.2656 257.251 81.2656C253.423 81.2656 249.705 83.3125 246.689 85.875L246.501 85.875L245.798 82.1562L238.126 82.1562L238.126 131.766ZM253.986 111.156C252.08 111.156 249.767 110.453 247.533 108.469L247.533 92.7188C249.955 90.2188 252.142 89.0156 254.564 89.0156C259.501 89.0156 261.673 92.7812 261.673 99.625C261.673 107.438 258.345 111.156 253.986 111.156ZM294.952 118.891C299.374 118.891 303.905 117.359 307.436 114.922L304.233 109.172C301.671 110.766 299.108 111.656 296.171 111.656C290.858 111.656 287.014 108.594 286.249 102.703L308.327 102.703C308.577 101.875 308.78 100.141 308.78 98.3594C308.78 88.5 303.655 81.2656 293.796 81.2656C285.296 81.2656 277.092 88.5 277.092 100.078C277.092 111.922 284.905 118.891 294.952 118.891ZM286.124 96.4375C286.827 91.25 290.155 88.5625 293.921 88.5625C298.53 88.5625 300.717 91.625 300.717 96.4375L286.124 96.4375ZM316.526 118L325.933 118L325.933 93.3594C328.558 90.7344 330.401 89.3281 333.355 89.3281C336.87 89.3281 338.401 91.25 338.401 96.8125L338.401 118L347.823 118L347.823 95.6562C347.823 86.6406 344.495 81.2656 336.745 81.2656C331.886 81.2656 328.23 83.8281 325.089 86.8906L324.901 86.8906L324.198 82.1562L316.526 82.1562L316.526 118ZM378.66 118.891C385.254 118.891 390.816 116.344 394.019 113.203L394.019 91.8281L377.191 91.8281L377.191 99.5625L385.582 99.5625L385.582 108.906C384.238 110.062 381.925 110.703 379.691 110.703C370.347 110.703 365.613 104.5 365.613 94.1875C365.613 84.0156 371.113 77.875 378.988 77.875C383.269 77.875 385.957 79.5938 388.332 81.8438L393.316 75.8281C390.254 72.6875 385.519 69.75 378.66 69.75C366.05 69.75 355.941 78.8906 355.941 94.5156C355.941 110.312 365.8 118.891 378.66 118.891ZM419.048 118.891C423.47 118.891 428.001 117.359 431.532 114.922L428.329 109.172C425.767 110.766 423.204 111.656 420.267 111.656C414.954 111.656 411.11 108.594 410.345 102.703L432.423 102.703C432.673 101.875 432.876 100.141 432.876 98.3594C432.876 88.5 427.751 81.2656 417.892 81.2656C409.392 81.2656 401.188 88.5 401.188 100.078C401.188 111.922 409.001 118.891 419.048 118.891ZM410.22 96.4375C410.923 91.25 414.251 88.5625 418.017 88.5625C422.626 88.5625 424.813 91.625 424.813 96.4375L410.22 96.4375ZM440.622 118L450.029 118L450.029 93.3594C452.513 90.6719 454.826 89.3281 456.872 89.3281C460.263 89.3281 461.935 91.25 461.935 96.8125L461.935 118L471.279 118L471.279 93.3594C473.826 90.6719 476.138 89.3281 478.185 89.3281C481.576 89.3281 483.169 91.25 483.169 96.8125L483.169 118L492.591 118L492.591 95.6562C492.591 86.6406 489.06 81.2656 481.451 81.2656C476.779 81.2656 473.326 84.0781 470.06 87.5312C468.326 83.5625 465.326 81.2656 460.201 81.2656C455.529 81.2656 452.201 83.8281 449.185 86.9531L448.997 86.9531L448.294 82.1562L440.622 82.1562L440.622 118ZM502.318 118L511.725 118L511.725 82.1562L502.318 82.1562L502.318 118ZM507.053 76.4062C510.24 76.4062 512.49 74.3594 512.49 71.2188C512.49 68.1406 510.24 66.0938 507.053 66.0938C503.787 66.0938 501.537 68.1406 501.537 71.2188C501.537 74.3594 503.787 76.4062 507.053 76.4062ZM521.774 118L531.181 118L531.181 93.3594C533.806 90.7344 535.649 89.3281 538.603 89.3281C542.118 89.3281 543.649 91.25 543.649 96.8125L543.649 118L553.071 118L553.071 95.6562C553.071 86.6406 549.743 81.2656 541.993 81.2656C537.134 81.2656 533.478 83.8281 530.337 86.8906L530.149 86.8906L529.446 82.1562L521.774 82.1562L521.774 118ZM562.798 118L572.205 118L572.205 82.1562L562.798 82.1562L562.798 118ZM567.533 76.4062C570.72 76.4062 572.97 74.3594 572.97 71.2188C572.97 68.1406 570.72 66.0938 567.533 66.0938C564.267 66.0938 562.017 68.1406 562.017 71.2188C562.017 74.3594 564.267 76.4062 567.533 76.4062Z" fill-rule="evenodd" fill="#000000"/>
	<g filter="url(#filter_7_18_dd)">
		<line id="直线 6" x1="125.772141" y1="115.518738" x2="146.994278" y2="149.360901" stroke="#F9F3F3" stroke-width="7.000000"/>
	</g>
	<path id="椭圆 3" d="M136.102 23.3085C139.106 19.6027 145.678 19.1672 151.663 22.8841C158.913 27.3867 162.565 36.0624 158.303 41.3195C154.041 46.5765 144.801 44.7931 138.899 38.6267C134.028 33.5363 133.097 27.0144 136.102 23.3085Z" fill-rule="evenodd" fill="#FAFAF7"/>
	<path id="椭圆 6" d="M171.952 79.7932C169.811 74.589 170.587 67.05 175.113 66.4239C179.638 65.7977 182.432 72.8427 181.785 78.4326C181.372 81.9997 179.56 84.2827 177.621 84.5511C175.681 84.8194 173.318 83.114 171.952 79.7932Z" fill-rule="evenodd" fill="#F6F6F3"/>
	<g filter="url(#filter_7_21_dd)">
		<ellipse id="椭圆 4" rx="22.867670" ry="22.875000" transform="matrix(0.999947 0.010317 -0.010317 0.999947 121.199 98.3625)" fill="#F5F6EE"/>
	</g>
	<line id="直线 7" x1="140.064392" y1="92.071877" x2="176.080978" y2="75.487518" stroke="#FDFAFA" stroke-width="7.000000"/>
	<line id="直线 8" x1="129.773956" y1="79.490631" x2="146.924713" y2="37.171875" stroke="#FAFAFA" stroke-width="7.000000"/>
	<path id="椭圆 5" d="M161.738 147.23C161.54 153.193 155.139 159.668 148.844 162.538C143.721 164.873 138.489 164.912 135.92 162.583C132.671 159.638 133.626 152.846 140.605 146.751C150.549 138.066 162.028 138.498 161.738 147.23Z" fill-rule="evenodd" fill="#F7F7F4"/>
</svg>
