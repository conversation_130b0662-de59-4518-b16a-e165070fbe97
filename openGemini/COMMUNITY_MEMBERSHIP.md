# openGemini Community Membership

**Note:** This document keeps changing based on the status and feedback of openGemini Community.

| Role                      | Requirements                                                                                                                     | Responsibilities                                                    | Privileges                                                                            |
|---------------------------|----------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------|---------------------------------------------------------------------------------------|
| [Member](#member)         | Nomination from 1 Maintainer, 1/2 majority vote, active participation in the community                                           | Welcome and guide new contributors                                  | Member of the openGemini GitHub organization                                          |
| [Committer](#committer)   | Nomination from 1 Maintainer, 1/2 majority vote, strong domain knowledge, active contributions to code and reviews               | Review and approve contributions from community members             | Write access to specific packages within the relevant repository                      |
| [Maintainer](#maintainer) | Nomination from 1 Maintainer, 2/3 majority vote, no objections from existing Maintainers, demonstrated strong technical judgment | Participate in release planning and feature development/maintenance | Top-level write access to the relevant repository; Name entry in the Maintainers file |

**Note:** It is mandatory for all openGemini community members to follow
openGemini [Code of Conduct](./CODE_OF_CONDUCT.md).

## Member

Members are active participants in the community who contribute by authoring PRs,
reviewing issues/PRs or participating in community discussions on slack/mailing list.

### Requirements

- Sponsor from 1 Maintainer
- Requires a 1/2 majority vote from the Maintainers, with no objections from existing Maintainers.
- Enabled two-factor authentication on their GitHub account
- Actively contributed to the community. Contributions may include, but are not limited to:
    - Authoring PRs
    - Reviewing issues/PRs authored by other community members
    - Participating in community discussions on slack/mailing list
    - Participate in openGemini community meetings

### Responsibilities and privileges

- Member of the openGemini GitHub organization
- Can be assigned to issues and PRs and community members can also request their review
- Participate in assigned issues and PRs
- Welcome new contributors
- Guide new contributors to relevant docs/files
- Help/Motivate new members in contributing to openGemini

## Committer

Committers are active members who have good experience and knowledge of the domain.
They have actively participated in the issue/PR reviews and have identified relevant issues during review.

### Requirements

- Nomination from 1 Maintainer
- Requires a 1/2 majority vote from the Maintainers, with no objections from existing Maintainers.
- Have reviewed good number of PRs
- Have good codebase knowledge

### Responsibilities and Privileges

- Review code to maintain/improve code quality
- Acknowledge and work on review requests from community members
- May approve code contributions for acceptance related to relevant expertise
- Have 'write access' to specific packages inside a repo
- Continue to contribute and guide other community members to contribute in openGemini project


## Maintainer

Maintainers are committers who have shown good technical judgement in feature design/development in the past.
Has overall knowledge of the project and features in the project.

### Requirements

- Nomination from 1 Maintainer
- Requires a 2/3 majority vote from the Maintainers, with no objections from existing Maintainers.
- Good technical judgement in feature design/development

### Responsibilities and privileges

- Participate in release planning
- Maintain project code quality
- Ensure API compatibility across different versions, maintaining both forward and backward compatibility as features evolve
- Analyze and propose new features/enhancements in openGemini project
- Demonstrate sound technical judgement
- Mentor contributors and committers
- Have top level write access to relevant repository (able click Merge PR button when manual check-in is necessary)
- Name entry in Maintainers file of the repository
- Participate & Drive design/development of multiple features

## Principles

- Nomination Requests: Members may request nomination for roles.
- Trust-Based Nominations: Nominations are built on trust within the community.
- Private Nominations: Nominations are handled privately through the mailing list, with public announcements following the decision.
- Role Removal or Downgrade: Roles may be removed or downgraded under the following circumstances:
  - Voluntary withdrawal
  - Violations of community conduct, leading to impeachment
  - Inactivity within the community, resulting in impeachment by the owners

## Modifying this Charter

Changes to this document require approval by a 2/3 majority vote of the Maintainers.
