[System]

# data directory
data_dir = /var/openGemini-castor

# model configuration directory
model_config_dir = /var/openGemini-castor/conf

# domain socket file, only used in socket_server mode
socket_file = /var/openGemini-castor/castor.sock

# directory of logging
logger_file = /log/openGemini-castor/castor.log

# level of logger
logger_level = INFO

# recevied data batch size of castor handler's attribute: batch_data
batch_size = 20

# timeout of agent flush batch data, unit: second
timeout = 0.1

# maximum connection size
max_connections = 128

# number of workers
num_workers = 8

# lower bound of cache size for storing timeseries meta-infomation
bucket_size = 300000

# cache lifetime, if cache not accessed within duration, it will be purged
data_lifetime = 3600
