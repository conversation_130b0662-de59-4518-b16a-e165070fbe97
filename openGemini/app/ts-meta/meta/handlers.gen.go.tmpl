// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package meta

import (
	"github.com/openGemini/openGemini/app/ts-meta/meta/message"
	"github.com/openGemini/openGemini/engine/executor"
	"github.com/openGemini/openGemini/lib/spdy/transport"
)

func New(typ uint8) RPCHandler {
	switch typ {
    {{- range .}}
    case message.{{.}}RequestMessage:
 		return &{{.}}{}
    {{- end}}
	default:
		return nil
	}
}

{{- range .}}
type {{.}} struct {
    BaseHandler

	req *message.{{.}}Request
}

func (h *{{.}}) SetRequestMsg(data transport.Codec) error {
	msg, ok := data.(*message.{{.}}Request)
	if !ok {
		return executor.NewInvalidTypeError("*message.{{.}}Request", data)
	}
	h.req = msg
	return nil
}

func (h *{{.}}) Instance() RPCHandler {
    return &{{.}}{}
}
{{end}}
