// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package handler

import (
	"github.com/openGemini/openGemini/engine/executor"
	"github.com/openGemini/openGemini/lib/codec"
	"github.com/openGemini/openGemini/lib/netstorage"
)

func NewHandler(typ uint8) RPCHandler {
	switch typ {
	{{- range .}}
    case netstorage.{{.}}RequestMessage:
        return &{{.}}{}
    {{- end}}
    default:
        return nil
    }
}

{{range .}}
type {{.}} struct {
	BaseHandler

	req *netstorage.{{.}}Request
	rsp *netstorage.{{.}}Response
}

func (h *{{.}}) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.{{.}}Response{}
    req, ok := msg.(*netstorage.{{.}}Request)
    if !ok {
        return executor.NewInvalidTypeError("*netstorage.{{.}}Request", msg)
    }
    h.req = req
    return nil
}
{{end}}
