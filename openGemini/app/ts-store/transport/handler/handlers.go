// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: handlers.go.tmpl

// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package handler

import (
	"github.com/openGemini/openGemini/engine/executor"
	"github.com/openGemini/openGemini/lib/codec"
	"github.com/openGemini/openGemini/lib/netstorage"
)

func NewHandler(typ uint8) RPCHandler {
	switch typ {
	case netstorage.SeriesKeysRequestMessage:
		return &SeriesKeys{}
	case netstorage.SeriesExactCardinalityRequestMessage:
		return &SeriesExactCardinality{}
	case netstorage.SeriesCardinalityRequestMessage:
		return &SeriesCardinality{}
	case netstorage.ShowTagValuesRequestMessage:
		return &ShowTagValues{}
	case netstorage.ShowTagValuesCardinalityRequestMessage:
		return &ShowTagValuesCardinality{}
	case netstorage.GetShardSplitPointsRequestMessage:
		return &GetShardSplitPoints{}
	case netstorage.DeleteRequestMessage:
		return &Delete{}
	case netstorage.CreateDataBaseRequestMessage:
		return &CreateDataBase{}
	case netstorage.ShowQueriesRequestMessage:
		return &ShowQueries{}
	case netstorage.KillQueryRequestMessage:
		return &KillQuery{}
	case netstorage.ShowTagKeysRequestMessage:
		return &ShowTagKeys{}
	case netstorage.RaftMessagesRequestMessage:
		return &RaftMessages{}
	default:
		return nil
	}
}

type SeriesKeys struct {
	BaseHandler

	req *netstorage.SeriesKeysRequest
	rsp *netstorage.SeriesKeysResponse
}

func (h *SeriesKeys) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.SeriesKeysResponse{}
	req, ok := msg.(*netstorage.SeriesKeysRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.SeriesKeysRequest", msg)
	}
	h.req = req
	return nil
}

type SeriesExactCardinality struct {
	BaseHandler

	req *netstorage.SeriesExactCardinalityRequest
	rsp *netstorage.SeriesExactCardinalityResponse
}

func (h *SeriesExactCardinality) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.SeriesExactCardinalityResponse{}
	req, ok := msg.(*netstorage.SeriesExactCardinalityRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.SeriesExactCardinalityRequest", msg)
	}
	h.req = req
	return nil
}

type SeriesCardinality struct {
	BaseHandler

	req *netstorage.SeriesCardinalityRequest
	rsp *netstorage.SeriesCardinalityResponse
}

func (h *SeriesCardinality) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.SeriesCardinalityResponse{}
	req, ok := msg.(*netstorage.SeriesCardinalityRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.SeriesCardinalityRequest", msg)
	}
	h.req = req
	return nil
}

type ShowTagValues struct {
	BaseHandler

	req *netstorage.ShowTagValuesRequest
	rsp *netstorage.ShowTagValuesResponse
}

func (h *ShowTagValues) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.ShowTagValuesResponse{}
	req, ok := msg.(*netstorage.ShowTagValuesRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.ShowTagValuesRequest", msg)
	}
	h.req = req
	return nil
}

type ShowTagValuesCardinality struct {
	BaseHandler

	req *netstorage.ShowTagValuesCardinalityRequest
	rsp *netstorage.ShowTagValuesCardinalityResponse
}

func (h *ShowTagValuesCardinality) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.ShowTagValuesCardinalityResponse{}
	req, ok := msg.(*netstorage.ShowTagValuesCardinalityRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.ShowTagValuesCardinalityRequest", msg)
	}
	h.req = req
	return nil
}

type GetShardSplitPoints struct {
	BaseHandler

	req *netstorage.GetShardSplitPointsRequest
	rsp *netstorage.GetShardSplitPointsResponse
}

func (h *GetShardSplitPoints) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.GetShardSplitPointsResponse{}
	req, ok := msg.(*netstorage.GetShardSplitPointsRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.GetShardSplitPointsRequest", msg)
	}
	h.req = req
	return nil
}

type Delete struct {
	BaseHandler

	req *netstorage.DeleteRequest
	rsp *netstorage.DeleteResponse
}

func (h *Delete) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.DeleteResponse{}
	req, ok := msg.(*netstorage.DeleteRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.DeleteRequest", msg)
	}
	h.req = req
	return nil
}

type CreateDataBase struct {
	BaseHandler

	req *netstorage.CreateDataBaseRequest
	rsp *netstorage.CreateDataBaseResponse
}

func (h *CreateDataBase) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.CreateDataBaseResponse{}
	req, ok := msg.(*netstorage.CreateDataBaseRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.CreateDataBaseRequest", msg)
	}
	h.req = req
	return nil
}

type ShowQueries struct {
	BaseHandler

	req *netstorage.ShowQueriesRequest
	rsp *netstorage.ShowQueriesResponse
}

func (h *ShowQueries) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.ShowQueriesResponse{}
	req, ok := msg.(*netstorage.ShowQueriesRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.ShowQueriesRequest", msg)
	}
	h.req = req
	return nil
}

type KillQuery struct {
	BaseHandler

	req *netstorage.KillQueryRequest
	rsp *netstorage.KillQueryResponse
}

func (h *KillQuery) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.KillQueryResponse{}
	req, ok := msg.(*netstorage.KillQueryRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.KillQueryRequest", msg)
	}
	h.req = req
	return nil
}

type ShowTagKeys struct {
	BaseHandler

	req *netstorage.ShowTagKeysRequest
	rsp *netstorage.ShowTagKeysResponse
}

func (h *ShowTagKeys) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.ShowTagKeysResponse{}
	req, ok := msg.(*netstorage.ShowTagKeysRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.ShowTagKeysRequest", msg)
	}
	h.req = req
	return nil
}

type RaftMessages struct {
	BaseHandler

	req *netstorage.RaftMessagesRequest
	rsp *netstorage.RaftMessagesResponse
}

func (h *RaftMessages) SetMessage(msg codec.BinaryCodec) error {
	h.rsp = &netstorage.RaftMessagesResponse{}
	req, ok := msg.(*netstorage.RaftMessagesRequest)
	if !ok {
		return executor.NewInvalidTypeError("*netstorage.RaftMessagesRequest", msg)
	}
	h.req = req
	return nil
}
