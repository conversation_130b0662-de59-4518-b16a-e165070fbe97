// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: lib/netstorage/data/data.proto

package netstorage_data

import (
	fmt "fmt"
	math "math"

    proto "github.com/openGemini/openGemini/lib/util/lifted/protobuf/proto"
    proto1 "github.com/openGemini/openGemini/lib/util/lifted/influx/meta/proto"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type SeriesKeysRequest struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	PtIDs                []uint32 `protobuf:"varint,2,rep,name=PtIDs" json:"PtIDs,omitempty"`
	Measurements         []string `protobuf:"bytes,3,rep,name=Measurements" json:"Measurements,omitempty"`
	Condition            *string  `protobuf:"bytes,4,opt,name=condition" json:"condition,omitempty"`
	Exact                *bool    `protobuf:"varint,5,opt,name=Exact" json:"Exact,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeriesKeysRequest) Reset()         { *m = SeriesKeysRequest{} }
func (m *SeriesKeysRequest) String() string { return proto.CompactTextString(m) }
func (*SeriesKeysRequest) ProtoMessage()    {}
func (*SeriesKeysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{0}
}
func (m *SeriesKeysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeriesKeysRequest.Unmarshal(m, b)
}
func (m *SeriesKeysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeriesKeysRequest.Marshal(b, m, deterministic)
}
func (m *SeriesKeysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesKeysRequest.Merge(m, src)
}
func (m *SeriesKeysRequest) XXX_Size() int {
	return xxx_messageInfo_SeriesKeysRequest.Size(m)
}
func (m *SeriesKeysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesKeysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesKeysRequest proto.InternalMessageInfo

func (m *SeriesKeysRequest) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *SeriesKeysRequest) GetPtIDs() []uint32 {
	if m != nil {
		return m.PtIDs
	}
	return nil
}

func (m *SeriesKeysRequest) GetMeasurements() []string {
	if m != nil {
		return m.Measurements
	}
	return nil
}

func (m *SeriesKeysRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

func (m *SeriesKeysRequest) GetExact() bool {
	if m != nil && m.Exact != nil {
		return *m.Exact
	}
	return false
}

type SeriesKeysResponse struct {
	Series               []string `protobuf:"bytes,1,rep,name=Series" json:"Series,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeriesKeysResponse) Reset()         { *m = SeriesKeysResponse{} }
func (m *SeriesKeysResponse) String() string { return proto.CompactTextString(m) }
func (*SeriesKeysResponse) ProtoMessage()    {}
func (*SeriesKeysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{1}
}
func (m *SeriesKeysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeriesKeysResponse.Unmarshal(m, b)
}
func (m *SeriesKeysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeriesKeysResponse.Marshal(b, m, deterministic)
}
func (m *SeriesKeysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesKeysResponse.Merge(m, src)
}
func (m *SeriesKeysResponse) XXX_Size() int {
	return xxx_messageInfo_SeriesKeysResponse.Size(m)
}
func (m *SeriesKeysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesKeysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesKeysResponse proto.InternalMessageInfo

func (m *SeriesKeysResponse) GetSeries() []string {
	if m != nil {
		return m.Series
	}
	return nil
}

func (m *SeriesKeysResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type ShowTagKeysRequest struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	PtIDs                []uint32 `protobuf:"varint,2,rep,name=PtIDs" json:"PtIDs,omitempty"`
	Measurements         []string `protobuf:"bytes,3,rep,name=Measurements" json:"Measurements,omitempty"`
	Condition            *string  `protobuf:"bytes,4,opt,name=condition" json:"condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowTagKeysRequest) Reset()         { *m = ShowTagKeysRequest{} }
func (m *ShowTagKeysRequest) String() string { return proto.CompactTextString(m) }
func (*ShowTagKeysRequest) ProtoMessage()    {}
func (*ShowTagKeysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{2}
}
func (m *ShowTagKeysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowTagKeysRequest.Unmarshal(m, b)
}
func (m *ShowTagKeysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowTagKeysRequest.Marshal(b, m, deterministic)
}
func (m *ShowTagKeysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowTagKeysRequest.Merge(m, src)
}
func (m *ShowTagKeysRequest) XXX_Size() int {
	return xxx_messageInfo_ShowTagKeysRequest.Size(m)
}
func (m *ShowTagKeysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowTagKeysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShowTagKeysRequest proto.InternalMessageInfo

func (m *ShowTagKeysRequest) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *ShowTagKeysRequest) GetPtIDs() []uint32 {
	if m != nil {
		return m.PtIDs
	}
	return nil
}

func (m *ShowTagKeysRequest) GetMeasurements() []string {
	if m != nil {
		return m.Measurements
	}
	return nil
}

func (m *ShowTagKeysRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

type ShowTagKeysResponse struct {
	TagKeys              []string `protobuf:"bytes,1,rep,name=TagKeys" json:"TagKeys,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowTagKeysResponse) Reset()         { *m = ShowTagKeysResponse{} }
func (m *ShowTagKeysResponse) String() string { return proto.CompactTextString(m) }
func (*ShowTagKeysResponse) ProtoMessage()    {}
func (*ShowTagKeysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{3}
}
func (m *ShowTagKeysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowTagKeysResponse.Unmarshal(m, b)
}
func (m *ShowTagKeysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowTagKeysResponse.Marshal(b, m, deterministic)
}
func (m *ShowTagKeysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowTagKeysResponse.Merge(m, src)
}
func (m *ShowTagKeysResponse) XXX_Size() int {
	return xxx_messageInfo_ShowTagKeysResponse.Size(m)
}
func (m *ShowTagKeysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowTagKeysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShowTagKeysResponse proto.InternalMessageInfo

func (m *ShowTagKeysResponse) GetTagKeys() []string {
	if m != nil {
		return m.TagKeys
	}
	return nil
}

func (m *ShowTagKeysResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type CreateDataBaseRequest struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	Pt                   *uint32  `protobuf:"varint,2,req,name=pt" json:"pt,omitempty"`
	Rp                   *string  `protobuf:"bytes,3,req,name=rp" json:"rp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDataBaseRequest) Reset()         { *m = CreateDataBaseRequest{} }
func (m *CreateDataBaseRequest) String() string { return proto.CompactTextString(m) }
func (*CreateDataBaseRequest) ProtoMessage()    {}
func (*CreateDataBaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{4}
}
func (m *CreateDataBaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDataBaseRequest.Unmarshal(m, b)
}
func (m *CreateDataBaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDataBaseRequest.Marshal(b, m, deterministic)
}
func (m *CreateDataBaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDataBaseRequest.Merge(m, src)
}
func (m *CreateDataBaseRequest) XXX_Size() int {
	return xxx_messageInfo_CreateDataBaseRequest.Size(m)
}
func (m *CreateDataBaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDataBaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDataBaseRequest proto.InternalMessageInfo

func (m *CreateDataBaseRequest) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *CreateDataBaseRequest) GetPt() uint32 {
	if m != nil && m.Pt != nil {
		return *m.Pt
	}
	return 0
}

func (m *CreateDataBaseRequest) GetRp() string {
	if m != nil && m.Rp != nil {
		return *m.Rp
	}
	return ""
}

type CreateDataBaseResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDataBaseResponse) Reset()         { *m = CreateDataBaseResponse{} }
func (m *CreateDataBaseResponse) String() string { return proto.CompactTextString(m) }
func (*CreateDataBaseResponse) ProtoMessage()    {}
func (*CreateDataBaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{5}
}
func (m *CreateDataBaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDataBaseResponse.Unmarshal(m, b)
}
func (m *CreateDataBaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDataBaseResponse.Marshal(b, m, deterministic)
}
func (m *CreateDataBaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDataBaseResponse.Merge(m, src)
}
func (m *CreateDataBaseResponse) XXX_Size() int {
	return xxx_messageInfo_CreateDataBaseResponse.Size(m)
}
func (m *CreateDataBaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDataBaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDataBaseResponse proto.InternalMessageInfo

func (m *CreateDataBaseResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type SysCtrlRequest struct {
	Mod                  *string           `protobuf:"bytes,1,req,name=Mod" json:"Mod,omitempty"`
	Param                map[string]string `protobuf:"bytes,2,rep,name=Param" json:"Param,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SysCtrlRequest) Reset()         { *m = SysCtrlRequest{} }
func (m *SysCtrlRequest) String() string { return proto.CompactTextString(m) }
func (*SysCtrlRequest) ProtoMessage()    {}
func (*SysCtrlRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{6}
}
func (m *SysCtrlRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SysCtrlRequest.Unmarshal(m, b)
}
func (m *SysCtrlRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SysCtrlRequest.Marshal(b, m, deterministic)
}
func (m *SysCtrlRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SysCtrlRequest.Merge(m, src)
}
func (m *SysCtrlRequest) XXX_Size() int {
	return xxx_messageInfo_SysCtrlRequest.Size(m)
}
func (m *SysCtrlRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SysCtrlRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SysCtrlRequest proto.InternalMessageInfo

func (m *SysCtrlRequest) GetMod() string {
	if m != nil && m.Mod != nil {
		return *m.Mod
	}
	return ""
}

func (m *SysCtrlRequest) GetParam() map[string]string {
	if m != nil {
		return m.Param
	}
	return nil
}

type SysCtrlResponse struct {
	Err                  *string           `protobuf:"bytes,1,req,name=Err" json:"Err,omitempty"`
	Result               map[string]string `protobuf:"bytes,2,rep,name=Result" json:"Result,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SysCtrlResponse) Reset()         { *m = SysCtrlResponse{} }
func (m *SysCtrlResponse) String() string { return proto.CompactTextString(m) }
func (*SysCtrlResponse) ProtoMessage()    {}
func (*SysCtrlResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{7}
}
func (m *SysCtrlResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SysCtrlResponse.Unmarshal(m, b)
}
func (m *SysCtrlResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SysCtrlResponse.Marshal(b, m, deterministic)
}
func (m *SysCtrlResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SysCtrlResponse.Merge(m, src)
}
func (m *SysCtrlResponse) XXX_Size() int {
	return xxx_messageInfo_SysCtrlResponse.Size(m)
}
func (m *SysCtrlResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SysCtrlResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SysCtrlResponse proto.InternalMessageInfo

func (m *SysCtrlResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func (m *SysCtrlResponse) GetResult() map[string]string {
	if m != nil {
		return m.Result
	}
	return nil
}

type GetShardSplitPointsRequest struct {
	DB                   *string  `protobuf:"bytes,1,req,name=DB" json:"DB,omitempty"`
	PtID                 *uint32  `protobuf:"varint,2,req,name=PtID" json:"PtID,omitempty"`
	ShardID              *uint64  `protobuf:"varint,3,req,name=ShardID" json:"ShardID,omitempty"`
	Idxes                []int64  `protobuf:"varint,4,rep,name=Idxes" json:"Idxes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShardSplitPointsRequest) Reset()         { *m = GetShardSplitPointsRequest{} }
func (m *GetShardSplitPointsRequest) String() string { return proto.CompactTextString(m) }
func (*GetShardSplitPointsRequest) ProtoMessage()    {}
func (*GetShardSplitPointsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{8}
}
func (m *GetShardSplitPointsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShardSplitPointsRequest.Unmarshal(m, b)
}
func (m *GetShardSplitPointsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShardSplitPointsRequest.Marshal(b, m, deterministic)
}
func (m *GetShardSplitPointsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShardSplitPointsRequest.Merge(m, src)
}
func (m *GetShardSplitPointsRequest) XXX_Size() int {
	return xxx_messageInfo_GetShardSplitPointsRequest.Size(m)
}
func (m *GetShardSplitPointsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShardSplitPointsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetShardSplitPointsRequest proto.InternalMessageInfo

func (m *GetShardSplitPointsRequest) GetDB() string {
	if m != nil && m.DB != nil {
		return *m.DB
	}
	return ""
}

func (m *GetShardSplitPointsRequest) GetPtID() uint32 {
	if m != nil && m.PtID != nil {
		return *m.PtID
	}
	return 0
}

func (m *GetShardSplitPointsRequest) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *GetShardSplitPointsRequest) GetIdxes() []int64 {
	if m != nil {
		return m.Idxes
	}
	return nil
}

type GetShardSplitPointsResponse struct {
	SplitPoints          []string `protobuf:"bytes,1,rep,name=SplitPoints" json:"SplitPoints,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetShardSplitPointsResponse) Reset()         { *m = GetShardSplitPointsResponse{} }
func (m *GetShardSplitPointsResponse) String() string { return proto.CompactTextString(m) }
func (*GetShardSplitPointsResponse) ProtoMessage()    {}
func (*GetShardSplitPointsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{9}
}
func (m *GetShardSplitPointsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetShardSplitPointsResponse.Unmarshal(m, b)
}
func (m *GetShardSplitPointsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetShardSplitPointsResponse.Marshal(b, m, deterministic)
}
func (m *GetShardSplitPointsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetShardSplitPointsResponse.Merge(m, src)
}
func (m *GetShardSplitPointsResponse) XXX_Size() int {
	return xxx_messageInfo_GetShardSplitPointsResponse.Size(m)
}
func (m *GetShardSplitPointsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetShardSplitPointsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetShardSplitPointsResponse proto.InternalMessageInfo

func (m *GetShardSplitPointsResponse) GetSplitPoints() []string {
	if m != nil {
		return m.SplitPoints
	}
	return nil
}

func (m *GetShardSplitPointsResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type DeleteRequest struct {
	DB                   *string  `protobuf:"bytes,1,req,name=DB" json:"DB,omitempty"`
	Rp                   *string  `protobuf:"bytes,2,opt,name=Rp" json:"Rp,omitempty"`
	Mst                  *string  `protobuf:"bytes,3,opt,name=Mst" json:"Mst,omitempty"`
	ShardIDs             []uint64 `protobuf:"varint,4,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	DeleteType           *int32   `protobuf:"varint,5,req,name=DeleteType" json:"DeleteType,omitempty"`
	PtId                 *uint32  `protobuf:"varint,6,opt,name=PtId" json:"PtId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRequest) Reset()         { *m = DeleteRequest{} }
func (m *DeleteRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRequest) ProtoMessage()    {}
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{10}
}
func (m *DeleteRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRequest.Unmarshal(m, b)
}
func (m *DeleteRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRequest.Marshal(b, m, deterministic)
}
func (m *DeleteRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRequest.Merge(m, src)
}
func (m *DeleteRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRequest.Size(m)
}
func (m *DeleteRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRequest proto.InternalMessageInfo

func (m *DeleteRequest) GetDB() string {
	if m != nil && m.DB != nil {
		return *m.DB
	}
	return ""
}

func (m *DeleteRequest) GetRp() string {
	if m != nil && m.Rp != nil {
		return *m.Rp
	}
	return ""
}

func (m *DeleteRequest) GetMst() string {
	if m != nil && m.Mst != nil {
		return *m.Mst
	}
	return ""
}

func (m *DeleteRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *DeleteRequest) GetDeleteType() int32 {
	if m != nil && m.DeleteType != nil {
		return *m.DeleteType
	}
	return 0
}

func (m *DeleteRequest) GetPtId() uint32 {
	if m != nil && m.PtId != nil {
		return *m.PtId
	}
	return 0
}

type DeleteResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteResponse) Reset()         { *m = DeleteResponse{} }
func (m *DeleteResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteResponse) ProtoMessage()    {}
func (*DeleteResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{11}
}
func (m *DeleteResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteResponse.Unmarshal(m, b)
}
func (m *DeleteResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteResponse.Marshal(b, m, deterministic)
}
func (m *DeleteResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteResponse.Merge(m, src)
}
func (m *DeleteResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteResponse.Size(m)
}
func (m *DeleteResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteResponse proto.InternalMessageInfo

func (m *DeleteResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type ShowTagValuesRequest struct {
	Db                   *string       `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	PtIDs                []uint32      `protobuf:"varint,2,rep,name=PtIDs" json:"PtIDs,omitempty"`
	TagKeys              []*MapTagKeys `protobuf:"bytes,3,rep,name=TagKeys" json:"TagKeys,omitempty"`
	Condition            *string       `protobuf:"bytes,4,opt,name=Condition" json:"Condition,omitempty"`
	Limit                *int32        `protobuf:"varint,5,opt,name=Limit" json:"Limit,omitempty"`
	Exact                *bool         `protobuf:"varint,6,opt,name=Exact" json:"Exact,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ShowTagValuesRequest) Reset()         { *m = ShowTagValuesRequest{} }
func (m *ShowTagValuesRequest) String() string { return proto.CompactTextString(m) }
func (*ShowTagValuesRequest) ProtoMessage()    {}
func (*ShowTagValuesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{12}
}
func (m *ShowTagValuesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowTagValuesRequest.Unmarshal(m, b)
}
func (m *ShowTagValuesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowTagValuesRequest.Marshal(b, m, deterministic)
}
func (m *ShowTagValuesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowTagValuesRequest.Merge(m, src)
}
func (m *ShowTagValuesRequest) XXX_Size() int {
	return xxx_messageInfo_ShowTagValuesRequest.Size(m)
}
func (m *ShowTagValuesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowTagValuesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ShowTagValuesRequest proto.InternalMessageInfo

func (m *ShowTagValuesRequest) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *ShowTagValuesRequest) GetPtIDs() []uint32 {
	if m != nil {
		return m.PtIDs
	}
	return nil
}

func (m *ShowTagValuesRequest) GetTagKeys() []*MapTagKeys {
	if m != nil {
		return m.TagKeys
	}
	return nil
}

func (m *ShowTagValuesRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

func (m *ShowTagValuesRequest) GetLimit() int32 {
	if m != nil && m.Limit != nil {
		return *m.Limit
	}
	return 0
}

func (m *ShowTagValuesRequest) GetExact() bool {
	if m != nil && m.Exact != nil {
		return *m.Exact
	}
	return false
}

type ShowTagValuesResponse struct {
	Err                  *string           `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	Values               []*TagValuesSlice `protobuf:"bytes,2,rep,name=Values" json:"Values,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ShowTagValuesResponse) Reset()         { *m = ShowTagValuesResponse{} }
func (m *ShowTagValuesResponse) String() string { return proto.CompactTextString(m) }
func (*ShowTagValuesResponse) ProtoMessage()    {}
func (*ShowTagValuesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{13}
}
func (m *ShowTagValuesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowTagValuesResponse.Unmarshal(m, b)
}
func (m *ShowTagValuesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowTagValuesResponse.Marshal(b, m, deterministic)
}
func (m *ShowTagValuesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowTagValuesResponse.Merge(m, src)
}
func (m *ShowTagValuesResponse) XXX_Size() int {
	return xxx_messageInfo_ShowTagValuesResponse.Size(m)
}
func (m *ShowTagValuesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowTagValuesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShowTagValuesResponse proto.InternalMessageInfo

func (m *ShowTagValuesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func (m *ShowTagValuesResponse) GetValues() []*TagValuesSlice {
	if m != nil {
		return m.Values
	}
	return nil
}

type MapTagKeys struct {
	Measurement          *string  `protobuf:"bytes,1,req,name=Measurement" json:"Measurement,omitempty"`
	Keys                 []string `protobuf:"bytes,2,rep,name=Keys" json:"Keys,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MapTagKeys) Reset()         { *m = MapTagKeys{} }
func (m *MapTagKeys) String() string { return proto.CompactTextString(m) }
func (*MapTagKeys) ProtoMessage()    {}
func (*MapTagKeys) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{14}
}
func (m *MapTagKeys) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MapTagKeys.Unmarshal(m, b)
}
func (m *MapTagKeys) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MapTagKeys.Marshal(b, m, deterministic)
}
func (m *MapTagKeys) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MapTagKeys.Merge(m, src)
}
func (m *MapTagKeys) XXX_Size() int {
	return xxx_messageInfo_MapTagKeys.Size(m)
}
func (m *MapTagKeys) XXX_DiscardUnknown() {
	xxx_messageInfo_MapTagKeys.DiscardUnknown(m)
}

var xxx_messageInfo_MapTagKeys proto.InternalMessageInfo

func (m *MapTagKeys) GetMeasurement() string {
	if m != nil && m.Measurement != nil {
		return *m.Measurement
	}
	return ""
}

func (m *MapTagKeys) GetKeys() []string {
	if m != nil {
		return m.Keys
	}
	return nil
}

type TagValuesSlice struct {
	Measurement          *string  `protobuf:"bytes,1,req,name=Measurement" json:"Measurement,omitempty"`
	Keys                 []string `protobuf:"bytes,2,rep,name=Keys" json:"Keys,omitempty"`
	Values               []string `protobuf:"bytes,3,rep,name=Values" json:"Values,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagValuesSlice) Reset()         { *m = TagValuesSlice{} }
func (m *TagValuesSlice) String() string { return proto.CompactTextString(m) }
func (*TagValuesSlice) ProtoMessage()    {}
func (*TagValuesSlice) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{15}
}
func (m *TagValuesSlice) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagValuesSlice.Unmarshal(m, b)
}
func (m *TagValuesSlice) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagValuesSlice.Marshal(b, m, deterministic)
}
func (m *TagValuesSlice) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagValuesSlice.Merge(m, src)
}
func (m *TagValuesSlice) XXX_Size() int {
	return xxx_messageInfo_TagValuesSlice.Size(m)
}
func (m *TagValuesSlice) XXX_DiscardUnknown() {
	xxx_messageInfo_TagValuesSlice.DiscardUnknown(m)
}

var xxx_messageInfo_TagValuesSlice proto.InternalMessageInfo

func (m *TagValuesSlice) GetMeasurement() string {
	if m != nil && m.Measurement != nil {
		return *m.Measurement
	}
	return ""
}

func (m *TagValuesSlice) GetKeys() []string {
	if m != nil {
		return m.Keys
	}
	return nil
}

func (m *TagValuesSlice) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

type ExactCardinalityResponse struct {
	Cardinality          map[string]uint64 `protobuf:"bytes,1,rep,name=Cardinality" json:"Cardinality,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	Err                  *string           `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ExactCardinalityResponse) Reset()         { *m = ExactCardinalityResponse{} }
func (m *ExactCardinalityResponse) String() string { return proto.CompactTextString(m) }
func (*ExactCardinalityResponse) ProtoMessage()    {}
func (*ExactCardinalityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{16}
}
func (m *ExactCardinalityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExactCardinalityResponse.Unmarshal(m, b)
}
func (m *ExactCardinalityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExactCardinalityResponse.Marshal(b, m, deterministic)
}
func (m *ExactCardinalityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExactCardinalityResponse.Merge(m, src)
}
func (m *ExactCardinalityResponse) XXX_Size() int {
	return xxx_messageInfo_ExactCardinalityResponse.Size(m)
}
func (m *ExactCardinalityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExactCardinalityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExactCardinalityResponse proto.InternalMessageInfo

func (m *ExactCardinalityResponse) GetCardinality() map[string]uint64 {
	if m != nil {
		return m.Cardinality
	}
	return nil
}

func (m *ExactCardinalityResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type PtRequest struct {
	Pt                   *proto1.DbPt `protobuf:"bytes,1,req,name=Pt" json:"Pt,omitempty"`
	MigrateType          *int32       `protobuf:"varint,2,req,name=MigrateType" json:"MigrateType,omitempty"`
	OpId                 *uint64      `protobuf:"varint,3,req,name=OpId" json:"OpId,omitempty"`
	AliveConnId          *uint64      `protobuf:"varint,4,opt,name=AliveConnId" json:"AliveConnId,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PtRequest) Reset()         { *m = PtRequest{} }
func (m *PtRequest) String() string { return proto.CompactTextString(m) }
func (*PtRequest) ProtoMessage()    {}
func (*PtRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{17}
}
func (m *PtRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PtRequest.Unmarshal(m, b)
}
func (m *PtRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PtRequest.Marshal(b, m, deterministic)
}
func (m *PtRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PtRequest.Merge(m, src)
}
func (m *PtRequest) XXX_Size() int {
	return xxx_messageInfo_PtRequest.Size(m)
}
func (m *PtRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_PtRequest.DiscardUnknown(m)
}

var xxx_messageInfo_PtRequest proto.InternalMessageInfo

func (m *PtRequest) GetPt() *proto1.DbPt {
	if m != nil {
		return m.Pt
	}
	return nil
}

func (m *PtRequest) GetMigrateType() int32 {
	if m != nil && m.MigrateType != nil {
		return *m.MigrateType
	}
	return 0
}

func (m *PtRequest) GetOpId() uint64 {
	if m != nil && m.OpId != nil {
		return *m.OpId
	}
	return 0
}

func (m *PtRequest) GetAliveConnId() uint64 {
	if m != nil && m.AliveConnId != nil {
		return *m.AliveConnId
	}
	return 0
}

type PtResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PtResponse) Reset()         { *m = PtResponse{} }
func (m *PtResponse) String() string { return proto.CompactTextString(m) }
func (*PtResponse) ProtoMessage()    {}
func (*PtResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{18}
}
func (m *PtResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PtResponse.Unmarshal(m, b)
}
func (m *PtResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PtResponse.Marshal(b, m, deterministic)
}
func (m *PtResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PtResponse.Merge(m, src)
}
func (m *PtResponse) XXX_Size() int {
	return xxx_messageInfo_PtResponse.Size(m)
}
func (m *PtResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_PtResponse.DiscardUnknown(m)
}

var xxx_messageInfo_PtResponse proto.InternalMessageInfo

func (m *PtResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type QueryExeInfo struct {
	QueryID              *uint64  `protobuf:"varint,1,req,name=QueryID" json:"QueryID,omitempty"`
	Stmt                 *string  `protobuf:"bytes,2,req,name=Stmt" json:"Stmt,omitempty"`
	Database             *string  `protobuf:"bytes,3,req,name=Database" json:"Database,omitempty"`
	BeginTime            *int64   `protobuf:"varint,4,req,name=BeginTime" json:"BeginTime,omitempty"`
	RunState             *int32   `protobuf:"varint,5,req,name=RunState" json:"RunState,omitempty"`
	PtID                 *uint32  `protobuf:"varint,6,req,name=PtID" json:"PtID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryExeInfo) Reset()         { *m = QueryExeInfo{} }
func (m *QueryExeInfo) String() string { return proto.CompactTextString(m) }
func (*QueryExeInfo) ProtoMessage()    {}
func (*QueryExeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{19}
}
func (m *QueryExeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryExeInfo.Unmarshal(m, b)
}
func (m *QueryExeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryExeInfo.Marshal(b, m, deterministic)
}
func (m *QueryExeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryExeInfo.Merge(m, src)
}
func (m *QueryExeInfo) XXX_Size() int {
	return xxx_messageInfo_QueryExeInfo.Size(m)
}
func (m *QueryExeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryExeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QueryExeInfo proto.InternalMessageInfo

func (m *QueryExeInfo) GetQueryID() uint64 {
	if m != nil && m.QueryID != nil {
		return *m.QueryID
	}
	return 0
}

func (m *QueryExeInfo) GetStmt() string {
	if m != nil && m.Stmt != nil {
		return *m.Stmt
	}
	return ""
}

func (m *QueryExeInfo) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *QueryExeInfo) GetBeginTime() int64 {
	if m != nil && m.BeginTime != nil {
		return *m.BeginTime
	}
	return 0
}

func (m *QueryExeInfo) GetRunState() int32 {
	if m != nil && m.RunState != nil {
		return *m.RunState
	}
	return 0
}

func (m *QueryExeInfo) GetPtID() uint32 {
	if m != nil && m.PtID != nil {
		return *m.PtID
	}
	return 0
}

type ShowQueriesResponse struct {
	QueryExeInfos        []*QueryExeInfo `protobuf:"bytes,1,rep,name=QueryExeInfos" json:"QueryExeInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ShowQueriesResponse) Reset()         { *m = ShowQueriesResponse{} }
func (m *ShowQueriesResponse) String() string { return proto.CompactTextString(m) }
func (*ShowQueriesResponse) ProtoMessage()    {}
func (*ShowQueriesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{20}
}
func (m *ShowQueriesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowQueriesResponse.Unmarshal(m, b)
}
func (m *ShowQueriesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowQueriesResponse.Marshal(b, m, deterministic)
}
func (m *ShowQueriesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowQueriesResponse.Merge(m, src)
}
func (m *ShowQueriesResponse) XXX_Size() int {
	return xxx_messageInfo_ShowQueriesResponse.Size(m)
}
func (m *ShowQueriesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowQueriesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShowQueriesResponse proto.InternalMessageInfo

func (m *ShowQueriesResponse) GetQueryExeInfos() []*QueryExeInfo {
	if m != nil {
		return m.QueryExeInfos
	}
	return nil
}

type KillQueryRequest struct {
	QueryID              *uint64  `protobuf:"varint,1,req,name=QueryID" json:"QueryID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KillQueryRequest) Reset()         { *m = KillQueryRequest{} }
func (m *KillQueryRequest) String() string { return proto.CompactTextString(m) }
func (*KillQueryRequest) ProtoMessage()    {}
func (*KillQueryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{21}
}
func (m *KillQueryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KillQueryRequest.Unmarshal(m, b)
}
func (m *KillQueryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KillQueryRequest.Marshal(b, m, deterministic)
}
func (m *KillQueryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KillQueryRequest.Merge(m, src)
}
func (m *KillQueryRequest) XXX_Size() int {
	return xxx_messageInfo_KillQueryRequest.Size(m)
}
func (m *KillQueryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_KillQueryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_KillQueryRequest proto.InternalMessageInfo

func (m *KillQueryRequest) GetQueryID() uint64 {
	if m != nil && m.QueryID != nil {
		return *m.QueryID
	}
	return 0
}

type KillQueryResponse struct {
	ErrCode              *uint32  `protobuf:"varint,1,opt,name=ErrCode" json:"ErrCode,omitempty"`
	ErrMsg               *string  `protobuf:"bytes,2,opt,name=ErrMsg" json:"ErrMsg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *KillQueryResponse) Reset()         { *m = KillQueryResponse{} }
func (m *KillQueryResponse) String() string { return proto.CompactTextString(m) }
func (*KillQueryResponse) ProtoMessage()    {}
func (*KillQueryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{22}
}
func (m *KillQueryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KillQueryResponse.Unmarshal(m, b)
}
func (m *KillQueryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KillQueryResponse.Marshal(b, m, deterministic)
}
func (m *KillQueryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KillQueryResponse.Merge(m, src)
}
func (m *KillQueryResponse) XXX_Size() int {
	return xxx_messageInfo_KillQueryResponse.Size(m)
}
func (m *KillQueryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_KillQueryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_KillQueryResponse proto.InternalMessageInfo

func (m *KillQueryResponse) GetErrCode() uint32 {
	if m != nil && m.ErrCode != nil {
		return *m.ErrCode
	}
	return 0
}

func (m *KillQueryResponse) GetErrMsg() string {
	if m != nil && m.ErrMsg != nil {
		return *m.ErrMsg
	}
	return ""
}

type SegregateNodeRequest struct {
	NodeId               *uint64  `protobuf:"varint,1,req,name=NodeId" json:"NodeId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SegregateNodeRequest) Reset()         { *m = SegregateNodeRequest{} }
func (m *SegregateNodeRequest) String() string { return proto.CompactTextString(m) }
func (*SegregateNodeRequest) ProtoMessage()    {}
func (*SegregateNodeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{23}
}
func (m *SegregateNodeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SegregateNodeRequest.Unmarshal(m, b)
}
func (m *SegregateNodeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SegregateNodeRequest.Marshal(b, m, deterministic)
}
func (m *SegregateNodeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SegregateNodeRequest.Merge(m, src)
}
func (m *SegregateNodeRequest) XXX_Size() int {
	return xxx_messageInfo_SegregateNodeRequest.Size(m)
}
func (m *SegregateNodeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SegregateNodeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SegregateNodeRequest proto.InternalMessageInfo

func (m *SegregateNodeRequest) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

type SegregateNodeResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SegregateNodeResponse) Reset()         { *m = SegregateNodeResponse{} }
func (m *SegregateNodeResponse) String() string { return proto.CompactTextString(m) }
func (*SegregateNodeResponse) ProtoMessage()    {}
func (*SegregateNodeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{24}
}
func (m *SegregateNodeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SegregateNodeResponse.Unmarshal(m, b)
}
func (m *SegregateNodeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SegregateNodeResponse.Marshal(b, m, deterministic)
}
func (m *SegregateNodeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SegregateNodeResponse.Merge(m, src)
}
func (m *SegregateNodeResponse) XXX_Size() int {
	return xxx_messageInfo_SegregateNodeResponse.Size(m)
}
func (m *SegregateNodeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SegregateNodeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SegregateNodeResponse proto.InternalMessageInfo

func (m *SegregateNodeResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type RaftMessagesRequest struct {
	Database             *string  `protobuf:"bytes,1,opt,name=Database" json:"Database,omitempty"`
	PtId                 *uint32  `protobuf:"varint,2,opt,name=PtId" json:"PtId,omitempty"`
	RaftMessages         []byte   `protobuf:"bytes,3,opt,name=RaftMessages" json:"RaftMessages,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RaftMessagesRequest) Reset()         { *m = RaftMessagesRequest{} }
func (m *RaftMessagesRequest) String() string { return proto.CompactTextString(m) }
func (*RaftMessagesRequest) ProtoMessage()    {}
func (*RaftMessagesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{25}
}
func (m *RaftMessagesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RaftMessagesRequest.Unmarshal(m, b)
}
func (m *RaftMessagesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RaftMessagesRequest.Marshal(b, m, deterministic)
}
func (m *RaftMessagesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftMessagesRequest.Merge(m, src)
}
func (m *RaftMessagesRequest) XXX_Size() int {
	return xxx_messageInfo_RaftMessagesRequest.Size(m)
}
func (m *RaftMessagesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftMessagesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RaftMessagesRequest proto.InternalMessageInfo

func (m *RaftMessagesRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *RaftMessagesRequest) GetPtId() uint32 {
	if m != nil && m.PtId != nil {
		return *m.PtId
	}
	return 0
}

func (m *RaftMessagesRequest) GetRaftMessages() []byte {
	if m != nil {
		return m.RaftMessages
	}
	return nil
}

type RaftMessagesResponse struct {
	ErrMsg               *string  `protobuf:"bytes,1,opt,name=ErrMsg" json:"ErrMsg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RaftMessagesResponse) Reset()         { *m = RaftMessagesResponse{} }
func (m *RaftMessagesResponse) String() string { return proto.CompactTextString(m) }
func (*RaftMessagesResponse) ProtoMessage()    {}
func (*RaftMessagesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{26}
}
func (m *RaftMessagesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RaftMessagesResponse.Unmarshal(m, b)
}
func (m *RaftMessagesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RaftMessagesResponse.Marshal(b, m, deterministic)
}
func (m *RaftMessagesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RaftMessagesResponse.Merge(m, src)
}
func (m *RaftMessagesResponse) XXX_Size() int {
	return xxx_messageInfo_RaftMessagesResponse.Size(m)
}
func (m *RaftMessagesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RaftMessagesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RaftMessagesResponse proto.InternalMessageInfo

func (m *RaftMessagesResponse) GetErrMsg() string {
	if m != nil && m.ErrMsg != nil {
		return *m.ErrMsg
	}
	return ""
}

type TransferLeadershipRequest struct {
	NodeId               *uint64  `protobuf:"varint,1,req,name=NodeId" json:"NodeId,omitempty"`
	PtId                 *uint32  `protobuf:"varint,2,req,name=PtId" json:"PtId,omitempty"`
	Database             *string  `protobuf:"bytes,3,req,name=Database" json:"Database,omitempty"`
	NewMasterPtId        *uint32  `protobuf:"varint,4,req,name=newMasterPtId" json:"newMasterPtId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransferLeadershipRequest) Reset()         { *m = TransferLeadershipRequest{} }
func (m *TransferLeadershipRequest) String() string { return proto.CompactTextString(m) }
func (*TransferLeadershipRequest) ProtoMessage()    {}
func (*TransferLeadershipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{27}
}
func (m *TransferLeadershipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransferLeadershipRequest.Unmarshal(m, b)
}
func (m *TransferLeadershipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransferLeadershipRequest.Marshal(b, m, deterministic)
}
func (m *TransferLeadershipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeadershipRequest.Merge(m, src)
}
func (m *TransferLeadershipRequest) XXX_Size() int {
	return xxx_messageInfo_TransferLeadershipRequest.Size(m)
}
func (m *TransferLeadershipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeadershipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeadershipRequest proto.InternalMessageInfo

func (m *TransferLeadershipRequest) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

func (m *TransferLeadershipRequest) GetPtId() uint32 {
	if m != nil && m.PtId != nil {
		return *m.PtId
	}
	return 0
}

func (m *TransferLeadershipRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *TransferLeadershipRequest) GetNewMasterPtId() uint32 {
	if m != nil && m.NewMasterPtId != nil {
		return *m.NewMasterPtId
	}
	return 0
}

type TransferLeadershipResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TransferLeadershipResponse) Reset()         { *m = TransferLeadershipResponse{} }
func (m *TransferLeadershipResponse) String() string { return proto.CompactTextString(m) }
func (*TransferLeadershipResponse) ProtoMessage()    {}
func (*TransferLeadershipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_2aaddb15866ce618, []int{28}
}
func (m *TransferLeadershipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TransferLeadershipResponse.Unmarshal(m, b)
}
func (m *TransferLeadershipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TransferLeadershipResponse.Marshal(b, m, deterministic)
}
func (m *TransferLeadershipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransferLeadershipResponse.Merge(m, src)
}
func (m *TransferLeadershipResponse) XXX_Size() int {
	return xxx_messageInfo_TransferLeadershipResponse.Size(m)
}
func (m *TransferLeadershipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TransferLeadershipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TransferLeadershipResponse proto.InternalMessageInfo

func (m *TransferLeadershipResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func init() {
	proto.RegisterType((*SeriesKeysRequest)(nil), "netstorage.data.SeriesKeysRequest")
	proto.RegisterType((*SeriesKeysResponse)(nil), "netstorage.data.SeriesKeysResponse")
	proto.RegisterType((*ShowTagKeysRequest)(nil), "netstorage.data.ShowTagKeysRequest")
	proto.RegisterType((*ShowTagKeysResponse)(nil), "netstorage.data.ShowTagKeysResponse")
	proto.RegisterType((*CreateDataBaseRequest)(nil), "netstorage.data.CreateDataBaseRequest")
	proto.RegisterType((*CreateDataBaseResponse)(nil), "netstorage.data.CreateDataBaseResponse")
	proto.RegisterType((*SysCtrlRequest)(nil), "netstorage.data.SysCtrlRequest")
	proto.RegisterMapType((map[string]string)(nil), "netstorage.data.SysCtrlRequest.ParamEntry")
	proto.RegisterType((*SysCtrlResponse)(nil), "netstorage.data.SysCtrlResponse")
	proto.RegisterMapType((map[string]string)(nil), "netstorage.data.SysCtrlResponse.ResultEntry")
	proto.RegisterType((*GetShardSplitPointsRequest)(nil), "netstorage.data.GetShardSplitPointsRequest")
	proto.RegisterType((*GetShardSplitPointsResponse)(nil), "netstorage.data.GetShardSplitPointsResponse")
	proto.RegisterType((*DeleteRequest)(nil), "netstorage.data.DeleteRequest")
	proto.RegisterType((*DeleteResponse)(nil), "netstorage.data.DeleteResponse")
	proto.RegisterType((*ShowTagValuesRequest)(nil), "netstorage.data.ShowTagValuesRequest")
	proto.RegisterType((*ShowTagValuesResponse)(nil), "netstorage.data.ShowTagValuesResponse")
	proto.RegisterType((*MapTagKeys)(nil), "netstorage.data.MapTagKeys")
	proto.RegisterType((*TagValuesSlice)(nil), "netstorage.data.TagValuesSlice")
	proto.RegisterType((*ExactCardinalityResponse)(nil), "netstorage.data.ExactCardinalityResponse")
	proto.RegisterMapType((map[string]uint64)(nil), "netstorage.data.ExactCardinalityResponse.CardinalityEntry")
	proto.RegisterType((*PtRequest)(nil), "netstorage.data.PtRequest")
	proto.RegisterType((*PtResponse)(nil), "netstorage.data.PtResponse")
	proto.RegisterType((*QueryExeInfo)(nil), "netstorage.data.QueryExeInfo")
	proto.RegisterType((*ShowQueriesResponse)(nil), "netstorage.data.ShowQueriesResponse")
	proto.RegisterType((*KillQueryRequest)(nil), "netstorage.data.KillQueryRequest")
	proto.RegisterType((*KillQueryResponse)(nil), "netstorage.data.KillQueryResponse")
	proto.RegisterType((*SegregateNodeRequest)(nil), "netstorage.data.SegregateNodeRequest")
	proto.RegisterType((*SegregateNodeResponse)(nil), "netstorage.data.SegregateNodeResponse")
	proto.RegisterType((*RaftMessagesRequest)(nil), "netstorage.data.RaftMessagesRequest")
	proto.RegisterType((*RaftMessagesResponse)(nil), "netstorage.data.RaftMessagesResponse")
	proto.RegisterType((*TransferLeadershipRequest)(nil), "netstorage.data.TransferLeadershipRequest")
	proto.RegisterType((*TransferLeadershipResponse)(nil), "netstorage.data.TransferLeadershipResponse")
}

func init() { proto.RegisterFile("lib/netstorage/data/data.proto", fileDescriptor_2aaddb15866ce618) }

var fileDescriptor_2aaddb15866ce618 = []byte{
	// 1117 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xcd, 0x8a, 0x1b, 0xc7,
	0x13, 0x67, 0x46, 0x1f, 0x7f, 0xab, 0x64, 0xc9, 0xbb, 0xb3, 0x1f, 0xcc, 0x5f, 0xeb, 0x6c, 0xc4,
	0x90, 0x83, 0x62, 0x16, 0x09, 0x16, 0x42, 0x1c, 0x07, 0x4c, 0xac, 0x0f, 0x8c, 0xb0, 0x95, 0xc8,
	0xad, 0x25, 0x07, 0x13, 0x02, 0xad, 0x9d, 0x5e, 0x6d, 0xe3, 0xd1, 0xcc, 0xa4, 0xbb, 0x65, 0xaf,
	0x20, 0x87, 0x5c, 0x73, 0xf4, 0x0b, 0xe4, 0x14, 0xf2, 0x16, 0x79, 0x81, 0x3c, 0x55, 0xe8, 0x8f,
	0x99, 0x69, 0x69, 0x57, 0x31, 0xce, 0x25, 0x17, 0xd1, 0x55, 0xd3, 0x55, 0xf5, 0xab, 0xaa, 0x5f,
	0x55, 0x0b, 0x4e, 0x23, 0x3a, 0xef, 0xc5, 0x44, 0x70, 0x91, 0x30, 0xbc, 0x20, 0xbd, 0x10, 0x0b,
	0xac, 0x7e, 0xba, 0x29, 0x4b, 0x44, 0xe2, 0x3d, 0x28, 0xbe, 0x75, 0xa5, 0xba, 0x75, 0x26, 0x0d,
	0x56, 0x82, 0x46, 0xbd, 0x88, 0x5e, 0x09, 0x12, 0xf6, 0x68, 0x7c, 0x15, 0xad, 0x6e, 0x7a, 0x4b,
	0x22, 0x70, 0x4f, 0xd9, 0xa8, 0xa3, 0x36, 0x0f, 0xde, 0x3b, 0xb0, 0x3f, 0x23, 0x8c, 0x12, 0xfe,
	0x82, 0xac, 0x39, 0x22, 0x3f, 0xad, 0x08, 0x17, 0x5e, 0x13, 0xdc, 0xe1, 0xdc, 0x77, 0xda, 0x6e,
	0xa7, 0x86, 0xdc, 0xe1, 0xdc, 0x3b, 0x84, 0xca, 0x54, 0x8c, 0x87, 0xdc, 0x77, 0xdb, 0xa5, 0x4e,
	0x03, 0x69, 0xc1, 0x0b, 0xe0, 0xfe, 0x84, 0x60, 0xbe, 0x62, 0x64, 0x49, 0x62, 0xc1, 0xfd, 0x52,
	0xbb, 0xd4, 0xa9, 0xa1, 0x0d, 0x9d, 0xf7, 0x10, 0x6a, 0x97, 0x49, 0x1c, 0x52, 0x41, 0x93, 0xd8,
	0x2f, 0xb7, 0x9d, 0x4e, 0x0d, 0x15, 0x0a, 0xe9, 0x77, 0x74, 0x83, 0x2f, 0x85, 0x5f, 0x69, 0x3b,
	0x9d, 0x7b, 0x48, 0x0b, 0xc1, 0x53, 0xf0, 0x6c, 0x48, 0x3c, 0x4d, 0x62, 0x4e, 0xbc, 0x63, 0xa8,
	0x6a, 0xad, 0xef, 0xa8, 0x38, 0x46, 0xf2, 0xf6, 0xa0, 0x34, 0x62, 0xcc, 0x77, 0x95, 0x6f, 0x79,
	0x0c, 0x7e, 0x06, 0x6f, 0x76, 0x9d, 0xbc, 0xbb, 0xc0, 0x8b, 0xff, 0x20, 0xa7, 0xe0, 0x19, 0x1c,
	0x6c, 0x44, 0x37, 0xf0, 0x7d, 0xf8, 0x9f, 0x51, 0x19, 0xfc, 0x99, 0x78, 0x47, 0x02, 0xcf, 0xe1,
	0x68, 0xc0, 0x08, 0x16, 0x64, 0x88, 0x05, 0xee, 0x63, 0x4e, 0x76, 0xe5, 0xd0, 0x04, 0x37, 0x15,
	0xbe, 0xdb, 0x76, 0x3b, 0x0d, 0xe4, 0xa6, 0xea, 0x3b, 0x4b, 0xfd, 0x92, 0xfe, 0xce, 0xd2, 0xe0,
	0x11, 0x1c, 0x6f, 0x3b, 0x32, 0x70, 0x4c, 0x50, 0xa7, 0x08, 0xfa, 0x9b, 0x03, 0xcd, 0xd9, 0x9a,
	0x0f, 0x04, 0x8b, 0xb2, 0x70, 0x7b, 0x50, 0x9a, 0x24, 0xa1, 0x89, 0x27, 0x8f, 0xde, 0x37, 0x50,
	0x99, 0x62, 0x86, 0x97, 0xaa, 0x68, 0xf5, 0xf3, 0x47, 0xdd, 0x2d, 0xf6, 0x75, 0x37, 0x3d, 0x74,
	0xd5, 0xe5, 0x51, 0x2c, 0xd8, 0x1a, 0x69, 0xc3, 0xd6, 0x63, 0x80, 0x42, 0x29, 0x23, 0xbc, 0x21,
	0xeb, 0x0c, 0xc6, 0x1b, 0xb2, 0x96, 0x6d, 0x79, 0x8b, 0xa3, 0x15, 0x31, 0xf5, 0xd0, 0xc2, 0x13,
	0xf7, 0xb1, 0x13, 0xfc, 0xee, 0xc0, 0x83, 0xdc, 0xfd, 0x76, 0x1a, 0xae, 0x49, 0xc3, 0x1b, 0x42,
	0x15, 0x11, 0xbe, 0x8a, 0x84, 0x81, 0x78, 0xb6, 0x1b, 0xa2, 0xf6, 0xd1, 0xd5, 0xd7, 0x35, 0x48,
	0x63, 0xdb, 0xfa, 0x0a, 0xea, 0x96, 0xfa, 0xa3, 0x60, 0xa6, 0xd0, 0x7a, 0x4e, 0xc4, 0xec, 0x1a,
	0xb3, 0x70, 0x96, 0x46, 0x54, 0x4c, 0x13, 0x1a, 0x8b, 0x0d, 0x16, 0xf6, 0xf3, 0x0e, 0xf6, 0x3d,
	0x0f, 0xca, 0x92, 0x78, 0xa6, 0x87, 0xea, 0x2c, 0xa9, 0xa2, 0xcc, 0xc7, 0x43, 0xd5, 0xca, 0x32,
	0xca, 0x44, 0x19, 0x75, 0x1c, 0xde, 0x10, 0xee, 0x97, 0xdb, 0xa5, 0x4e, 0x09, 0x69, 0x21, 0x78,
	0x05, 0x27, 0x77, 0x46, 0x34, 0x35, 0x6a, 0x43, 0xdd, 0x52, 0x1b, 0xf6, 0xd9, 0xaa, 0x3b, 0x18,
	0xf8, 0xde, 0x81, 0xc6, 0x90, 0x44, 0x44, 0x90, 0x5d, 0xc0, 0x9b, 0xe0, 0xa2, 0xd4, 0x98, 0xb8,
	0x28, 0x55, 0x5c, 0xe1, 0xc2, 0x2f, 0x69, 0x1f, 0x13, 0x2e, 0xbc, 0x16, 0xdc, 0x33, 0xb8, 0x35,
	0xde, 0x32, 0xca, 0x65, 0xef, 0x14, 0x40, 0xbb, 0xbf, 0x58, 0xa7, 0xc4, 0xaf, 0xb4, 0xdd, 0x4e,
	0x05, 0x59, 0x1a, 0x53, 0x96, 0xd0, 0xaf, 0xb6, 0x1d, 0x53, 0x96, 0x30, 0x08, 0xa0, 0x99, 0x41,
	0xda, 0x49, 0xe2, 0x3f, 0x1d, 0x38, 0x34, 0xd3, 0xf7, 0xbd, 0xec, 0xc8, 0x47, 0x4e, 0xff, 0x17,
	0xc5, 0x90, 0x96, 0x14, 0x7b, 0x4e, 0x6e, 0xb1, 0x67, 0x82, 0xd3, 0x6c, 0xb4, 0xf3, 0x09, 0x7e,
	0x08, 0xb5, 0xc1, 0xf6, 0x42, 0x18, 0xd8, 0x4b, 0xee, 0x25, 0x5d, 0x52, 0xbd, 0xe4, 0x2a, 0x48,
	0x0b, 0xc5, 0xea, 0xab, 0xda, 0xab, 0x6f, 0x0e, 0x47, 0x5b, 0xf0, 0x77, 0xa5, 0xea, 0x7d, 0x09,
	0x55, 0x7d, 0xc7, 0x10, 0xfd, 0xd3, 0x5b, 0x50, 0x73, 0x2f, 0xb3, 0x88, 0x5e, 0x12, 0x64, 0xae,
	0x07, 0x7d, 0x80, 0x22, 0x09, 0xc9, 0x0e, 0x6b, 0xb9, 0x99, 0x0a, 0xd9, 0x2a, 0xd9, 0x0b, 0x55,
	0x11, 0x57, 0x11, 0x47, 0x9d, 0x83, 0x1f, 0xa1, 0xb9, 0xe9, 0xfd, 0xdf, 0xf9, 0x91, 0x4b, 0xdd,
	0x24, 0xa1, 0x17, 0x6d, 0x86, 0xf1, 0x2f, 0x07, 0x7c, 0x55, 0x91, 0x01, 0x66, 0x21, 0x8d, 0x71,
	0x44, 0xc5, 0x3a, 0xaf, 0xc5, 0x0f, 0x50, 0xb7, 0xd4, 0x8a, 0xd0, 0xf5, 0xf3, 0x27, 0xb7, 0xd2,
	0xdf, 0x65, 0xdf, 0xb5, 0x74, 0x7a, 0xea, 0x6d, 0x77, 0xb7, 0x87, 0xa1, 0xf5, 0x14, 0xf6, 0xb6,
	0x4d, 0x3e, 0xb4, 0x11, 0xca, 0xf6, 0x46, 0xf8, 0xc5, 0x81, 0xda, 0x54, 0x64, 0x4c, 0x3c, 0x01,
	0x77, 0xaa, 0xeb, 0x53, 0x3f, 0xaf, 0xeb, 0x57, 0xb8, 0x3b, 0x9c, 0x4f, 0x05, 0x72, 0xa7, 0x42,
	0x55, 0x91, 0x2e, 0x18, 0x36, 0x83, 0xe1, 0xaa, 0xc1, 0xb0, 0x55, 0xb2, 0x8a, 0xdf, 0xa5, 0xe3,
	0xd0, 0x6c, 0x06, 0x75, 0x96, 0x56, 0xcf, 0x22, 0xfa, 0x96, 0x0c, 0x92, 0x38, 0x1e, 0x87, 0x8a,
	0x81, 0x65, 0x64, 0xab, 0x82, 0x53, 0x00, 0x89, 0x60, 0xe7, 0xdc, 0xfc, 0xe1, 0xc0, 0xfd, 0x57,
	0x2b, 0xc2, 0xd6, 0xa3, 0x1b, 0x32, 0x8e, 0xaf, 0x12, 0xb9, 0x83, 0x94, 0x3c, 0x1e, 0x2a, 0xa8,
	0x65, 0x94, 0x89, 0x12, 0xc0, 0x4c, 0x2c, 0xf5, 0xab, 0x53, 0x43, 0xea, 0x2c, 0x47, 0x5d, 0xbe,
	0x30, 0x73, 0xcc, 0x89, 0x79, 0x7d, 0x72, 0x59, 0x0e, 0x47, 0x9f, 0x2c, 0x68, 0x7c, 0x41, 0x97,
	0xc4, 0x2f, 0xb7, 0xdd, 0x4e, 0x09, 0x15, 0x0a, 0x69, 0x89, 0x56, 0xf1, 0x4c, 0x60, 0x91, 0xad,
	0x81, 0x5c, 0xce, 0x77, 0x63, 0xb5, 0xd8, 0x8d, 0xc1, 0x6b, 0xfd, 0xba, 0x4a, 0x30, 0xd4, 0x1a,
	0x8f, 0x01, 0x34, 0x6c, 0xf8, 0xdc, 0x90, 0xe2, 0x93, 0x5b, 0xa4, 0xb0, 0x6f, 0xa1, 0x4d, 0x9b,
	0xe0, 0x0c, 0xf6, 0x5e, 0xd0, 0x28, 0x52, 0xca, 0xac, 0x5b, 0x3b, 0xeb, 0x10, 0x8c, 0x60, 0xdf,
	0xba, 0x5d, 0xbc, 0xf2, 0x23, 0xc6, 0x06, 0x49, 0x48, 0x54, 0x75, 0x1b, 0x28, 0x13, 0x25, 0xd3,
	0x47, 0x8c, 0x4d, 0xf8, 0xc2, 0x30, 0xcb, 0x48, 0x41, 0x17, 0x0e, 0x67, 0x64, 0xc1, 0xc8, 0x02,
	0x0b, 0xf2, 0x6d, 0x12, 0xe6, 0xfb, 0xf6, 0x18, 0xaa, 0x52, 0x1c, 0x87, 0x26, 0xae, 0x91, 0x82,
	0xcf, 0xe1, 0x68, 0xeb, 0xfe, 0xce, 0xa6, 0x52, 0x38, 0x40, 0xf8, 0x4a, 0x4c, 0x08, 0xe7, 0x78,
	0x51, 0xac, 0x42, 0xbb, 0x59, 0xfa, 0x76, 0xd1, 0xac, 0x6c, 0xef, 0xba, 0xc5, 0xde, 0x95, 0x7f,
	0x89, 0x6c, 0x37, 0x6a, 0xc5, 0xdf, 0x47, 0x1b, 0x3a, 0x99, 0xc5, 0x66, 0xa8, 0xe2, 0x4f, 0x9b,
	0xc9, 0xda, 0xd9, 0xc8, 0xfa, 0x57, 0x07, 0xfe, 0x7f, 0xc1, 0x70, 0xcc, 0xaf, 0x08, 0x7b, 0x49,
	0x70, 0x48, 0x18, 0xbf, 0xa6, 0xe9, 0x07, 0x72, 0xb7, 0xd0, 0xb9, 0x39, 0xba, 0x7f, 0xa2, 0xde,
	0x67, 0xd0, 0x88, 0xc9, 0xbb, 0x09, 0xe6, 0x82, 0x30, 0x65, 0x58, 0x56, 0x86, 0x9b, 0xca, 0xa0,
	0x0b, 0xad, 0xbb, 0xa0, 0xec, 0x2a, 0x6b, 0xff, 0xe0, 0xf5, 0x7e, 0xf7, 0xeb, 0x2d, 0x5e, 0xfd,
	0x1d, 0x00, 0x00, 0xff, 0xff, 0x76, 0x49, 0x48, 0x40, 0xa7, 0x0b, 0x00, 0x00,
}
