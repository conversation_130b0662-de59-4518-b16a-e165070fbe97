syntax = "proto2";
package netstorage.data;

import "lib/util/lifted/influx/meta/proto/meta.proto";

option go_package = ".;netstorage.data";

message SeriesKeysRequest {
    required string Db           = 1;
    repeated uint32 PtIDs        = 2;
    repeated string Measurements = 3;
    optional string condition    = 4;
    optional bool Exact          = 5;
}

message SeriesKeysResponse {
    repeated string Series = 1;
    optional string Err    = 2;
}

message ShowTagKeysRequest {
    required string Db           = 1;
    repeated uint32 PtIDs        = 2;
    repeated string Measurements = 3;
    optional string condition    = 4;
}

message ShowTagKeysResponse {
    repeated string TagKeys = 1;
    optional string Err    = 2;
}

message CreateDataBaseRequest {
    required string Db = 1;
    required uint32 pt = 2;
    required string rp = 3;
}

message CreateDataBaseResponse {
    optional string Err = 1;
}

message SysCtrlRequest {
    required string Mod 	  = 1;
    map<string,string> Param = 2;
}

message SysCtrlResponse {
    required string Err 	  	= 1;
    map<string,string> Result = 2;
}

message GetShardSplitPointsRequest {
    required string DB = 1;
    required uint32 PtID = 2;
    required uint64 ShardID = 3;
    repeated int64 Idxes = 4;
}

message GetShardSplitPointsResponse {
    repeated string SplitPoints = 1;
    optional string Err = 2;
}

message DeleteRequest {
    required string DB  = 1;
    optional string Rp  = 2;
    optional string Mst = 3;
    repeated uint64 ShardIDs = 4;
    required int32  DeleteType = 5;
    optional uint32 PtId = 6;
}

message DeleteResponse {
    optional string Err = 1;
}

message ShowTagValuesRequest {
    required string Db           = 1;
    repeated uint32 PtIDs        = 2;
    repeated MapTagKeys TagKeys  = 3;
    optional string Condition    = 4;
    optional int32 Limit         = 5;
    optional bool Exact          = 6;
}

message ShowTagValuesResponse {
    optional string Err            = 1;
    repeated TagValuesSlice Values = 2;
}

message MapTagKeys {
    required string Measurement = 1;
    repeated string Keys        = 2;
}

message TagValuesSlice {
    required string Measurement = 1;
    repeated string Keys        = 2;
    repeated string Values      = 3;
}

message ExactCardinalityResponse {
    map<string, uint64> Cardinality = 1;
    optional string Err    = 2;
}

message PtRequest {
    required proto.DbPt Pt = 1;
    required int32 MigrateType = 2;
    required uint64 OpId = 3;
    optional uint64 AliveConnId = 4;
}

message PtResponse {
    optional string Err = 1;
}

message QueryExeInfo {
    required uint64 QueryID = 1;
    required string Stmt = 2;
    required string Database = 3;
    required int64  BeginTime = 4;
    required int32  RunState = 5;
    required uint32 PtID = 6;
}

message ShowQueriesResponse {
    repeated QueryExeInfo QueryExeInfos = 1;
}

message KillQueryRequest {
    required uint64 QueryID = 1;
}

message KillQueryResponse {
    optional uint32 ErrCode = 1;
    optional string ErrMsg = 2;
}

message SegregateNodeRequest {
    required uint64 NodeId = 1;
}

message SegregateNodeResponse {
    optional string Err = 1;
}

message RaftMessagesRequest {
    optional string Database = 1;
    optional uint32 PtId = 2;
    optional bytes RaftMessages = 3;
}

message RaftMessagesResponse {
    optional string ErrMsg = 1;
}

message TransferLeadershipRequest {
    required uint64 NodeId = 1;
    required uint32 PtId = 2;
    required string Database = 3;
    required uint32 newMasterPtId = 4;
}

message TransferLeadershipResponse {
    optional string Err = 1;
}
