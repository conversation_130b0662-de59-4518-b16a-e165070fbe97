package promql

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *FPoint) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "T":
			z.T, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "F":
			z.F, err = dc.ReadFloat64()
			if err != nil {
				err = msgp.WrapError(err, "F")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z FPoint) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "T"
	err = en.Append(0x82, 0xa1, 0x54)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.T)
	if err != nil {
		err = msgp.WrapError(err, "T")
		return
	}
	// write "F"
	err = en.Append(0xa1, 0x46)
	if err != nil {
		return
	}
	err = en.WriteFloat64(z.F)
	if err != nil {
		err = msgp.WrapError(err, "F")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z FPoint) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "T"
	o = append(o, 0x82, 0xa1, 0x54)
	o = msgp.AppendInt64(o, z.T)
	// string "F"
	o = append(o, 0xa1, 0x46)
	o = msgp.AppendFloat64(o, z.F)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *FPoint) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "T":
			z.T, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "F":
			z.F, bts, err = msgp.ReadFloat64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "F")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z FPoint) Msgsize() (s int) {
	s = 1 + 2 + msgp.Int64Size + 2 + msgp.Float64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Matrix) DecodeMsg(dc *msgp.Reader) (err error) {
	var zb0003 uint32
	zb0003, err = dc.ReadArrayHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if cap((*z)) >= int(zb0003) {
		(*z) = (*z)[:zb0003]
	} else {
		(*z) = make(Matrix, zb0003)
	}
	for zb0001 := range *z {
		var field []byte
		_ = field
		var zb0004 uint32
		zb0004, err = dc.ReadMapHeader()
		if err != nil {
			err = msgp.WrapError(err, zb0001)
			return
		}
		for zb0004 > 0 {
			zb0004--
			field, err = dc.ReadMapKeyPtr()
			if err != nil {
				err = msgp.WrapError(err, zb0001)
				return
			}
			switch msgp.UnsafeString(field) {
			case "metric":
				err = (*z)[zb0001].Metric.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Metric")
					return
				}
			case "floats":
				var zb0005 uint32
				zb0005, err = dc.ReadArrayHeader()
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Floats")
					return
				}
				if cap((*z)[zb0001].Floats) >= int(zb0005) {
					(*z)[zb0001].Floats = ((*z)[zb0001].Floats)[:zb0005]
				} else {
					(*z)[zb0001].Floats = make([]FPoint, zb0005)
				}
				for zb0002 := range (*z)[zb0001].Floats {
					var zb0006 uint32
					zb0006, err = dc.ReadMapHeader()
					if err != nil {
						err = msgp.WrapError(err, zb0001, "Floats", zb0002)
						return
					}
					for zb0006 > 0 {
						zb0006--
						field, err = dc.ReadMapKeyPtr()
						if err != nil {
							err = msgp.WrapError(err, zb0001, "Floats", zb0002)
							return
						}
						switch msgp.UnsafeString(field) {
						case "T":
							(*z)[zb0001].Floats[zb0002].T, err = dc.ReadInt64()
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002, "T")
								return
							}
						case "F":
							(*z)[zb0001].Floats[zb0002].F, err = dc.ReadFloat64()
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002, "F")
								return
							}
						default:
							err = dc.Skip()
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002)
								return
							}
						}
					}
				}
			default:
				err = dc.Skip()
				if err != nil {
					err = msgp.WrapError(err, zb0001)
					return
				}
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z Matrix) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteArrayHeader(uint32(len(z)))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0007 := range z {
		// map header, size 2
		// write "metric"
		err = en.Append(0x82, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
		if err != nil {
			return
		}
		err = z[zb0007].Metric.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, zb0007, "Metric")
			return
		}
		// write "floats"
		err = en.Append(0xa6, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x73)
		if err != nil {
			return
		}
		err = en.WriteArrayHeader(uint32(len(z[zb0007].Floats)))
		if err != nil {
			err = msgp.WrapError(err, zb0007, "Floats")
			return
		}
		for zb0008 := range z[zb0007].Floats {
			// map header, size 2
			// write "T"
			err = en.Append(0x82, 0xa1, 0x54)
			if err != nil {
				return
			}
			err = en.WriteInt64(z[zb0007].Floats[zb0008].T)
			if err != nil {
				err = msgp.WrapError(err, zb0007, "Floats", zb0008, "T")
				return
			}
			// write "F"
			err = en.Append(0xa1, 0x46)
			if err != nil {
				return
			}
			err = en.WriteFloat64(z[zb0007].Floats[zb0008].F)
			if err != nil {
				err = msgp.WrapError(err, zb0007, "Floats", zb0008, "F")
				return
			}
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z Matrix) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendArrayHeader(o, uint32(len(z)))
	for zb0007 := range z {
		// map header, size 2
		// string "metric"
		o = append(o, 0x82, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
		o, err = z[zb0007].Metric.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, zb0007, "Metric")
			return
		}
		// string "floats"
		o = append(o, 0xa6, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x73)
		o = msgp.AppendArrayHeader(o, uint32(len(z[zb0007].Floats)))
		for zb0008 := range z[zb0007].Floats {
			// map header, size 2
			// string "T"
			o = append(o, 0x82, 0xa1, 0x54)
			o = msgp.AppendInt64(o, z[zb0007].Floats[zb0008].T)
			// string "F"
			o = append(o, 0xa1, 0x46)
			o = msgp.AppendFloat64(o, z[zb0007].Floats[zb0008].F)
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Matrix) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var zb0003 uint32
	zb0003, bts, err = msgp.ReadArrayHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if cap((*z)) >= int(zb0003) {
		(*z) = (*z)[:zb0003]
	} else {
		(*z) = make(Matrix, zb0003)
	}
	for zb0001 := range *z {
		var field []byte
		_ = field
		var zb0004 uint32
		zb0004, bts, err = msgp.ReadMapHeaderBytes(bts)
		if err != nil {
			err = msgp.WrapError(err, zb0001)
			return
		}
		for zb0004 > 0 {
			zb0004--
			field, bts, err = msgp.ReadMapKeyZC(bts)
			if err != nil {
				err = msgp.WrapError(err, zb0001)
				return
			}
			switch msgp.UnsafeString(field) {
			case "metric":
				bts, err = (*z)[zb0001].Metric.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Metric")
					return
				}
			case "floats":
				var zb0005 uint32
				zb0005, bts, err = msgp.ReadArrayHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Floats")
					return
				}
				if cap((*z)[zb0001].Floats) >= int(zb0005) {
					(*z)[zb0001].Floats = ((*z)[zb0001].Floats)[:zb0005]
				} else {
					(*z)[zb0001].Floats = make([]FPoint, zb0005)
				}
				for zb0002 := range (*z)[zb0001].Floats {
					var zb0006 uint32
					zb0006, bts, err = msgp.ReadMapHeaderBytes(bts)
					if err != nil {
						err = msgp.WrapError(err, zb0001, "Floats", zb0002)
						return
					}
					for zb0006 > 0 {
						zb0006--
						field, bts, err = msgp.ReadMapKeyZC(bts)
						if err != nil {
							err = msgp.WrapError(err, zb0001, "Floats", zb0002)
							return
						}
						switch msgp.UnsafeString(field) {
						case "T":
							(*z)[zb0001].Floats[zb0002].T, bts, err = msgp.ReadInt64Bytes(bts)
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002, "T")
								return
							}
						case "F":
							(*z)[zb0001].Floats[zb0002].F, bts, err = msgp.ReadFloat64Bytes(bts)
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002, "F")
								return
							}
						default:
							bts, err = msgp.Skip(bts)
							if err != nil {
								err = msgp.WrapError(err, zb0001, "Floats", zb0002)
								return
							}
						}
					}
				}
			default:
				bts, err = msgp.Skip(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001)
					return
				}
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z Matrix) Msgsize() (s int) {
	s = msgp.ArrayHeaderSize
	for zb0007 := range z {
		s += 1 + 7 + z[zb0007].Metric.Msgsize() + 7 + msgp.ArrayHeaderSize + (len(z[zb0007].Floats) * (5 + msgp.Int64Size + msgp.Float64Size))
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Sample) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "f":
			z.F, err = dc.ReadFloat64()
			if err != nil {
				err = msgp.WrapError(err, "F")
				return
			}
		case "metric":
			err = z.Metric.DecodeMsg(dc)
			if err != nil {
				err = msgp.WrapError(err, "Metric")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *Sample) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "t"
	err = en.Append(0x83, 0xa1, 0x74)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.T)
	if err != nil {
		err = msgp.WrapError(err, "T")
		return
	}
	// write "f"
	err = en.Append(0xa1, 0x66)
	if err != nil {
		return
	}
	err = en.WriteFloat64(z.F)
	if err != nil {
		err = msgp.WrapError(err, "F")
		return
	}
	// write "metric"
	err = en.Append(0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
	if err != nil {
		return
	}
	err = z.Metric.EncodeMsg(en)
	if err != nil {
		err = msgp.WrapError(err, "Metric")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *Sample) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "t"
	o = append(o, 0x83, 0xa1, 0x74)
	o = msgp.AppendInt64(o, z.T)
	// string "f"
	o = append(o, 0xa1, 0x66)
	o = msgp.AppendFloat64(o, z.F)
	// string "metric"
	o = append(o, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
	o, err = z.Metric.MarshalMsg(o)
	if err != nil {
		err = msgp.WrapError(err, "Metric")
		return
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Sample) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "f":
			z.F, bts, err = msgp.ReadFloat64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "F")
				return
			}
		case "metric":
			bts, err = z.Metric.UnmarshalMsg(bts)
			if err != nil {
				err = msgp.WrapError(err, "Metric")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *Sample) Msgsize() (s int) {
	s = 1 + 2 + msgp.Int64Size + 2 + msgp.Float64Size + 7 + z.Metric.Msgsize()
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Scalar) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "v":
			z.V, err = dc.ReadFloat64()
			if err != nil {
				err = msgp.WrapError(err, "V")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z Scalar) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "t"
	err = en.Append(0x82, 0xa1, 0x74)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.T)
	if err != nil {
		err = msgp.WrapError(err, "T")
		return
	}
	// write "v"
	err = en.Append(0xa1, 0x76)
	if err != nil {
		return
	}
	err = en.WriteFloat64(z.V)
	if err != nil {
		err = msgp.WrapError(err, "V")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z Scalar) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "t"
	o = append(o, 0x82, 0xa1, 0x74)
	o = msgp.AppendInt64(o, z.T)
	// string "v"
	o = append(o, 0xa1, 0x76)
	o = msgp.AppendFloat64(o, z.V)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Scalar) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "v":
			z.V, bts, err = msgp.ReadFloat64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "V")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z Scalar) Msgsize() (s int) {
	s = 1 + 2 + msgp.Int64Size + 2 + msgp.Float64Size
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Series) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "metric":
			err = z.Metric.DecodeMsg(dc)
			if err != nil {
				err = msgp.WrapError(err, "Metric")
				return
			}
		case "floats":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Floats")
				return
			}
			if cap(z.Floats) >= int(zb0002) {
				z.Floats = (z.Floats)[:zb0002]
			} else {
				z.Floats = make([]FPoint, zb0002)
			}
			for za0001 := range z.Floats {
				var zb0003 uint32
				zb0003, err = dc.ReadMapHeader()
				if err != nil {
					err = msgp.WrapError(err, "Floats", za0001)
					return
				}
				for zb0003 > 0 {
					zb0003--
					field, err = dc.ReadMapKeyPtr()
					if err != nil {
						err = msgp.WrapError(err, "Floats", za0001)
						return
					}
					switch msgp.UnsafeString(field) {
					case "T":
						z.Floats[za0001].T, err = dc.ReadInt64()
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001, "T")
							return
						}
					case "F":
						z.Floats[za0001].F, err = dc.ReadFloat64()
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001, "F")
							return
						}
					default:
						err = dc.Skip()
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001)
							return
						}
					}
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *Series) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "metric"
	err = en.Append(0x82, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
	if err != nil {
		return
	}
	err = z.Metric.EncodeMsg(en)
	if err != nil {
		err = msgp.WrapError(err, "Metric")
		return
	}
	// write "floats"
	err = en.Append(0xa6, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Floats)))
	if err != nil {
		err = msgp.WrapError(err, "Floats")
		return
	}
	for za0001 := range z.Floats {
		// map header, size 2
		// write "T"
		err = en.Append(0x82, 0xa1, 0x54)
		if err != nil {
			return
		}
		err = en.WriteInt64(z.Floats[za0001].T)
		if err != nil {
			err = msgp.WrapError(err, "Floats", za0001, "T")
			return
		}
		// write "F"
		err = en.Append(0xa1, 0x46)
		if err != nil {
			return
		}
		err = en.WriteFloat64(z.Floats[za0001].F)
		if err != nil {
			err = msgp.WrapError(err, "Floats", za0001, "F")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *Series) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "metric"
	o = append(o, 0x82, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
	o, err = z.Metric.MarshalMsg(o)
	if err != nil {
		err = msgp.WrapError(err, "Metric")
		return
	}
	// string "floats"
	o = append(o, 0xa6, 0x66, 0x6c, 0x6f, 0x61, 0x74, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Floats)))
	for za0001 := range z.Floats {
		// map header, size 2
		// string "T"
		o = append(o, 0x82, 0xa1, 0x54)
		o = msgp.AppendInt64(o, z.Floats[za0001].T)
		// string "F"
		o = append(o, 0xa1, 0x46)
		o = msgp.AppendFloat64(o, z.Floats[za0001].F)
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Series) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "metric":
			bts, err = z.Metric.UnmarshalMsg(bts)
			if err != nil {
				err = msgp.WrapError(err, "Metric")
				return
			}
		case "floats":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Floats")
				return
			}
			if cap(z.Floats) >= int(zb0002) {
				z.Floats = (z.Floats)[:zb0002]
			} else {
				z.Floats = make([]FPoint, zb0002)
			}
			for za0001 := range z.Floats {
				var zb0003 uint32
				zb0003, bts, err = msgp.ReadMapHeaderBytes(bts)
				if err != nil {
					err = msgp.WrapError(err, "Floats", za0001)
					return
				}
				for zb0003 > 0 {
					zb0003--
					field, bts, err = msgp.ReadMapKeyZC(bts)
					if err != nil {
						err = msgp.WrapError(err, "Floats", za0001)
						return
					}
					switch msgp.UnsafeString(field) {
					case "T":
						z.Floats[za0001].T, bts, err = msgp.ReadInt64Bytes(bts)
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001, "T")
							return
						}
					case "F":
						z.Floats[za0001].F, bts, err = msgp.ReadFloat64Bytes(bts)
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001, "F")
							return
						}
					default:
						bts, err = msgp.Skip(bts)
						if err != nil {
							err = msgp.WrapError(err, "Floats", za0001)
							return
						}
					}
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *Series) Msgsize() (s int) {
	s = 1 + 7 + z.Metric.Msgsize() + 7 + msgp.ArrayHeaderSize + (len(z.Floats) * (5 + msgp.Int64Size + msgp.Float64Size))
	return
}

// DecodeMsg implements msgp.Decodable
func (z *String) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "v":
			z.V, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "V")
				return
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z String) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "t"
	err = en.Append(0x82, 0xa1, 0x74)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.T)
	if err != nil {
		err = msgp.WrapError(err, "T")
		return
	}
	// write "v"
	err = en.Append(0xa1, 0x76)
	if err != nil {
		return
	}
	err = en.WriteString(z.V)
	if err != nil {
		err = msgp.WrapError(err, "V")
		return
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z String) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "t"
	o = append(o, 0x82, 0xa1, 0x74)
	o = msgp.AppendInt64(o, z.T)
	// string "v"
	o = append(o, 0xa1, 0x76)
	o = msgp.AppendString(o, z.V)
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *String) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "t":
			z.T, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "T")
				return
			}
		case "v":
			z.V, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "V")
				return
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z String) Msgsize() (s int) {
	s = 1 + 2 + msgp.Int64Size + 2 + msgp.StringPrefixSize + len(z.V)
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Vector) DecodeMsg(dc *msgp.Reader) (err error) {
	var zb0002 uint32
	zb0002, err = dc.ReadArrayHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if cap((*z)) >= int(zb0002) {
		(*z) = (*z)[:zb0002]
	} else {
		(*z) = make(Vector, zb0002)
	}
	for zb0001 := range *z {
		var field []byte
		_ = field
		var zb0003 uint32
		zb0003, err = dc.ReadMapHeader()
		if err != nil {
			err = msgp.WrapError(err, zb0001)
			return
		}
		for zb0003 > 0 {
			zb0003--
			field, err = dc.ReadMapKeyPtr()
			if err != nil {
				err = msgp.WrapError(err, zb0001)
				return
			}
			switch msgp.UnsafeString(field) {
			case "t":
				(*z)[zb0001].T, err = dc.ReadInt64()
				if err != nil {
					err = msgp.WrapError(err, zb0001, "T")
					return
				}
			case "f":
				(*z)[zb0001].F, err = dc.ReadFloat64()
				if err != nil {
					err = msgp.WrapError(err, zb0001, "F")
					return
				}
			case "metric":
				err = (*z)[zb0001].Metric.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Metric")
					return
				}
			default:
				err = dc.Skip()
				if err != nil {
					err = msgp.WrapError(err, zb0001)
					return
				}
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z Vector) EncodeMsg(en *msgp.Writer) (err error) {
	err = en.WriteArrayHeader(uint32(len(z)))
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0004 := range z {
		// map header, size 3
		// write "t"
		err = en.Append(0x83, 0xa1, 0x74)
		if err != nil {
			return
		}
		err = en.WriteInt64(z[zb0004].T)
		if err != nil {
			err = msgp.WrapError(err, zb0004, "T")
			return
		}
		// write "f"
		err = en.Append(0xa1, 0x66)
		if err != nil {
			return
		}
		err = en.WriteFloat64(z[zb0004].F)
		if err != nil {
			err = msgp.WrapError(err, zb0004, "F")
			return
		}
		// write "metric"
		err = en.Append(0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
		if err != nil {
			return
		}
		err = z[zb0004].Metric.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, zb0004, "Metric")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z Vector) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	o = msgp.AppendArrayHeader(o, uint32(len(z)))
	for zb0004 := range z {
		// map header, size 3
		// string "t"
		o = append(o, 0x83, 0xa1, 0x74)
		o = msgp.AppendInt64(o, z[zb0004].T)
		// string "f"
		o = append(o, 0xa1, 0x66)
		o = msgp.AppendFloat64(o, z[zb0004].F)
		// string "metric"
		o = append(o, 0xa6, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63)
		o, err = z[zb0004].Metric.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, zb0004, "Metric")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Vector) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var zb0002 uint32
	zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	if cap((*z)) >= int(zb0002) {
		(*z) = (*z)[:zb0002]
	} else {
		(*z) = make(Vector, zb0002)
	}
	for zb0001 := range *z {
		var field []byte
		_ = field
		var zb0003 uint32
		zb0003, bts, err = msgp.ReadMapHeaderBytes(bts)
		if err != nil {
			err = msgp.WrapError(err, zb0001)
			return
		}
		for zb0003 > 0 {
			zb0003--
			field, bts, err = msgp.ReadMapKeyZC(bts)
			if err != nil {
				err = msgp.WrapError(err, zb0001)
				return
			}
			switch msgp.UnsafeString(field) {
			case "t":
				(*z)[zb0001].T, bts, err = msgp.ReadInt64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "T")
					return
				}
			case "f":
				(*z)[zb0001].F, bts, err = msgp.ReadFloat64Bytes(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "F")
					return
				}
			case "metric":
				bts, err = (*z)[zb0001].Metric.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001, "Metric")
					return
				}
			default:
				bts, err = msgp.Skip(bts)
				if err != nil {
					err = msgp.WrapError(err, zb0001)
					return
				}
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z Vector) Msgsize() (s int) {
	s = msgp.ArrayHeaderSize
	for zb0004 := range z {
		s += 1 + 2 + msgp.Int64Size + 2 + msgp.Float64Size + 7 + z[zb0004].Metric.Msgsize()
	}
	return
}
