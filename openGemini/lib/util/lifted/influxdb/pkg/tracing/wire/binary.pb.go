// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: binary.proto

package wire

import (
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	_ "github.com/gogo/protobuf/types"
	github_com_gogo_protobuf_types "github.com/gogo/protobuf/types"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Field_FieldType int32

const (
	FieldTypeString   Field_FieldType = 0
	FieldTypeBool     Field_FieldType = 1
	FieldTypeInt64    Field_FieldType = 2
	FieldTypeUint64   Field_FieldType = 3
	FieldTypeDuration Field_FieldType = 4
	FieldTypeFloat64  Field_FieldType = 6
)

var Field_FieldType_name = map[int32]string{
	0: "STRING",
	1: "BOOL",
	2: "INT_64",
	3: "UINT_64",
	4: "DURATION",
	6: "FLOAT_64",
}

var Field_FieldType_value = map[string]int32{
	"STRING":   0,
	"BOOL":     1,
	"INT_64":   2,
	"UINT_64":  3,
	"DURATION": 4,
	"FLOAT_64": 6,
}

func (x Field_FieldType) String() string {
	return proto.EnumName(Field_FieldType_name, int32(x))
}

func (Field_FieldType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{3, 0}
}

type SpanContext struct {
	TraceID uint64 `protobuf:"varint,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	SpanID  uint64 `protobuf:"varint,2,opt,name=span_id,json=spanId,proto3" json:"span_id,omitempty"`
}

func (m *SpanContext) Reset()         { *m = SpanContext{} }
func (m *SpanContext) String() string { return proto.CompactTextString(m) }
func (*SpanContext) ProtoMessage()    {}
func (*SpanContext) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{0}
}
func (m *SpanContext) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *SpanContext) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_SpanContext.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *SpanContext) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpanContext.Merge(m, src)
}
func (m *SpanContext) XXX_Size() int {
	return m.Size()
}
func (m *SpanContext) XXX_DiscardUnknown() {
	xxx_messageInfo_SpanContext.DiscardUnknown(m)
}

var xxx_messageInfo_SpanContext proto.InternalMessageInfo

func (m *SpanContext) GetTraceID() uint64 {
	if m != nil {
		return m.TraceID
	}
	return 0
}

func (m *SpanContext) GetSpanID() uint64 {
	if m != nil {
		return m.SpanID
	}
	return 0
}

type Span struct {
	Context      SpanContext `protobuf:"bytes,1,opt,name=context,proto3" json:"context"`
	ParentSpanID uint64      `protobuf:"varint,2,opt,name=parent_span_id,json=parentSpanId,proto3" json:"parent_span_id,omitempty"`
	Name         string      `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Start        time.Time   `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3,stdtime" json:"start_time"`
	Labels       []string    `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty"`
	Fields       []Field     `protobuf:"bytes,6,rep,name=fields,proto3" json:"fields"`
}

func (m *Span) Reset()         { *m = Span{} }
func (m *Span) String() string { return proto.CompactTextString(m) }
func (*Span) ProtoMessage()    {}
func (*Span) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{1}
}
func (m *Span) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Span) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Span.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Span) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Span.Merge(m, src)
}
func (m *Span) XXX_Size() int {
	return m.Size()
}
func (m *Span) XXX_DiscardUnknown() {
	xxx_messageInfo_Span.DiscardUnknown(m)
}

var xxx_messageInfo_Span proto.InternalMessageInfo

func (m *Span) GetContext() SpanContext {
	if m != nil {
		return m.Context
	}
	return SpanContext{}
}

func (m *Span) GetParentSpanID() uint64 {
	if m != nil {
		return m.ParentSpanID
	}
	return 0
}

func (m *Span) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Span) GetStart() time.Time {
	if m != nil {
		return m.Start
	}
	return time.Time{}
}

func (m *Span) GetLabels() []string {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *Span) GetFields() []Field {
	if m != nil {
		return m.Fields
	}
	return nil
}

type Trace struct {
	Spans []*Span `protobuf:"bytes,1,rep,name=spans,proto3" json:"spans,omitempty"`
}

func (m *Trace) Reset()         { *m = Trace{} }
func (m *Trace) String() string { return proto.CompactTextString(m) }
func (*Trace) ProtoMessage()    {}
func (*Trace) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{2}
}
func (m *Trace) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Trace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Trace.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Trace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Trace.Merge(m, src)
}
func (m *Trace) XXX_Size() int {
	return m.Size()
}
func (m *Trace) XXX_DiscardUnknown() {
	xxx_messageInfo_Trace.DiscardUnknown(m)
}

var xxx_messageInfo_Trace proto.InternalMessageInfo

func (m *Trace) GetSpans() []*Span {
	if m != nil {
		return m.Spans
	}
	return nil
}

type Field struct {
	Key       string          `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	FieldType Field_FieldType `protobuf:"varint,2,opt,name=field_type,json=fieldType,proto3,enum=wire.Field_FieldType" json:"field_type,omitempty"`
	// Types that are valid to be assigned to Value:
	//	*Field_NumericVal
	//	*Field_StringVal
	Value isField_Value `protobuf_oneof:"value"`
}

func (m *Field) Reset()         { *m = Field{} }
func (m *Field) String() string { return proto.CompactTextString(m) }
func (*Field) ProtoMessage()    {}
func (*Field) Descriptor() ([]byte, []int) {
	return fileDescriptor_3aeef8c45497084a, []int{3}
}
func (m *Field) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Field) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Field.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Field) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Field.Merge(m, src)
}
func (m *Field) XXX_Size() int {
	return m.Size()
}
func (m *Field) XXX_DiscardUnknown() {
	xxx_messageInfo_Field.DiscardUnknown(m)
}

var xxx_messageInfo_Field proto.InternalMessageInfo

type isField_Value interface {
	isField_Value()
	MarshalTo([]byte) (int, error)
	Size() int
}

type Field_NumericVal struct {
	NumericVal int64 `protobuf:"fixed64,3,opt,name=numeric_val,json=numericVal,proto3,oneof" json:"numeric_val,omitempty"`
}
type Field_StringVal struct {
	StringVal string `protobuf:"bytes,4,opt,name=string_val,json=stringVal,proto3,oneof" json:"string_val,omitempty"`
}

func (*Field_NumericVal) isField_Value() {}
func (*Field_StringVal) isField_Value()  {}

func (m *Field) GetValue() isField_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *Field) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Field) GetFieldType() Field_FieldType {
	if m != nil {
		return m.FieldType
	}
	return FieldTypeString
}

func (m *Field) GetNumericVal() int64 {
	if x, ok := m.GetValue().(*Field_NumericVal); ok {
		return x.NumericVal
	}
	return 0
}

func (m *Field) GetStringVal() string {
	if x, ok := m.GetValue().(*Field_StringVal); ok {
		return x.StringVal
	}
	return ""
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*Field) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*Field_NumericVal)(nil),
		(*Field_StringVal)(nil),
	}
}

func init() {
	proto.RegisterEnum("wire.Field_FieldType", Field_FieldType_name, Field_FieldType_value)
	proto.RegisterType((*SpanContext)(nil), "wire.SpanContext")
	proto.RegisterType((*Span)(nil), "wire.Span")
	proto.RegisterType((*Trace)(nil), "wire.Trace")
	proto.RegisterType((*Field)(nil), "wire.Field")
}

func init() { proto.RegisterFile("binary.proto", fileDescriptor_3aeef8c45497084a) }

var fileDescriptor_3aeef8c45497084a = []byte{
	// 621 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x52, 0x3f, 0x6f, 0x9b, 0x5e,
	0x14, 0x85, 0x18, 0xe3, 0x70, 0x9d, 0xf8, 0x87, 0xdf, 0x2f, 0xa9, 0x10, 0x95, 0x00, 0x39, 0x52,
	0xe5, 0x2c, 0x44, 0x49, 0x23, 0xef, 0x21, 0x56, 0x5a, 0xa4, 0xc8, 0xae, 0xb0, 0xd3, 0xa1, 0x8b,
	0xf5, 0x1c, 0xbf, 0x58, 0xa8, 0x18, 0x10, 0x3c, 0xa7, 0xf5, 0x37, 0xa8, 0x3c, 0x65, 0xea, 0xe6,
	0xa9, 0x43, 0xbf, 0x4a, 0xc6, 0x8c, 0x55, 0x07, 0x5a, 0xe1, 0x2f, 0x52, 0xbd, 0x87, 0xff, 0xa4,
	0x5d, 0xd0, 0xbd, 0xf7, 0x9c, 0x7b, 0xce, 0xbb, 0x47, 0xc0, 0xde, 0xd0, 0x0f, 0x71, 0x32, 0xb3,
	0xe3, 0x24, 0xa2, 0x11, 0x92, 0x3e, 0xf9, 0x09, 0xd1, 0x0f, 0xc6, 0xd1, 0x38, 0xe2, 0x83, 0x13,
	0x56, 0x15, 0x98, 0x6e, 0x8e, 0xa3, 0x68, 0x1c, 0x90, 0x13, 0xde, 0x0d, 0xa7, 0x77, 0x27, 0xd4,
	0x9f, 0x90, 0x94, 0xe2, 0x49, 0x5c, 0x10, 0x1a, 0x1f, 0xa0, 0xda, 0x8b, 0x71, 0x78, 0x19, 0x85,
	0x94, 0x7c, 0xa6, 0xe8, 0x15, 0xec, 0xd2, 0x04, 0xdf, 0x92, 0x81, 0x3f, 0xd2, 0x44, 0x4b, 0x6c,
	0x4a, 0x4e, 0x35, 0xcf, 0xcc, 0x4a, 0x9f, 0xcd, 0xdc, 0xb6, 0x57, 0xe1, 0xa0, 0x3b, 0x42, 0x47,
	0x50, 0x49, 0x63, 0x1c, 0x32, 0xda, 0x0e, 0xa7, 0x41, 0x9e, 0x99, 0x32, 0x53, 0x72, 0xdb, 0x9e,
	0xcc, 0x20, 0x77, 0xd4, 0xf8, 0xba, 0x03, 0x12, 0x1b, 0xa1, 0x53, 0xa8, 0xdc, 0x16, 0x06, 0x5c,
	0xb4, 0x7a, 0x56, 0xb7, 0xd9, 0x9b, 0xed, 0x67, 0xce, 0x8e, 0xf4, 0x98, 0x99, 0x82, 0xb7, 0xe6,
	0xa1, 0x16, 0xd4, 0x62, 0x9c, 0x90, 0x90, 0x0e, 0xfe, 0xf6, 0x51, 0xf3, 0xcc, 0xdc, 0x7b, 0xc7,
	0x91, 0x95, 0xdb, 0x5e, 0xbc, 0xed, 0x46, 0x08, 0x81, 0x14, 0xe2, 0x09, 0xd1, 0x4a, 0x96, 0xd8,
	0x54, 0x3c, 0x5e, 0xa3, 0x6b, 0x80, 0x94, 0xe2, 0x84, 0x0e, 0xd8, 0xf1, 0x9a, 0xc4, 0x5f, 0xa0,
	0xdb, 0x45, 0x32, 0xf6, 0x3a, 0x19, 0xbb, 0xbf, 0x4e, 0xc6, 0xa9, 0xb3, 0xa7, 0xe4, 0x99, 0x59,
	0xee, 0xb1, 0xad, 0x87, 0x5f, 0xa6, 0xe8, 0x29, 0x5c, 0x80, 0x51, 0xd0, 0x0b, 0x90, 0x03, 0x3c,
	0x24, 0x41, 0xaa, 0x95, 0xad, 0x52, 0x53, 0xf1, 0x56, 0x1d, 0x3a, 0x06, 0xf9, 0xce, 0x27, 0xc1,
	0x28, 0xd5, 0x64, 0xab, 0xd4, 0xac, 0x9e, 0x55, 0x8b, 0x1b, 0xaf, 0xd8, 0x6c, 0x75, 0xdd, 0x8a,
	0xd0, 0x38, 0x86, 0x32, 0x4f, 0x14, 0x59, 0x50, 0x66, 0xe7, 0xa5, 0x9a, 0xc8, 0x57, 0x60, 0x1b,
	0x8b, 0x57, 0x00, 0x8d, 0xef, 0x25, 0x28, 0x73, 0x09, 0xa4, 0x42, 0xe9, 0x23, 0x99, 0xf1, 0x00,
	0x15, 0x8f, 0x95, 0xe8, 0x12, 0x80, 0x0b, 0x0e, 0xe8, 0x2c, 0x26, 0x3c, 0x9f, 0xda, 0xd9, 0xe1,
	0x33, 0xd7, 0xe2, 0xdb, 0x9f, 0xc5, 0xc4, 0xd9, 0xcf, 0x33, 0x53, 0xd9, 0xb4, 0x9e, 0x72, 0xb7,
	0x2e, 0xd1, 0x29, 0x54, 0xc3, 0xe9, 0x84, 0x24, 0xfe, 0xed, 0xe0, 0x1e, 0x07, 0x3c, 0x37, 0xd5,
	0xa9, 0xe5, 0x99, 0x09, 0x9d, 0x62, 0xfc, 0x1e, 0x07, 0x6f, 0x05, 0x0f, 0xc2, 0x4d, 0x87, 0x6c,
	0x96, 0x67, 0xe2, 0x87, 0x63, 0xbe, 0xc1, 0xf2, 0x54, 0x0a, 0x83, 0x1e, 0x9f, 0x16, 0x0b, 0x4a,
	0xba, 0x6e, 0x1a, 0x3f, 0x45, 0xd8, 0x7a, 0x23, 0x13, 0xe4, 0x5e, 0xdf, 0x73, 0x3b, 0x6f, 0x54,
	0x41, 0xff, 0x7f, 0xbe, 0xb0, 0xfe, 0xdb, 0x40, 0xc5, 0x3a, 0x7a, 0x09, 0x92, 0xd3, 0xed, 0x5e,
	0xab, 0xa2, 0x5e, 0x9f, 0x2f, 0xac, 0xfd, 0xed, 0x11, 0x51, 0x14, 0x20, 0x03, 0x64, 0xb7, 0xd3,
	0x1f, 0xb4, 0xce, 0xd5, 0x1d, 0x1d, 0xcd, 0x17, 0x56, 0x6d, 0x03, 0xbb, 0x21, 0x6d, 0x9d, 0x23,
	0x0b, 0x2a, 0x37, 0x2b, 0x42, 0xe9, 0x1f, 0xf9, 0x1b, 0x9f, 0x33, 0x8e, 0x60, 0xb7, 0x7d, 0xe3,
	0x5d, 0xf4, 0xdd, 0x6e, 0x47, 0x95, 0xf4, 0xc3, 0xf9, 0xc2, 0xaa, 0x6f, 0x28, 0xed, 0x69, 0x82,
	0xa9, 0x1f, 0x85, 0xa8, 0x01, 0xbb, 0x57, 0xd7, 0xdd, 0x0b, 0xae, 0x23, 0xeb, 0x07, 0xf3, 0x85,
	0xa5, 0x6e, 0x48, 0x57, 0x41, 0x84, 0x69, 0xeb, 0x5c, 0x97, 0xbe, 0x7c, 0x33, 0x04, 0xa7, 0x02,
	0xe5, 0x7b, 0x1c, 0x4c, 0x89, 0xa3, 0x3d, 0xe6, 0x86, 0xf8, 0x94, 0x1b, 0xe2, 0xef, 0xdc, 0x10,
	0x1f, 0x96, 0x86, 0xf0, 0xb4, 0x34, 0x84, 0x1f, 0x4b, 0x43, 0x18, 0xca, 0xfc, 0x1f, 0x7b, 0xfd,
	0x27, 0x00, 0x00, 0xff, 0xff, 0xbf, 0x66, 0xcd, 0x47, 0xb7, 0x03, 0x00, 0x00,
}

func (m *SpanContext) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SpanContext) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *SpanContext) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.SpanID != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.SpanID))
		i--
		dAtA[i] = 0x10
	}
	if m.TraceID != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.TraceID))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Span) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Span) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Span) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Fields) > 0 {
		for iNdEx := len(m.Fields) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Fields[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBinary(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x32
		}
	}
	if len(m.Labels) > 0 {
		for iNdEx := len(m.Labels) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Labels[iNdEx])
			copy(dAtA[i:], m.Labels[iNdEx])
			i = encodeVarintBinary(dAtA, i, uint64(len(m.Labels[iNdEx])))
			i--
			dAtA[i] = 0x2a
		}
	}
	n1, err1 := github_com_gogo_protobuf_types.StdTimeMarshalTo(m.Start, dAtA[i-github_com_gogo_protobuf_types.SizeOfStdTime(m.Start):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintBinary(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x22
	if len(m.Name) > 0 {
		i -= len(m.Name)
		copy(dAtA[i:], m.Name)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Name)))
		i--
		dAtA[i] = 0x1a
	}
	if m.ParentSpanID != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.ParentSpanID))
		i--
		dAtA[i] = 0x10
	}
	{
		size, err := m.Context.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintBinary(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func (m *Trace) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Trace) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Trace) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Spans) > 0 {
		for iNdEx := len(m.Spans) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Spans[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintBinary(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *Field) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Field) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Field) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Value != nil {
		{
			size := m.Value.Size()
			i -= size
			if _, err := m.Value.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	if m.FieldType != 0 {
		i = encodeVarintBinary(dAtA, i, uint64(m.FieldType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintBinary(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Field_NumericVal) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Field_NumericVal) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= 8
	encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.NumericVal))
	i--
	dAtA[i] = 0x19
	return len(dAtA) - i, nil
}
func (m *Field_StringVal) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Field_StringVal) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	i -= len(m.StringVal)
	copy(dAtA[i:], m.StringVal)
	i = encodeVarintBinary(dAtA, i, uint64(len(m.StringVal)))
	i--
	dAtA[i] = 0x22
	return len(dAtA) - i, nil
}
func encodeVarintBinary(dAtA []byte, offset int, v uint64) int {
	offset -= sovBinary(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *SpanContext) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TraceID != 0 {
		n += 1 + sovBinary(uint64(m.TraceID))
	}
	if m.SpanID != 0 {
		n += 1 + sovBinary(uint64(m.SpanID))
	}
	return n
}

func (m *Span) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = m.Context.Size()
	n += 1 + l + sovBinary(uint64(l))
	if m.ParentSpanID != 0 {
		n += 1 + sovBinary(uint64(m.ParentSpanID))
	}
	l = len(m.Name)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	l = github_com_gogo_protobuf_types.SizeOfStdTime(m.Start)
	n += 1 + l + sovBinary(uint64(l))
	if len(m.Labels) > 0 {
		for _, s := range m.Labels {
			l = len(s)
			n += 1 + l + sovBinary(uint64(l))
		}
	}
	if len(m.Fields) > 0 {
		for _, e := range m.Fields {
			l = e.Size()
			n += 1 + l + sovBinary(uint64(l))
		}
	}
	return n
}

func (m *Trace) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Spans) > 0 {
		for _, e := range m.Spans {
			l = e.Size()
			n += 1 + l + sovBinary(uint64(l))
		}
	}
	return n
}

func (m *Field) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovBinary(uint64(l))
	}
	if m.FieldType != 0 {
		n += 1 + sovBinary(uint64(m.FieldType))
	}
	if m.Value != nil {
		n += m.Value.Size()
	}
	return n
}

func (m *Field_NumericVal) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	n += 9
	return n
}
func (m *Field_StringVal) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.StringVal)
	n += 1 + l + sovBinary(uint64(l))
	return n
}

func sovBinary(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozBinary(x uint64) (n int) {
	return sovBinary(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *SpanContext) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SpanContext: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SpanContext: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field TraceID", wireType)
			}
			m.TraceID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TraceID |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpanID", wireType)
			}
			m.SpanID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SpanID |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Span) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Span: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Span: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Context", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Context.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ParentSpanID", wireType)
			}
			m.ParentSpanID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentSpanID |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_gogo_protobuf_types.StdTimeUnmarshal(&m.Start, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Labels", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Labels = append(m.Labels, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Fields", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Fields = append(m.Fields, Field{})
			if err := m.Fields[len(m.Fields)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Trace) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Trace: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Trace: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Spans", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Spans = append(m.Spans, &Span{})
			if err := m.Spans[len(m.Spans)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Field) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Field: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Field: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FieldType", wireType)
			}
			m.FieldType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FieldType |= Field_FieldType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 1 {
				return fmt.Errorf("proto: wrong wireType = %d for field NumericVal", wireType)
			}
			var v int64
			if (iNdEx + 8) > l {
				return io.ErrUnexpectedEOF
			}
			v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
			iNdEx += 8
			m.Value = &Field_NumericVal{v}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StringVal", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthBinary
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthBinary
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = &Field_StringVal{string(dAtA[iNdEx:postIndex])}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipBinary(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthBinary
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipBinary(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowBinary
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowBinary
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthBinary
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupBinary
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthBinary
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthBinary        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowBinary          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupBinary = fmt.Errorf("proto: unexpected end of group")
)
