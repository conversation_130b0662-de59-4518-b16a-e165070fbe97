package promql2influxql

// Code generated by github.com/tinylib/msgp DO NOT EDIT.

import (
	"github.com/openGemini/openGemini/lib/util/lifted/prometheus/promql"
	"github.com/tinylib/msgp/msgp"
)

// DecodeMsg implements msgp.Decodable
func (z *CachedResponse) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "key":
			z.Key, err = dc.ReadString()
			if err != nil {
				err = msgp.WrapError(err, "Key")
				return
			}
		case "extents":
			var zb0002 uint32
			zb0002, err = dc.ReadArrayHeader()
			if err != nil {
				err = msgp.WrapError(err, "Extents")
				return
			}
			if cap(z.Extents) >= int(zb0002) {
				z.Extents = (z.Extents)[:zb0002]
			} else {
				z.Extents = make([]Extent, zb0002)
			}
			for za0001 := range z.Extents {
				err = z.Extents[za0001].DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Extents", za0001)
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *CachedResponse) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 2
	// write "key"
	err = en.Append(0x82, 0xa3, 0x6b, 0x65, 0x79)
	if err != nil {
		return
	}
	err = en.WriteString(z.Key)
	if err != nil {
		err = msgp.WrapError(err, "Key")
		return
	}
	// write "extents"
	err = en.Append(0xa7, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73)
	if err != nil {
		return
	}
	err = en.WriteArrayHeader(uint32(len(z.Extents)))
	if err != nil {
		err = msgp.WrapError(err, "Extents")
		return
	}
	for za0001 := range z.Extents {
		err = z.Extents[za0001].EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Extents", za0001)
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *CachedResponse) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 2
	// string "key"
	o = append(o, 0x82, 0xa3, 0x6b, 0x65, 0x79)
	o = msgp.AppendString(o, z.Key)
	// string "extents"
	o = append(o, 0xa7, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x74, 0x73)
	o = msgp.AppendArrayHeader(o, uint32(len(z.Extents)))
	for za0001 := range z.Extents {
		o, err = z.Extents[za0001].MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Extents", za0001)
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *CachedResponse) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "key":
			z.Key, bts, err = msgp.ReadStringBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Key")
				return
			}
		case "extents":
			var zb0002 uint32
			zb0002, bts, err = msgp.ReadArrayHeaderBytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Extents")
				return
			}
			if cap(z.Extents) >= int(zb0002) {
				z.Extents = (z.Extents)[:zb0002]
			} else {
				z.Extents = make([]Extent, zb0002)
			}
			for za0001 := range z.Extents {
				bts, err = z.Extents[za0001].UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Extents", za0001)
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *CachedResponse) Msgsize() (s int) {
	s = 1 + 4 + msgp.StringPrefixSize + len(z.Key) + 8 + msgp.ArrayHeaderSize
	for za0001 := range z.Extents {
		s += z.Extents[za0001].Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *Extent) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "start":
			z.Start, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "Start")
				return
			}
		case "end":
			z.End, err = dc.ReadInt64()
			if err != nil {
				err = msgp.WrapError(err, "End")
				return
			}
		case "response":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Response")
					return
				}
				z.Response = nil
			} else {
				if z.Response == nil {
					z.Response = new(PromQueryResponse)
				}
				err = z.Response.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Response")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *Extent) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 3
	// write "start"
	err = en.Append(0x83, 0xa5, 0x73, 0x74, 0x61, 0x72, 0x74)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.Start)
	if err != nil {
		err = msgp.WrapError(err, "Start")
		return
	}
	// write "end"
	err = en.Append(0xa3, 0x65, 0x6e, 0x64)
	if err != nil {
		return
	}
	err = en.WriteInt64(z.End)
	if err != nil {
		err = msgp.WrapError(err, "End")
		return
	}
	// write "response"
	err = en.Append(0xa8, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65)
	if err != nil {
		return
	}
	if z.Response == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.Response.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Response")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *Extent) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 3
	// string "start"
	o = append(o, 0x83, 0xa5, 0x73, 0x74, 0x61, 0x72, 0x74)
	o = msgp.AppendInt64(o, z.Start)
	// string "end"
	o = append(o, 0xa3, 0x65, 0x6e, 0x64)
	o = msgp.AppendInt64(o, z.End)
	// string "response"
	o = append(o, 0xa8, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65)
	if z.Response == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.Response.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Response")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *Extent) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "start":
			z.Start, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "Start")
				return
			}
		case "end":
			z.End, bts, err = msgp.ReadInt64Bytes(bts)
			if err != nil {
				err = msgp.WrapError(err, "End")
				return
			}
		case "response":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Response = nil
			} else {
				if z.Response == nil {
					z.Response = new(PromQueryResponse)
				}
				bts, err = z.Response.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Response")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *Extent) Msgsize() (s int) {
	s = 1 + 6 + msgp.Int64Size + 4 + msgp.Int64Size + 9
	if z.Response == nil {
		s += msgp.NilSize
	} else {
		s += z.Response.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *PromDataMatrix) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "matrix":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Matrix")
					return
				}
				z.Matrix = nil
			} else {
				if z.Matrix == nil {
					z.Matrix = new(promql.Matrix)
				}
				err = z.Matrix.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Matrix")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *PromDataMatrix) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 1
	// write "matrix"
	err = en.Append(0x81, 0xa6, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78)
	if err != nil {
		return
	}
	if z.Matrix == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.Matrix.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Matrix")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *PromDataMatrix) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 1
	// string "matrix"
	o = append(o, 0x81, 0xa6, 0x6d, 0x61, 0x74, 0x72, 0x69, 0x78)
	if z.Matrix == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.Matrix.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Matrix")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *PromDataMatrix) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "matrix":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Matrix = nil
			} else {
				if z.Matrix == nil {
					z.Matrix = new(promql.Matrix)
				}
				bts, err = z.Matrix.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Matrix")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *PromDataMatrix) Msgsize() (s int) {
	s = 1 + 7
	if z.Matrix == nil {
		s += msgp.NilSize
	} else {
		s += z.Matrix.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *PromDataScalar) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "scalar":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Scalar")
					return
				}
				z.Scalar = nil
			} else {
				if z.Scalar == nil {
					z.Scalar = new(promql.Scalar)
				}
				err = z.Scalar.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Scalar")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *PromDataScalar) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 1
	// write "scalar"
	err = en.Append(0x81, 0xa6, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72)
	if err != nil {
		return
	}
	if z.Scalar == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.Scalar.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Scalar")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *PromDataScalar) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 1
	// string "scalar"
	o = append(o, 0x81, 0xa6, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x72)
	if z.Scalar == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.Scalar.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Scalar")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *PromDataScalar) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "scalar":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Scalar = nil
			} else {
				if z.Scalar == nil {
					z.Scalar = new(promql.Scalar)
				}
				bts, err = z.Scalar.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Scalar")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *PromDataScalar) Msgsize() (s int) {
	s = 1 + 7
	if z.Scalar == nil {
		s += msgp.NilSize
	} else {
		s += z.Scalar.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *PromDataString) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "string":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "String")
					return
				}
				z.String = nil
			} else {
				if z.String == nil {
					z.String = new(promql.String)
				}
				err = z.String.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "String")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *PromDataString) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 1
	// write "string"
	err = en.Append(0x81, 0xa6, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67)
	if err != nil {
		return
	}
	if z.String == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.String.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "String")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *PromDataString) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 1
	// string "string"
	o = append(o, 0x81, 0xa6, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67)
	if z.String == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.String.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "String")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *PromDataString) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "string":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.String = nil
			} else {
				if z.String == nil {
					z.String = new(promql.String)
				}
				bts, err = z.String.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "String")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *PromDataString) Msgsize() (s int) {
	s = 1 + 7
	if z.String == nil {
		s += msgp.NilSize
	} else {
		s += z.String.Msgsize()
	}
	return
}

// DecodeMsg implements msgp.Decodable
func (z *PromDataVector) DecodeMsg(dc *msgp.Reader) (err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, err = dc.ReadMapHeader()
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, err = dc.ReadMapKeyPtr()
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "vector":
			if dc.IsNil() {
				err = dc.ReadNil()
				if err != nil {
					err = msgp.WrapError(err, "Vector")
					return
				}
				z.Vector = nil
			} else {
				if z.Vector == nil {
					z.Vector = new(promql.Vector)
				}
				err = z.Vector.DecodeMsg(dc)
				if err != nil {
					err = msgp.WrapError(err, "Vector")
					return
				}
			}
		default:
			err = dc.Skip()
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	return
}

// EncodeMsg implements msgp.Encodable
func (z *PromDataVector) EncodeMsg(en *msgp.Writer) (err error) {
	// map header, size 1
	// write "vector"
	err = en.Append(0x81, 0xa6, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72)
	if err != nil {
		return
	}
	if z.Vector == nil {
		err = en.WriteNil()
		if err != nil {
			return
		}
	} else {
		err = z.Vector.EncodeMsg(en)
		if err != nil {
			err = msgp.WrapError(err, "Vector")
			return
		}
	}
	return
}

// MarshalMsg implements msgp.Marshaler
func (z *PromDataVector) MarshalMsg(b []byte) (o []byte, err error) {
	o = msgp.Require(b, z.Msgsize())
	// map header, size 1
	// string "vector"
	o = append(o, 0x81, 0xa6, 0x76, 0x65, 0x63, 0x74, 0x6f, 0x72)
	if z.Vector == nil {
		o = msgp.AppendNil(o)
	} else {
		o, err = z.Vector.MarshalMsg(o)
		if err != nil {
			err = msgp.WrapError(err, "Vector")
			return
		}
	}
	return
}

// UnmarshalMsg implements msgp.Unmarshaler
func (z *PromDataVector) UnmarshalMsg(bts []byte) (o []byte, err error) {
	var field []byte
	_ = field
	var zb0001 uint32
	zb0001, bts, err = msgp.ReadMapHeaderBytes(bts)
	if err != nil {
		err = msgp.WrapError(err)
		return
	}
	for zb0001 > 0 {
		zb0001--
		field, bts, err = msgp.ReadMapKeyZC(bts)
		if err != nil {
			err = msgp.WrapError(err)
			return
		}
		switch msgp.UnsafeString(field) {
		case "vector":
			if msgp.IsNil(bts) {
				bts, err = msgp.ReadNilBytes(bts)
				if err != nil {
					return
				}
				z.Vector = nil
			} else {
				if z.Vector == nil {
					z.Vector = new(promql.Vector)
				}
				bts, err = z.Vector.UnmarshalMsg(bts)
				if err != nil {
					err = msgp.WrapError(err, "Vector")
					return
				}
			}
		default:
			bts, err = msgp.Skip(bts)
			if err != nil {
				err = msgp.WrapError(err)
				return
			}
		}
	}
	o = bts
	return
}

// Msgsize returns an upper bound estimate of the number of bytes occupied by the serialized message
func (z *PromDataVector) Msgsize() (s int) {
	s = 1 + 7
	if z.Vector == nil {
		s += msgp.NilSize
	} else {
		s += z.Vector.Msgsize()
	}
	return
}
