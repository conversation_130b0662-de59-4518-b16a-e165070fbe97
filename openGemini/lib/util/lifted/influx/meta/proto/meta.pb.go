// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: meta.proto

package proto

import (
	fmt "fmt"
	proto "github.com/openGemini/openGemini/lib/util/lifted/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Command_Type int32

const (
	Command_CreateDatabaseCommand                 Command_Type = 3
	Command_DropDatabaseCommand                   Command_Type = 4
	Command_CreateRetentionPolicyCommand          Command_Type = 5
	Command_DropRetentionPolicyCommand            Command_Type = 6
	Command_SetDefaultRetentionPolicyCommand      Command_Type = 7
	Command_UpdateRetentionPolicyCommand          Command_Type = 8
	Command_CreateShardGroupCommand               Command_Type = 9
	Command_DeleteShardGroupCommand               Command_Type = 10
	Command_CreateUserCommand                     Command_Type = 13
	Command_DropUserCommand                       Command_Type = 14
	Command_UpdateUserCommand                     Command_Type = 15
	Command_SetPrivilegeCommand                   Command_Type = 16
	Command_SetDataCommand                        Command_Type = 17
	Command_SetAdminPrivilegeCommand              Command_Type = 18
	Command_CreateSubscriptionCommand             Command_Type = 21
	Command_DropSubscriptionCommand               Command_Type = 22
	Command_CreateMetaNodeCommand                 Command_Type = 24
	Command_CreateDataNodeCommand                 Command_Type = 25
	Command_UpdateDataNodeCommand                 Command_Type = 26
	Command_DeleteMetaNodeCommand                 Command_Type = 27
	Command_DeleteDataNodeCommand                 Command_Type = 28
	Command_SetMetaNodeCommand                    Command_Type = 29
	Command_DropShardCommand                      Command_Type = 30
	Command_MarkDatabaseDeleteCommand             Command_Type = 31
	Command_UpdateShardOwnerCommand               Command_Type = 35
	Command_MarkRetentionPolicyDeleteCommand      Command_Type = 39
	Command_CreateMeasurementCommand              Command_Type = 49
	Command_AlterShardKeyCmd                      Command_Type = 50
	Command_ReShardingCommand                     Command_Type = 52
	Command_UpdateSchemaCommand                   Command_Type = 53
	Command_ReportShardsCommand                   Command_Type = 54
	Command_PruneGroupsCommand                    Command_Type = 57
	Command_MarkMeasurementDeleteCommand          Command_Type = 58
	Command_DropMeasurementCommand                Command_Type = 59
	Command_TimeRangeCommand                      Command_Type = 60
	Command_ShardDurationCommand                  Command_Type = 61
	Command_DeleteIndexGroupCommand               Command_Type = 62
	Command_UpdateShardInfoTierCommand            Command_Type = 63
	Command_UpdateNodeStatusCommand               Command_Type = 64
	Command_CreateEventCommand                    Command_Type = 65
	Command_UpdateEventCommand                    Command_Type = 66
	Command_UpdatePtInfoCommand                   Command_Type = 67
	Command_RemoveEventCommand                    Command_Type = 68
	Command_CreateDownSamplePolicyCommand         Command_Type = 69
	Command_DropDownSamplePolicyCommand           Command_Type = 70
	Command_GetDownSamplePolicyCommand            Command_Type = 71
	Command_CreateDbPtViewCommand                 Command_Type = 72
	Command_GetMeasurementInfoWithinSameRpCommand Command_Type = 73
	Command_UpdateShardDownSampleInfoCommand      Command_Type = 74
	Command_MarkTakeoverCommand                   Command_Type = 75
	Command_MarkBalancerCommand                   Command_Type = 76
	Command_CreateStreamCommand                   Command_Type = 77
	Command_DropStreamCommand                     Command_Type = 78
	Command_GetMeasurementInfoStoreCommand        Command_Type = 79
	Command_VerifyDataNodeCommand                 Command_Type = 80
	Command_ExpandGroupsCommand                   Command_Type = 81
	Command_UpdatePtVersionCommand                Command_Type = 82
	Command_GetDBBriefInfoCommand                 Command_Type = 83
	Command_GetMeasurementsInfoCommand            Command_Type = 84
	Command_RegisterQueryIDOffsetCommand          Command_Type = 85
	Command_CreateContinuousQueryCommand          Command_Type = 86
	Command_Sql2MetaHeartbeatCommand              Command_Type = 87
	Command_ContinuousQueryReportCommand          Command_Type = 88
	Command_GetContinuousQueryLeaseCommand        Command_Type = 89
	Command_DropContinuousQueryCommand            Command_Type = 90
	Command_NotifyCQLeaseChangedCommand           Command_Type = 91
	Command_SetNodeSegregateStatusCommand         Command_Type = 92
	Command_RemoveNodeCommand                     Command_Type = 93
	Command_UpdateReplicationCommand              Command_Type = 94
	Command_UpdateNodeTmpIndexCommand             Command_Type = 95
	Command_CreateSqlNodeCommand                  Command_Type = 96
	Command_UpdateSqlNodeStatusCommand            Command_Type = 97
	Command_InsertFilesCommand                    Command_Type = 98
	Command_UpdateMeasurementCommand              Command_Type = 101
	Command_UpdateMetaNodeStatusCommand           Command_Type = 102
	Command_ShowClusterCommand                    Command_Type = 103
	Command_IndexDurationCommand                  Command_Type = 104
	Command_UpdateIndexInfoTierCommand            Command_Type = 105
)

var Command_Type_name = map[int32]string{
	3:   "CreateDatabaseCommand",
	4:   "DropDatabaseCommand",
	5:   "CreateRetentionPolicyCommand",
	6:   "DropRetentionPolicyCommand",
	7:   "SetDefaultRetentionPolicyCommand",
	8:   "UpdateRetentionPolicyCommand",
	9:   "CreateShardGroupCommand",
	10:  "DeleteShardGroupCommand",
	13:  "CreateUserCommand",
	14:  "DropUserCommand",
	15:  "UpdateUserCommand",
	16:  "SetPrivilegeCommand",
	17:  "SetDataCommand",
	18:  "SetAdminPrivilegeCommand",
	21:  "CreateSubscriptionCommand",
	22:  "DropSubscriptionCommand",
	24:  "CreateMetaNodeCommand",
	25:  "CreateDataNodeCommand",
	26:  "UpdateDataNodeCommand",
	27:  "DeleteMetaNodeCommand",
	28:  "DeleteDataNodeCommand",
	29:  "SetMetaNodeCommand",
	30:  "DropShardCommand",
	31:  "MarkDatabaseDeleteCommand",
	35:  "UpdateShardOwnerCommand",
	39:  "MarkRetentionPolicyDeleteCommand",
	49:  "CreateMeasurementCommand",
	50:  "AlterShardKeyCmd",
	52:  "ReShardingCommand",
	53:  "UpdateSchemaCommand",
	54:  "ReportShardsCommand",
	57:  "PruneGroupsCommand",
	58:  "MarkMeasurementDeleteCommand",
	59:  "DropMeasurementCommand",
	60:  "TimeRangeCommand",
	61:  "ShardDurationCommand",
	62:  "DeleteIndexGroupCommand",
	63:  "UpdateShardInfoTierCommand",
	64:  "UpdateNodeStatusCommand",
	65:  "CreateEventCommand",
	66:  "UpdateEventCommand",
	67:  "UpdatePtInfoCommand",
	68:  "RemoveEventCommand",
	69:  "CreateDownSamplePolicyCommand",
	70:  "DropDownSamplePolicyCommand",
	71:  "GetDownSamplePolicyCommand",
	72:  "CreateDbPtViewCommand",
	73:  "GetMeasurementInfoWithinSameRpCommand",
	74:  "UpdateShardDownSampleInfoCommand",
	75:  "MarkTakeoverCommand",
	76:  "MarkBalancerCommand",
	77:  "CreateStreamCommand",
	78:  "DropStreamCommand",
	79:  "GetMeasurementInfoStoreCommand",
	80:  "VerifyDataNodeCommand",
	81:  "ExpandGroupsCommand",
	82:  "UpdatePtVersionCommand",
	83:  "GetDBBriefInfoCommand",
	84:  "GetMeasurementsInfoCommand",
	85:  "RegisterQueryIDOffsetCommand",
	86:  "CreateContinuousQueryCommand",
	87:  "Sql2MetaHeartbeatCommand",
	88:  "ContinuousQueryReportCommand",
	89:  "GetContinuousQueryLeaseCommand",
	90:  "DropContinuousQueryCommand",
	91:  "NotifyCQLeaseChangedCommand",
	92:  "SetNodeSegregateStatusCommand",
	93:  "RemoveNodeCommand",
	94:  "UpdateReplicationCommand",
	95:  "UpdateNodeTmpIndexCommand",
	96:  "CreateSqlNodeCommand",
	97:  "UpdateSqlNodeStatusCommand",
	98:  "InsertFilesCommand",
	101: "UpdateMeasurementCommand",
	102: "UpdateMetaNodeStatusCommand",
	103: "ShowClusterCommand",
	104: "IndexDurationCommand",
	105: "UpdateIndexInfoTierCommand",
}

var Command_Type_value = map[string]int32{
	"CreateDatabaseCommand":                 3,
	"DropDatabaseCommand":                   4,
	"CreateRetentionPolicyCommand":          5,
	"DropRetentionPolicyCommand":            6,
	"SetDefaultRetentionPolicyCommand":      7,
	"UpdateRetentionPolicyCommand":          8,
	"CreateShardGroupCommand":               9,
	"DeleteShardGroupCommand":               10,
	"CreateUserCommand":                     13,
	"DropUserCommand":                       14,
	"UpdateUserCommand":                     15,
	"SetPrivilegeCommand":                   16,
	"SetDataCommand":                        17,
	"SetAdminPrivilegeCommand":              18,
	"CreateSubscriptionCommand":             21,
	"DropSubscriptionCommand":               22,
	"CreateMetaNodeCommand":                 24,
	"CreateDataNodeCommand":                 25,
	"UpdateDataNodeCommand":                 26,
	"DeleteMetaNodeCommand":                 27,
	"DeleteDataNodeCommand":                 28,
	"SetMetaNodeCommand":                    29,
	"DropShardCommand":                      30,
	"MarkDatabaseDeleteCommand":             31,
	"UpdateShardOwnerCommand":               35,
	"MarkRetentionPolicyDeleteCommand":      39,
	"CreateMeasurementCommand":              49,
	"AlterShardKeyCmd":                      50,
	"ReShardingCommand":                     52,
	"UpdateSchemaCommand":                   53,
	"ReportShardsCommand":                   54,
	"PruneGroupsCommand":                    57,
	"MarkMeasurementDeleteCommand":          58,
	"DropMeasurementCommand":                59,
	"TimeRangeCommand":                      60,
	"ShardDurationCommand":                  61,
	"DeleteIndexGroupCommand":               62,
	"UpdateShardInfoTierCommand":            63,
	"UpdateNodeStatusCommand":               64,
	"CreateEventCommand":                    65,
	"UpdateEventCommand":                    66,
	"UpdatePtInfoCommand":                   67,
	"RemoveEventCommand":                    68,
	"CreateDownSamplePolicyCommand":         69,
	"DropDownSamplePolicyCommand":           70,
	"GetDownSamplePolicyCommand":            71,
	"CreateDbPtViewCommand":                 72,
	"GetMeasurementInfoWithinSameRpCommand": 73,
	"UpdateShardDownSampleInfoCommand":      74,
	"MarkTakeoverCommand":                   75,
	"MarkBalancerCommand":                   76,
	"CreateStreamCommand":                   77,
	"DropStreamCommand":                     78,
	"GetMeasurementInfoStoreCommand":        79,
	"VerifyDataNodeCommand":                 80,
	"ExpandGroupsCommand":                   81,
	"UpdatePtVersionCommand":                82,
	"GetDBBriefInfoCommand":                 83,
	"GetMeasurementsInfoCommand":            84,
	"RegisterQueryIDOffsetCommand":          85,
	"CreateContinuousQueryCommand":          86,
	"Sql2MetaHeartbeatCommand":              87,
	"ContinuousQueryReportCommand":          88,
	"GetContinuousQueryLeaseCommand":        89,
	"DropContinuousQueryCommand":            90,
	"NotifyCQLeaseChangedCommand":           91,
	"SetNodeSegregateStatusCommand":         92,
	"RemoveNodeCommand":                     93,
	"UpdateReplicationCommand":              94,
	"UpdateNodeTmpIndexCommand":             95,
	"CreateSqlNodeCommand":                  96,
	"UpdateSqlNodeStatusCommand":            97,
	"InsertFilesCommand":                    98,
	"UpdateMeasurementCommand":              101,
	"UpdateMetaNodeStatusCommand":           102,
	"ShowClusterCommand":                    103,
	"IndexDurationCommand":                  104,
	"UpdateIndexInfoTierCommand":            105,
}

func (x Command_Type) Enum() *Command_Type {
	p := new(Command_Type)
	*p = x
	return p
}

func (x Command_Type) String() string {
	return proto.EnumName(Command_Type_name, int32(x))
}

func (x *Command_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Command_Type_value, data, "Command_Type")
	if err != nil {
		return err
	}
	*x = Command_Type(value)
	return nil
}

func (Command_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{35, 0}
}

type Data struct {
	Term                 *uint64                  `protobuf:"varint,1,req,name=Term" json:"Term,omitempty"`
	Index                *uint64                  `protobuf:"varint,2,req,name=Index" json:"Index,omitempty"`
	ClusterID            *uint64                  `protobuf:"varint,3,req,name=ClusterID" json:"ClusterID,omitempty"`
	Nodes                []*NodeInfo              `protobuf:"bytes,4,rep,name=Nodes" json:"Nodes,omitempty"`
	Databases            []*DatabaseInfo          `protobuf:"bytes,5,rep,name=Databases" json:"Databases,omitempty"`
	Users                []*UserInfo              `protobuf:"bytes,6,rep,name=Users" json:"Users,omitempty"`
	MaxNodeID            *uint64                  `protobuf:"varint,7,req,name=MaxNodeID" json:"MaxNodeID,omitempty"`
	MaxShardGroupID      *uint64                  `protobuf:"varint,8,req,name=MaxShardGroupID" json:"MaxShardGroupID,omitempty"`
	MaxShardID           *uint64                  `protobuf:"varint,9,req,name=MaxShardID" json:"MaxShardID,omitempty"`
	DataNodes            []*DataNode              `protobuf:"bytes,10,rep,name=DataNodes" json:"DataNodes,omitempty"`
	MetaNodes            []*NodeInfo              `protobuf:"bytes,11,rep,name=MetaNodes" json:"MetaNodes,omitempty"`
	ClusterPtNum         *uint32                  `protobuf:"varint,14,req,name=ClusterPtNum" json:"ClusterPtNum,omitempty"`
	PtView               map[string]*DBPtInfo     `protobuf:"bytes,15,rep,name=PtView" json:"PtView,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	PtNumPerNode         *uint32                  `protobuf:"varint,16,opt,name=PtNumPerNode" json:"PtNumPerNode,omitempty"`
	MaxIndexGroupID      *uint64                  `protobuf:"varint,17,req,name=MaxIndexGroupID" json:"MaxIndexGroupID,omitempty"`
	MaxIndexID           *uint64                  `protobuf:"varint,18,req,name=MaxIndexID" json:"MaxIndexID,omitempty"`
	MaxEventOpId         *uint64                  `protobuf:"varint,19,opt,name=MaxEventOpId" json:"MaxEventOpId,omitempty"`
	TakeOverEnabled      *bool                    `protobuf:"varint,20,opt,name=TakeOverEnabled" json:"TakeOverEnabled,omitempty"`
	MigrateEvents        []*MigrateEventInfo      `protobuf:"bytes,21,rep,name=MigrateEvents" json:"MigrateEvents,omitempty"`
	BalancerEnabled      *bool                    `protobuf:"varint,22,opt,name=BalancerEnabled" json:"BalancerEnabled,omitempty"`
	MaxDownSampleID      *uint64                  `protobuf:"varint,23,opt,name=MaxDownSampleID" json:"MaxDownSampleID,omitempty"`
	Streams              []*StreamInfo            `protobuf:"bytes,24,rep,name=Streams" json:"Streams,omitempty"`
	MaxStreamID          *uint64                  `protobuf:"varint,25,opt,name=MaxStreamID" json:"MaxStreamID,omitempty"`
	MaxConnId            *uint64                  `protobuf:"varint,26,opt,name=MaxConnId" json:"MaxConnId,omitempty"`
	QueryIDInit          map[string]uint64        `protobuf:"bytes,27,rep,name=QueryIDInit" json:"QueryIDInit,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	ReplicaGroups        map[string]*Replications `protobuf:"bytes,28,rep,name=ReplicaGroups" json:"ReplicaGroups,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MaxSubscriptionID    *uint64                  `protobuf:"varint,29,opt,name=MaxSubscriptionID" json:"MaxSubscriptionID,omitempty"`
	MaxCQChangeID        *uint64                  `protobuf:"varint,30,opt,name=MaxCQChangeID" json:"MaxCQChangeID,omitempty"`
	NumOfShards          *int32                   `protobuf:"varint,31,opt,name=NumOfShards" json:"NumOfShards,omitempty"`
	IsSQLiteEnabled      *bool                    `protobuf:"varint,32,opt,name=IsSQLiteEnabled" json:"IsSQLiteEnabled,omitempty"`
	SqlNodes             []*DataNode              `protobuf:"bytes,33,rep,name=SqlNodes" json:"SqlNodes,omitempty"`
	MaxMstID             *uint64                  `protobuf:"varint,34,opt,name=MaxMstID" json:"MaxMstID,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *Data) Reset()         { *m = Data{} }
func (m *Data) String() string { return proto.CompactTextString(m) }
func (*Data) ProtoMessage()    {}
func (*Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{0}
}
func (m *Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Data.Unmarshal(m, b)
}
func (m *Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Data.Marshal(b, m, deterministic)
}
func (m *Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Data.Merge(m, src)
}
func (m *Data) XXX_Size() int {
	return xxx_messageInfo_Data.Size(m)
}
func (m *Data) XXX_DiscardUnknown() {
	xxx_messageInfo_Data.DiscardUnknown(m)
}

var xxx_messageInfo_Data proto.InternalMessageInfo

func (m *Data) GetTerm() uint64 {
	if m != nil && m.Term != nil {
		return *m.Term
	}
	return 0
}

func (m *Data) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

func (m *Data) GetClusterID() uint64 {
	if m != nil && m.ClusterID != nil {
		return *m.ClusterID
	}
	return 0
}

func (m *Data) GetNodes() []*NodeInfo {
	if m != nil {
		return m.Nodes
	}
	return nil
}

func (m *Data) GetDatabases() []*DatabaseInfo {
	if m != nil {
		return m.Databases
	}
	return nil
}

func (m *Data) GetUsers() []*UserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *Data) GetMaxNodeID() uint64 {
	if m != nil && m.MaxNodeID != nil {
		return *m.MaxNodeID
	}
	return 0
}

func (m *Data) GetMaxShardGroupID() uint64 {
	if m != nil && m.MaxShardGroupID != nil {
		return *m.MaxShardGroupID
	}
	return 0
}

func (m *Data) GetMaxShardID() uint64 {
	if m != nil && m.MaxShardID != nil {
		return *m.MaxShardID
	}
	return 0
}

func (m *Data) GetDataNodes() []*DataNode {
	if m != nil {
		return m.DataNodes
	}
	return nil
}

func (m *Data) GetMetaNodes() []*NodeInfo {
	if m != nil {
		return m.MetaNodes
	}
	return nil
}

func (m *Data) GetClusterPtNum() uint32 {
	if m != nil && m.ClusterPtNum != nil {
		return *m.ClusterPtNum
	}
	return 0
}

func (m *Data) GetPtView() map[string]*DBPtInfo {
	if m != nil {
		return m.PtView
	}
	return nil
}

func (m *Data) GetPtNumPerNode() uint32 {
	if m != nil && m.PtNumPerNode != nil {
		return *m.PtNumPerNode
	}
	return 0
}

func (m *Data) GetMaxIndexGroupID() uint64 {
	if m != nil && m.MaxIndexGroupID != nil {
		return *m.MaxIndexGroupID
	}
	return 0
}

func (m *Data) GetMaxIndexID() uint64 {
	if m != nil && m.MaxIndexID != nil {
		return *m.MaxIndexID
	}
	return 0
}

func (m *Data) GetMaxEventOpId() uint64 {
	if m != nil && m.MaxEventOpId != nil {
		return *m.MaxEventOpId
	}
	return 0
}

func (m *Data) GetTakeOverEnabled() bool {
	if m != nil && m.TakeOverEnabled != nil {
		return *m.TakeOverEnabled
	}
	return false
}

func (m *Data) GetMigrateEvents() []*MigrateEventInfo {
	if m != nil {
		return m.MigrateEvents
	}
	return nil
}

func (m *Data) GetBalancerEnabled() bool {
	if m != nil && m.BalancerEnabled != nil {
		return *m.BalancerEnabled
	}
	return false
}

func (m *Data) GetMaxDownSampleID() uint64 {
	if m != nil && m.MaxDownSampleID != nil {
		return *m.MaxDownSampleID
	}
	return 0
}

func (m *Data) GetStreams() []*StreamInfo {
	if m != nil {
		return m.Streams
	}
	return nil
}

func (m *Data) GetMaxStreamID() uint64 {
	if m != nil && m.MaxStreamID != nil {
		return *m.MaxStreamID
	}
	return 0
}

func (m *Data) GetMaxConnId() uint64 {
	if m != nil && m.MaxConnId != nil {
		return *m.MaxConnId
	}
	return 0
}

func (m *Data) GetQueryIDInit() map[string]uint64 {
	if m != nil {
		return m.QueryIDInit
	}
	return nil
}

func (m *Data) GetReplicaGroups() map[string]*Replications {
	if m != nil {
		return m.ReplicaGroups
	}
	return nil
}

func (m *Data) GetMaxSubscriptionID() uint64 {
	if m != nil && m.MaxSubscriptionID != nil {
		return *m.MaxSubscriptionID
	}
	return 0
}

func (m *Data) GetMaxCQChangeID() uint64 {
	if m != nil && m.MaxCQChangeID != nil {
		return *m.MaxCQChangeID
	}
	return 0
}

func (m *Data) GetNumOfShards() int32 {
	if m != nil && m.NumOfShards != nil {
		return *m.NumOfShards
	}
	return 0
}

func (m *Data) GetIsSQLiteEnabled() bool {
	if m != nil && m.IsSQLiteEnabled != nil {
		return *m.IsSQLiteEnabled
	}
	return false
}

func (m *Data) GetSqlNodes() []*DataNode {
	if m != nil {
		return m.SqlNodes
	}
	return nil
}

func (m *Data) GetMaxMstID() uint64 {
	if m != nil && m.MaxMstID != nil {
		return *m.MaxMstID
	}
	return 0
}

type Replications struct {
	Groups               []*ReplicaGroup `protobuf:"bytes,1,rep,name=Groups" json:"Groups,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *Replications) Reset()         { *m = Replications{} }
func (m *Replications) String() string { return proto.CompactTextString(m) }
func (*Replications) ProtoMessage()    {}
func (*Replications) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{1}
}
func (m *Replications) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Replications.Unmarshal(m, b)
}
func (m *Replications) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Replications.Marshal(b, m, deterministic)
}
func (m *Replications) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Replications.Merge(m, src)
}
func (m *Replications) XXX_Size() int {
	return xxx_messageInfo_Replications.Size(m)
}
func (m *Replications) XXX_DiscardUnknown() {
	xxx_messageInfo_Replications.DiscardUnknown(m)
}

var xxx_messageInfo_Replications proto.InternalMessageInfo

func (m *Replications) GetGroups() []*ReplicaGroup {
	if m != nil {
		return m.Groups
	}
	return nil
}

type ReplicaGroup struct {
	ID                   *uint32  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	MasterID             *uint32  `protobuf:"varint,2,req,name=MasterID" json:"MasterID,omitempty"`
	Peers                []*Peer  `protobuf:"bytes,3,rep,name=Peers" json:"Peers,omitempty"`
	Status               *uint32  `protobuf:"varint,4,req,name=Status" json:"Status,omitempty"`
	Term                 *uint64  `protobuf:"varint,5,req,name=Term" json:"Term,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplicaGroup) Reset()         { *m = ReplicaGroup{} }
func (m *ReplicaGroup) String() string { return proto.CompactTextString(m) }
func (*ReplicaGroup) ProtoMessage()    {}
func (*ReplicaGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{2}
}
func (m *ReplicaGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplicaGroup.Unmarshal(m, b)
}
func (m *ReplicaGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplicaGroup.Marshal(b, m, deterministic)
}
func (m *ReplicaGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplicaGroup.Merge(m, src)
}
func (m *ReplicaGroup) XXX_Size() int {
	return xxx_messageInfo_ReplicaGroup.Size(m)
}
func (m *ReplicaGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplicaGroup.DiscardUnknown(m)
}

var xxx_messageInfo_ReplicaGroup proto.InternalMessageInfo

func (m *ReplicaGroup) GetID() uint32 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *ReplicaGroup) GetMasterID() uint32 {
	if m != nil && m.MasterID != nil {
		return *m.MasterID
	}
	return 0
}

func (m *ReplicaGroup) GetPeers() []*Peer {
	if m != nil {
		return m.Peers
	}
	return nil
}

func (m *ReplicaGroup) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *ReplicaGroup) GetTerm() uint64 {
	if m != nil && m.Term != nil {
		return *m.Term
	}
	return 0
}

type Peer struct {
	ID                   *uint32  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Role                 *uint32  `protobuf:"varint,2,req,name=Role" json:"Role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Peer) Reset()         { *m = Peer{} }
func (m *Peer) String() string { return proto.CompactTextString(m) }
func (*Peer) ProtoMessage()    {}
func (*Peer) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{3}
}
func (m *Peer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Peer.Unmarshal(m, b)
}
func (m *Peer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Peer.Marshal(b, m, deterministic)
}
func (m *Peer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Peer.Merge(m, src)
}
func (m *Peer) XXX_Size() int {
	return xxx_messageInfo_Peer.Size(m)
}
func (m *Peer) XXX_DiscardUnknown() {
	xxx_messageInfo_Peer.DiscardUnknown(m)
}

var xxx_messageInfo_Peer proto.InternalMessageInfo

func (m *Peer) GetID() uint32 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *Peer) GetRole() uint32 {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return 0
}

type PtOwner struct {
	NodeID               *uint64  `protobuf:"varint,1,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PtOwner) Reset()         { *m = PtOwner{} }
func (m *PtOwner) String() string { return proto.CompactTextString(m) }
func (*PtOwner) ProtoMessage()    {}
func (*PtOwner) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{4}
}
func (m *PtOwner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PtOwner.Unmarshal(m, b)
}
func (m *PtOwner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PtOwner.Marshal(b, m, deterministic)
}
func (m *PtOwner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PtOwner.Merge(m, src)
}
func (m *PtOwner) XXX_Size() int {
	return xxx_messageInfo_PtOwner.Size(m)
}
func (m *PtOwner) XXX_DiscardUnknown() {
	xxx_messageInfo_PtOwner.DiscardUnknown(m)
}

var xxx_messageInfo_PtOwner proto.InternalMessageInfo

func (m *PtOwner) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

type PtInfo struct {
	Owner                *PtOwner `protobuf:"bytes,1,req,name=Owner" json:"Owner,omitempty"`
	Status               *uint32  `protobuf:"varint,2,req,name=Status" json:"Status,omitempty"`
	PtId                 *uint32  `protobuf:"varint,3,req,name=PtId" json:"PtId,omitempty"`
	Ver                  *uint64  `protobuf:"varint,4,opt,name=Ver" json:"Ver,omitempty"`
	RGID                 *uint32  `protobuf:"varint,5,opt,name=RGID" json:"RGID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PtInfo) Reset()         { *m = PtInfo{} }
func (m *PtInfo) String() string { return proto.CompactTextString(m) }
func (*PtInfo) ProtoMessage()    {}
func (*PtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{5}
}
func (m *PtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PtInfo.Unmarshal(m, b)
}
func (m *PtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PtInfo.Marshal(b, m, deterministic)
}
func (m *PtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PtInfo.Merge(m, src)
}
func (m *PtInfo) XXX_Size() int {
	return xxx_messageInfo_PtInfo.Size(m)
}
func (m *PtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PtInfo proto.InternalMessageInfo

func (m *PtInfo) GetOwner() *PtOwner {
	if m != nil {
		return m.Owner
	}
	return nil
}

func (m *PtInfo) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *PtInfo) GetPtId() uint32 {
	if m != nil && m.PtId != nil {
		return *m.PtId
	}
	return 0
}

func (m *PtInfo) GetVer() uint64 {
	if m != nil && m.Ver != nil {
		return *m.Ver
	}
	return 0
}

func (m *PtInfo) GetRGID() uint32 {
	if m != nil && m.RGID != nil {
		return *m.RGID
	}
	return 0
}

type DBPtInfo struct {
	DbPt                 []*PtInfo `protobuf:"bytes,1,rep,name=DbPt" json:"DbPt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DBPtInfo) Reset()         { *m = DBPtInfo{} }
func (m *DBPtInfo) String() string { return proto.CompactTextString(m) }
func (*DBPtInfo) ProtoMessage()    {}
func (*DBPtInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{6}
}
func (m *DBPtInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DBPtInfo.Unmarshal(m, b)
}
func (m *DBPtInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DBPtInfo.Marshal(b, m, deterministic)
}
func (m *DBPtInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DBPtInfo.Merge(m, src)
}
func (m *DBPtInfo) XXX_Size() int {
	return xxx_messageInfo_DBPtInfo.Size(m)
}
func (m *DBPtInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DBPtInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DBPtInfo proto.InternalMessageInfo

func (m *DBPtInfo) GetDbPt() []*PtInfo {
	if m != nil {
		return m.DbPt
	}
	return nil
}

type NodeInfo struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Host                 *string  `protobuf:"bytes,2,req,name=Host" json:"Host,omitempty"`
	RPCAddr              *string  `protobuf:"bytes,5,opt,name=RPCAddr" json:"RPCAddr,omitempty"`
	TCPHost              *string  `protobuf:"bytes,3,opt,name=TCPHost" json:"TCPHost,omitempty"`
	Status               *int64   `protobuf:"varint,4,req,name=Status" json:"Status,omitempty"`
	LTime                *uint64  `protobuf:"varint,6,req,name=LTime" json:"LTime,omitempty"`
	GossipAddr           *string  `protobuf:"bytes,7,req,name=GossipAddr" json:"GossipAddr,omitempty"`
	SegregateStatus      *uint64  `protobuf:"varint,8,opt,name=SegregateStatus" json:"SegregateStatus,omitempty"`
	Role                 *string  `protobuf:"bytes,10,opt,name=Role" json:"Role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NodeInfo) Reset()         { *m = NodeInfo{} }
func (m *NodeInfo) String() string { return proto.CompactTextString(m) }
func (*NodeInfo) ProtoMessage()    {}
func (*NodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{7}
}
func (m *NodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NodeInfo.Unmarshal(m, b)
}
func (m *NodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NodeInfo.Marshal(b, m, deterministic)
}
func (m *NodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NodeInfo.Merge(m, src)
}
func (m *NodeInfo) XXX_Size() int {
	return xxx_messageInfo_NodeInfo.Size(m)
}
func (m *NodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NodeInfo proto.InternalMessageInfo

func (m *NodeInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *NodeInfo) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

func (m *NodeInfo) GetRPCAddr() string {
	if m != nil && m.RPCAddr != nil {
		return *m.RPCAddr
	}
	return ""
}

func (m *NodeInfo) GetTCPHost() string {
	if m != nil && m.TCPHost != nil {
		return *m.TCPHost
	}
	return ""
}

func (m *NodeInfo) GetStatus() int64 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *NodeInfo) GetLTime() uint64 {
	if m != nil && m.LTime != nil {
		return *m.LTime
	}
	return 0
}

func (m *NodeInfo) GetGossipAddr() string {
	if m != nil && m.GossipAddr != nil {
		return *m.GossipAddr
	}
	return ""
}

func (m *NodeInfo) GetSegregateStatus() uint64 {
	if m != nil && m.SegregateStatus != nil {
		return *m.SegregateStatus
	}
	return 0
}

func (m *NodeInfo) GetRole() string {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return ""
}

type DataNode struct {
	Ni                   *NodeInfo `protobuf:"bytes,1,req,name=Ni" json:"Ni,omitempty"`
	ConnID               *uint64   `protobuf:"varint,2,opt,name=ConnID" json:"ConnID,omitempty"`
	AliveConnID          *uint64   `protobuf:"varint,3,opt,name=AliveConnID" json:"AliveConnID,omitempty"`
	Az                   *string   `protobuf:"bytes,4,opt,name=Az" json:"Az,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *DataNode) Reset()         { *m = DataNode{} }
func (m *DataNode) String() string { return proto.CompactTextString(m) }
func (*DataNode) ProtoMessage()    {}
func (*DataNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{8}
}
func (m *DataNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataNode.Unmarshal(m, b)
}
func (m *DataNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataNode.Marshal(b, m, deterministic)
}
func (m *DataNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataNode.Merge(m, src)
}
func (m *DataNode) XXX_Size() int {
	return xxx_messageInfo_DataNode.Size(m)
}
func (m *DataNode) XXX_DiscardUnknown() {
	xxx_messageInfo_DataNode.DiscardUnknown(m)
}

var xxx_messageInfo_DataNode proto.InternalMessageInfo

func (m *DataNode) GetNi() *NodeInfo {
	if m != nil {
		return m.Ni
	}
	return nil
}

func (m *DataNode) GetConnID() uint64 {
	if m != nil && m.ConnID != nil {
		return *m.ConnID
	}
	return 0
}

func (m *DataNode) GetAliveConnID() uint64 {
	if m != nil && m.AliveConnID != nil {
		return *m.AliveConnID
	}
	return 0
}

func (m *DataNode) GetAz() string {
	if m != nil && m.Az != nil {
		return *m.Az
	}
	return ""
}

type DatabaseInfo struct {
	Name                   *string                `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	DefaultRetentionPolicy *string                `protobuf:"bytes,2,req,name=DefaultRetentionPolicy" json:"DefaultRetentionPolicy,omitempty"`
	RetentionPolicies      []*RetentionPolicyInfo `protobuf:"bytes,3,rep,name=RetentionPolicies" json:"RetentionPolicies,omitempty"`
	ContinuousQueries      []*ContinuousQueryInfo `protobuf:"bytes,4,rep,name=ContinuousQueries" json:"ContinuousQueries,omitempty"`
	MarkDeleted            *bool                  `protobuf:"varint,5,opt,name=MarkDeleted" json:"MarkDeleted,omitempty"`
	ShardKey               *ShardKeyInfo          `protobuf:"bytes,6,opt,name=ShardKey" json:"ShardKey,omitempty"`
	EnableTagArray         *bool                  `protobuf:"varint,7,opt,name=EnableTagArray" json:"EnableTagArray,omitempty"`
	ReplicaN               *int64                 `protobuf:"varint,8,opt,name=ReplicaN" json:"ReplicaN,omitempty"`
	Options                *ObsOptions            `protobuf:"bytes,21,opt,name=Options" json:"Options,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}               `json:"-"`
	XXX_unrecognized       []byte                 `json:"-"`
	XXX_sizecache          int32                  `json:"-"`
}

func (m *DatabaseInfo) Reset()         { *m = DatabaseInfo{} }
func (m *DatabaseInfo) String() string { return proto.CompactTextString(m) }
func (*DatabaseInfo) ProtoMessage()    {}
func (*DatabaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{9}
}
func (m *DatabaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatabaseInfo.Unmarshal(m, b)
}
func (m *DatabaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatabaseInfo.Marshal(b, m, deterministic)
}
func (m *DatabaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatabaseInfo.Merge(m, src)
}
func (m *DatabaseInfo) XXX_Size() int {
	return xxx_messageInfo_DatabaseInfo.Size(m)
}
func (m *DatabaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DatabaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DatabaseInfo proto.InternalMessageInfo

func (m *DatabaseInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DatabaseInfo) GetDefaultRetentionPolicy() string {
	if m != nil && m.DefaultRetentionPolicy != nil {
		return *m.DefaultRetentionPolicy
	}
	return ""
}

func (m *DatabaseInfo) GetRetentionPolicies() []*RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicies
	}
	return nil
}

func (m *DatabaseInfo) GetContinuousQueries() []*ContinuousQueryInfo {
	if m != nil {
		return m.ContinuousQueries
	}
	return nil
}

func (m *DatabaseInfo) GetMarkDeleted() bool {
	if m != nil && m.MarkDeleted != nil {
		return *m.MarkDeleted
	}
	return false
}

func (m *DatabaseInfo) GetShardKey() *ShardKeyInfo {
	if m != nil {
		return m.ShardKey
	}
	return nil
}

func (m *DatabaseInfo) GetEnableTagArray() bool {
	if m != nil && m.EnableTagArray != nil {
		return *m.EnableTagArray
	}
	return false
}

func (m *DatabaseInfo) GetReplicaN() int64 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *DatabaseInfo) GetOptions() *ObsOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

type RetentionPolicySpec struct {
	Name                 *string  `protobuf:"bytes,1,opt,name=Name" json:"Name,omitempty"`
	Duration             *int64   `protobuf:"varint,2,opt,name=Duration" json:"Duration,omitempty"`
	ShardGroupDuration   *int64   `protobuf:"varint,3,opt,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	ReplicaN             *uint32  `protobuf:"varint,4,opt,name=ReplicaN" json:"ReplicaN,omitempty"`
	WarmDuration         *int64   `protobuf:"varint,5,opt,name=WarmDuration" json:"WarmDuration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RetentionPolicySpec) Reset()         { *m = RetentionPolicySpec{} }
func (m *RetentionPolicySpec) String() string { return proto.CompactTextString(m) }
func (*RetentionPolicySpec) ProtoMessage()    {}
func (*RetentionPolicySpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{10}
}
func (m *RetentionPolicySpec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetentionPolicySpec.Unmarshal(m, b)
}
func (m *RetentionPolicySpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetentionPolicySpec.Marshal(b, m, deterministic)
}
func (m *RetentionPolicySpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetentionPolicySpec.Merge(m, src)
}
func (m *RetentionPolicySpec) XXX_Size() int {
	return xxx_messageInfo_RetentionPolicySpec.Size(m)
}
func (m *RetentionPolicySpec) XXX_DiscardUnknown() {
	xxx_messageInfo_RetentionPolicySpec.DiscardUnknown(m)
}

var xxx_messageInfo_RetentionPolicySpec proto.InternalMessageInfo

func (m *RetentionPolicySpec) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *RetentionPolicySpec) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *RetentionPolicySpec) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *RetentionPolicySpec) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *RetentionPolicySpec) GetWarmDuration() int64 {
	if m != nil && m.WarmDuration != nil {
		return *m.WarmDuration
	}
	return 0
}

type MeasurementInfo struct {
	Name                 *string               `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	ShardKeys            []*ShardKeyInfo       `protobuf:"bytes,2,rep,name=ShardKeys" json:"ShardKeys,omitempty"`
	Schema               map[string]int32      `protobuf:"bytes,3,rep,name=Schema" json:"Schema,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	MarkDeleted          *bool                 `protobuf:"varint,4,opt,name=MarkDeleted" json:"MarkDeleted,omitempty"`
	IndexRelation        *IndexRelation        `protobuf:"bytes,5,opt,name=indexRelation" json:"indexRelation,omitempty"`
	EngineType           *uint32               `protobuf:"varint,6,opt,name=EngineType" json:"EngineType,omitempty"`
	ColStoreInfo         *ColStoreInfo         `protobuf:"bytes,7,opt,name=ColStoreInfo" json:"ColStoreInfo,omitempty"`
	ObsOptions           *ObsOptions           `protobuf:"bytes,8,opt,name=ObsOptions" json:"ObsOptions,omitempty"`
	ShardIdxes           map[uint64]*Idxes     `protobuf:"bytes,9,rep,name=ShardIdxes" json:"ShardIdxes,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	InitNumOfShards      *int32                `protobuf:"varint,10,opt,name=InitNumOfShards" json:"InitNumOfShards,omitempty"`
	ID                   *uint64               `protobuf:"varint,11,opt,name=ID" json:"ID,omitempty"`
	Options              *Options              `protobuf:"bytes,21,opt,name=Options" json:"Options,omitempty"`
	SchemaUseForClean    map[string]*SchemaVal `protobuf:"bytes,22,rep,name=SchemaUseForClean" json:"SchemaUseForClean,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MeasurementInfo) Reset()         { *m = MeasurementInfo{} }
func (m *MeasurementInfo) String() string { return proto.CompactTextString(m) }
func (*MeasurementInfo) ProtoMessage()    {}
func (*MeasurementInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{11}
}
func (m *MeasurementInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementInfo.Unmarshal(m, b)
}
func (m *MeasurementInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementInfo.Marshal(b, m, deterministic)
}
func (m *MeasurementInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementInfo.Merge(m, src)
}
func (m *MeasurementInfo) XXX_Size() int {
	return xxx_messageInfo_MeasurementInfo.Size(m)
}
func (m *MeasurementInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementInfo proto.InternalMessageInfo

func (m *MeasurementInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *MeasurementInfo) GetShardKeys() []*ShardKeyInfo {
	if m != nil {
		return m.ShardKeys
	}
	return nil
}

func (m *MeasurementInfo) GetSchema() map[string]int32 {
	if m != nil {
		return m.Schema
	}
	return nil
}

func (m *MeasurementInfo) GetMarkDeleted() bool {
	if m != nil && m.MarkDeleted != nil {
		return *m.MarkDeleted
	}
	return false
}

func (m *MeasurementInfo) GetIndexRelation() *IndexRelation {
	if m != nil {
		return m.IndexRelation
	}
	return nil
}

func (m *MeasurementInfo) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func (m *MeasurementInfo) GetColStoreInfo() *ColStoreInfo {
	if m != nil {
		return m.ColStoreInfo
	}
	return nil
}

func (m *MeasurementInfo) GetObsOptions() *ObsOptions {
	if m != nil {
		return m.ObsOptions
	}
	return nil
}

func (m *MeasurementInfo) GetShardIdxes() map[uint64]*Idxes {
	if m != nil {
		return m.ShardIdxes
	}
	return nil
}

func (m *MeasurementInfo) GetInitNumOfShards() int32 {
	if m != nil && m.InitNumOfShards != nil {
		return *m.InitNumOfShards
	}
	return 0
}

func (m *MeasurementInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *MeasurementInfo) GetOptions() *Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *MeasurementInfo) GetSchemaUseForClean() map[string]*SchemaVal {
	if m != nil {
		return m.SchemaUseForClean
	}
	return nil
}

type SchemaVal struct {
	Typ                  *int32   `protobuf:"varint,1,req,name=Typ" json:"Typ,omitempty"`
	EndTime              *int32   `protobuf:"varint,2,opt,name=EndTime" json:"EndTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SchemaVal) Reset()         { *m = SchemaVal{} }
func (m *SchemaVal) String() string { return proto.CompactTextString(m) }
func (*SchemaVal) ProtoMessage()    {}
func (*SchemaVal) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{12}
}
func (m *SchemaVal) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SchemaVal.Unmarshal(m, b)
}
func (m *SchemaVal) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SchemaVal.Marshal(b, m, deterministic)
}
func (m *SchemaVal) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SchemaVal.Merge(m, src)
}
func (m *SchemaVal) XXX_Size() int {
	return xxx_messageInfo_SchemaVal.Size(m)
}
func (m *SchemaVal) XXX_DiscardUnknown() {
	xxx_messageInfo_SchemaVal.DiscardUnknown(m)
}

var xxx_messageInfo_SchemaVal proto.InternalMessageInfo

func (m *SchemaVal) GetTyp() int32 {
	if m != nil && m.Typ != nil {
		return *m.Typ
	}
	return 0
}

func (m *SchemaVal) GetEndTime() int32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type RetentionPolicyInfo struct {
	Name                 *string               `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Duration             *int64                `protobuf:"varint,2,req,name=Duration" json:"Duration,omitempty"`
	ShardGroupDuration   *int64                `protobuf:"varint,3,req,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	ReplicaN             *uint32               `protobuf:"varint,4,req,name=ReplicaN" json:"ReplicaN,omitempty"`
	Measurements         []*MeasurementInfo    `protobuf:"bytes,5,rep,name=Measurements" json:"Measurements,omitempty"`
	MstVersions          map[string]uint32     `protobuf:"bytes,14,rep,name=MstVersions" json:"MstVersions,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	ShardGroups          []*ShardGroupInfo     `protobuf:"bytes,6,rep,name=ShardGroups" json:"ShardGroups,omitempty"`
	Subscriptions        []*SubscriptionInfo   `protobuf:"bytes,7,rep,name=Subscriptions" json:"Subscriptions,omitempty"`
	MarkDeleted          *bool                 `protobuf:"varint,8,opt,name=MarkDeleted" json:"MarkDeleted,omitempty"`
	HotDuration          *int64                `protobuf:"varint,9,req,name=HotDuration" json:"HotDuration,omitempty"`
	WarmDuration         *int64                `protobuf:"varint,10,req,name=WarmDuration" json:"WarmDuration,omitempty"`
	IndexGroupDuration   *int64                `protobuf:"varint,11,req,name=IndexGroupDuration" json:"IndexGroupDuration,omitempty"`
	IndexGroups          []*IndexGroupInfo     `protobuf:"bytes,12,rep,name=IndexGroups" json:"IndexGroups,omitempty"`
	DownSamplePolicyInfo *DownSamplePolicyInfo `protobuf:"bytes,13,opt,name=DownSamplePolicyInfo" json:"DownSamplePolicyInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RetentionPolicyInfo) Reset()         { *m = RetentionPolicyInfo{} }
func (m *RetentionPolicyInfo) String() string { return proto.CompactTextString(m) }
func (*RetentionPolicyInfo) ProtoMessage()    {}
func (*RetentionPolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{13}
}
func (m *RetentionPolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetentionPolicyInfo.Unmarshal(m, b)
}
func (m *RetentionPolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetentionPolicyInfo.Marshal(b, m, deterministic)
}
func (m *RetentionPolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetentionPolicyInfo.Merge(m, src)
}
func (m *RetentionPolicyInfo) XXX_Size() int {
	return xxx_messageInfo_RetentionPolicyInfo.Size(m)
}
func (m *RetentionPolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RetentionPolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RetentionPolicyInfo proto.InternalMessageInfo

func (m *RetentionPolicyInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *RetentionPolicyInfo) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *RetentionPolicyInfo) GetMeasurements() []*MeasurementInfo {
	if m != nil {
		return m.Measurements
	}
	return nil
}

func (m *RetentionPolicyInfo) GetMstVersions() map[string]uint32 {
	if m != nil {
		return m.MstVersions
	}
	return nil
}

func (m *RetentionPolicyInfo) GetShardGroups() []*ShardGroupInfo {
	if m != nil {
		return m.ShardGroups
	}
	return nil
}

func (m *RetentionPolicyInfo) GetSubscriptions() []*SubscriptionInfo {
	if m != nil {
		return m.Subscriptions
	}
	return nil
}

func (m *RetentionPolicyInfo) GetMarkDeleted() bool {
	if m != nil && m.MarkDeleted != nil {
		return *m.MarkDeleted
	}
	return false
}

func (m *RetentionPolicyInfo) GetHotDuration() int64 {
	if m != nil && m.HotDuration != nil {
		return *m.HotDuration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetWarmDuration() int64 {
	if m != nil && m.WarmDuration != nil {
		return *m.WarmDuration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetIndexGroupDuration() int64 {
	if m != nil && m.IndexGroupDuration != nil {
		return *m.IndexGroupDuration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetIndexGroups() []*IndexGroupInfo {
	if m != nil {
		return m.IndexGroups
	}
	return nil
}

func (m *RetentionPolicyInfo) GetDownSamplePolicyInfo() *DownSamplePolicyInfo {
	if m != nil {
		return m.DownSamplePolicyInfo
	}
	return nil
}

type ContinuousQueryInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Query                *string  `protobuf:"bytes,2,req,name=Query" json:"Query,omitempty"`
	LastRunTime          *int64   `protobuf:"varint,3,opt,name=LastRunTime" json:"LastRunTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinuousQueryInfo) Reset()         { *m = ContinuousQueryInfo{} }
func (m *ContinuousQueryInfo) String() string { return proto.CompactTextString(m) }
func (*ContinuousQueryInfo) ProtoMessage()    {}
func (*ContinuousQueryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{14}
}
func (m *ContinuousQueryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinuousQueryInfo.Unmarshal(m, b)
}
func (m *ContinuousQueryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinuousQueryInfo.Marshal(b, m, deterministic)
}
func (m *ContinuousQueryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinuousQueryInfo.Merge(m, src)
}
func (m *ContinuousQueryInfo) XXX_Size() int {
	return xxx_messageInfo_ContinuousQueryInfo.Size(m)
}
func (m *ContinuousQueryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinuousQueryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ContinuousQueryInfo proto.InternalMessageInfo

func (m *ContinuousQueryInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ContinuousQueryInfo) GetQuery() string {
	if m != nil && m.Query != nil {
		return *m.Query
	}
	return ""
}

func (m *ContinuousQueryInfo) GetLastRunTime() int64 {
	if m != nil && m.LastRunTime != nil {
		return *m.LastRunTime
	}
	return 0
}

type ShardGroupInfo struct {
	ID                   *uint64      `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	StartTime            *int64       `protobuf:"varint,2,req,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64       `protobuf:"varint,3,req,name=EndTime" json:"EndTime,omitempty"`
	DeletedAt            *int64       `protobuf:"varint,4,req,name=DeletedAt" json:"DeletedAt,omitempty"`
	Shards               []*ShardInfo `protobuf:"bytes,5,rep,name=Shards" json:"Shards,omitempty"`
	TruncatedAt          *int64       `protobuf:"varint,6,opt,name=TruncatedAt" json:"TruncatedAt,omitempty"`
	EngineType           *uint32      `protobuf:"varint,7,opt,name=EngineType" json:"EngineType,omitempty"`
	Version              *uint32      `protobuf:"varint,12,opt,name=version" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ShardGroupInfo) Reset()         { *m = ShardGroupInfo{} }
func (m *ShardGroupInfo) String() string { return proto.CompactTextString(m) }
func (*ShardGroupInfo) ProtoMessage()    {}
func (*ShardGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{15}
}
func (m *ShardGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardGroupInfo.Unmarshal(m, b)
}
func (m *ShardGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardGroupInfo.Marshal(b, m, deterministic)
}
func (m *ShardGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardGroupInfo.Merge(m, src)
}
func (m *ShardGroupInfo) XXX_Size() int {
	return xxx_messageInfo_ShardGroupInfo.Size(m)
}
func (m *ShardGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardGroupInfo proto.InternalMessageInfo

func (m *ShardGroupInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *ShardGroupInfo) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *ShardGroupInfo) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *ShardGroupInfo) GetDeletedAt() int64 {
	if m != nil && m.DeletedAt != nil {
		return *m.DeletedAt
	}
	return 0
}

func (m *ShardGroupInfo) GetShards() []*ShardInfo {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *ShardGroupInfo) GetTruncatedAt() int64 {
	if m != nil && m.TruncatedAt != nil {
		return *m.TruncatedAt
	}
	return 0
}

func (m *ShardGroupInfo) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func (m *ShardGroupInfo) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

type ShardInfo struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	OwnerIDs             []uint32 `protobuf:"varint,2,rep,name=OwnerIDs" json:"OwnerIDs,omitempty"` // Deprecated: Do not use.
	Min                  *string  `protobuf:"bytes,3,req,name=Min" json:"Min,omitempty"`
	Max                  *string  `protobuf:"bytes,4,req,name=Max" json:"Max,omitempty"`
	Tier                 *uint64  `protobuf:"varint,5,req,name=Tier" json:"Tier,omitempty"`
	IndexID              *uint64  `protobuf:"varint,6,req,name=IndexID" json:"IndexID,omitempty"`
	DownSampleLevel      *int64   `protobuf:"varint,7,req,name=DownSampleLevel" json:"DownSampleLevel,omitempty"`
	DownSampleID         *uint64  `protobuf:"varint,8,opt,name=DownSampleID" json:"DownSampleID,omitempty"`
	ReadOnly             *bool    `protobuf:"varint,9,opt,name=ReadOnly" json:"ReadOnly,omitempty"`
	MarkDelete           *bool    `protobuf:"varint,10,opt,name=MarkDelete" json:"MarkDelete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardInfo) Reset()         { *m = ShardInfo{} }
func (m *ShardInfo) String() string { return proto.CompactTextString(m) }
func (*ShardInfo) ProtoMessage()    {}
func (*ShardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{16}
}
func (m *ShardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardInfo.Unmarshal(m, b)
}
func (m *ShardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardInfo.Marshal(b, m, deterministic)
}
func (m *ShardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardInfo.Merge(m, src)
}
func (m *ShardInfo) XXX_Size() int {
	return xxx_messageInfo_ShardInfo.Size(m)
}
func (m *ShardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardInfo proto.InternalMessageInfo

func (m *ShardInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

// Deprecated: Do not use.
func (m *ShardInfo) GetOwnerIDs() []uint32 {
	if m != nil {
		return m.OwnerIDs
	}
	return nil
}

func (m *ShardInfo) GetMin() string {
	if m != nil && m.Min != nil {
		return *m.Min
	}
	return ""
}

func (m *ShardInfo) GetMax() string {
	if m != nil && m.Max != nil {
		return *m.Max
	}
	return ""
}

func (m *ShardInfo) GetTier() uint64 {
	if m != nil && m.Tier != nil {
		return *m.Tier
	}
	return 0
}

func (m *ShardInfo) GetIndexID() uint64 {
	if m != nil && m.IndexID != nil {
		return *m.IndexID
	}
	return 0
}

func (m *ShardInfo) GetDownSampleLevel() int64 {
	if m != nil && m.DownSampleLevel != nil {
		return *m.DownSampleLevel
	}
	return 0
}

func (m *ShardInfo) GetDownSampleID() uint64 {
	if m != nil && m.DownSampleID != nil {
		return *m.DownSampleID
	}
	return 0
}

func (m *ShardInfo) GetReadOnly() bool {
	if m != nil && m.ReadOnly != nil {
		return *m.ReadOnly
	}
	return false
}

func (m *ShardInfo) GetMarkDelete() bool {
	if m != nil && m.MarkDelete != nil {
		return *m.MarkDelete
	}
	return false
}

type ShardKeyInfo struct {
	ShardKey             []string `protobuf:"bytes,1,rep,name=ShardKey" json:"ShardKey,omitempty"`
	Type                 *string  `protobuf:"bytes,2,opt,name=Type" json:"Type,omitempty"`
	SgID                 *uint64  `protobuf:"varint,3,opt,name=SgID" json:"SgID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardKeyInfo) Reset()         { *m = ShardKeyInfo{} }
func (m *ShardKeyInfo) String() string { return proto.CompactTextString(m) }
func (*ShardKeyInfo) ProtoMessage()    {}
func (*ShardKeyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{17}
}
func (m *ShardKeyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardKeyInfo.Unmarshal(m, b)
}
func (m *ShardKeyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardKeyInfo.Marshal(b, m, deterministic)
}
func (m *ShardKeyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardKeyInfo.Merge(m, src)
}
func (m *ShardKeyInfo) XXX_Size() int {
	return xxx_messageInfo_ShardKeyInfo.Size(m)
}
func (m *ShardKeyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardKeyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardKeyInfo proto.InternalMessageInfo

func (m *ShardKeyInfo) GetShardKey() []string {
	if m != nil {
		return m.ShardKey
	}
	return nil
}

func (m *ShardKeyInfo) GetType() string {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return ""
}

func (m *ShardKeyInfo) GetSgID() uint64 {
	if m != nil && m.SgID != nil {
		return *m.SgID
	}
	return 0
}

type Idxes struct {
	Idx                  []int32  `protobuf:"varint,1,rep,name=Idx" json:"Idx,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Idxes) Reset()         { *m = Idxes{} }
func (m *Idxes) String() string { return proto.CompactTextString(m) }
func (*Idxes) ProtoMessage()    {}
func (*Idxes) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{18}
}
func (m *Idxes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Idxes.Unmarshal(m, b)
}
func (m *Idxes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Idxes.Marshal(b, m, deterministic)
}
func (m *Idxes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Idxes.Merge(m, src)
}
func (m *Idxes) XXX_Size() int {
	return xxx_messageInfo_Idxes.Size(m)
}
func (m *Idxes) XXX_DiscardUnknown() {
	xxx_messageInfo_Idxes.DiscardUnknown(m)
}

var xxx_messageInfo_Idxes proto.InternalMessageInfo

func (m *Idxes) GetIdx() []int32 {
	if m != nil {
		return m.Idx
	}
	return nil
}

type SubscriptionInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Mode                 *string  `protobuf:"bytes,2,req,name=Mode" json:"Mode,omitempty"`
	Destinations         []string `protobuf:"bytes,3,rep,name=Destinations" json:"Destinations,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscriptionInfo) Reset()         { *m = SubscriptionInfo{} }
func (m *SubscriptionInfo) String() string { return proto.CompactTextString(m) }
func (*SubscriptionInfo) ProtoMessage()    {}
func (*SubscriptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{19}
}
func (m *SubscriptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscriptionInfo.Unmarshal(m, b)
}
func (m *SubscriptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscriptionInfo.Marshal(b, m, deterministic)
}
func (m *SubscriptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscriptionInfo.Merge(m, src)
}
func (m *SubscriptionInfo) XXX_Size() int {
	return xxx_messageInfo_SubscriptionInfo.Size(m)
}
func (m *SubscriptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscriptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubscriptionInfo proto.InternalMessageInfo

func (m *SubscriptionInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *SubscriptionInfo) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *SubscriptionInfo) GetDestinations() []string {
	if m != nil {
		return m.Destinations
	}
	return nil
}

type ShardOwner struct {
	NodeID               *uint64  `protobuf:"varint,1,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardOwner) Reset()         { *m = ShardOwner{} }
func (m *ShardOwner) String() string { return proto.CompactTextString(m) }
func (*ShardOwner) ProtoMessage()    {}
func (*ShardOwner) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{20}
}
func (m *ShardOwner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardOwner.Unmarshal(m, b)
}
func (m *ShardOwner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardOwner.Marshal(b, m, deterministic)
}
func (m *ShardOwner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardOwner.Merge(m, src)
}
func (m *ShardOwner) XXX_Size() int {
	return xxx_messageInfo_ShardOwner.Size(m)
}
func (m *ShardOwner) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardOwner.DiscardUnknown(m)
}

var xxx_messageInfo_ShardOwner proto.InternalMessageInfo

func (m *ShardOwner) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

type UserInfo struct {
	Name                 *string          `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string          `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	Admin                *bool            `protobuf:"varint,3,req,name=Admin" json:"Admin,omitempty"`
	RwUser               *bool            `protobuf:"varint,4,opt,name=RwUser" json:"RwUser,omitempty"`
	Privileges           []*UserPrivilege `protobuf:"bytes,5,rep,name=Privileges" json:"Privileges,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{21}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (m *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(m, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UserInfo) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

func (m *UserInfo) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

func (m *UserInfo) GetRwUser() bool {
	if m != nil && m.RwUser != nil {
		return *m.RwUser
	}
	return false
}

func (m *UserInfo) GetPrivileges() []*UserPrivilege {
	if m != nil {
		return m.Privileges
	}
	return nil
}

type UserPrivilege struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Privilege            *int32   `protobuf:"varint,2,req,name=Privilege" json:"Privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{22}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (m *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(m, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UserPrivilege) GetPrivilege() int32 {
	if m != nil && m.Privilege != nil {
		return *m.Privilege
	}
	return 0
}

type IndexRelation struct {
	Rid                  *uint32         `protobuf:"varint,1,req,name=Rid" json:"Rid,omitempty"`
	Oid                  []uint32        `protobuf:"varint,2,rep,name=Oid" json:"Oid,omitempty"`
	IndexName            []string        `protobuf:"bytes,3,rep,name=IndexName" json:"IndexName,omitempty"`
	IndexLists           []*IndexList    `protobuf:"bytes,4,rep,name=IndexLists" json:"IndexLists,omitempty"`
	IndexOptions         []*IndexOptions `protobuf:"bytes,5,rep,name=IndexOptions" json:"IndexOptions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *IndexRelation) Reset()         { *m = IndexRelation{} }
func (m *IndexRelation) String() string { return proto.CompactTextString(m) }
func (*IndexRelation) ProtoMessage()    {}
func (*IndexRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{23}
}
func (m *IndexRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexRelation.Unmarshal(m, b)
}
func (m *IndexRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexRelation.Marshal(b, m, deterministic)
}
func (m *IndexRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexRelation.Merge(m, src)
}
func (m *IndexRelation) XXX_Size() int {
	return xxx_messageInfo_IndexRelation.Size(m)
}
func (m *IndexRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexRelation.DiscardUnknown(m)
}

var xxx_messageInfo_IndexRelation proto.InternalMessageInfo

func (m *IndexRelation) GetRid() uint32 {
	if m != nil && m.Rid != nil {
		return *m.Rid
	}
	return 0
}

func (m *IndexRelation) GetOid() []uint32 {
	if m != nil {
		return m.Oid
	}
	return nil
}

func (m *IndexRelation) GetIndexName() []string {
	if m != nil {
		return m.IndexName
	}
	return nil
}

func (m *IndexRelation) GetIndexLists() []*IndexList {
	if m != nil {
		return m.IndexLists
	}
	return nil
}

func (m *IndexRelation) GetIndexOptions() []*IndexOptions {
	if m != nil {
		return m.IndexOptions
	}
	return nil
}

type IndexList struct {
	IList                []string `protobuf:"bytes,1,rep,name=IList" json:"IList,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexList) Reset()         { *m = IndexList{} }
func (m *IndexList) String() string { return proto.CompactTextString(m) }
func (*IndexList) ProtoMessage()    {}
func (*IndexList) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{24}
}
func (m *IndexList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexList.Unmarshal(m, b)
}
func (m *IndexList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexList.Marshal(b, m, deterministic)
}
func (m *IndexList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexList.Merge(m, src)
}
func (m *IndexList) XXX_Size() int {
	return xxx_messageInfo_IndexList.Size(m)
}
func (m *IndexList) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexList.DiscardUnknown(m)
}

var xxx_messageInfo_IndexList proto.InternalMessageInfo

func (m *IndexList) GetIList() []string {
	if m != nil {
		return m.IList
	}
	return nil
}

type RpMeasurementsFieldsInfo struct {
	MeasurementInfos     []*MeasurementFieldsInfo `protobuf:"bytes,1,rep,name=MeasurementInfos" json:"MeasurementInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *RpMeasurementsFieldsInfo) Reset()         { *m = RpMeasurementsFieldsInfo{} }
func (m *RpMeasurementsFieldsInfo) String() string { return proto.CompactTextString(m) }
func (*RpMeasurementsFieldsInfo) ProtoMessage()    {}
func (*RpMeasurementsFieldsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{25}
}
func (m *RpMeasurementsFieldsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RpMeasurementsFieldsInfo.Unmarshal(m, b)
}
func (m *RpMeasurementsFieldsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RpMeasurementsFieldsInfo.Marshal(b, m, deterministic)
}
func (m *RpMeasurementsFieldsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RpMeasurementsFieldsInfo.Merge(m, src)
}
func (m *RpMeasurementsFieldsInfo) XXX_Size() int {
	return xxx_messageInfo_RpMeasurementsFieldsInfo.Size(m)
}
func (m *RpMeasurementsFieldsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RpMeasurementsFieldsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RpMeasurementsFieldsInfo proto.InternalMessageInfo

func (m *RpMeasurementsFieldsInfo) GetMeasurementInfos() []*MeasurementFieldsInfo {
	if m != nil {
		return m.MeasurementInfos
	}
	return nil
}

type MeasurementFieldsInfo struct {
	MstName              *string                  `protobuf:"bytes,1,req,name=MstName" json:"MstName,omitempty"`
	TypeFields           []*MeasurementTypeFields `protobuf:"bytes,2,rep,name=TypeFields" json:"TypeFields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MeasurementFieldsInfo) Reset()         { *m = MeasurementFieldsInfo{} }
func (m *MeasurementFieldsInfo) String() string { return proto.CompactTextString(m) }
func (*MeasurementFieldsInfo) ProtoMessage()    {}
func (*MeasurementFieldsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{26}
}
func (m *MeasurementFieldsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementFieldsInfo.Unmarshal(m, b)
}
func (m *MeasurementFieldsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementFieldsInfo.Marshal(b, m, deterministic)
}
func (m *MeasurementFieldsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementFieldsInfo.Merge(m, src)
}
func (m *MeasurementFieldsInfo) XXX_Size() int {
	return xxx_messageInfo_MeasurementFieldsInfo.Size(m)
}
func (m *MeasurementFieldsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementFieldsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementFieldsInfo proto.InternalMessageInfo

func (m *MeasurementFieldsInfo) GetMstName() string {
	if m != nil && m.MstName != nil {
		return *m.MstName
	}
	return ""
}

func (m *MeasurementFieldsInfo) GetTypeFields() []*MeasurementTypeFields {
	if m != nil {
		return m.TypeFields
	}
	return nil
}

type MeasurementTypeFields struct {
	Fields               []string `protobuf:"bytes,1,rep,name=Fields" json:"Fields,omitempty"`
	Type                 *int64   `protobuf:"varint,2,req,name=Type" json:"Type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementTypeFields) Reset()         { *m = MeasurementTypeFields{} }
func (m *MeasurementTypeFields) String() string { return proto.CompactTextString(m) }
func (*MeasurementTypeFields) ProtoMessage()    {}
func (*MeasurementTypeFields) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{27}
}
func (m *MeasurementTypeFields) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementTypeFields.Unmarshal(m, b)
}
func (m *MeasurementTypeFields) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementTypeFields.Marshal(b, m, deterministic)
}
func (m *MeasurementTypeFields) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementTypeFields.Merge(m, src)
}
func (m *MeasurementTypeFields) XXX_Size() int {
	return xxx_messageInfo_MeasurementTypeFields.Size(m)
}
func (m *MeasurementTypeFields) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementTypeFields.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementTypeFields proto.InternalMessageInfo

func (m *MeasurementTypeFields) GetFields() []string {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *MeasurementTypeFields) GetType() int64 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

type StreamInfo struct {
	Name                 *string                `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	ID                   *uint64                `protobuf:"varint,2,req,name=ID" json:"ID,omitempty"`
	SrcMst               *StreamMeasurementInfo `protobuf:"bytes,3,req,name=SrcMst" json:"SrcMst,omitempty"`
	DesMst               *StreamMeasurementInfo `protobuf:"bytes,4,req,name=DesMst" json:"DesMst,omitempty"`
	Interval             *int64                 `protobuf:"varint,5,req,name=Interval" json:"Interval,omitempty"`
	Delay                *int64                 `protobuf:"varint,6,req,name=Delay" json:"Delay,omitempty"`
	Dims                 []string               `protobuf:"bytes,7,rep,name=Dims" json:"Dims,omitempty"`
	Calls                []*StreamCall          `protobuf:"bytes,8,rep,name=Calls" json:"Calls,omitempty"`
	Cond                 *string                `protobuf:"bytes,9,opt,name=Cond" json:"Cond,omitempty"`
	IsSelectAll          *bool                  `protobuf:"varint,10,opt,name=IsSelectAll" json:"IsSelectAll,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *StreamInfo) Reset()         { *m = StreamInfo{} }
func (m *StreamInfo) String() string { return proto.CompactTextString(m) }
func (*StreamInfo) ProtoMessage()    {}
func (*StreamInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{28}
}
func (m *StreamInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamInfo.Unmarshal(m, b)
}
func (m *StreamInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamInfo.Marshal(b, m, deterministic)
}
func (m *StreamInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamInfo.Merge(m, src)
}
func (m *StreamInfo) XXX_Size() int {
	return xxx_messageInfo_StreamInfo.Size(m)
}
func (m *StreamInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StreamInfo proto.InternalMessageInfo

func (m *StreamInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *StreamInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *StreamInfo) GetSrcMst() *StreamMeasurementInfo {
	if m != nil {
		return m.SrcMst
	}
	return nil
}

func (m *StreamInfo) GetDesMst() *StreamMeasurementInfo {
	if m != nil {
		return m.DesMst
	}
	return nil
}

func (m *StreamInfo) GetInterval() int64 {
	if m != nil && m.Interval != nil {
		return *m.Interval
	}
	return 0
}

func (m *StreamInfo) GetDelay() int64 {
	if m != nil && m.Delay != nil {
		return *m.Delay
	}
	return 0
}

func (m *StreamInfo) GetDims() []string {
	if m != nil {
		return m.Dims
	}
	return nil
}

func (m *StreamInfo) GetCalls() []*StreamCall {
	if m != nil {
		return m.Calls
	}
	return nil
}

func (m *StreamInfo) GetCond() string {
	if m != nil && m.Cond != nil {
		return *m.Cond
	}
	return ""
}

func (m *StreamInfo) GetIsSelectAll() bool {
	if m != nil && m.IsSelectAll != nil {
		return *m.IsSelectAll
	}
	return false
}

type StreamInfos struct {
	Infos                []*StreamInfo `protobuf:"bytes,1,rep,name=Infos" json:"Infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *StreamInfos) Reset()         { *m = StreamInfos{} }
func (m *StreamInfos) String() string { return proto.CompactTextString(m) }
func (*StreamInfos) ProtoMessage()    {}
func (*StreamInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{29}
}
func (m *StreamInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamInfos.Unmarshal(m, b)
}
func (m *StreamInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamInfos.Marshal(b, m, deterministic)
}
func (m *StreamInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamInfos.Merge(m, src)
}
func (m *StreamInfos) XXX_Size() int {
	return xxx_messageInfo_StreamInfos.Size(m)
}
func (m *StreamInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamInfos.DiscardUnknown(m)
}

var xxx_messageInfo_StreamInfos proto.InternalMessageInfo

func (m *StreamInfos) GetInfos() []*StreamInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type StreamMeasurementInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,3,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StreamMeasurementInfo) Reset()         { *m = StreamMeasurementInfo{} }
func (m *StreamMeasurementInfo) String() string { return proto.CompactTextString(m) }
func (*StreamMeasurementInfo) ProtoMessage()    {}
func (*StreamMeasurementInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{30}
}
func (m *StreamMeasurementInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamMeasurementInfo.Unmarshal(m, b)
}
func (m *StreamMeasurementInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamMeasurementInfo.Marshal(b, m, deterministic)
}
func (m *StreamMeasurementInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamMeasurementInfo.Merge(m, src)
}
func (m *StreamMeasurementInfo) XXX_Size() int {
	return xxx_messageInfo_StreamMeasurementInfo.Size(m)
}
func (m *StreamMeasurementInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamMeasurementInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StreamMeasurementInfo proto.InternalMessageInfo

func (m *StreamMeasurementInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *StreamMeasurementInfo) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *StreamMeasurementInfo) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

type StreamCall struct {
	Call                 *string  `protobuf:"bytes,1,req,name=Call" json:"Call,omitempty"`
	Field                *string  `protobuf:"bytes,2,req,name=Field" json:"Field,omitempty"`
	Alias                *string  `protobuf:"bytes,3,req,name=Alias" json:"Alias,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StreamCall) Reset()         { *m = StreamCall{} }
func (m *StreamCall) String() string { return proto.CompactTextString(m) }
func (*StreamCall) ProtoMessage()    {}
func (*StreamCall) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{31}
}
func (m *StreamCall) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamCall.Unmarshal(m, b)
}
func (m *StreamCall) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamCall.Marshal(b, m, deterministic)
}
func (m *StreamCall) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamCall.Merge(m, src)
}
func (m *StreamCall) XXX_Size() int {
	return xxx_messageInfo_StreamCall.Size(m)
}
func (m *StreamCall) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamCall.DiscardUnknown(m)
}

var xxx_messageInfo_StreamCall proto.InternalMessageInfo

func (m *StreamCall) GetCall() string {
	if m != nil && m.Call != nil {
		return *m.Call
	}
	return ""
}

func (m *StreamCall) GetField() string {
	if m != nil && m.Field != nil {
		return *m.Field
	}
	return ""
}

func (m *StreamCall) GetAlias() string {
	if m != nil && m.Alias != nil {
		return *m.Alias
	}
	return ""
}

type ColStoreInfo struct {
	PrimaryKey           []string `protobuf:"bytes,1,rep,name=PrimaryKey" json:"PrimaryKey,omitempty"`
	SortKey              []string `protobuf:"bytes,2,rep,name=SortKey" json:"SortKey,omitempty"`
	PropertyKey          []string `protobuf:"bytes,3,rep,name=PropertyKey" json:"PropertyKey,omitempty"`
	PropertyValue        []string `protobuf:"bytes,4,rep,name=PropertyValue" json:"PropertyValue,omitempty"`
	TimeClusterDuration  *int64   `protobuf:"varint,5,opt,name=TimeClusterDuration" json:"TimeClusterDuration,omitempty"`
	CompactionType       *int32   `protobuf:"varint,6,opt,name=CompactionType" json:"CompactionType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ColStoreInfo) Reset()         { *m = ColStoreInfo{} }
func (m *ColStoreInfo) String() string { return proto.CompactTextString(m) }
func (*ColStoreInfo) ProtoMessage()    {}
func (*ColStoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{32}
}
func (m *ColStoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ColStoreInfo.Unmarshal(m, b)
}
func (m *ColStoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ColStoreInfo.Marshal(b, m, deterministic)
}
func (m *ColStoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ColStoreInfo.Merge(m, src)
}
func (m *ColStoreInfo) XXX_Size() int {
	return xxx_messageInfo_ColStoreInfo.Size(m)
}
func (m *ColStoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ColStoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ColStoreInfo proto.InternalMessageInfo

func (m *ColStoreInfo) GetPrimaryKey() []string {
	if m != nil {
		return m.PrimaryKey
	}
	return nil
}

func (m *ColStoreInfo) GetSortKey() []string {
	if m != nil {
		return m.SortKey
	}
	return nil
}

func (m *ColStoreInfo) GetPropertyKey() []string {
	if m != nil {
		return m.PropertyKey
	}
	return nil
}

func (m *ColStoreInfo) GetPropertyValue() []string {
	if m != nil {
		return m.PropertyValue
	}
	return nil
}

func (m *ColStoreInfo) GetTimeClusterDuration() int64 {
	if m != nil && m.TimeClusterDuration != nil {
		return *m.TimeClusterDuration
	}
	return 0
}

func (m *ColStoreInfo) GetCompactionType() int32 {
	if m != nil && m.CompactionType != nil {
		return *m.CompactionType
	}
	return 0
}

type IndexOption struct {
	Tokens               *string  `protobuf:"bytes,1,opt,name=Tokens" json:"Tokens,omitempty"`
	Tokenizers           *string  `protobuf:"bytes,2,opt,name=Tokenizers" json:"Tokenizers,omitempty"`
	TimeClusterDuration  *int64   `protobuf:"varint,3,opt,name=TimeClusterDuration" json:"TimeClusterDuration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexOption) Reset()         { *m = IndexOption{} }
func (m *IndexOption) String() string { return proto.CompactTextString(m) }
func (*IndexOption) ProtoMessage()    {}
func (*IndexOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{33}
}
func (m *IndexOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexOption.Unmarshal(m, b)
}
func (m *IndexOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexOption.Marshal(b, m, deterministic)
}
func (m *IndexOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexOption.Merge(m, src)
}
func (m *IndexOption) XXX_Size() int {
	return xxx_messageInfo_IndexOption.Size(m)
}
func (m *IndexOption) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexOption.DiscardUnknown(m)
}

var xxx_messageInfo_IndexOption proto.InternalMessageInfo

func (m *IndexOption) GetTokens() string {
	if m != nil && m.Tokens != nil {
		return *m.Tokens
	}
	return ""
}

func (m *IndexOption) GetTokenizers() string {
	if m != nil && m.Tokenizers != nil {
		return *m.Tokenizers
	}
	return ""
}

func (m *IndexOption) GetTimeClusterDuration() int64 {
	if m != nil && m.TimeClusterDuration != nil {
		return *m.TimeClusterDuration
	}
	return 0
}

type IndexOptions struct {
	Infos                []*IndexOption `protobuf:"bytes,1,rep,name=Infos" json:"Infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *IndexOptions) Reset()         { *m = IndexOptions{} }
func (m *IndexOptions) String() string { return proto.CompactTextString(m) }
func (*IndexOptions) ProtoMessage()    {}
func (*IndexOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{34}
}
func (m *IndexOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexOptions.Unmarshal(m, b)
}
func (m *IndexOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexOptions.Marshal(b, m, deterministic)
}
func (m *IndexOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexOptions.Merge(m, src)
}
func (m *IndexOptions) XXX_Size() int {
	return xxx_messageInfo_IndexOptions.Size(m)
}
func (m *IndexOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexOptions.DiscardUnknown(m)
}

var xxx_messageInfo_IndexOptions proto.InternalMessageInfo

func (m *IndexOptions) GetInfos() []*IndexOption {
	if m != nil {
		return m.Infos
	}
	return nil
}

type Command struct {
	Type                         *Command_Type `protobuf:"varint,1,req,name=type,enum=proto.Command_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}      `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *Command) Reset()         { *m = Command{} }
func (m *Command) String() string { return proto.CompactTextString(m) }
func (*Command) ProtoMessage()    {}
func (*Command) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{35}
}

var extRange_Command = []proto.ExtensionRange{
	{Start: 100, End: 536870911},
}

func (*Command) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_Command
}

func (m *Command) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Command.Unmarshal(m, b)
}
func (m *Command) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Command.Marshal(b, m, deterministic)
}
func (m *Command) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Command.Merge(m, src)
}
func (m *Command) XXX_Size() int {
	return xxx_messageInfo_Command.Size(m)
}
func (m *Command) XXX_DiscardUnknown() {
	xxx_messageInfo_Command.DiscardUnknown(m)
}

var xxx_messageInfo_Command proto.InternalMessageInfo

func (m *Command) GetType() Command_Type {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return Command_CreateDatabaseCommand
}

type CreateDatabaseCommand struct {
	Name                 *string              `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	RetentionPolicy      *RetentionPolicyInfo `protobuf:"bytes,2,opt,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	ReplicaNum           *uint32              `protobuf:"varint,3,opt,name=ReplicaNum" json:"ReplicaNum,omitempty"`
	Ski                  *ShardKeyInfo        `protobuf:"bytes,4,opt,name=Ski" json:"Ski,omitempty"`
	EnableTagArray       *bool                `protobuf:"varint,5,opt,name=EnableTagArray" json:"EnableTagArray,omitempty"`
	Options              *ObsOptions          `protobuf:"bytes,21,opt,name=Options" json:"Options,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CreateDatabaseCommand) Reset()         { *m = CreateDatabaseCommand{} }
func (m *CreateDatabaseCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDatabaseCommand) ProtoMessage()    {}
func (*CreateDatabaseCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{36}
}
func (m *CreateDatabaseCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDatabaseCommand.Unmarshal(m, b)
}
func (m *CreateDatabaseCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDatabaseCommand.Marshal(b, m, deterministic)
}
func (m *CreateDatabaseCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDatabaseCommand.Merge(m, src)
}
func (m *CreateDatabaseCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDatabaseCommand.Size(m)
}
func (m *CreateDatabaseCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDatabaseCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDatabaseCommand proto.InternalMessageInfo

func (m *CreateDatabaseCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateDatabaseCommand) GetRetentionPolicy() *RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicy
	}
	return nil
}

func (m *CreateDatabaseCommand) GetReplicaNum() uint32 {
	if m != nil && m.ReplicaNum != nil {
		return *m.ReplicaNum
	}
	return 0
}

func (m *CreateDatabaseCommand) GetSki() *ShardKeyInfo {
	if m != nil {
		return m.Ski
	}
	return nil
}

func (m *CreateDatabaseCommand) GetEnableTagArray() bool {
	if m != nil && m.EnableTagArray != nil {
		return *m.EnableTagArray
	}
	return false
}

func (m *CreateDatabaseCommand) GetOptions() *ObsOptions {
	if m != nil {
		return m.Options
	}
	return nil
}

var E_CreateDatabaseCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDatabaseCommand)(nil),
	Field:         103,
	Name:          "proto.CreateDatabaseCommand.command",
	Tag:           "bytes,103,opt,name=command",
	Filename:      "meta.proto",
}

type DropDatabaseCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropDatabaseCommand) Reset()         { *m = DropDatabaseCommand{} }
func (m *DropDatabaseCommand) String() string { return proto.CompactTextString(m) }
func (*DropDatabaseCommand) ProtoMessage()    {}
func (*DropDatabaseCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{37}
}
func (m *DropDatabaseCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropDatabaseCommand.Unmarshal(m, b)
}
func (m *DropDatabaseCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropDatabaseCommand.Marshal(b, m, deterministic)
}
func (m *DropDatabaseCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropDatabaseCommand.Merge(m, src)
}
func (m *DropDatabaseCommand) XXX_Size() int {
	return xxx_messageInfo_DropDatabaseCommand.Size(m)
}
func (m *DropDatabaseCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropDatabaseCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropDatabaseCommand proto.InternalMessageInfo

func (m *DropDatabaseCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropDatabaseCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropDatabaseCommand)(nil),
	Field:         104,
	Name:          "proto.DropDatabaseCommand.command",
	Tag:           "bytes,104,opt,name=command",
	Filename:      "meta.proto",
}

type CreateRetentionPolicyCommand struct {
	Database             *string              `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *RetentionPolicyInfo `protobuf:"bytes,2,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	DefaultRP            *bool                `protobuf:"varint,3,req,name=DefaultRP" json:"DefaultRP,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CreateRetentionPolicyCommand) Reset()         { *m = CreateRetentionPolicyCommand{} }
func (m *CreateRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*CreateRetentionPolicyCommand) ProtoMessage()    {}
func (*CreateRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{38}
}
func (m *CreateRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *CreateRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *CreateRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRetentionPolicyCommand.Merge(m, src)
}
func (m *CreateRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Size(m)
}
func (m *CreateRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRetentionPolicyCommand proto.InternalMessageInfo

func (m *CreateRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateRetentionPolicyCommand) GetRetentionPolicy() *RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicy
	}
	return nil
}

func (m *CreateRetentionPolicyCommand) GetDefaultRP() bool {
	if m != nil && m.DefaultRP != nil {
		return *m.DefaultRP
	}
	return false
}

var E_CreateRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateRetentionPolicyCommand)(nil),
	Field:         105,
	Name:          "proto.CreateRetentionPolicyCommand.command",
	Tag:           "bytes,105,opt,name=command",
	Filename:      "meta.proto",
}

type DropRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropRetentionPolicyCommand) Reset()         { *m = DropRetentionPolicyCommand{} }
func (m *DropRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*DropRetentionPolicyCommand) ProtoMessage()    {}
func (*DropRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{39}
}
func (m *DropRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *DropRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *DropRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropRetentionPolicyCommand.Merge(m, src)
}
func (m *DropRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_DropRetentionPolicyCommand.Size(m)
}
func (m *DropRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropRetentionPolicyCommand proto.InternalMessageInfo

func (m *DropRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropRetentionPolicyCommand)(nil),
	Field:         106,
	Name:          "proto.DropRetentionPolicyCommand.command",
	Tag:           "bytes,106,opt,name=command",
	Filename:      "meta.proto",
}

type SetDefaultRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultRetentionPolicyCommand) Reset()         { *m = SetDefaultRetentionPolicyCommand{} }
func (m *SetDefaultRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*SetDefaultRetentionPolicyCommand) ProtoMessage()    {}
func (*SetDefaultRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{40}
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultRetentionPolicyCommand.Merge(m, src)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Size(m)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultRetentionPolicyCommand proto.InternalMessageInfo

func (m *SetDefaultRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *SetDefaultRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_SetDefaultRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetDefaultRetentionPolicyCommand)(nil),
	Field:         107,
	Name:          "proto.SetDefaultRetentionPolicyCommand.command",
	Tag:           "bytes,107,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	NewName              *string  `protobuf:"bytes,3,opt,name=NewName" json:"NewName,omitempty"`
	Duration             *int64   `protobuf:"varint,4,opt,name=Duration" json:"Duration,omitempty"`
	ReplicaN             *uint32  `protobuf:"varint,5,opt,name=ReplicaN" json:"ReplicaN,omitempty"`
	ShardGroupDuration   *int64   `protobuf:"varint,6,opt,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	MakeDefault          *bool    `protobuf:"varint,7,req,name=MakeDefault" json:"MakeDefault,omitempty"`
	HotDuration          *int64   `protobuf:"varint,9,opt,name=HotDuration" json:"HotDuration,omitempty"`
	WarmDuration         *int64   `protobuf:"varint,10,opt,name=WarmDuration" json:"WarmDuration,omitempty"`
	IndexGroupDuration   *int64   `protobuf:"varint,11,opt,name=IndexGroupDuration" json:"IndexGroupDuration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRetentionPolicyCommand) Reset()         { *m = UpdateRetentionPolicyCommand{} }
func (m *UpdateRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateRetentionPolicyCommand) ProtoMessage()    {}
func (*UpdateRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{41}
}
func (m *UpdateRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *UpdateRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *UpdateRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRetentionPolicyCommand.Merge(m, src)
}
func (m *UpdateRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Size(m)
}
func (m *UpdateRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRetentionPolicyCommand proto.InternalMessageInfo

func (m *UpdateRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetNewName() string {
	if m != nil && m.NewName != nil {
		return *m.NewName
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetMakeDefault() bool {
	if m != nil && m.MakeDefault != nil {
		return *m.MakeDefault
	}
	return false
}

func (m *UpdateRetentionPolicyCommand) GetHotDuration() int64 {
	if m != nil && m.HotDuration != nil {
		return *m.HotDuration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetWarmDuration() int64 {
	if m != nil && m.WarmDuration != nil {
		return *m.WarmDuration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetIndexGroupDuration() int64 {
	if m != nil && m.IndexGroupDuration != nil {
		return *m.IndexGroupDuration
	}
	return 0
}

var E_UpdateRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateRetentionPolicyCommand)(nil),
	Field:         108,
	Name:          "proto.UpdateRetentionPolicyCommand.command",
	Tag:           "bytes,108,opt,name=command",
	Filename:      "meta.proto",
}

type CreateShardGroupCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	Timestamp            *int64   `protobuf:"varint,3,req,name=Timestamp" json:"Timestamp,omitempty"`
	ShardTier            *uint64  `protobuf:"varint,4,req,name=ShardTier" json:"ShardTier,omitempty"`
	EngineType           *uint32  `protobuf:"varint,5,opt,name=EngineType" json:"EngineType,omitempty"`
	Version              *uint32  `protobuf:"varint,11,opt,name=Version" json:"Version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateShardGroupCommand) Reset()         { *m = CreateShardGroupCommand{} }
func (m *CreateShardGroupCommand) String() string { return proto.CompactTextString(m) }
func (*CreateShardGroupCommand) ProtoMessage()    {}
func (*CreateShardGroupCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{42}
}
func (m *CreateShardGroupCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateShardGroupCommand.Unmarshal(m, b)
}
func (m *CreateShardGroupCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateShardGroupCommand.Marshal(b, m, deterministic)
}
func (m *CreateShardGroupCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateShardGroupCommand.Merge(m, src)
}
func (m *CreateShardGroupCommand) XXX_Size() int {
	return xxx_messageInfo_CreateShardGroupCommand.Size(m)
}
func (m *CreateShardGroupCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateShardGroupCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateShardGroupCommand proto.InternalMessageInfo

func (m *CreateShardGroupCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateShardGroupCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *CreateShardGroupCommand) GetTimestamp() int64 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

func (m *CreateShardGroupCommand) GetShardTier() uint64 {
	if m != nil && m.ShardTier != nil {
		return *m.ShardTier
	}
	return 0
}

func (m *CreateShardGroupCommand) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func (m *CreateShardGroupCommand) GetVersion() uint32 {
	if m != nil && m.Version != nil {
		return *m.Version
	}
	return 0
}

var E_CreateShardGroupCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateShardGroupCommand)(nil),
	Field:         109,
	Name:          "proto.CreateShardGroupCommand.command",
	Tag:           "bytes,109,opt,name=command",
	Filename:      "meta.proto",
}

type DeleteShardGroupCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	ShardGroupID         *uint64  `protobuf:"varint,3,req,name=ShardGroupID" json:"ShardGroupID,omitempty"`
	DeletedAt            *int64   `protobuf:"varint,6,opt,name=DeletedAt" json:"DeletedAt,omitempty"`
	DeleteType           *int32   `protobuf:"varint,7,opt,name=DeleteType" json:"DeleteType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteShardGroupCommand) Reset()         { *m = DeleteShardGroupCommand{} }
func (m *DeleteShardGroupCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteShardGroupCommand) ProtoMessage()    {}
func (*DeleteShardGroupCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{43}
}
func (m *DeleteShardGroupCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteShardGroupCommand.Unmarshal(m, b)
}
func (m *DeleteShardGroupCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteShardGroupCommand.Marshal(b, m, deterministic)
}
func (m *DeleteShardGroupCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteShardGroupCommand.Merge(m, src)
}
func (m *DeleteShardGroupCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteShardGroupCommand.Size(m)
}
func (m *DeleteShardGroupCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteShardGroupCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteShardGroupCommand proto.InternalMessageInfo

func (m *DeleteShardGroupCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DeleteShardGroupCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *DeleteShardGroupCommand) GetShardGroupID() uint64 {
	if m != nil && m.ShardGroupID != nil {
		return *m.ShardGroupID
	}
	return 0
}

func (m *DeleteShardGroupCommand) GetDeletedAt() int64 {
	if m != nil && m.DeletedAt != nil {
		return *m.DeletedAt
	}
	return 0
}

func (m *DeleteShardGroupCommand) GetDeleteType() int32 {
	if m != nil && m.DeleteType != nil {
		return *m.DeleteType
	}
	return 0
}

var E_DeleteShardGroupCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteShardGroupCommand)(nil),
	Field:         110,
	Name:          "proto.DeleteShardGroupCommand.command",
	Tag:           "bytes,110,opt,name=command",
	Filename:      "meta.proto",
}

type CreateUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string  `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	Admin                *bool    `protobuf:"varint,3,req,name=Admin" json:"Admin,omitempty"`
	RwUser               *bool    `protobuf:"varint,4,opt,name=RwUser" json:"RwUser,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateUserCommand) Reset()         { *m = CreateUserCommand{} }
func (m *CreateUserCommand) String() string { return proto.CompactTextString(m) }
func (*CreateUserCommand) ProtoMessage()    {}
func (*CreateUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{44}
}
func (m *CreateUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateUserCommand.Unmarshal(m, b)
}
func (m *CreateUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateUserCommand.Marshal(b, m, deterministic)
}
func (m *CreateUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateUserCommand.Merge(m, src)
}
func (m *CreateUserCommand) XXX_Size() int {
	return xxx_messageInfo_CreateUserCommand.Size(m)
}
func (m *CreateUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateUserCommand proto.InternalMessageInfo

func (m *CreateUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateUserCommand) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

func (m *CreateUserCommand) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

func (m *CreateUserCommand) GetRwUser() bool {
	if m != nil && m.RwUser != nil {
		return *m.RwUser
	}
	return false
}

var E_CreateUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateUserCommand)(nil),
	Field:         113,
	Name:          "proto.CreateUserCommand.command",
	Tag:           "bytes,113,opt,name=command",
	Filename:      "meta.proto",
}

type DropUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropUserCommand) Reset()         { *m = DropUserCommand{} }
func (m *DropUserCommand) String() string { return proto.CompactTextString(m) }
func (*DropUserCommand) ProtoMessage()    {}
func (*DropUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{45}
}
func (m *DropUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropUserCommand.Unmarshal(m, b)
}
func (m *DropUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropUserCommand.Marshal(b, m, deterministic)
}
func (m *DropUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropUserCommand.Merge(m, src)
}
func (m *DropUserCommand) XXX_Size() int {
	return xxx_messageInfo_DropUserCommand.Size(m)
}
func (m *DropUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropUserCommand proto.InternalMessageInfo

func (m *DropUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropUserCommand)(nil),
	Field:         114,
	Name:          "proto.DropUserCommand.command",
	Tag:           "bytes,114,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string  `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserCommand) Reset()         { *m = UpdateUserCommand{} }
func (m *UpdateUserCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateUserCommand) ProtoMessage()    {}
func (*UpdateUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{46}
}
func (m *UpdateUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserCommand.Unmarshal(m, b)
}
func (m *UpdateUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserCommand.Marshal(b, m, deterministic)
}
func (m *UpdateUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserCommand.Merge(m, src)
}
func (m *UpdateUserCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateUserCommand.Size(m)
}
func (m *UpdateUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserCommand proto.InternalMessageInfo

func (m *UpdateUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UpdateUserCommand) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

var E_UpdateUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateUserCommand)(nil),
	Field:         115,
	Name:          "proto.UpdateUserCommand.command",
	Tag:           "bytes,115,opt,name=command",
	Filename:      "meta.proto",
}

type SetPrivilegeCommand struct {
	Username             *string  `protobuf:"bytes,1,req,name=Username" json:"Username,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	Privilege            *int32   `protobuf:"varint,3,req,name=Privilege" json:"Privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrivilegeCommand) Reset()         { *m = SetPrivilegeCommand{} }
func (m *SetPrivilegeCommand) String() string { return proto.CompactTextString(m) }
func (*SetPrivilegeCommand) ProtoMessage()    {}
func (*SetPrivilegeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{47}
}
func (m *SetPrivilegeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrivilegeCommand.Unmarshal(m, b)
}
func (m *SetPrivilegeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrivilegeCommand.Marshal(b, m, deterministic)
}
func (m *SetPrivilegeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrivilegeCommand.Merge(m, src)
}
func (m *SetPrivilegeCommand) XXX_Size() int {
	return xxx_messageInfo_SetPrivilegeCommand.Size(m)
}
func (m *SetPrivilegeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrivilegeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrivilegeCommand proto.InternalMessageInfo

func (m *SetPrivilegeCommand) GetUsername() string {
	if m != nil && m.Username != nil {
		return *m.Username
	}
	return ""
}

func (m *SetPrivilegeCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *SetPrivilegeCommand) GetPrivilege() int32 {
	if m != nil && m.Privilege != nil {
		return *m.Privilege
	}
	return 0
}

var E_SetPrivilegeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetPrivilegeCommand)(nil),
	Field:         116,
	Name:          "proto.SetPrivilegeCommand.command",
	Tag:           "bytes,116,opt,name=command",
	Filename:      "meta.proto",
}

type SetDataCommand struct {
	Data                 *Data    `protobuf:"bytes,1,req,name=Data" json:"Data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDataCommand) Reset()         { *m = SetDataCommand{} }
func (m *SetDataCommand) String() string { return proto.CompactTextString(m) }
func (*SetDataCommand) ProtoMessage()    {}
func (*SetDataCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{48}
}
func (m *SetDataCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDataCommand.Unmarshal(m, b)
}
func (m *SetDataCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDataCommand.Marshal(b, m, deterministic)
}
func (m *SetDataCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDataCommand.Merge(m, src)
}
func (m *SetDataCommand) XXX_Size() int {
	return xxx_messageInfo_SetDataCommand.Size(m)
}
func (m *SetDataCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDataCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetDataCommand proto.InternalMessageInfo

func (m *SetDataCommand) GetData() *Data {
	if m != nil {
		return m.Data
	}
	return nil
}

var E_SetDataCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetDataCommand)(nil),
	Field:         117,
	Name:          "proto.SetDataCommand.command",
	Tag:           "bytes,117,opt,name=command",
	Filename:      "meta.proto",
}

type SetAdminPrivilegeCommand struct {
	Username             *string  `protobuf:"bytes,1,req,name=Username" json:"Username,omitempty"`
	Admin                *bool    `protobuf:"varint,2,req,name=Admin" json:"Admin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdminPrivilegeCommand) Reset()         { *m = SetAdminPrivilegeCommand{} }
func (m *SetAdminPrivilegeCommand) String() string { return proto.CompactTextString(m) }
func (*SetAdminPrivilegeCommand) ProtoMessage()    {}
func (*SetAdminPrivilegeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{49}
}
func (m *SetAdminPrivilegeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Unmarshal(m, b)
}
func (m *SetAdminPrivilegeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Marshal(b, m, deterministic)
}
func (m *SetAdminPrivilegeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdminPrivilegeCommand.Merge(m, src)
}
func (m *SetAdminPrivilegeCommand) XXX_Size() int {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Size(m)
}
func (m *SetAdminPrivilegeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdminPrivilegeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdminPrivilegeCommand proto.InternalMessageInfo

func (m *SetAdminPrivilegeCommand) GetUsername() string {
	if m != nil && m.Username != nil {
		return *m.Username
	}
	return ""
}

func (m *SetAdminPrivilegeCommand) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

var E_SetAdminPrivilegeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetAdminPrivilegeCommand)(nil),
	Field:         118,
	Name:          "proto.SetAdminPrivilegeCommand.command",
	Tag:           "bytes,118,opt,name=command",
	Filename:      "meta.proto",
}

type CreateSubscriptionCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,3,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	Mode                 *string  `protobuf:"bytes,4,req,name=Mode" json:"Mode,omitempty"`
	Destinations         []string `protobuf:"bytes,5,rep,name=Destinations" json:"Destinations,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSubscriptionCommand) Reset()         { *m = CreateSubscriptionCommand{} }
func (m *CreateSubscriptionCommand) String() string { return proto.CompactTextString(m) }
func (*CreateSubscriptionCommand) ProtoMessage()    {}
func (*CreateSubscriptionCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{50}
}
func (m *CreateSubscriptionCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSubscriptionCommand.Unmarshal(m, b)
}
func (m *CreateSubscriptionCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSubscriptionCommand.Marshal(b, m, deterministic)
}
func (m *CreateSubscriptionCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSubscriptionCommand.Merge(m, src)
}
func (m *CreateSubscriptionCommand) XXX_Size() int {
	return xxx_messageInfo_CreateSubscriptionCommand.Size(m)
}
func (m *CreateSubscriptionCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSubscriptionCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSubscriptionCommand proto.InternalMessageInfo

func (m *CreateSubscriptionCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetDestinations() []string {
	if m != nil {
		return m.Destinations
	}
	return nil
}

var E_CreateSubscriptionCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateSubscriptionCommand)(nil),
	Field:         121,
	Name:          "proto.CreateSubscriptionCommand.command",
	Tag:           "bytes,121,opt,name=command",
	Filename:      "meta.proto",
}

type DropSubscriptionCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,3,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropSubscriptionCommand) Reset()         { *m = DropSubscriptionCommand{} }
func (m *DropSubscriptionCommand) String() string { return proto.CompactTextString(m) }
func (*DropSubscriptionCommand) ProtoMessage()    {}
func (*DropSubscriptionCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{51}
}
func (m *DropSubscriptionCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropSubscriptionCommand.Unmarshal(m, b)
}
func (m *DropSubscriptionCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropSubscriptionCommand.Marshal(b, m, deterministic)
}
func (m *DropSubscriptionCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropSubscriptionCommand.Merge(m, src)
}
func (m *DropSubscriptionCommand) XXX_Size() int {
	return xxx_messageInfo_DropSubscriptionCommand.Size(m)
}
func (m *DropSubscriptionCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropSubscriptionCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropSubscriptionCommand proto.InternalMessageInfo

func (m *DropSubscriptionCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DropSubscriptionCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropSubscriptionCommand) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

var E_DropSubscriptionCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropSubscriptionCommand)(nil),
	Field:         122,
	Name:          "proto.DropSubscriptionCommand.command",
	Tag:           "bytes,122,opt,name=command",
	Filename:      "meta.proto",
}

type CreateMetaNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	RPCAddr              *string  `protobuf:"bytes,4,req,name=RPCAddr" json:"RPCAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	Rand                 *uint64  `protobuf:"varint,3,req,name=Rand" json:"Rand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMetaNodeCommand) Reset()         { *m = CreateMetaNodeCommand{} }
func (m *CreateMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateMetaNodeCommand) ProtoMessage()    {}
func (*CreateMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{52}
}
func (m *CreateMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMetaNodeCommand.Unmarshal(m, b)
}
func (m *CreateMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMetaNodeCommand.Merge(m, src)
}
func (m *CreateMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateMetaNodeCommand.Size(m)
}
func (m *CreateMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMetaNodeCommand proto.InternalMessageInfo

func (m *CreateMetaNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *CreateMetaNodeCommand) GetRPCAddr() string {
	if m != nil && m.RPCAddr != nil {
		return *m.RPCAddr
	}
	return ""
}

func (m *CreateMetaNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

func (m *CreateMetaNodeCommand) GetRand() uint64 {
	if m != nil && m.Rand != nil {
		return *m.Rand
	}
	return 0
}

var E_CreateMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateMetaNodeCommand)(nil),
	Field:         124,
	Name:          "proto.CreateMetaNodeCommand.command",
	Tag:           "bytes,124,opt,name=command",
	Filename:      "meta.proto",
}

type CreateDataNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	Role                 *string  `protobuf:"bytes,10,opt,name=Role" json:"Role,omitempty"`
	Az                   *string  `protobuf:"bytes,11,opt,name=Az" json:"Az,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDataNodeCommand) Reset()         { *m = CreateDataNodeCommand{} }
func (m *CreateDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDataNodeCommand) ProtoMessage()    {}
func (*CreateDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{53}
}
func (m *CreateDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDataNodeCommand.Unmarshal(m, b)
}
func (m *CreateDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDataNodeCommand.Merge(m, src)
}
func (m *CreateDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDataNodeCommand.Size(m)
}
func (m *CreateDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDataNodeCommand proto.InternalMessageInfo

func (m *CreateDataNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *CreateDataNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

func (m *CreateDataNodeCommand) GetRole() string {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return ""
}

func (m *CreateDataNodeCommand) GetAz() string {
	if m != nil && m.Az != nil {
		return *m.Az
	}
	return ""
}

var E_CreateDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDataNodeCommand)(nil),
	Field:         125,
	Name:          "proto.CreateDataNodeCommand.command",
	Tag:           "bytes,125,opt,name=command",
	Filename:      "meta.proto",
}

type DataNodeEvent struct {
	Host                 []byte   `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	EventType            *int64   `protobuf:"varint,2,req,name=EventType" json:"EventType,omitempty"`
	LTime                *uint64  `protobuf:"varint,3,req,name=LTime" json:"LTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DataNodeEvent) Reset()         { *m = DataNodeEvent{} }
func (m *DataNodeEvent) String() string { return proto.CompactTextString(m) }
func (*DataNodeEvent) ProtoMessage()    {}
func (*DataNodeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{54}
}
func (m *DataNodeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataNodeEvent.Unmarshal(m, b)
}
func (m *DataNodeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataNodeEvent.Marshal(b, m, deterministic)
}
func (m *DataNodeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataNodeEvent.Merge(m, src)
}
func (m *DataNodeEvent) XXX_Size() int {
	return xxx_messageInfo_DataNodeEvent.Size(m)
}
func (m *DataNodeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_DataNodeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_DataNodeEvent proto.InternalMessageInfo

func (m *DataNodeEvent) GetHost() []byte {
	if m != nil {
		return m.Host
	}
	return nil
}

func (m *DataNodeEvent) GetEventType() int64 {
	if m != nil && m.EventType != nil {
		return *m.EventType
	}
	return 0
}

func (m *DataNodeEvent) GetLTime() uint64 {
	if m != nil && m.LTime != nil {
		return *m.LTime
	}
	return 0
}

type DeleteMetaNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMetaNodeCommand) Reset()         { *m = DeleteMetaNodeCommand{} }
func (m *DeleteMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteMetaNodeCommand) ProtoMessage()    {}
func (*DeleteMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{55}
}
func (m *DeleteMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMetaNodeCommand.Unmarshal(m, b)
}
func (m *DeleteMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *DeleteMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMetaNodeCommand.Merge(m, src)
}
func (m *DeleteMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteMetaNodeCommand.Size(m)
}
func (m *DeleteMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMetaNodeCommand proto.InternalMessageInfo

func (m *DeleteMetaNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DeleteMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteMetaNodeCommand)(nil),
	Field:         127,
	Name:          "proto.DeleteMetaNodeCommand.command",
	Tag:           "bytes,127,opt,name=command",
	Filename:      "meta.proto",
}

type DeleteDataNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteDataNodeCommand) Reset()         { *m = DeleteDataNodeCommand{} }
func (m *DeleteDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteDataNodeCommand) ProtoMessage()    {}
func (*DeleteDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{56}
}
func (m *DeleteDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteDataNodeCommand.Unmarshal(m, b)
}
func (m *DeleteDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *DeleteDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteDataNodeCommand.Merge(m, src)
}
func (m *DeleteDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteDataNodeCommand.Size(m)
}
func (m *DeleteDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteDataNodeCommand proto.InternalMessageInfo

func (m *DeleteDataNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DeleteDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteDataNodeCommand)(nil),
	Field:         128,
	Name:          "proto.DeleteDataNodeCommand.command",
	Tag:           "bytes,128,opt,name=command",
	Filename:      "meta.proto",
}

type Response struct {
	OK                   *bool    `protobuf:"varint,1,req,name=OK" json:"OK,omitempty"`
	Error                *string  `protobuf:"bytes,2,opt,name=Error" json:"Error,omitempty"`
	Index                *uint64  `protobuf:"varint,3,opt,name=Index" json:"Index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{57}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetOK() bool {
	if m != nil && m.OK != nil {
		return *m.OK
	}
	return false
}

func (m *Response) GetError() string {
	if m != nil && m.Error != nil {
		return *m.Error
	}
	return ""
}

func (m *Response) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

// SetMetaNodeCommand is for the initial metanode in a cluster or
// if the single host restarts and its hostname changes, this will update it
type SetMetaNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	RPCAddr              *string  `protobuf:"bytes,4,req,name=RPCAddr" json:"RPCAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	Rand                 *uint64  `protobuf:"varint,3,req,name=Rand" json:"Rand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMetaNodeCommand) Reset()         { *m = SetMetaNodeCommand{} }
func (m *SetMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*SetMetaNodeCommand) ProtoMessage()    {}
func (*SetMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{58}
}
func (m *SetMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMetaNodeCommand.Unmarshal(m, b)
}
func (m *SetMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *SetMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMetaNodeCommand.Merge(m, src)
}
func (m *SetMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_SetMetaNodeCommand.Size(m)
}
func (m *SetMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetMetaNodeCommand proto.InternalMessageInfo

func (m *SetMetaNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *SetMetaNodeCommand) GetRPCAddr() string {
	if m != nil && m.RPCAddr != nil {
		return *m.RPCAddr
	}
	return ""
}

func (m *SetMetaNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

func (m *SetMetaNodeCommand) GetRand() uint64 {
	if m != nil && m.Rand != nil {
		return *m.Rand
	}
	return 0
}

var E_SetMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetMetaNodeCommand)(nil),
	Field:         129,
	Name:          "proto.SetMetaNodeCommand.command",
	Tag:           "bytes,129,opt,name=command",
	Filename:      "meta.proto",
}

type DropShardCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropShardCommand) Reset()         { *m = DropShardCommand{} }
func (m *DropShardCommand) String() string { return proto.CompactTextString(m) }
func (*DropShardCommand) ProtoMessage()    {}
func (*DropShardCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{59}
}
func (m *DropShardCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropShardCommand.Unmarshal(m, b)
}
func (m *DropShardCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropShardCommand.Marshal(b, m, deterministic)
}
func (m *DropShardCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropShardCommand.Merge(m, src)
}
func (m *DropShardCommand) XXX_Size() int {
	return xxx_messageInfo_DropShardCommand.Size(m)
}
func (m *DropShardCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropShardCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropShardCommand proto.InternalMessageInfo

func (m *DropShardCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DropShardCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropShardCommand)(nil),
	Field:         130,
	Name:          "proto.DropShardCommand.command",
	Tag:           "bytes,130,opt,name=command",
	Filename:      "meta.proto",
}

type MarkDatabaseDeleteCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkDatabaseDeleteCommand) Reset()         { *m = MarkDatabaseDeleteCommand{} }
func (m *MarkDatabaseDeleteCommand) String() string { return proto.CompactTextString(m) }
func (*MarkDatabaseDeleteCommand) ProtoMessage()    {}
func (*MarkDatabaseDeleteCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{60}
}
func (m *MarkDatabaseDeleteCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkDatabaseDeleteCommand.Unmarshal(m, b)
}
func (m *MarkDatabaseDeleteCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkDatabaseDeleteCommand.Marshal(b, m, deterministic)
}
func (m *MarkDatabaseDeleteCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkDatabaseDeleteCommand.Merge(m, src)
}
func (m *MarkDatabaseDeleteCommand) XXX_Size() int {
	return xxx_messageInfo_MarkDatabaseDeleteCommand.Size(m)
}
func (m *MarkDatabaseDeleteCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkDatabaseDeleteCommand.DiscardUnknown(m)
}

var xxx_messageInfo_MarkDatabaseDeleteCommand proto.InternalMessageInfo

func (m *MarkDatabaseDeleteCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_MarkDatabaseDeleteCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*MarkDatabaseDeleteCommand)(nil),
	Field:         131,
	Name:          "proto.MarkDatabaseDeleteCommand.command",
	Tag:           "bytes,131,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateShardOwnerCommand struct {
	ShardId              *int64   `protobuf:"varint,1,req,name=ShardId" json:"ShardId,omitempty"`
	OwnerId              *int64   `protobuf:"varint,2,req,name=OwnerId" json:"OwnerId,omitempty"`
	DbName               *string  `protobuf:"bytes,3,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,4,req,name=RpName" json:"RpName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateShardOwnerCommand) Reset()         { *m = UpdateShardOwnerCommand{} }
func (m *UpdateShardOwnerCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateShardOwnerCommand) ProtoMessage()    {}
func (*UpdateShardOwnerCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{61}
}
func (m *UpdateShardOwnerCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateShardOwnerCommand.Unmarshal(m, b)
}
func (m *UpdateShardOwnerCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateShardOwnerCommand.Marshal(b, m, deterministic)
}
func (m *UpdateShardOwnerCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateShardOwnerCommand.Merge(m, src)
}
func (m *UpdateShardOwnerCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateShardOwnerCommand.Size(m)
}
func (m *UpdateShardOwnerCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateShardOwnerCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateShardOwnerCommand proto.InternalMessageInfo

func (m *UpdateShardOwnerCommand) GetShardId() int64 {
	if m != nil && m.ShardId != nil {
		return *m.ShardId
	}
	return 0
}

func (m *UpdateShardOwnerCommand) GetOwnerId() int64 {
	if m != nil && m.OwnerId != nil {
		return *m.OwnerId
	}
	return 0
}

func (m *UpdateShardOwnerCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *UpdateShardOwnerCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

var E_UpdateShardOwnerCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateShardOwnerCommand)(nil),
	Field:         135,
	Name:          "proto.UpdateShardOwnerCommand.command",
	Tag:           "bytes,135,opt,name=command",
	Filename:      "meta.proto",
}

type MarkRetentionPolicyDeleteCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRetentionPolicyDeleteCommand) Reset()         { *m = MarkRetentionPolicyDeleteCommand{} }
func (m *MarkRetentionPolicyDeleteCommand) String() string { return proto.CompactTextString(m) }
func (*MarkRetentionPolicyDeleteCommand) ProtoMessage()    {}
func (*MarkRetentionPolicyDeleteCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{62}
}
func (m *MarkRetentionPolicyDeleteCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRetentionPolicyDeleteCommand.Unmarshal(m, b)
}
func (m *MarkRetentionPolicyDeleteCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRetentionPolicyDeleteCommand.Marshal(b, m, deterministic)
}
func (m *MarkRetentionPolicyDeleteCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRetentionPolicyDeleteCommand.Merge(m, src)
}
func (m *MarkRetentionPolicyDeleteCommand) XXX_Size() int {
	return xxx_messageInfo_MarkRetentionPolicyDeleteCommand.Size(m)
}
func (m *MarkRetentionPolicyDeleteCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRetentionPolicyDeleteCommand.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRetentionPolicyDeleteCommand proto.InternalMessageInfo

func (m *MarkRetentionPolicyDeleteCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *MarkRetentionPolicyDeleteCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_MarkRetentionPolicyDeleteCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*MarkRetentionPolicyDeleteCommand)(nil),
	Field:         139,
	Name:          "proto.MarkRetentionPolicyDeleteCommand.command",
	Tag:           "bytes,139,opt,name=command",
	Filename:      "meta.proto",
}

type CreateMeasurementCommand struct {
	DBName               *string        `protobuf:"bytes,1,req,name=DBName" json:"DBName,omitempty"`
	RpName               *string        `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	Name                 *string        `protobuf:"bytes,3,req,name=Name" json:"Name,omitempty"`
	Ski                  *ShardKeyInfo  `protobuf:"bytes,4,opt,name=Ski" json:"Ski,omitempty"`
	IR                   *IndexRelation `protobuf:"bytes,5,opt,name=IR" json:"IR,omitempty"`
	EngineType           *uint32        `protobuf:"varint,6,opt,name=EngineType" json:"EngineType,omitempty"`
	ColStoreInfo         *ColStoreInfo  `protobuf:"bytes,7,opt,name=ColStoreInfo" json:"ColStoreInfo,omitempty"`
	SchemaInfo           []*FieldSchema `protobuf:"bytes,8,rep,name=SchemaInfo" json:"SchemaInfo,omitempty"`
	Options              *Options       `protobuf:"bytes,9,opt,name=Options" json:"Options,omitempty"`
	InitNumOfShards      *int32         `protobuf:"varint,10,opt,name=InitNumOfShards" json:"InitNumOfShards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CreateMeasurementCommand) Reset()         { *m = CreateMeasurementCommand{} }
func (m *CreateMeasurementCommand) String() string { return proto.CompactTextString(m) }
func (*CreateMeasurementCommand) ProtoMessage()    {}
func (*CreateMeasurementCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{63}
}
func (m *CreateMeasurementCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMeasurementCommand.Unmarshal(m, b)
}
func (m *CreateMeasurementCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMeasurementCommand.Marshal(b, m, deterministic)
}
func (m *CreateMeasurementCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMeasurementCommand.Merge(m, src)
}
func (m *CreateMeasurementCommand) XXX_Size() int {
	return xxx_messageInfo_CreateMeasurementCommand.Size(m)
}
func (m *CreateMeasurementCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMeasurementCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMeasurementCommand proto.InternalMessageInfo

func (m *CreateMeasurementCommand) GetDBName() string {
	if m != nil && m.DBName != nil {
		return *m.DBName
	}
	return ""
}

func (m *CreateMeasurementCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *CreateMeasurementCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateMeasurementCommand) GetSki() *ShardKeyInfo {
	if m != nil {
		return m.Ski
	}
	return nil
}

func (m *CreateMeasurementCommand) GetIR() *IndexRelation {
	if m != nil {
		return m.IR
	}
	return nil
}

func (m *CreateMeasurementCommand) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func (m *CreateMeasurementCommand) GetColStoreInfo() *ColStoreInfo {
	if m != nil {
		return m.ColStoreInfo
	}
	return nil
}

func (m *CreateMeasurementCommand) GetSchemaInfo() []*FieldSchema {
	if m != nil {
		return m.SchemaInfo
	}
	return nil
}

func (m *CreateMeasurementCommand) GetOptions() *Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *CreateMeasurementCommand) GetInitNumOfShards() int32 {
	if m != nil && m.InitNumOfShards != nil {
		return *m.InitNumOfShards
	}
	return 0
}

var E_CreateMeasurementCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateMeasurementCommand)(nil),
	Field:         149,
	Name:          "proto.CreateMeasurementCommand.command",
	Tag:           "bytes,149,opt,name=command",
	Filename:      "meta.proto",
}

type AlterShardKeyCmd struct {
	DBName               *string       `protobuf:"bytes,1,req,name=DBName" json:"DBName,omitempty"`
	RpName               *string       `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	Name                 *string       `protobuf:"bytes,3,req,name=Name" json:"Name,omitempty"`
	Ski                  *ShardKeyInfo `protobuf:"bytes,4,opt,name=Ski" json:"Ski,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AlterShardKeyCmd) Reset()         { *m = AlterShardKeyCmd{} }
func (m *AlterShardKeyCmd) String() string { return proto.CompactTextString(m) }
func (*AlterShardKeyCmd) ProtoMessage()    {}
func (*AlterShardKeyCmd) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{64}
}
func (m *AlterShardKeyCmd) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlterShardKeyCmd.Unmarshal(m, b)
}
func (m *AlterShardKeyCmd) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlterShardKeyCmd.Marshal(b, m, deterministic)
}
func (m *AlterShardKeyCmd) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlterShardKeyCmd.Merge(m, src)
}
func (m *AlterShardKeyCmd) XXX_Size() int {
	return xxx_messageInfo_AlterShardKeyCmd.Size(m)
}
func (m *AlterShardKeyCmd) XXX_DiscardUnknown() {
	xxx_messageInfo_AlterShardKeyCmd.DiscardUnknown(m)
}

var xxx_messageInfo_AlterShardKeyCmd proto.InternalMessageInfo

func (m *AlterShardKeyCmd) GetDBName() string {
	if m != nil && m.DBName != nil {
		return *m.DBName
	}
	return ""
}

func (m *AlterShardKeyCmd) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *AlterShardKeyCmd) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *AlterShardKeyCmd) GetSki() *ShardKeyInfo {
	if m != nil {
		return m.Ski
	}
	return nil
}

var E_AlterShardKeyCmd_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*AlterShardKeyCmd)(nil),
	Field:         150,
	Name:          "proto.AlterShardKeyCmd.command",
	Tag:           "bytes,150,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateDbPtStatusCommand struct {
	TaskID               *uint64  `protobuf:"varint,1,req,name=TaskID" json:"TaskID,omitempty"`
	NodeId               *uint64  `protobuf:"varint,2,req,name=NodeId" json:"NodeId,omitempty"`
	DB                   *string  `protobuf:"bytes,3,req,name=DB" json:"DB,omitempty"`
	PT                   *uint32  `protobuf:"varint,4,req,name=PT" json:"PT,omitempty"`
	Error                *string  `protobuf:"bytes,5,opt,name=Error" json:"Error,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDbPtStatusCommand) Reset()         { *m = UpdateDbPtStatusCommand{} }
func (m *UpdateDbPtStatusCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateDbPtStatusCommand) ProtoMessage()    {}
func (*UpdateDbPtStatusCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{65}
}
func (m *UpdateDbPtStatusCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDbPtStatusCommand.Unmarshal(m, b)
}
func (m *UpdateDbPtStatusCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDbPtStatusCommand.Marshal(b, m, deterministic)
}
func (m *UpdateDbPtStatusCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDbPtStatusCommand.Merge(m, src)
}
func (m *UpdateDbPtStatusCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateDbPtStatusCommand.Size(m)
}
func (m *UpdateDbPtStatusCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDbPtStatusCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDbPtStatusCommand proto.InternalMessageInfo

func (m *UpdateDbPtStatusCommand) GetTaskID() uint64 {
	if m != nil && m.TaskID != nil {
		return *m.TaskID
	}
	return 0
}

func (m *UpdateDbPtStatusCommand) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

func (m *UpdateDbPtStatusCommand) GetDB() string {
	if m != nil && m.DB != nil {
		return *m.DB
	}
	return ""
}

func (m *UpdateDbPtStatusCommand) GetPT() uint32 {
	if m != nil && m.PT != nil {
		return *m.PT
	}
	return 0
}

func (m *UpdateDbPtStatusCommand) GetError() string {
	if m != nil && m.Error != nil {
		return *m.Error
	}
	return ""
}

var E_UpdateDbPtStatusCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateDbPtStatusCommand)(nil),
	Field:         151,
	Name:          "proto.UpdateDbPtStatusCommand.command",
	Tag:           "bytes,151,opt,name=command",
	Filename:      "meta.proto",
}

type ReShardingCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	RpName               *string  `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	ShardGroupID         *uint64  `protobuf:"varint,3,req,name=ShardGroupID" json:"ShardGroupID,omitempty"`
	SplitTime            *int64   `protobuf:"varint,4,req,name=SplitTime" json:"SplitTime,omitempty"`
	ShardBounds          []string `protobuf:"bytes,5,rep,name=ShardBounds" json:"ShardBounds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReShardingCommand) Reset()         { *m = ReShardingCommand{} }
func (m *ReShardingCommand) String() string { return proto.CompactTextString(m) }
func (*ReShardingCommand) ProtoMessage()    {}
func (*ReShardingCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{66}
}
func (m *ReShardingCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReShardingCommand.Unmarshal(m, b)
}
func (m *ReShardingCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReShardingCommand.Marshal(b, m, deterministic)
}
func (m *ReShardingCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReShardingCommand.Merge(m, src)
}
func (m *ReShardingCommand) XXX_Size() int {
	return xxx_messageInfo_ReShardingCommand.Size(m)
}
func (m *ReShardingCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ReShardingCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ReShardingCommand proto.InternalMessageInfo

func (m *ReShardingCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *ReShardingCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *ReShardingCommand) GetShardGroupID() uint64 {
	if m != nil && m.ShardGroupID != nil {
		return *m.ShardGroupID
	}
	return 0
}

func (m *ReShardingCommand) GetSplitTime() int64 {
	if m != nil && m.SplitTime != nil {
		return *m.SplitTime
	}
	return 0
}

func (m *ReShardingCommand) GetShardBounds() []string {
	if m != nil {
		return m.ShardBounds
	}
	return nil
}

var E_ReShardingCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ReShardingCommand)(nil),
	Field:         152,
	Name:          "proto.ReShardingCommand.command",
	Tag:           "bytes,152,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateSchemaCommand struct {
	Database             *string        `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	RpName               *string        `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	Measurement          *string        `protobuf:"bytes,3,req,name=Measurement" json:"Measurement,omitempty"`
	FieldToCreate        []*FieldSchema `protobuf:"bytes,4,rep,name=FieldToCreate" json:"FieldToCreate,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateSchemaCommand) Reset()         { *m = UpdateSchemaCommand{} }
func (m *UpdateSchemaCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateSchemaCommand) ProtoMessage()    {}
func (*UpdateSchemaCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{67}
}
func (m *UpdateSchemaCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchemaCommand.Unmarshal(m, b)
}
func (m *UpdateSchemaCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchemaCommand.Marshal(b, m, deterministic)
}
func (m *UpdateSchemaCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchemaCommand.Merge(m, src)
}
func (m *UpdateSchemaCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateSchemaCommand.Size(m)
}
func (m *UpdateSchemaCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchemaCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchemaCommand proto.InternalMessageInfo

func (m *UpdateSchemaCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UpdateSchemaCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *UpdateSchemaCommand) GetMeasurement() string {
	if m != nil && m.Measurement != nil {
		return *m.Measurement
	}
	return ""
}

func (m *UpdateSchemaCommand) GetFieldToCreate() []*FieldSchema {
	if m != nil {
		return m.FieldToCreate
	}
	return nil
}

var E_UpdateSchemaCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateSchemaCommand)(nil),
	Field:         153,
	Name:          "proto.UpdateSchemaCommand.command",
	Tag:           "bytes,153,opt,name=command",
	Filename:      "meta.proto",
}

type FieldSchema struct {
	FieldName            *string  `protobuf:"bytes,1,req,name=FieldName" json:"FieldName,omitempty"`
	FieldType            *int32   `protobuf:"varint,2,req,name=FieldType" json:"FieldType,omitempty"`
	EndTime              *int32   `protobuf:"varint,3,opt,name=EndTime" json:"EndTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FieldSchema) Reset()         { *m = FieldSchema{} }
func (m *FieldSchema) String() string { return proto.CompactTextString(m) }
func (*FieldSchema) ProtoMessage()    {}
func (*FieldSchema) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{68}
}
func (m *FieldSchema) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldSchema.Unmarshal(m, b)
}
func (m *FieldSchema) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldSchema.Marshal(b, m, deterministic)
}
func (m *FieldSchema) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldSchema.Merge(m, src)
}
func (m *FieldSchema) XXX_Size() int {
	return xxx_messageInfo_FieldSchema.Size(m)
}
func (m *FieldSchema) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldSchema.DiscardUnknown(m)
}

var xxx_messageInfo_FieldSchema proto.InternalMessageInfo

func (m *FieldSchema) GetFieldName() string {
	if m != nil && m.FieldName != nil {
		return *m.FieldName
	}
	return ""
}

func (m *FieldSchema) GetFieldType() int32 {
	if m != nil && m.FieldType != nil {
		return *m.FieldType
	}
	return 0
}

func (m *FieldSchema) GetEndTime() int32 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type IndexInfo struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	OwnerIDs             []uint32 `protobuf:"varint,2,rep,name=OwnerIDs" json:"OwnerIDs,omitempty"`
	MarkDelete           *bool    `protobuf:"varint,3,opt,name=MarkDelete" json:"MarkDelete,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexInfo) Reset()         { *m = IndexInfo{} }
func (m *IndexInfo) String() string { return proto.CompactTextString(m) }
func (*IndexInfo) ProtoMessage()    {}
func (*IndexInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{69}
}
func (m *IndexInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexInfo.Unmarshal(m, b)
}
func (m *IndexInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexInfo.Marshal(b, m, deterministic)
}
func (m *IndexInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexInfo.Merge(m, src)
}
func (m *IndexInfo) XXX_Size() int {
	return xxx_messageInfo_IndexInfo.Size(m)
}
func (m *IndexInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IndexInfo proto.InternalMessageInfo

func (m *IndexInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *IndexInfo) GetOwnerIDs() []uint32 {
	if m != nil {
		return m.OwnerIDs
	}
	return nil
}

func (m *IndexInfo) GetMarkDelete() bool {
	if m != nil && m.MarkDelete != nil {
		return *m.MarkDelete
	}
	return false
}

type IndexGroupInfo struct {
	ID                   *uint64      `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	StartTime            *int64       `protobuf:"varint,2,req,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64       `protobuf:"varint,3,req,name=EndTime" json:"EndTime,omitempty"`
	DeletedAt            *int64       `protobuf:"varint,4,req,name=DeletedAt" json:"DeletedAt,omitempty"`
	Indexes              []*IndexInfo `protobuf:"bytes,5,rep,name=Indexes" json:"Indexes,omitempty"`
	EngineType           *uint32      `protobuf:"varint,6,opt,name=EngineType" json:"EngineType,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *IndexGroupInfo) Reset()         { *m = IndexGroupInfo{} }
func (m *IndexGroupInfo) String() string { return proto.CompactTextString(m) }
func (*IndexGroupInfo) ProtoMessage()    {}
func (*IndexGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{70}
}
func (m *IndexGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexGroupInfo.Unmarshal(m, b)
}
func (m *IndexGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexGroupInfo.Marshal(b, m, deterministic)
}
func (m *IndexGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexGroupInfo.Merge(m, src)
}
func (m *IndexGroupInfo) XXX_Size() int {
	return xxx_messageInfo_IndexGroupInfo.Size(m)
}
func (m *IndexGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IndexGroupInfo proto.InternalMessageInfo

func (m *IndexGroupInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *IndexGroupInfo) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *IndexGroupInfo) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *IndexGroupInfo) GetDeletedAt() int64 {
	if m != nil && m.DeletedAt != nil {
		return *m.DeletedAt
	}
	return 0
}

func (m *IndexGroupInfo) GetIndexes() []*IndexInfo {
	if m != nil {
		return m.Indexes
	}
	return nil
}

func (m *IndexGroupInfo) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

type ShardStatus struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	ShardSize            *uint64  `protobuf:"varint,2,req,name=ShardSize" json:"ShardSize,omitempty"`
	SeriesCount          *int32   `protobuf:"varint,3,req,name=SeriesCount" json:"SeriesCount,omitempty"`
	MaxTime              *int64   `protobuf:"varint,4,req,name=MaxTime" json:"MaxTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardStatus) Reset()         { *m = ShardStatus{} }
func (m *ShardStatus) String() string { return proto.CompactTextString(m) }
func (*ShardStatus) ProtoMessage()    {}
func (*ShardStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{71}
}
func (m *ShardStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardStatus.Unmarshal(m, b)
}
func (m *ShardStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardStatus.Marshal(b, m, deterministic)
}
func (m *ShardStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardStatus.Merge(m, src)
}
func (m *ShardStatus) XXX_Size() int {
	return xxx_messageInfo_ShardStatus.Size(m)
}
func (m *ShardStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardStatus.DiscardUnknown(m)
}

var xxx_messageInfo_ShardStatus proto.InternalMessageInfo

func (m *ShardStatus) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *ShardStatus) GetShardSize() uint64 {
	if m != nil && m.ShardSize != nil {
		return *m.ShardSize
	}
	return 0
}

func (m *ShardStatus) GetSeriesCount() int32 {
	if m != nil && m.SeriesCount != nil {
		return *m.SeriesCount
	}
	return 0
}

func (m *ShardStatus) GetMaxTime() int64 {
	if m != nil && m.MaxTime != nil {
		return *m.MaxTime
	}
	return 0
}

type RpShardStatus struct {
	RpName               *string      `protobuf:"bytes,1,req,name=RpName" json:"RpName,omitempty"`
	ShardStats           *ShardStatus `protobuf:"bytes,2,req,name=ShardStats" json:"ShardStats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RpShardStatus) Reset()         { *m = RpShardStatus{} }
func (m *RpShardStatus) String() string { return proto.CompactTextString(m) }
func (*RpShardStatus) ProtoMessage()    {}
func (*RpShardStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{72}
}
func (m *RpShardStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RpShardStatus.Unmarshal(m, b)
}
func (m *RpShardStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RpShardStatus.Marshal(b, m, deterministic)
}
func (m *RpShardStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RpShardStatus.Merge(m, src)
}
func (m *RpShardStatus) XXX_Size() int {
	return xxx_messageInfo_RpShardStatus.Size(m)
}
func (m *RpShardStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_RpShardStatus.DiscardUnknown(m)
}

var xxx_messageInfo_RpShardStatus proto.InternalMessageInfo

func (m *RpShardStatus) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *RpShardStatus) GetShardStats() *ShardStatus {
	if m != nil {
		return m.ShardStats
	}
	return nil
}

type DBPtStatus struct {
	DB                   *string          `protobuf:"bytes,1,req,name=DB" json:"DB,omitempty"`
	PtID                 *uint32          `protobuf:"varint,2,req,name=PtID" json:"PtID,omitempty"`
	RpStats              []*RpShardStatus `protobuf:"bytes,3,rep,name=RpStats" json:"RpStats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *DBPtStatus) Reset()         { *m = DBPtStatus{} }
func (m *DBPtStatus) String() string { return proto.CompactTextString(m) }
func (*DBPtStatus) ProtoMessage()    {}
func (*DBPtStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{73}
}
func (m *DBPtStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DBPtStatus.Unmarshal(m, b)
}
func (m *DBPtStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DBPtStatus.Marshal(b, m, deterministic)
}
func (m *DBPtStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DBPtStatus.Merge(m, src)
}
func (m *DBPtStatus) XXX_Size() int {
	return xxx_messageInfo_DBPtStatus.Size(m)
}
func (m *DBPtStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_DBPtStatus.DiscardUnknown(m)
}

var xxx_messageInfo_DBPtStatus proto.InternalMessageInfo

func (m *DBPtStatus) GetDB() string {
	if m != nil && m.DB != nil {
		return *m.DB
	}
	return ""
}

func (m *DBPtStatus) GetPtID() uint32 {
	if m != nil && m.PtID != nil {
		return *m.PtID
	}
	return 0
}

func (m *DBPtStatus) GetRpStats() []*RpShardStatus {
	if m != nil {
		return m.RpStats
	}
	return nil
}

type ReportShardsLoadCommand struct {
	DBPTStat             []*DBPtStatus `protobuf:"bytes,1,rep,name=DBPTStat" json:"DBPTStat,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportShardsLoadCommand) Reset()         { *m = ReportShardsLoadCommand{} }
func (m *ReportShardsLoadCommand) String() string { return proto.CompactTextString(m) }
func (*ReportShardsLoadCommand) ProtoMessage()    {}
func (*ReportShardsLoadCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{74}
}
func (m *ReportShardsLoadCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportShardsLoadCommand.Unmarshal(m, b)
}
func (m *ReportShardsLoadCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportShardsLoadCommand.Marshal(b, m, deterministic)
}
func (m *ReportShardsLoadCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportShardsLoadCommand.Merge(m, src)
}
func (m *ReportShardsLoadCommand) XXX_Size() int {
	return xxx_messageInfo_ReportShardsLoadCommand.Size(m)
}
func (m *ReportShardsLoadCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportShardsLoadCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ReportShardsLoadCommand proto.InternalMessageInfo

func (m *ReportShardsLoadCommand) GetDBPTStat() []*DBPtStatus {
	if m != nil {
		return m.DBPTStat
	}
	return nil
}

var E_ReportShardsLoadCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ReportShardsLoadCommand)(nil),
	Field:         154,
	Name:          "proto.ReportShardsLoadCommand.command",
	Tag:           "bytes,154,opt,name=command",
	Filename:      "meta.proto",
}

type DownSamplePolicyInfo struct {
	Calls                []*DownSampleOperators `protobuf:"bytes,1,rep,name=Calls" json:"Calls,omitempty"`
	DownSamplePolicies   []*DownSamplePolicy    `protobuf:"bytes,2,rep,name=DownSamplePolicies" json:"DownSamplePolicies,omitempty"`
	Duration             *int64                 `protobuf:"varint,3,req,name=Duration" json:"Duration,omitempty"`
	TaskID               *uint64                `protobuf:"varint,4,opt,name=TaskID" json:"TaskID,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *DownSamplePolicyInfo) Reset()         { *m = DownSamplePolicyInfo{} }
func (m *DownSamplePolicyInfo) String() string { return proto.CompactTextString(m) }
func (*DownSamplePolicyInfo) ProtoMessage()    {}
func (*DownSamplePolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{75}
}
func (m *DownSamplePolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownSamplePolicyInfo.Unmarshal(m, b)
}
func (m *DownSamplePolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownSamplePolicyInfo.Marshal(b, m, deterministic)
}
func (m *DownSamplePolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownSamplePolicyInfo.Merge(m, src)
}
func (m *DownSamplePolicyInfo) XXX_Size() int {
	return xxx_messageInfo_DownSamplePolicyInfo.Size(m)
}
func (m *DownSamplePolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DownSamplePolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DownSamplePolicyInfo proto.InternalMessageInfo

func (m *DownSamplePolicyInfo) GetCalls() []*DownSampleOperators {
	if m != nil {
		return m.Calls
	}
	return nil
}

func (m *DownSamplePolicyInfo) GetDownSamplePolicies() []*DownSamplePolicy {
	if m != nil {
		return m.DownSamplePolicies
	}
	return nil
}

func (m *DownSamplePolicyInfo) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *DownSamplePolicyInfo) GetTaskID() uint64 {
	if m != nil && m.TaskID != nil {
		return *m.TaskID
	}
	return 0
}

type DownSamplePolicy struct {
	SampleInterval       *int64   `protobuf:"varint,1,req,name=SampleInterval" json:"SampleInterval,omitempty"`
	TimeInterval         *int64   `protobuf:"varint,2,req,name=TimeInterval" json:"TimeInterval,omitempty"`
	WaterMark            *int64   `protobuf:"varint,3,req,name=WaterMark" json:"WaterMark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownSamplePolicy) Reset()         { *m = DownSamplePolicy{} }
func (m *DownSamplePolicy) String() string { return proto.CompactTextString(m) }
func (*DownSamplePolicy) ProtoMessage()    {}
func (*DownSamplePolicy) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{76}
}
func (m *DownSamplePolicy) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownSamplePolicy.Unmarshal(m, b)
}
func (m *DownSamplePolicy) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownSamplePolicy.Marshal(b, m, deterministic)
}
func (m *DownSamplePolicy) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownSamplePolicy.Merge(m, src)
}
func (m *DownSamplePolicy) XXX_Size() int {
	return xxx_messageInfo_DownSamplePolicy.Size(m)
}
func (m *DownSamplePolicy) XXX_DiscardUnknown() {
	xxx_messageInfo_DownSamplePolicy.DiscardUnknown(m)
}

var xxx_messageInfo_DownSamplePolicy proto.InternalMessageInfo

func (m *DownSamplePolicy) GetSampleInterval() int64 {
	if m != nil && m.SampleInterval != nil {
		return *m.SampleInterval
	}
	return 0
}

func (m *DownSamplePolicy) GetTimeInterval() int64 {
	if m != nil && m.TimeInterval != nil {
		return *m.TimeInterval
	}
	return 0
}

func (m *DownSamplePolicy) GetWaterMark() int64 {
	if m != nil && m.WaterMark != nil {
		return *m.WaterMark
	}
	return 0
}

type DownSampleOperators struct {
	AggOps               []string `protobuf:"bytes,1,rep,name=AggOps" json:"AggOps,omitempty"`
	DataType             *int64   `protobuf:"varint,2,req,name=DataType" json:"DataType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownSampleOperators) Reset()         { *m = DownSampleOperators{} }
func (m *DownSampleOperators) String() string { return proto.CompactTextString(m) }
func (*DownSampleOperators) ProtoMessage()    {}
func (*DownSampleOperators) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{77}
}
func (m *DownSampleOperators) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownSampleOperators.Unmarshal(m, b)
}
func (m *DownSampleOperators) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownSampleOperators.Marshal(b, m, deterministic)
}
func (m *DownSampleOperators) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownSampleOperators.Merge(m, src)
}
func (m *DownSampleOperators) XXX_Size() int {
	return xxx_messageInfo_DownSampleOperators.Size(m)
}
func (m *DownSampleOperators) XXX_DiscardUnknown() {
	xxx_messageInfo_DownSampleOperators.DiscardUnknown(m)
}

var xxx_messageInfo_DownSampleOperators proto.InternalMessageInfo

func (m *DownSampleOperators) GetAggOps() []string {
	if m != nil {
		return m.AggOps
	}
	return nil
}

func (m *DownSampleOperators) GetDataType() int64 {
	if m != nil && m.DataType != nil {
		return *m.DataType
	}
	return 0
}

type DownSamplePolicyInfoWithDbRp struct {
	Info                 *DownSamplePolicyInfo `protobuf:"bytes,1,req,name=Info" json:"Info,omitempty"`
	DbName               *string               `protobuf:"bytes,2,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string               `protobuf:"bytes,3,req,name=RpName" json:"RpName,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *DownSamplePolicyInfoWithDbRp) Reset()         { *m = DownSamplePolicyInfoWithDbRp{} }
func (m *DownSamplePolicyInfoWithDbRp) String() string { return proto.CompactTextString(m) }
func (*DownSamplePolicyInfoWithDbRp) ProtoMessage()    {}
func (*DownSamplePolicyInfoWithDbRp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{78}
}
func (m *DownSamplePolicyInfoWithDbRp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownSamplePolicyInfoWithDbRp.Unmarshal(m, b)
}
func (m *DownSamplePolicyInfoWithDbRp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownSamplePolicyInfoWithDbRp.Marshal(b, m, deterministic)
}
func (m *DownSamplePolicyInfoWithDbRp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownSamplePolicyInfoWithDbRp.Merge(m, src)
}
func (m *DownSamplePolicyInfoWithDbRp) XXX_Size() int {
	return xxx_messageInfo_DownSamplePolicyInfoWithDbRp.Size(m)
}
func (m *DownSamplePolicyInfoWithDbRp) XXX_DiscardUnknown() {
	xxx_messageInfo_DownSamplePolicyInfoWithDbRp.DiscardUnknown(m)
}

var xxx_messageInfo_DownSamplePolicyInfoWithDbRp proto.InternalMessageInfo

func (m *DownSamplePolicyInfoWithDbRp) GetInfo() *DownSamplePolicyInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *DownSamplePolicyInfoWithDbRp) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *DownSamplePolicyInfoWithDbRp) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

type DownSamplePoliciesInfoWithDbRp struct {
	Infos                []*DownSamplePolicyInfoWithDbRp `protobuf:"bytes,1,rep,name=Infos" json:"Infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *DownSamplePoliciesInfoWithDbRp) Reset()         { *m = DownSamplePoliciesInfoWithDbRp{} }
func (m *DownSamplePoliciesInfoWithDbRp) String() string { return proto.CompactTextString(m) }
func (*DownSamplePoliciesInfoWithDbRp) ProtoMessage()    {}
func (*DownSamplePoliciesInfoWithDbRp) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{79}
}
func (m *DownSamplePoliciesInfoWithDbRp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownSamplePoliciesInfoWithDbRp.Unmarshal(m, b)
}
func (m *DownSamplePoliciesInfoWithDbRp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownSamplePoliciesInfoWithDbRp.Marshal(b, m, deterministic)
}
func (m *DownSamplePoliciesInfoWithDbRp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownSamplePoliciesInfoWithDbRp.Merge(m, src)
}
func (m *DownSamplePoliciesInfoWithDbRp) XXX_Size() int {
	return xxx_messageInfo_DownSamplePoliciesInfoWithDbRp.Size(m)
}
func (m *DownSamplePoliciesInfoWithDbRp) XXX_DiscardUnknown() {
	xxx_messageInfo_DownSamplePoliciesInfoWithDbRp.DiscardUnknown(m)
}

var xxx_messageInfo_DownSamplePoliciesInfoWithDbRp proto.InternalMessageInfo

func (m *DownSamplePoliciesInfoWithDbRp) GetInfos() []*DownSamplePolicyInfoWithDbRp {
	if m != nil {
		return m.Infos
	}
	return nil
}

type ShardDownSampleUpdateInfos struct {
	Infos                []*ShardDownSampleUpdateInfo `protobuf:"bytes,1,rep,name=Infos" json:"Infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ShardDownSampleUpdateInfos) Reset()         { *m = ShardDownSampleUpdateInfos{} }
func (m *ShardDownSampleUpdateInfos) String() string { return proto.CompactTextString(m) }
func (*ShardDownSampleUpdateInfos) ProtoMessage()    {}
func (*ShardDownSampleUpdateInfos) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{80}
}
func (m *ShardDownSampleUpdateInfos) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardDownSampleUpdateInfos.Unmarshal(m, b)
}
func (m *ShardDownSampleUpdateInfos) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardDownSampleUpdateInfos.Marshal(b, m, deterministic)
}
func (m *ShardDownSampleUpdateInfos) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardDownSampleUpdateInfos.Merge(m, src)
}
func (m *ShardDownSampleUpdateInfos) XXX_Size() int {
	return xxx_messageInfo_ShardDownSampleUpdateInfos.Size(m)
}
func (m *ShardDownSampleUpdateInfos) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardDownSampleUpdateInfos.DiscardUnknown(m)
}

var xxx_messageInfo_ShardDownSampleUpdateInfos proto.InternalMessageInfo

func (m *ShardDownSampleUpdateInfos) GetInfos() []*ShardDownSampleUpdateInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type ShardDownSampleUpdateInfo struct {
	Ident                *ShardIdentifier `protobuf:"bytes,1,req,name=Ident" json:"Ident,omitempty"`
	DownSampleLvl        *int64           `protobuf:"varint,2,req,name=DownSampleLvl" json:"DownSampleLvl,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ShardDownSampleUpdateInfo) Reset()         { *m = ShardDownSampleUpdateInfo{} }
func (m *ShardDownSampleUpdateInfo) String() string { return proto.CompactTextString(m) }
func (*ShardDownSampleUpdateInfo) ProtoMessage()    {}
func (*ShardDownSampleUpdateInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{81}
}
func (m *ShardDownSampleUpdateInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardDownSampleUpdateInfo.Unmarshal(m, b)
}
func (m *ShardDownSampleUpdateInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardDownSampleUpdateInfo.Marshal(b, m, deterministic)
}
func (m *ShardDownSampleUpdateInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardDownSampleUpdateInfo.Merge(m, src)
}
func (m *ShardDownSampleUpdateInfo) XXX_Size() int {
	return xxx_messageInfo_ShardDownSampleUpdateInfo.Size(m)
}
func (m *ShardDownSampleUpdateInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardDownSampleUpdateInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardDownSampleUpdateInfo proto.InternalMessageInfo

func (m *ShardDownSampleUpdateInfo) GetIdent() *ShardIdentifier {
	if m != nil {
		return m.Ident
	}
	return nil
}

func (m *ShardDownSampleUpdateInfo) GetDownSampleLvl() int64 {
	if m != nil && m.DownSampleLvl != nil {
		return *m.DownSampleLvl
	}
	return 0
}

type PruneGroupsCommand struct {
	ShardGroup           *bool    `protobuf:"varint,1,req,name=ShardGroup" json:"ShardGroup,omitempty"`
	ID                   *uint64  `protobuf:"varint,2,opt,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PruneGroupsCommand) Reset()         { *m = PruneGroupsCommand{} }
func (m *PruneGroupsCommand) String() string { return proto.CompactTextString(m) }
func (*PruneGroupsCommand) ProtoMessage()    {}
func (*PruneGroupsCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{82}
}
func (m *PruneGroupsCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PruneGroupsCommand.Unmarshal(m, b)
}
func (m *PruneGroupsCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PruneGroupsCommand.Marshal(b, m, deterministic)
}
func (m *PruneGroupsCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PruneGroupsCommand.Merge(m, src)
}
func (m *PruneGroupsCommand) XXX_Size() int {
	return xxx_messageInfo_PruneGroupsCommand.Size(m)
}
func (m *PruneGroupsCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_PruneGroupsCommand.DiscardUnknown(m)
}

var xxx_messageInfo_PruneGroupsCommand proto.InternalMessageInfo

func (m *PruneGroupsCommand) GetShardGroup() bool {
	if m != nil && m.ShardGroup != nil {
		return *m.ShardGroup
	}
	return false
}

func (m *PruneGroupsCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_PruneGroupsCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*PruneGroupsCommand)(nil),
	Field:         157,
	Name:          "proto.PruneGroupsCommand.command",
	Tag:           "bytes,157,opt,name=command",
	Filename:      "meta.proto",
}

type MarkMeasurementDeleteCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	Measurement          *string  `protobuf:"bytes,3,req,name=Measurement" json:"Measurement,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkMeasurementDeleteCommand) Reset()         { *m = MarkMeasurementDeleteCommand{} }
func (m *MarkMeasurementDeleteCommand) String() string { return proto.CompactTextString(m) }
func (*MarkMeasurementDeleteCommand) ProtoMessage()    {}
func (*MarkMeasurementDeleteCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{83}
}
func (m *MarkMeasurementDeleteCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkMeasurementDeleteCommand.Unmarshal(m, b)
}
func (m *MarkMeasurementDeleteCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkMeasurementDeleteCommand.Marshal(b, m, deterministic)
}
func (m *MarkMeasurementDeleteCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkMeasurementDeleteCommand.Merge(m, src)
}
func (m *MarkMeasurementDeleteCommand) XXX_Size() int {
	return xxx_messageInfo_MarkMeasurementDeleteCommand.Size(m)
}
func (m *MarkMeasurementDeleteCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkMeasurementDeleteCommand.DiscardUnknown(m)
}

var xxx_messageInfo_MarkMeasurementDeleteCommand proto.InternalMessageInfo

func (m *MarkMeasurementDeleteCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *MarkMeasurementDeleteCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *MarkMeasurementDeleteCommand) GetMeasurement() string {
	if m != nil && m.Measurement != nil {
		return *m.Measurement
	}
	return ""
}

var E_MarkMeasurementDeleteCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*MarkMeasurementDeleteCommand)(nil),
	Field:         158,
	Name:          "proto.MarkMeasurementDeleteCommand.command",
	Tag:           "bytes,158,opt,name=command",
	Filename:      "meta.proto",
}

type DropMeasurementCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	Measurement          *string  `protobuf:"bytes,3,req,name=Measurement" json:"Measurement,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropMeasurementCommand) Reset()         { *m = DropMeasurementCommand{} }
func (m *DropMeasurementCommand) String() string { return proto.CompactTextString(m) }
func (*DropMeasurementCommand) ProtoMessage()    {}
func (*DropMeasurementCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{84}
}
func (m *DropMeasurementCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropMeasurementCommand.Unmarshal(m, b)
}
func (m *DropMeasurementCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropMeasurementCommand.Marshal(b, m, deterministic)
}
func (m *DropMeasurementCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropMeasurementCommand.Merge(m, src)
}
func (m *DropMeasurementCommand) XXX_Size() int {
	return xxx_messageInfo_DropMeasurementCommand.Size(m)
}
func (m *DropMeasurementCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropMeasurementCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropMeasurementCommand proto.InternalMessageInfo

func (m *DropMeasurementCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropMeasurementCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *DropMeasurementCommand) GetMeasurement() string {
	if m != nil && m.Measurement != nil {
		return *m.Measurement
	}
	return ""
}

var E_DropMeasurementCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropMeasurementCommand)(nil),
	Field:         159,
	Name:          "proto.DropMeasurementCommand.command",
	Tag:           "bytes,159,opt,name=command",
	Filename:      "meta.proto",
}

type NodeStartInfo struct {
	DataIndex            *uint64              `protobuf:"varint,1,req,name=DataIndex" json:"DataIndex,omitempty"`
	NodeID               *uint64              `protobuf:"varint,2,req,name=NodeID" json:"NodeID,omitempty"`
	PtIds                []uint32             `protobuf:"varint,3,rep,name=PtIds" json:"PtIds,omitempty"`
	Durations            []*ShardDurationInfo `protobuf:"bytes,4,rep,name=Durations" json:"Durations,omitempty"`
	LTime                *uint64              `protobuf:"varint,5,req,name=LTime" json:"LTime,omitempty"`
	DBBriefInfo          []*DatabaseBriefInfo `protobuf:"bytes,6,rep,name=DBBriefInfo" json:"DBBriefInfo,omitempty"`
	ConnId               *uint64              `protobuf:"varint,7,opt,name=connId" json:"connId,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *NodeStartInfo) Reset()         { *m = NodeStartInfo{} }
func (m *NodeStartInfo) String() string { return proto.CompactTextString(m) }
func (*NodeStartInfo) ProtoMessage()    {}
func (*NodeStartInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{85}
}
func (m *NodeStartInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NodeStartInfo.Unmarshal(m, b)
}
func (m *NodeStartInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NodeStartInfo.Marshal(b, m, deterministic)
}
func (m *NodeStartInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NodeStartInfo.Merge(m, src)
}
func (m *NodeStartInfo) XXX_Size() int {
	return xxx_messageInfo_NodeStartInfo.Size(m)
}
func (m *NodeStartInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NodeStartInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NodeStartInfo proto.InternalMessageInfo

func (m *NodeStartInfo) GetDataIndex() uint64 {
	if m != nil && m.DataIndex != nil {
		return *m.DataIndex
	}
	return 0
}

func (m *NodeStartInfo) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

func (m *NodeStartInfo) GetPtIds() []uint32 {
	if m != nil {
		return m.PtIds
	}
	return nil
}

func (m *NodeStartInfo) GetDurations() []*ShardDurationInfo {
	if m != nil {
		return m.Durations
	}
	return nil
}

func (m *NodeStartInfo) GetLTime() uint64 {
	if m != nil && m.LTime != nil {
		return *m.LTime
	}
	return 0
}

func (m *NodeStartInfo) GetDBBriefInfo() []*DatabaseBriefInfo {
	if m != nil {
		return m.DBBriefInfo
	}
	return nil
}

func (m *NodeStartInfo) GetConnId() uint64 {
	if m != nil && m.ConnId != nil {
		return *m.ConnId
	}
	return 0
}

type TimeRangeCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	ShardID              *uint64  `protobuf:"varint,3,req,name=ShardID" json:"ShardID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeCommand) Reset()         { *m = TimeRangeCommand{} }
func (m *TimeRangeCommand) String() string { return proto.CompactTextString(m) }
func (*TimeRangeCommand) ProtoMessage()    {}
func (*TimeRangeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{86}
}
func (m *TimeRangeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeCommand.Unmarshal(m, b)
}
func (m *TimeRangeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeCommand.Marshal(b, m, deterministic)
}
func (m *TimeRangeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeCommand.Merge(m, src)
}
func (m *TimeRangeCommand) XXX_Size() int {
	return xxx_messageInfo_TimeRangeCommand.Size(m)
}
func (m *TimeRangeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeCommand proto.InternalMessageInfo

func (m *TimeRangeCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *TimeRangeCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *TimeRangeCommand) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

var E_TimeRangeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*TimeRangeCommand)(nil),
	Field:         160,
	Name:          "proto.TimeRangeCommand.command",
	Tag:           "bytes,160,opt,name=command",
	Filename:      "meta.proto",
}

type ShardDurationCommand struct {
	Index                *uint64  `protobuf:"varint,1,req,name=Index" json:"Index,omitempty"`
	Pts                  []uint32 `protobuf:"varint,2,rep,name=Pts" json:"Pts,omitempty"`
	NodeId               *uint64  `protobuf:"varint,3,opt,name=nodeId" json:"nodeId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardDurationCommand) Reset()         { *m = ShardDurationCommand{} }
func (m *ShardDurationCommand) String() string { return proto.CompactTextString(m) }
func (*ShardDurationCommand) ProtoMessage()    {}
func (*ShardDurationCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{87}
}
func (m *ShardDurationCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardDurationCommand.Unmarshal(m, b)
}
func (m *ShardDurationCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardDurationCommand.Marshal(b, m, deterministic)
}
func (m *ShardDurationCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardDurationCommand.Merge(m, src)
}
func (m *ShardDurationCommand) XXX_Size() int {
	return xxx_messageInfo_ShardDurationCommand.Size(m)
}
func (m *ShardDurationCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardDurationCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ShardDurationCommand proto.InternalMessageInfo

func (m *ShardDurationCommand) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

func (m *ShardDurationCommand) GetPts() []uint32 {
	if m != nil {
		return m.Pts
	}
	return nil
}

func (m *ShardDurationCommand) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

var E_ShardDurationCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ShardDurationCommand)(nil),
	Field:         161,
	Name:          "proto.ShardDurationCommand.command",
	Tag:           "bytes,161,opt,name=command",
	Filename:      "meta.proto",
}

type DurationDescriptor struct {
	TierType             *uint64  `protobuf:"varint,1,req,name=TierType" json:"TierType,omitempty"`
	TierDuration         *int64   `protobuf:"varint,2,req,name=TierDuration" json:"TierDuration,omitempty"`
	Duration             *int64   `protobuf:"varint,3,req,name=Duration" json:"Duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DurationDescriptor) Reset()         { *m = DurationDescriptor{} }
func (m *DurationDescriptor) String() string { return proto.CompactTextString(m) }
func (*DurationDescriptor) ProtoMessage()    {}
func (*DurationDescriptor) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{88}
}
func (m *DurationDescriptor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DurationDescriptor.Unmarshal(m, b)
}
func (m *DurationDescriptor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DurationDescriptor.Marshal(b, m, deterministic)
}
func (m *DurationDescriptor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DurationDescriptor.Merge(m, src)
}
func (m *DurationDescriptor) XXX_Size() int {
	return xxx_messageInfo_DurationDescriptor.Size(m)
}
func (m *DurationDescriptor) XXX_DiscardUnknown() {
	xxx_messageInfo_DurationDescriptor.DiscardUnknown(m)
}

var xxx_messageInfo_DurationDescriptor proto.InternalMessageInfo

func (m *DurationDescriptor) GetTierType() uint64 {
	if m != nil && m.TierType != nil {
		return *m.TierType
	}
	return 0
}

func (m *DurationDescriptor) GetTierDuration() int64 {
	if m != nil && m.TierDuration != nil {
		return *m.TierDuration
	}
	return 0
}

func (m *DurationDescriptor) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

type ShardIdentifier struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	ShardGroupID         *uint64  `protobuf:"varint,2,req,name=ShardGroupID" json:"ShardGroupID,omitempty"`
	OwnerDb              *string  `protobuf:"bytes,3,req,name=OwnerDb" json:"OwnerDb,omitempty"`
	OwnerPt              *uint32  `protobuf:"varint,4,req,name=OwnerPt" json:"OwnerPt,omitempty"`
	Policy               *string  `protobuf:"bytes,5,req,name=Policy" json:"Policy,omitempty"`
	ShardType            *string  `protobuf:"bytes,6,req,name=ShardType" json:"ShardType,omitempty"`
	DownSampleLevel      *int64   `protobuf:"varint,7,opt,name=DownSampleLevel" json:"DownSampleLevel,omitempty"`
	DownSampleID         *uint64  `protobuf:"varint,8,opt,name=DownSampleID" json:"DownSampleID,omitempty"`
	ReadOnly             *bool    `protobuf:"varint,9,opt,name=ReadOnly" json:"ReadOnly,omitempty"`
	EngineType           *uint32  `protobuf:"varint,10,opt,name=EngineType" json:"EngineType,omitempty"`
	StartTime            *int64   `protobuf:"varint,11,opt,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64   `protobuf:"varint,12,opt,name=EndTime" json:"EndTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardIdentifier) Reset()         { *m = ShardIdentifier{} }
func (m *ShardIdentifier) String() string { return proto.CompactTextString(m) }
func (*ShardIdentifier) ProtoMessage()    {}
func (*ShardIdentifier) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{89}
}
func (m *ShardIdentifier) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardIdentifier.Unmarshal(m, b)
}
func (m *ShardIdentifier) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardIdentifier.Marshal(b, m, deterministic)
}
func (m *ShardIdentifier) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardIdentifier.Merge(m, src)
}
func (m *ShardIdentifier) XXX_Size() int {
	return xxx_messageInfo_ShardIdentifier.Size(m)
}
func (m *ShardIdentifier) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardIdentifier.DiscardUnknown(m)
}

var xxx_messageInfo_ShardIdentifier proto.InternalMessageInfo

func (m *ShardIdentifier) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *ShardIdentifier) GetShardGroupID() uint64 {
	if m != nil && m.ShardGroupID != nil {
		return *m.ShardGroupID
	}
	return 0
}

func (m *ShardIdentifier) GetOwnerDb() string {
	if m != nil && m.OwnerDb != nil {
		return *m.OwnerDb
	}
	return ""
}

func (m *ShardIdentifier) GetOwnerPt() uint32 {
	if m != nil && m.OwnerPt != nil {
		return *m.OwnerPt
	}
	return 0
}

func (m *ShardIdentifier) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *ShardIdentifier) GetShardType() string {
	if m != nil && m.ShardType != nil {
		return *m.ShardType
	}
	return ""
}

func (m *ShardIdentifier) GetDownSampleLevel() int64 {
	if m != nil && m.DownSampleLevel != nil {
		return *m.DownSampleLevel
	}
	return 0
}

func (m *ShardIdentifier) GetDownSampleID() uint64 {
	if m != nil && m.DownSampleID != nil {
		return *m.DownSampleID
	}
	return 0
}

func (m *ShardIdentifier) GetReadOnly() bool {
	if m != nil && m.ReadOnly != nil {
		return *m.ReadOnly
	}
	return false
}

func (m *ShardIdentifier) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func (m *ShardIdentifier) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *ShardIdentifier) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type IndexIdentifier struct {
	IndexID              *uint64  `protobuf:"varint,1,req,name=IndexID" json:"IndexID,omitempty"`
	IndexGroupID         *uint64  `protobuf:"varint,2,req,name=IndexGroupID" json:"IndexGroupID,omitempty"`
	OwnerDb              *string  `protobuf:"bytes,3,req,name=OwnerDb" json:"OwnerDb,omitempty"`
	OwnerPt              *uint32  `protobuf:"varint,4,req,name=OwnerPt" json:"OwnerPt,omitempty"`
	Policy               *string  `protobuf:"bytes,5,req,name=Policy" json:"Policy,omitempty"`
	StartTime            *int64   `protobuf:"varint,6,opt,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64   `protobuf:"varint,7,opt,name=EndTime" json:"EndTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexIdentifier) Reset()         { *m = IndexIdentifier{} }
func (m *IndexIdentifier) String() string { return proto.CompactTextString(m) }
func (*IndexIdentifier) ProtoMessage()    {}
func (*IndexIdentifier) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{90}
}
func (m *IndexIdentifier) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexIdentifier.Unmarshal(m, b)
}
func (m *IndexIdentifier) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexIdentifier.Marshal(b, m, deterministic)
}
func (m *IndexIdentifier) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexIdentifier.Merge(m, src)
}
func (m *IndexIdentifier) XXX_Size() int {
	return xxx_messageInfo_IndexIdentifier.Size(m)
}
func (m *IndexIdentifier) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexIdentifier.DiscardUnknown(m)
}

var xxx_messageInfo_IndexIdentifier proto.InternalMessageInfo

func (m *IndexIdentifier) GetIndexID() uint64 {
	if m != nil && m.IndexID != nil {
		return *m.IndexID
	}
	return 0
}

func (m *IndexIdentifier) GetIndexGroupID() uint64 {
	if m != nil && m.IndexGroupID != nil {
		return *m.IndexGroupID
	}
	return 0
}

func (m *IndexIdentifier) GetOwnerDb() string {
	if m != nil && m.OwnerDb != nil {
		return *m.OwnerDb
	}
	return ""
}

func (m *IndexIdentifier) GetOwnerPt() uint32 {
	if m != nil && m.OwnerPt != nil {
		return *m.OwnerPt
	}
	return 0
}

func (m *IndexIdentifier) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *IndexIdentifier) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *IndexIdentifier) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type TimeRangeInfo struct {
	StartTime            *int64   `protobuf:"varint,1,req,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64   `protobuf:"varint,2,req,name=EndTime" json:"EndTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeInfo) Reset()         { *m = TimeRangeInfo{} }
func (m *TimeRangeInfo) String() string { return proto.CompactTextString(m) }
func (*TimeRangeInfo) ProtoMessage()    {}
func (*TimeRangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{91}
}
func (m *TimeRangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeInfo.Unmarshal(m, b)
}
func (m *TimeRangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeInfo.Marshal(b, m, deterministic)
}
func (m *TimeRangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeInfo.Merge(m, src)
}
func (m *TimeRangeInfo) XXX_Size() int {
	return xxx_messageInfo_TimeRangeInfo.Size(m)
}
func (m *TimeRangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeInfo proto.InternalMessageInfo

func (m *TimeRangeInfo) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *TimeRangeInfo) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

type IndexDescriptor struct {
	IndexID              *uint64        `protobuf:"varint,1,req,name=IndexID" json:"IndexID,omitempty"`
	IndexGroupID         *uint64        `protobuf:"varint,2,req,name=IndexGroupID" json:"IndexGroupID,omitempty"`
	TimeRange            *TimeRangeInfo `protobuf:"bytes,3,req,name=TimeRange" json:"TimeRange,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *IndexDescriptor) Reset()         { *m = IndexDescriptor{} }
func (m *IndexDescriptor) String() string { return proto.CompactTextString(m) }
func (*IndexDescriptor) ProtoMessage()    {}
func (*IndexDescriptor) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{92}
}
func (m *IndexDescriptor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDescriptor.Unmarshal(m, b)
}
func (m *IndexDescriptor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDescriptor.Marshal(b, m, deterministic)
}
func (m *IndexDescriptor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDescriptor.Merge(m, src)
}
func (m *IndexDescriptor) XXX_Size() int {
	return xxx_messageInfo_IndexDescriptor.Size(m)
}
func (m *IndexDescriptor) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDescriptor.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDescriptor proto.InternalMessageInfo

func (m *IndexDescriptor) GetIndexID() uint64 {
	if m != nil && m.IndexID != nil {
		return *m.IndexID
	}
	return 0
}

func (m *IndexDescriptor) GetIndexGroupID() uint64 {
	if m != nil && m.IndexGroupID != nil {
		return *m.IndexGroupID
	}
	return 0
}

func (m *IndexDescriptor) GetTimeRange() *TimeRangeInfo {
	if m != nil {
		return m.TimeRange
	}
	return nil
}

type ShardDurationInfo struct {
	Ident                *ShardIdentifier    `protobuf:"bytes,1,opt,name=Ident" json:"Ident,omitempty"`
	DurationInfo         *DurationDescriptor `protobuf:"bytes,2,req,name=DurationInfo" json:"DurationInfo,omitempty"`
	MeasurementInfo      []*MeasurementInfo  `protobuf:"bytes,3,rep,name=MeasurementInfo" json:"MeasurementInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ShardDurationInfo) Reset()         { *m = ShardDurationInfo{} }
func (m *ShardDurationInfo) String() string { return proto.CompactTextString(m) }
func (*ShardDurationInfo) ProtoMessage()    {}
func (*ShardDurationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{93}
}
func (m *ShardDurationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardDurationInfo.Unmarshal(m, b)
}
func (m *ShardDurationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardDurationInfo.Marshal(b, m, deterministic)
}
func (m *ShardDurationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardDurationInfo.Merge(m, src)
}
func (m *ShardDurationInfo) XXX_Size() int {
	return xxx_messageInfo_ShardDurationInfo.Size(m)
}
func (m *ShardDurationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardDurationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardDurationInfo proto.InternalMessageInfo

func (m *ShardDurationInfo) GetIdent() *ShardIdentifier {
	if m != nil {
		return m.Ident
	}
	return nil
}

func (m *ShardDurationInfo) GetDurationInfo() *DurationDescriptor {
	if m != nil {
		return m.DurationInfo
	}
	return nil
}

func (m *ShardDurationInfo) GetMeasurementInfo() []*MeasurementInfo {
	if m != nil {
		return m.MeasurementInfo
	}
	return nil
}

type IndexDurationInfo struct {
	Ident                *IndexIdentifier    `protobuf:"bytes,1,opt,name=Ident" json:"Ident,omitempty"`
	DurationInfo         *DurationDescriptor `protobuf:"bytes,2,req,name=DurationInfo" json:"DurationInfo,omitempty"`
	MeasurementInfo      []*MeasurementInfo  `protobuf:"bytes,3,rep,name=MeasurementInfo" json:"MeasurementInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *IndexDurationInfo) Reset()         { *m = IndexDurationInfo{} }
func (m *IndexDurationInfo) String() string { return proto.CompactTextString(m) }
func (*IndexDurationInfo) ProtoMessage()    {}
func (*IndexDurationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{94}
}
func (m *IndexDurationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDurationInfo.Unmarshal(m, b)
}
func (m *IndexDurationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDurationInfo.Marshal(b, m, deterministic)
}
func (m *IndexDurationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDurationInfo.Merge(m, src)
}
func (m *IndexDurationInfo) XXX_Size() int {
	return xxx_messageInfo_IndexDurationInfo.Size(m)
}
func (m *IndexDurationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDurationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDurationInfo proto.InternalMessageInfo

func (m *IndexDurationInfo) GetIdent() *IndexIdentifier {
	if m != nil {
		return m.Ident
	}
	return nil
}

func (m *IndexDurationInfo) GetDurationInfo() *DurationDescriptor {
	if m != nil {
		return m.DurationInfo
	}
	return nil
}

func (m *IndexDurationInfo) GetMeasurementInfo() []*MeasurementInfo {
	if m != nil {
		return m.MeasurementInfo
	}
	return nil
}

type ShardTimeRangeInfo struct {
	TimeRange            *TimeRangeInfo     `protobuf:"bytes,1,req,name=TimeRange" json:"TimeRange,omitempty"`
	OwnerIndex           *IndexDescriptor   `protobuf:"bytes,2,req,name=OwnerIndex" json:"OwnerIndex,omitempty"`
	ShardDuration        *ShardDurationInfo `protobuf:"bytes,3,req,name=ShardDuration" json:"ShardDuration,omitempty"`
	ShardType            *string            `protobuf:"bytes,4,opt,name=ShardType" json:"ShardType,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ShardTimeRangeInfo) Reset()         { *m = ShardTimeRangeInfo{} }
func (m *ShardTimeRangeInfo) String() string { return proto.CompactTextString(m) }
func (*ShardTimeRangeInfo) ProtoMessage()    {}
func (*ShardTimeRangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{95}
}
func (m *ShardTimeRangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardTimeRangeInfo.Unmarshal(m, b)
}
func (m *ShardTimeRangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardTimeRangeInfo.Marshal(b, m, deterministic)
}
func (m *ShardTimeRangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardTimeRangeInfo.Merge(m, src)
}
func (m *ShardTimeRangeInfo) XXX_Size() int {
	return xxx_messageInfo_ShardTimeRangeInfo.Size(m)
}
func (m *ShardTimeRangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardTimeRangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardTimeRangeInfo proto.InternalMessageInfo

func (m *ShardTimeRangeInfo) GetTimeRange() *TimeRangeInfo {
	if m != nil {
		return m.TimeRange
	}
	return nil
}

func (m *ShardTimeRangeInfo) GetOwnerIndex() *IndexDescriptor {
	if m != nil {
		return m.OwnerIndex
	}
	return nil
}

func (m *ShardTimeRangeInfo) GetShardDuration() *ShardDurationInfo {
	if m != nil {
		return m.ShardDuration
	}
	return nil
}

func (m *ShardTimeRangeInfo) GetShardType() string {
	if m != nil && m.ShardType != nil {
		return *m.ShardType
	}
	return ""
}

type ShardDurationResponse struct {
	DataIndex            *uint64              `protobuf:"varint,1,req,name=DataIndex" json:"DataIndex,omitempty"`
	Durations            []*ShardDurationInfo `protobuf:"bytes,2,rep,name=Durations" json:"Durations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ShardDurationResponse) Reset()         { *m = ShardDurationResponse{} }
func (m *ShardDurationResponse) String() string { return proto.CompactTextString(m) }
func (*ShardDurationResponse) ProtoMessage()    {}
func (*ShardDurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{96}
}
func (m *ShardDurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardDurationResponse.Unmarshal(m, b)
}
func (m *ShardDurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardDurationResponse.Marshal(b, m, deterministic)
}
func (m *ShardDurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardDurationResponse.Merge(m, src)
}
func (m *ShardDurationResponse) XXX_Size() int {
	return xxx_messageInfo_ShardDurationResponse.Size(m)
}
func (m *ShardDurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardDurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShardDurationResponse proto.InternalMessageInfo

func (m *ShardDurationResponse) GetDataIndex() uint64 {
	if m != nil && m.DataIndex != nil {
		return *m.DataIndex
	}
	return 0
}

func (m *ShardDurationResponse) GetDurations() []*ShardDurationInfo {
	if m != nil {
		return m.Durations
	}
	return nil
}

type IndexDurationResponse struct {
	DataIndex            *uint64              `protobuf:"varint,1,req,name=DataIndex" json:"DataIndex,omitempty"`
	Durations            []*IndexDurationInfo `protobuf:"bytes,2,rep,name=Durations" json:"Durations,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *IndexDurationResponse) Reset()         { *m = IndexDurationResponse{} }
func (m *IndexDurationResponse) String() string { return proto.CompactTextString(m) }
func (*IndexDurationResponse) ProtoMessage()    {}
func (*IndexDurationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{97}
}
func (m *IndexDurationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDurationResponse.Unmarshal(m, b)
}
func (m *IndexDurationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDurationResponse.Marshal(b, m, deterministic)
}
func (m *IndexDurationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDurationResponse.Merge(m, src)
}
func (m *IndexDurationResponse) XXX_Size() int {
	return xxx_messageInfo_IndexDurationResponse.Size(m)
}
func (m *IndexDurationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDurationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDurationResponse proto.InternalMessageInfo

func (m *IndexDurationResponse) GetDataIndex() uint64 {
	if m != nil && m.DataIndex != nil {
		return *m.DataIndex
	}
	return 0
}

func (m *IndexDurationResponse) GetDurations() []*IndexDurationInfo {
	if m != nil {
		return m.Durations
	}
	return nil
}

type DeleteIndexGroupCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	IndexGroupID         *uint64  `protobuf:"varint,3,req,name=IndexGroupID" json:"IndexGroupID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteIndexGroupCommand) Reset()         { *m = DeleteIndexGroupCommand{} }
func (m *DeleteIndexGroupCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteIndexGroupCommand) ProtoMessage()    {}
func (*DeleteIndexGroupCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{98}
}
func (m *DeleteIndexGroupCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteIndexGroupCommand.Unmarshal(m, b)
}
func (m *DeleteIndexGroupCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteIndexGroupCommand.Marshal(b, m, deterministic)
}
func (m *DeleteIndexGroupCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteIndexGroupCommand.Merge(m, src)
}
func (m *DeleteIndexGroupCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteIndexGroupCommand.Size(m)
}
func (m *DeleteIndexGroupCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteIndexGroupCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteIndexGroupCommand proto.InternalMessageInfo

func (m *DeleteIndexGroupCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DeleteIndexGroupCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *DeleteIndexGroupCommand) GetIndexGroupID() uint64 {
	if m != nil && m.IndexGroupID != nil {
		return *m.IndexGroupID
	}
	return 0
}

var E_DeleteIndexGroupCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteIndexGroupCommand)(nil),
	Field:         162,
	Name:          "proto.DeleteIndexGroupCommand.command",
	Tag:           "bytes,162,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateShardInfoTierCommand struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	Tier                 *uint64  `protobuf:"varint,2,req,name=Tier" json:"Tier,omitempty"`
	DbName               *string  `protobuf:"bytes,3,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,4,req,name=RpName" json:"RpName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateShardInfoTierCommand) Reset()         { *m = UpdateShardInfoTierCommand{} }
func (m *UpdateShardInfoTierCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateShardInfoTierCommand) ProtoMessage()    {}
func (*UpdateShardInfoTierCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{99}
}
func (m *UpdateShardInfoTierCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateShardInfoTierCommand.Unmarshal(m, b)
}
func (m *UpdateShardInfoTierCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateShardInfoTierCommand.Marshal(b, m, deterministic)
}
func (m *UpdateShardInfoTierCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateShardInfoTierCommand.Merge(m, src)
}
func (m *UpdateShardInfoTierCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateShardInfoTierCommand.Size(m)
}
func (m *UpdateShardInfoTierCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateShardInfoTierCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateShardInfoTierCommand proto.InternalMessageInfo

func (m *UpdateShardInfoTierCommand) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *UpdateShardInfoTierCommand) GetTier() uint64 {
	if m != nil && m.Tier != nil {
		return *m.Tier
	}
	return 0
}

func (m *UpdateShardInfoTierCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *UpdateShardInfoTierCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

var E_UpdateShardInfoTierCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateShardInfoTierCommand)(nil),
	Field:         163,
	Name:          "proto.UpdateShardInfoTierCommand.command",
	Tag:           "bytes,163,opt,name=command",
	Filename:      "meta.proto",
}

type CardinalityInfo struct {
	TimeRange            *TimeRangeInfo `protobuf:"bytes,1,req,name=TimeRange" json:"TimeRange,omitempty"`
	Cardinality          *uint64        `protobuf:"varint,2,req,name=Cardinality" json:"Cardinality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CardinalityInfo) Reset()         { *m = CardinalityInfo{} }
func (m *CardinalityInfo) String() string { return proto.CompactTextString(m) }
func (*CardinalityInfo) ProtoMessage()    {}
func (*CardinalityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{100}
}
func (m *CardinalityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardinalityInfo.Unmarshal(m, b)
}
func (m *CardinalityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardinalityInfo.Marshal(b, m, deterministic)
}
func (m *CardinalityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardinalityInfo.Merge(m, src)
}
func (m *CardinalityInfo) XXX_Size() int {
	return xxx_messageInfo_CardinalityInfo.Size(m)
}
func (m *CardinalityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CardinalityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CardinalityInfo proto.InternalMessageInfo

func (m *CardinalityInfo) GetTimeRange() *TimeRangeInfo {
	if m != nil {
		return m.TimeRange
	}
	return nil
}

func (m *CardinalityInfo) GetCardinality() uint64 {
	if m != nil && m.Cardinality != nil {
		return *m.Cardinality
	}
	return 0
}

type MeasurementCardinalityInfo struct {
	Name                 *string            `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Cardinality          []*CardinalityInfo `protobuf:"bytes,2,rep,name=Cardinality" json:"Cardinality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MeasurementCardinalityInfo) Reset()         { *m = MeasurementCardinalityInfo{} }
func (m *MeasurementCardinalityInfo) String() string { return proto.CompactTextString(m) }
func (*MeasurementCardinalityInfo) ProtoMessage()    {}
func (*MeasurementCardinalityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{101}
}
func (m *MeasurementCardinalityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementCardinalityInfo.Unmarshal(m, b)
}
func (m *MeasurementCardinalityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementCardinalityInfo.Marshal(b, m, deterministic)
}
func (m *MeasurementCardinalityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementCardinalityInfo.Merge(m, src)
}
func (m *MeasurementCardinalityInfo) XXX_Size() int {
	return xxx_messageInfo_MeasurementCardinalityInfo.Size(m)
}
func (m *MeasurementCardinalityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementCardinalityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementCardinalityInfo proto.InternalMessageInfo

func (m *MeasurementCardinalityInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *MeasurementCardinalityInfo) GetCardinality() []*CardinalityInfo {
	if m != nil {
		return m.Cardinality
	}
	return nil
}

type CardinalityResponse struct {
	Infos                []*MeasurementCardinalityInfo `protobuf:"bytes,1,rep,name=Infos" json:"Infos,omitempty"`
	Err                  *string                       `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *CardinalityResponse) Reset()         { *m = CardinalityResponse{} }
func (m *CardinalityResponse) String() string { return proto.CompactTextString(m) }
func (*CardinalityResponse) ProtoMessage()    {}
func (*CardinalityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{102}
}
func (m *CardinalityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CardinalityResponse.Unmarshal(m, b)
}
func (m *CardinalityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CardinalityResponse.Marshal(b, m, deterministic)
}
func (m *CardinalityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CardinalityResponse.Merge(m, src)
}
func (m *CardinalityResponse) XXX_Size() int {
	return xxx_messageInfo_CardinalityResponse.Size(m)
}
func (m *CardinalityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CardinalityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CardinalityResponse proto.InternalMessageInfo

func (m *CardinalityResponse) GetInfos() []*MeasurementCardinalityInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *CardinalityResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type UpdateNodeStatusCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Status               *int32   `protobuf:"varint,2,req,name=Status" json:"Status,omitempty"`
	Ltime                *uint64  `protobuf:"varint,3,req,name=Ltime" json:"Ltime,omitempty"`
	GossipAddr           *string  `protobuf:"bytes,4,req,name=GossipAddr" json:"GossipAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNodeStatusCommand) Reset()         { *m = UpdateNodeStatusCommand{} }
func (m *UpdateNodeStatusCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateNodeStatusCommand) ProtoMessage()    {}
func (*UpdateNodeStatusCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{103}
}
func (m *UpdateNodeStatusCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNodeStatusCommand.Unmarshal(m, b)
}
func (m *UpdateNodeStatusCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNodeStatusCommand.Marshal(b, m, deterministic)
}
func (m *UpdateNodeStatusCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNodeStatusCommand.Merge(m, src)
}
func (m *UpdateNodeStatusCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateNodeStatusCommand.Size(m)
}
func (m *UpdateNodeStatusCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNodeStatusCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNodeStatusCommand proto.InternalMessageInfo

func (m *UpdateNodeStatusCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *UpdateNodeStatusCommand) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *UpdateNodeStatusCommand) GetLtime() uint64 {
	if m != nil && m.Ltime != nil {
		return *m.Ltime
	}
	return 0
}

func (m *UpdateNodeStatusCommand) GetGossipAddr() string {
	if m != nil && m.GossipAddr != nil {
		return *m.GossipAddr
	}
	return ""
}

var E_UpdateNodeStatusCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateNodeStatusCommand)(nil),
	Field:         164,
	Name:          "proto.UpdateNodeStatusCommand.command",
	Tag:           "bytes,164,opt,name=command",
	Filename:      "meta.proto",
}

type DbPt struct {
	Db                   *string                       `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	Pt                   *PtInfo                       `protobuf:"bytes,2,req,name=Pt" json:"Pt,omitempty"`
	Shards               map[uint64]*ShardDurationInfo `protobuf:"bytes,3,rep,name=Shards" json:"Shards,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	DBBriefInfo          *DatabaseBriefInfo            `protobuf:"bytes,4,opt,name=DBBriefInfo" json:"DBBriefInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *DbPt) Reset()         { *m = DbPt{} }
func (m *DbPt) String() string { return proto.CompactTextString(m) }
func (*DbPt) ProtoMessage()    {}
func (*DbPt) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{104}
}
func (m *DbPt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DbPt.Unmarshal(m, b)
}
func (m *DbPt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DbPt.Marshal(b, m, deterministic)
}
func (m *DbPt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DbPt.Merge(m, src)
}
func (m *DbPt) XXX_Size() int {
	return xxx_messageInfo_DbPt.Size(m)
}
func (m *DbPt) XXX_DiscardUnknown() {
	xxx_messageInfo_DbPt.DiscardUnknown(m)
}

var xxx_messageInfo_DbPt proto.InternalMessageInfo

func (m *DbPt) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *DbPt) GetPt() *PtInfo {
	if m != nil {
		return m.Pt
	}
	return nil
}

func (m *DbPt) GetShards() map[uint64]*ShardDurationInfo {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *DbPt) GetDBBriefInfo() *DatabaseBriefInfo {
	if m != nil {
		return m.DBBriefInfo
	}
	return nil
}

type MigrateEventInfo struct {
	EventId              *string  `protobuf:"bytes,1,opt,name=eventId" json:"eventId,omitempty"`
	EventType            *int32   `protobuf:"varint,2,opt,name=eventType" json:"eventType,omitempty"`
	OpId                 *uint64  `protobuf:"varint,3,opt,name=opId" json:"opId,omitempty"`
	Pti                  *DbPt    `protobuf:"bytes,4,opt,name=pti" json:"pti,omitempty"`
	CurrState            *int32   `protobuf:"varint,5,opt,name=currState" json:"currState,omitempty"`
	PreState             *int32   `protobuf:"varint,6,opt,name=preState" json:"preState,omitempty"`
	Src                  *uint64  `protobuf:"varint,7,opt,name=src" json:"src,omitempty"`
	Dest                 *uint64  `protobuf:"varint,8,opt,name=dest" json:"dest,omitempty"`
	CheckConflict        *bool    `protobuf:"varint,9,opt,name=checkConflict" json:"checkConflict,omitempty"`
	AliveConnId          *uint64  `protobuf:"varint,10,opt,name=aliveConnId" json:"aliveConnId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MigrateEventInfo) Reset()         { *m = MigrateEventInfo{} }
func (m *MigrateEventInfo) String() string { return proto.CompactTextString(m) }
func (*MigrateEventInfo) ProtoMessage()    {}
func (*MigrateEventInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{105}
}
func (m *MigrateEventInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MigrateEventInfo.Unmarshal(m, b)
}
func (m *MigrateEventInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MigrateEventInfo.Marshal(b, m, deterministic)
}
func (m *MigrateEventInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MigrateEventInfo.Merge(m, src)
}
func (m *MigrateEventInfo) XXX_Size() int {
	return xxx_messageInfo_MigrateEventInfo.Size(m)
}
func (m *MigrateEventInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MigrateEventInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MigrateEventInfo proto.InternalMessageInfo

func (m *MigrateEventInfo) GetEventId() string {
	if m != nil && m.EventId != nil {
		return *m.EventId
	}
	return ""
}

func (m *MigrateEventInfo) GetEventType() int32 {
	if m != nil && m.EventType != nil {
		return *m.EventType
	}
	return 0
}

func (m *MigrateEventInfo) GetOpId() uint64 {
	if m != nil && m.OpId != nil {
		return *m.OpId
	}
	return 0
}

func (m *MigrateEventInfo) GetPti() *DbPt {
	if m != nil {
		return m.Pti
	}
	return nil
}

func (m *MigrateEventInfo) GetCurrState() int32 {
	if m != nil && m.CurrState != nil {
		return *m.CurrState
	}
	return 0
}

func (m *MigrateEventInfo) GetPreState() int32 {
	if m != nil && m.PreState != nil {
		return *m.PreState
	}
	return 0
}

func (m *MigrateEventInfo) GetSrc() uint64 {
	if m != nil && m.Src != nil {
		return *m.Src
	}
	return 0
}

func (m *MigrateEventInfo) GetDest() uint64 {
	if m != nil && m.Dest != nil {
		return *m.Dest
	}
	return 0
}

func (m *MigrateEventInfo) GetCheckConflict() bool {
	if m != nil && m.CheckConflict != nil {
		return *m.CheckConflict
	}
	return false
}

func (m *MigrateEventInfo) GetAliveConnId() uint64 {
	if m != nil && m.AliveConnId != nil {
		return *m.AliveConnId
	}
	return 0
}

type CreateEventCommand struct {
	EventInfo            *MigrateEventInfo `protobuf:"bytes,1,req,name=eventInfo" json:"eventInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateEventCommand) Reset()         { *m = CreateEventCommand{} }
func (m *CreateEventCommand) String() string { return proto.CompactTextString(m) }
func (*CreateEventCommand) ProtoMessage()    {}
func (*CreateEventCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{106}
}
func (m *CreateEventCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEventCommand.Unmarshal(m, b)
}
func (m *CreateEventCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEventCommand.Marshal(b, m, deterministic)
}
func (m *CreateEventCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEventCommand.Merge(m, src)
}
func (m *CreateEventCommand) XXX_Size() int {
	return xxx_messageInfo_CreateEventCommand.Size(m)
}
func (m *CreateEventCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEventCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEventCommand proto.InternalMessageInfo

func (m *CreateEventCommand) GetEventInfo() *MigrateEventInfo {
	if m != nil {
		return m.EventInfo
	}
	return nil
}

var E_CreateEventCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateEventCommand)(nil),
	Field:         165,
	Name:          "proto.CreateEventCommand.command",
	Tag:           "bytes,165,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateEventCommand struct {
	EventInfo            *MigrateEventInfo `protobuf:"bytes,1,req,name=eventInfo" json:"eventInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateEventCommand) Reset()         { *m = UpdateEventCommand{} }
func (m *UpdateEventCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateEventCommand) ProtoMessage()    {}
func (*UpdateEventCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{107}
}
func (m *UpdateEventCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEventCommand.Unmarshal(m, b)
}
func (m *UpdateEventCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEventCommand.Marshal(b, m, deterministic)
}
func (m *UpdateEventCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEventCommand.Merge(m, src)
}
func (m *UpdateEventCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateEventCommand.Size(m)
}
func (m *UpdateEventCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEventCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEventCommand proto.InternalMessageInfo

func (m *UpdateEventCommand) GetEventInfo() *MigrateEventInfo {
	if m != nil {
		return m.EventInfo
	}
	return nil
}

var E_UpdateEventCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateEventCommand)(nil),
	Field:         166,
	Name:          "proto.UpdateEventCommand.command",
	Tag:           "bytes,166,opt,name=command",
	Filename:      "meta.proto",
}

type UpdatePtInfoCommand struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	Pt                   *PtInfo  `protobuf:"bytes,2,req,name=Pt" json:"Pt,omitempty"`
	OwnerNode            *uint64  `protobuf:"varint,3,opt,name=OwnerNode" json:"OwnerNode,omitempty"`
	Status               *uint32  `protobuf:"varint,4,opt,name=Status" json:"Status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePtInfoCommand) Reset()         { *m = UpdatePtInfoCommand{} }
func (m *UpdatePtInfoCommand) String() string { return proto.CompactTextString(m) }
func (*UpdatePtInfoCommand) ProtoMessage()    {}
func (*UpdatePtInfoCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{108}
}
func (m *UpdatePtInfoCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePtInfoCommand.Unmarshal(m, b)
}
func (m *UpdatePtInfoCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePtInfoCommand.Marshal(b, m, deterministic)
}
func (m *UpdatePtInfoCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePtInfoCommand.Merge(m, src)
}
func (m *UpdatePtInfoCommand) XXX_Size() int {
	return xxx_messageInfo_UpdatePtInfoCommand.Size(m)
}
func (m *UpdatePtInfoCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePtInfoCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePtInfoCommand proto.InternalMessageInfo

func (m *UpdatePtInfoCommand) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *UpdatePtInfoCommand) GetPt() *PtInfo {
	if m != nil {
		return m.Pt
	}
	return nil
}

func (m *UpdatePtInfoCommand) GetOwnerNode() uint64 {
	if m != nil && m.OwnerNode != nil {
		return *m.OwnerNode
	}
	return 0
}

func (m *UpdatePtInfoCommand) GetStatus() uint32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

var E_UpdatePtInfoCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdatePtInfoCommand)(nil),
	Field:         167,
	Name:          "proto.UpdatePtInfoCommand.command",
	Tag:           "bytes,167,opt,name=command",
	Filename:      "meta.proto",
}

type RemoveEventCommand struct {
	EventId              *string  `protobuf:"bytes,1,req,name=eventId" json:"eventId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveEventCommand) Reset()         { *m = RemoveEventCommand{} }
func (m *RemoveEventCommand) String() string { return proto.CompactTextString(m) }
func (*RemoveEventCommand) ProtoMessage()    {}
func (*RemoveEventCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{109}
}
func (m *RemoveEventCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveEventCommand.Unmarshal(m, b)
}
func (m *RemoveEventCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveEventCommand.Marshal(b, m, deterministic)
}
func (m *RemoveEventCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveEventCommand.Merge(m, src)
}
func (m *RemoveEventCommand) XXX_Size() int {
	return xxx_messageInfo_RemoveEventCommand.Size(m)
}
func (m *RemoveEventCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveEventCommand.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveEventCommand proto.InternalMessageInfo

func (m *RemoveEventCommand) GetEventId() string {
	if m != nil && m.EventId != nil {
		return *m.EventId
	}
	return ""
}

var E_RemoveEventCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*RemoveEventCommand)(nil),
	Field:         168,
	Name:          "proto.RemoveEventCommand.command",
	Tag:           "bytes,168,opt,name=command",
	Filename:      "meta.proto",
}

type CreateDownSamplePolicyCommand struct {
	DownSamplePolicyInfo *DownSamplePolicyInfo `protobuf:"bytes,1,req,name=DownSamplePolicyInfo" json:"DownSamplePolicyInfo,omitempty"`
	Database             *string               `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	Name                 *string               `protobuf:"bytes,3,req,name=name" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CreateDownSamplePolicyCommand) Reset()         { *m = CreateDownSamplePolicyCommand{} }
func (m *CreateDownSamplePolicyCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDownSamplePolicyCommand) ProtoMessage()    {}
func (*CreateDownSamplePolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{110}
}
func (m *CreateDownSamplePolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDownSamplePolicyCommand.Unmarshal(m, b)
}
func (m *CreateDownSamplePolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDownSamplePolicyCommand.Marshal(b, m, deterministic)
}
func (m *CreateDownSamplePolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDownSamplePolicyCommand.Merge(m, src)
}
func (m *CreateDownSamplePolicyCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDownSamplePolicyCommand.Size(m)
}
func (m *CreateDownSamplePolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDownSamplePolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDownSamplePolicyCommand proto.InternalMessageInfo

func (m *CreateDownSamplePolicyCommand) GetDownSamplePolicyInfo() *DownSamplePolicyInfo {
	if m != nil {
		return m.DownSamplePolicyInfo
	}
	return nil
}

func (m *CreateDownSamplePolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateDownSamplePolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_CreateDownSamplePolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDownSamplePolicyCommand)(nil),
	Field:         169,
	Name:          "proto.CreateDownSamplePolicyCommand.command",
	Tag:           "bytes,169,opt,name=command",
	Filename:      "meta.proto",
}

type DropDownSamplePolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	RpName               *string  `protobuf:"bytes,2,req,name=rpName" json:"rpName,omitempty"`
	DropAll              *bool    `protobuf:"varint,3,opt,name=dropAll" json:"dropAll,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropDownSamplePolicyCommand) Reset()         { *m = DropDownSamplePolicyCommand{} }
func (m *DropDownSamplePolicyCommand) String() string { return proto.CompactTextString(m) }
func (*DropDownSamplePolicyCommand) ProtoMessage()    {}
func (*DropDownSamplePolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{111}
}
func (m *DropDownSamplePolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropDownSamplePolicyCommand.Unmarshal(m, b)
}
func (m *DropDownSamplePolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropDownSamplePolicyCommand.Marshal(b, m, deterministic)
}
func (m *DropDownSamplePolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropDownSamplePolicyCommand.Merge(m, src)
}
func (m *DropDownSamplePolicyCommand) XXX_Size() int {
	return xxx_messageInfo_DropDownSamplePolicyCommand.Size(m)
}
func (m *DropDownSamplePolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropDownSamplePolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropDownSamplePolicyCommand proto.InternalMessageInfo

func (m *DropDownSamplePolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropDownSamplePolicyCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *DropDownSamplePolicyCommand) GetDropAll() bool {
	if m != nil && m.DropAll != nil {
		return *m.DropAll
	}
	return false
}

var E_DropDownSamplePolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropDownSamplePolicyCommand)(nil),
	Field:         170,
	Name:          "proto.DropDownSamplePolicyCommand.command",
	Tag:           "bytes,170,opt,name=command",
	Filename:      "meta.proto",
}

type GetDownSamplePolicyCommand struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDownSamplePolicyCommand) Reset()         { *m = GetDownSamplePolicyCommand{} }
func (m *GetDownSamplePolicyCommand) String() string { return proto.CompactTextString(m) }
func (*GetDownSamplePolicyCommand) ProtoMessage()    {}
func (*GetDownSamplePolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{112}
}
func (m *GetDownSamplePolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDownSamplePolicyCommand.Unmarshal(m, b)
}
func (m *GetDownSamplePolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDownSamplePolicyCommand.Marshal(b, m, deterministic)
}
func (m *GetDownSamplePolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDownSamplePolicyCommand.Merge(m, src)
}
func (m *GetDownSamplePolicyCommand) XXX_Size() int {
	return xxx_messageInfo_GetDownSamplePolicyCommand.Size(m)
}
func (m *GetDownSamplePolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDownSamplePolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_GetDownSamplePolicyCommand proto.InternalMessageInfo

var E_GetDownSamplePolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*GetDownSamplePolicyCommand)(nil),
	Field:         171,
	Name:          "proto.GetDownSamplePolicyCommand.command",
	Tag:           "bytes,171,opt,name=command",
	Filename:      "meta.proto",
}

type CreateDbPtViewCommand struct {
	DbName               *string  `protobuf:"bytes,1,req,name=DbName" json:"DbName,omitempty"`
	ReplicaNum           *uint32  `protobuf:"varint,2,opt,name=replicaNum" json:"replicaNum,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDbPtViewCommand) Reset()         { *m = CreateDbPtViewCommand{} }
func (m *CreateDbPtViewCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDbPtViewCommand) ProtoMessage()    {}
func (*CreateDbPtViewCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{113}
}
func (m *CreateDbPtViewCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDbPtViewCommand.Unmarshal(m, b)
}
func (m *CreateDbPtViewCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDbPtViewCommand.Marshal(b, m, deterministic)
}
func (m *CreateDbPtViewCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDbPtViewCommand.Merge(m, src)
}
func (m *CreateDbPtViewCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDbPtViewCommand.Size(m)
}
func (m *CreateDbPtViewCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDbPtViewCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDbPtViewCommand proto.InternalMessageInfo

func (m *CreateDbPtViewCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *CreateDbPtViewCommand) GetReplicaNum() uint32 {
	if m != nil && m.ReplicaNum != nil {
		return *m.ReplicaNum
	}
	return 0
}

var E_CreateDbPtViewCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDbPtViewCommand)(nil),
	Field:         172,
	Name:          "proto.CreateDbPtViewCommand.command",
	Tag:           "bytes,172,opt,name=command",
	Filename:      "meta.proto",
}

type GetMeasurementInfoWithinSameRpCommand struct {
	DbName               *string  `protobuf:"bytes,1,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	DataTypes            []int64  `protobuf:"varint,3,rep,name=DataTypes" json:"DataTypes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMeasurementInfoWithinSameRpCommand) Reset()         { *m = GetMeasurementInfoWithinSameRpCommand{} }
func (m *GetMeasurementInfoWithinSameRpCommand) String() string { return proto.CompactTextString(m) }
func (*GetMeasurementInfoWithinSameRpCommand) ProtoMessage()    {}
func (*GetMeasurementInfoWithinSameRpCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{114}
}
func (m *GetMeasurementInfoWithinSameRpCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand.Unmarshal(m, b)
}
func (m *GetMeasurementInfoWithinSameRpCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand.Marshal(b, m, deterministic)
}
func (m *GetMeasurementInfoWithinSameRpCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand.Merge(m, src)
}
func (m *GetMeasurementInfoWithinSameRpCommand) XXX_Size() int {
	return xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand.Size(m)
}
func (m *GetMeasurementInfoWithinSameRpCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand.DiscardUnknown(m)
}

var xxx_messageInfo_GetMeasurementInfoWithinSameRpCommand proto.InternalMessageInfo

func (m *GetMeasurementInfoWithinSameRpCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *GetMeasurementInfoWithinSameRpCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *GetMeasurementInfoWithinSameRpCommand) GetDataTypes() []int64 {
	if m != nil {
		return m.DataTypes
	}
	return nil
}

var E_GetMeasurementInfoWithinSameRpCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*GetMeasurementInfoWithinSameRpCommand)(nil),
	Field:         173,
	Name:          "proto.GetMeasurementInfoWithinSameRpCommand.command",
	Tag:           "bytes,173,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateShardDownSampleInfoCommand struct {
	Ident                *ShardIdentifier `protobuf:"bytes,1,req,name=Ident" json:"Ident,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateShardDownSampleInfoCommand) Reset()         { *m = UpdateShardDownSampleInfoCommand{} }
func (m *UpdateShardDownSampleInfoCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateShardDownSampleInfoCommand) ProtoMessage()    {}
func (*UpdateShardDownSampleInfoCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{115}
}
func (m *UpdateShardDownSampleInfoCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateShardDownSampleInfoCommand.Unmarshal(m, b)
}
func (m *UpdateShardDownSampleInfoCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateShardDownSampleInfoCommand.Marshal(b, m, deterministic)
}
func (m *UpdateShardDownSampleInfoCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateShardDownSampleInfoCommand.Merge(m, src)
}
func (m *UpdateShardDownSampleInfoCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateShardDownSampleInfoCommand.Size(m)
}
func (m *UpdateShardDownSampleInfoCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateShardDownSampleInfoCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateShardDownSampleInfoCommand proto.InternalMessageInfo

func (m *UpdateShardDownSampleInfoCommand) GetIdent() *ShardIdentifier {
	if m != nil {
		return m.Ident
	}
	return nil
}

var E_UpdateShardDownSampleInfoCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateShardDownSampleInfoCommand)(nil),
	Field:         174,
	Name:          "proto.UpdateShardDownSampleInfoCommand.command",
	Tag:           "bytes,174,opt,name=command",
	Filename:      "meta.proto",
}

type MarkTakeoverCommand struct {
	Enable               *bool    `protobuf:"varint,1,req,name=enable" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkTakeoverCommand) Reset()         { *m = MarkTakeoverCommand{} }
func (m *MarkTakeoverCommand) String() string { return proto.CompactTextString(m) }
func (*MarkTakeoverCommand) ProtoMessage()    {}
func (*MarkTakeoverCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{116}
}
func (m *MarkTakeoverCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkTakeoverCommand.Unmarshal(m, b)
}
func (m *MarkTakeoverCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkTakeoverCommand.Marshal(b, m, deterministic)
}
func (m *MarkTakeoverCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkTakeoverCommand.Merge(m, src)
}
func (m *MarkTakeoverCommand) XXX_Size() int {
	return xxx_messageInfo_MarkTakeoverCommand.Size(m)
}
func (m *MarkTakeoverCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkTakeoverCommand.DiscardUnknown(m)
}

var xxx_messageInfo_MarkTakeoverCommand proto.InternalMessageInfo

func (m *MarkTakeoverCommand) GetEnable() bool {
	if m != nil && m.Enable != nil {
		return *m.Enable
	}
	return false
}

var E_MarkTakeoverCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*MarkTakeoverCommand)(nil),
	Field:         175,
	Name:          "proto.MarkTakeoverCommand.command",
	Tag:           "bytes,175,opt,name=command",
	Filename:      "meta.proto",
}

type MarkBalancerCommand struct {
	Enable               *bool    `protobuf:"varint,1,req,name=enable" json:"enable,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkBalancerCommand) Reset()         { *m = MarkBalancerCommand{} }
func (m *MarkBalancerCommand) String() string { return proto.CompactTextString(m) }
func (*MarkBalancerCommand) ProtoMessage()    {}
func (*MarkBalancerCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{117}
}
func (m *MarkBalancerCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkBalancerCommand.Unmarshal(m, b)
}
func (m *MarkBalancerCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkBalancerCommand.Marshal(b, m, deterministic)
}
func (m *MarkBalancerCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkBalancerCommand.Merge(m, src)
}
func (m *MarkBalancerCommand) XXX_Size() int {
	return xxx_messageInfo_MarkBalancerCommand.Size(m)
}
func (m *MarkBalancerCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkBalancerCommand.DiscardUnknown(m)
}

var xxx_messageInfo_MarkBalancerCommand proto.InternalMessageInfo

func (m *MarkBalancerCommand) GetEnable() bool {
	if m != nil && m.Enable != nil {
		return *m.Enable
	}
	return false
}

var E_MarkBalancerCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*MarkBalancerCommand)(nil),
	Field:         176,
	Name:          "proto.MarkBalancerCommand.command",
	Tag:           "bytes,176,opt,name=command",
	Filename:      "meta.proto",
}

type CreateStreamCommand struct {
	StreamInfo           *StreamInfo `protobuf:"bytes,1,req,name=StreamInfo" json:"StreamInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CreateStreamCommand) Reset()         { *m = CreateStreamCommand{} }
func (m *CreateStreamCommand) String() string { return proto.CompactTextString(m) }
func (*CreateStreamCommand) ProtoMessage()    {}
func (*CreateStreamCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{118}
}
func (m *CreateStreamCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateStreamCommand.Unmarshal(m, b)
}
func (m *CreateStreamCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateStreamCommand.Marshal(b, m, deterministic)
}
func (m *CreateStreamCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateStreamCommand.Merge(m, src)
}
func (m *CreateStreamCommand) XXX_Size() int {
	return xxx_messageInfo_CreateStreamCommand.Size(m)
}
func (m *CreateStreamCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateStreamCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateStreamCommand proto.InternalMessageInfo

func (m *CreateStreamCommand) GetStreamInfo() *StreamInfo {
	if m != nil {
		return m.StreamInfo
	}
	return nil
}

var E_CreateStreamCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateStreamCommand)(nil),
	Field:         177,
	Name:          "proto.CreateStreamCommand.command",
	Tag:           "bytes,177,opt,name=command",
	Filename:      "meta.proto",
}

type DropStreamCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropStreamCommand) Reset()         { *m = DropStreamCommand{} }
func (m *DropStreamCommand) String() string { return proto.CompactTextString(m) }
func (*DropStreamCommand) ProtoMessage()    {}
func (*DropStreamCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{119}
}
func (m *DropStreamCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropStreamCommand.Unmarshal(m, b)
}
func (m *DropStreamCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropStreamCommand.Marshal(b, m, deterministic)
}
func (m *DropStreamCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropStreamCommand.Merge(m, src)
}
func (m *DropStreamCommand) XXX_Size() int {
	return xxx_messageInfo_DropStreamCommand.Size(m)
}
func (m *DropStreamCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropStreamCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropStreamCommand proto.InternalMessageInfo

func (m *DropStreamCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropStreamCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropStreamCommand)(nil),
	Field:         178,
	Name:          "proto.DropStreamCommand.command",
	Tag:           "bytes,178,opt,name=command",
	Filename:      "meta.proto",
}

type GetMeasurementInfoStoreCommand struct {
	DbName               *string  `protobuf:"bytes,1,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	MstName              *string  `protobuf:"bytes,3,req,name=MstName" json:"MstName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMeasurementInfoStoreCommand) Reset()         { *m = GetMeasurementInfoStoreCommand{} }
func (m *GetMeasurementInfoStoreCommand) String() string { return proto.CompactTextString(m) }
func (*GetMeasurementInfoStoreCommand) ProtoMessage()    {}
func (*GetMeasurementInfoStoreCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{120}
}
func (m *GetMeasurementInfoStoreCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMeasurementInfoStoreCommand.Unmarshal(m, b)
}
func (m *GetMeasurementInfoStoreCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMeasurementInfoStoreCommand.Marshal(b, m, deterministic)
}
func (m *GetMeasurementInfoStoreCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMeasurementInfoStoreCommand.Merge(m, src)
}
func (m *GetMeasurementInfoStoreCommand) XXX_Size() int {
	return xxx_messageInfo_GetMeasurementInfoStoreCommand.Size(m)
}
func (m *GetMeasurementInfoStoreCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMeasurementInfoStoreCommand.DiscardUnknown(m)
}

var xxx_messageInfo_GetMeasurementInfoStoreCommand proto.InternalMessageInfo

func (m *GetMeasurementInfoStoreCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *GetMeasurementInfoStoreCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

func (m *GetMeasurementInfoStoreCommand) GetMstName() string {
	if m != nil && m.MstName != nil {
		return *m.MstName
	}
	return ""
}

var E_GetMeasurementInfoStoreCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*GetMeasurementInfoStoreCommand)(nil),
	Field:         179,
	Name:          "proto.GetMeasurementInfoStoreCommand.command",
	Tag:           "bytes,179,opt,name=command",
	Filename:      "meta.proto",
}

type VerifyDataNodeCommand struct {
	NodeID               *uint64  `protobuf:"varint,1,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VerifyDataNodeCommand) Reset()         { *m = VerifyDataNodeCommand{} }
func (m *VerifyDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*VerifyDataNodeCommand) ProtoMessage()    {}
func (*VerifyDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{121}
}
func (m *VerifyDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VerifyDataNodeCommand.Unmarshal(m, b)
}
func (m *VerifyDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VerifyDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *VerifyDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VerifyDataNodeCommand.Merge(m, src)
}
func (m *VerifyDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_VerifyDataNodeCommand.Size(m)
}
func (m *VerifyDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_VerifyDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_VerifyDataNodeCommand proto.InternalMessageInfo

func (m *VerifyDataNodeCommand) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

var E_VerifyDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*VerifyDataNodeCommand)(nil),
	Field:         180,
	Name:          "proto.VerifyDataNodeCommand.command",
	Tag:           "bytes,180,opt,name=command",
	Filename:      "meta.proto",
}

type ExpandGroupsCommand struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpandGroupsCommand) Reset()         { *m = ExpandGroupsCommand{} }
func (m *ExpandGroupsCommand) String() string { return proto.CompactTextString(m) }
func (*ExpandGroupsCommand) ProtoMessage()    {}
func (*ExpandGroupsCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{122}
}
func (m *ExpandGroupsCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandGroupsCommand.Unmarshal(m, b)
}
func (m *ExpandGroupsCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandGroupsCommand.Marshal(b, m, deterministic)
}
func (m *ExpandGroupsCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandGroupsCommand.Merge(m, src)
}
func (m *ExpandGroupsCommand) XXX_Size() int {
	return xxx_messageInfo_ExpandGroupsCommand.Size(m)
}
func (m *ExpandGroupsCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandGroupsCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandGroupsCommand proto.InternalMessageInfo

var E_ExpandGroupsCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ExpandGroupsCommand)(nil),
	Field:         181,
	Name:          "proto.ExpandGroupsCommand.command",
	Tag:           "bytes,181,opt,name=command",
	Filename:      "meta.proto",
}

type UpdatePtVersionCommand struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	Pt                   *uint32  `protobuf:"varint,2,req,name=Pt" json:"Pt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePtVersionCommand) Reset()         { *m = UpdatePtVersionCommand{} }
func (m *UpdatePtVersionCommand) String() string { return proto.CompactTextString(m) }
func (*UpdatePtVersionCommand) ProtoMessage()    {}
func (*UpdatePtVersionCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{123}
}
func (m *UpdatePtVersionCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePtVersionCommand.Unmarshal(m, b)
}
func (m *UpdatePtVersionCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePtVersionCommand.Marshal(b, m, deterministic)
}
func (m *UpdatePtVersionCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePtVersionCommand.Merge(m, src)
}
func (m *UpdatePtVersionCommand) XXX_Size() int {
	return xxx_messageInfo_UpdatePtVersionCommand.Size(m)
}
func (m *UpdatePtVersionCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePtVersionCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePtVersionCommand proto.InternalMessageInfo

func (m *UpdatePtVersionCommand) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *UpdatePtVersionCommand) GetPt() uint32 {
	if m != nil && m.Pt != nil {
		return *m.Pt
	}
	return 0
}

var E_UpdatePtVersionCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdatePtVersionCommand)(nil),
	Field:         182,
	Name:          "proto.UpdatePtVersionCommand.command",
	Tag:           "bytes,182,opt,name=command",
	Filename:      "meta.proto",
}

type GetMeasurementsInfoCommand struct {
	DbName               *string  `protobuf:"bytes,1,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,2,req,name=RpName" json:"RpName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMeasurementsInfoCommand) Reset()         { *m = GetMeasurementsInfoCommand{} }
func (m *GetMeasurementsInfoCommand) String() string { return proto.CompactTextString(m) }
func (*GetMeasurementsInfoCommand) ProtoMessage()    {}
func (*GetMeasurementsInfoCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{124}
}
func (m *GetMeasurementsInfoCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMeasurementsInfoCommand.Unmarshal(m, b)
}
func (m *GetMeasurementsInfoCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMeasurementsInfoCommand.Marshal(b, m, deterministic)
}
func (m *GetMeasurementsInfoCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMeasurementsInfoCommand.Merge(m, src)
}
func (m *GetMeasurementsInfoCommand) XXX_Size() int {
	return xxx_messageInfo_GetMeasurementsInfoCommand.Size(m)
}
func (m *GetMeasurementsInfoCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMeasurementsInfoCommand.DiscardUnknown(m)
}

var xxx_messageInfo_GetMeasurementsInfoCommand proto.InternalMessageInfo

func (m *GetMeasurementsInfoCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *GetMeasurementsInfoCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

var E_GetMeasurementsInfoCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*GetMeasurementsInfoCommand)(nil),
	Field:         183,
	Name:          "proto.GetMeasurementsInfoCommand.command",
	Tag:           "bytes,183,opt,name=command",
	Filename:      "meta.proto",
}

type DatabaseBriefInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	EnableTagArray       *bool    `protobuf:"varint,2,req,name=EnableTagArray" json:"EnableTagArray,omitempty"`
	Replicas             *int32   `protobuf:"varint,3,opt,name=replicas" json:"replicas,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DatabaseBriefInfo) Reset()         { *m = DatabaseBriefInfo{} }
func (m *DatabaseBriefInfo) String() string { return proto.CompactTextString(m) }
func (*DatabaseBriefInfo) ProtoMessage()    {}
func (*DatabaseBriefInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{125}
}
func (m *DatabaseBriefInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatabaseBriefInfo.Unmarshal(m, b)
}
func (m *DatabaseBriefInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatabaseBriefInfo.Marshal(b, m, deterministic)
}
func (m *DatabaseBriefInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatabaseBriefInfo.Merge(m, src)
}
func (m *DatabaseBriefInfo) XXX_Size() int {
	return xxx_messageInfo_DatabaseBriefInfo.Size(m)
}
func (m *DatabaseBriefInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DatabaseBriefInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DatabaseBriefInfo proto.InternalMessageInfo

func (m *DatabaseBriefInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DatabaseBriefInfo) GetEnableTagArray() bool {
	if m != nil && m.EnableTagArray != nil {
		return *m.EnableTagArray
	}
	return false
}

func (m *DatabaseBriefInfo) GetReplicas() int32 {
	if m != nil && m.Replicas != nil {
		return *m.Replicas
	}
	return 0
}

type MeasurementsInfo struct {
	MeasurementsInfo     []*MeasurementInfo `protobuf:"bytes,1,rep,name=MeasurementsInfo" json:"MeasurementsInfo,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MeasurementsInfo) Reset()         { *m = MeasurementsInfo{} }
func (m *MeasurementsInfo) String() string { return proto.CompactTextString(m) }
func (*MeasurementsInfo) ProtoMessage()    {}
func (*MeasurementsInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{126}
}
func (m *MeasurementsInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementsInfo.Unmarshal(m, b)
}
func (m *MeasurementsInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementsInfo.Marshal(b, m, deterministic)
}
func (m *MeasurementsInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementsInfo.Merge(m, src)
}
func (m *MeasurementsInfo) XXX_Size() int {
	return xxx_messageInfo_MeasurementsInfo.Size(m)
}
func (m *MeasurementsInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementsInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementsInfo proto.InternalMessageInfo

func (m *MeasurementsInfo) GetMeasurementsInfo() []*MeasurementInfo {
	if m != nil {
		return m.MeasurementsInfo
	}
	return nil
}

type RegisterQueryIDOffsetCommand struct {
	Host                 *string  `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegisterQueryIDOffsetCommand) Reset()         { *m = RegisterQueryIDOffsetCommand{} }
func (m *RegisterQueryIDOffsetCommand) String() string { return proto.CompactTextString(m) }
func (*RegisterQueryIDOffsetCommand) ProtoMessage()    {}
func (*RegisterQueryIDOffsetCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{127}
}
func (m *RegisterQueryIDOffsetCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegisterQueryIDOffsetCommand.Unmarshal(m, b)
}
func (m *RegisterQueryIDOffsetCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegisterQueryIDOffsetCommand.Marshal(b, m, deterministic)
}
func (m *RegisterQueryIDOffsetCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegisterQueryIDOffsetCommand.Merge(m, src)
}
func (m *RegisterQueryIDOffsetCommand) XXX_Size() int {
	return xxx_messageInfo_RegisterQueryIDOffsetCommand.Size(m)
}
func (m *RegisterQueryIDOffsetCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_RegisterQueryIDOffsetCommand.DiscardUnknown(m)
}

var xxx_messageInfo_RegisterQueryIDOffsetCommand proto.InternalMessageInfo

func (m *RegisterQueryIDOffsetCommand) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

var E_RegisterQueryIDOffsetCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*RegisterQueryIDOffsetCommand)(nil),
	Field:         184,
	Name:          "proto.RegisterQueryIDOffsetCommand.command",
	Tag:           "bytes,184,opt,name=command",
	Filename:      "meta.proto",
}

type CreateContinuousQueryCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	Query                *string  `protobuf:"bytes,3,req,name=Query" json:"Query,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateContinuousQueryCommand) Reset()         { *m = CreateContinuousQueryCommand{} }
func (m *CreateContinuousQueryCommand) String() string { return proto.CompactTextString(m) }
func (*CreateContinuousQueryCommand) ProtoMessage()    {}
func (*CreateContinuousQueryCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{128}
}
func (m *CreateContinuousQueryCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateContinuousQueryCommand.Unmarshal(m, b)
}
func (m *CreateContinuousQueryCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateContinuousQueryCommand.Marshal(b, m, deterministic)
}
func (m *CreateContinuousQueryCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateContinuousQueryCommand.Merge(m, src)
}
func (m *CreateContinuousQueryCommand) XXX_Size() int {
	return xxx_messageInfo_CreateContinuousQueryCommand.Size(m)
}
func (m *CreateContinuousQueryCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateContinuousQueryCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateContinuousQueryCommand proto.InternalMessageInfo

func (m *CreateContinuousQueryCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateContinuousQueryCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateContinuousQueryCommand) GetQuery() string {
	if m != nil && m.Query != nil {
		return *m.Query
	}
	return ""
}

var E_CreateContinuousQueryCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateContinuousQueryCommand)(nil),
	Field:         185,
	Name:          "proto.CreateContinuousQueryCommand.command",
	Tag:           "bytes,185,opt,name=command",
	Filename:      "meta.proto",
}

type Sql2MetaHeartbeatCommand struct {
	Host                 *string  `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Sql2MetaHeartbeatCommand) Reset()         { *m = Sql2MetaHeartbeatCommand{} }
func (m *Sql2MetaHeartbeatCommand) String() string { return proto.CompactTextString(m) }
func (*Sql2MetaHeartbeatCommand) ProtoMessage()    {}
func (*Sql2MetaHeartbeatCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{129}
}
func (m *Sql2MetaHeartbeatCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Sql2MetaHeartbeatCommand.Unmarshal(m, b)
}
func (m *Sql2MetaHeartbeatCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Sql2MetaHeartbeatCommand.Marshal(b, m, deterministic)
}
func (m *Sql2MetaHeartbeatCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Sql2MetaHeartbeatCommand.Merge(m, src)
}
func (m *Sql2MetaHeartbeatCommand) XXX_Size() int {
	return xxx_messageInfo_Sql2MetaHeartbeatCommand.Size(m)
}
func (m *Sql2MetaHeartbeatCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_Sql2MetaHeartbeatCommand.DiscardUnknown(m)
}

var xxx_messageInfo_Sql2MetaHeartbeatCommand proto.InternalMessageInfo

func (m *Sql2MetaHeartbeatCommand) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

var E_Sql2MetaHeartbeatCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*Sql2MetaHeartbeatCommand)(nil),
	Field:         186,
	Name:          "proto.Sql2MetaHeartbeatCommand.command",
	Tag:           "bytes,186,opt,name=command",
	Filename:      "meta.proto",
}

type ContinuousQueryReportCommand struct {
	CQStates             []*CQState `protobuf:"bytes,1,rep,name=CQStates" json:"CQStates,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ContinuousQueryReportCommand) Reset()         { *m = ContinuousQueryReportCommand{} }
func (m *ContinuousQueryReportCommand) String() string { return proto.CompactTextString(m) }
func (*ContinuousQueryReportCommand) ProtoMessage()    {}
func (*ContinuousQueryReportCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{130}
}
func (m *ContinuousQueryReportCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinuousQueryReportCommand.Unmarshal(m, b)
}
func (m *ContinuousQueryReportCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinuousQueryReportCommand.Marshal(b, m, deterministic)
}
func (m *ContinuousQueryReportCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinuousQueryReportCommand.Merge(m, src)
}
func (m *ContinuousQueryReportCommand) XXX_Size() int {
	return xxx_messageInfo_ContinuousQueryReportCommand.Size(m)
}
func (m *ContinuousQueryReportCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinuousQueryReportCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ContinuousQueryReportCommand proto.InternalMessageInfo

func (m *ContinuousQueryReportCommand) GetCQStates() []*CQState {
	if m != nil {
		return m.CQStates
	}
	return nil
}

var E_ContinuousQueryReportCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ContinuousQueryReportCommand)(nil),
	Field:         187,
	Name:          "proto.ContinuousQueryReportCommand.command",
	Tag:           "bytes,187,opt,name=command",
	Filename:      "meta.proto",
}

type CQState struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	LastRunTime          *int64   `protobuf:"varint,2,req,name=LastRunTime" json:"LastRunTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CQState) Reset()         { *m = CQState{} }
func (m *CQState) String() string { return proto.CompactTextString(m) }
func (*CQState) ProtoMessage()    {}
func (*CQState) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{131}
}
func (m *CQState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CQState.Unmarshal(m, b)
}
func (m *CQState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CQState.Marshal(b, m, deterministic)
}
func (m *CQState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CQState.Merge(m, src)
}
func (m *CQState) XXX_Size() int {
	return xxx_messageInfo_CQState.Size(m)
}
func (m *CQState) XXX_DiscardUnknown() {
	xxx_messageInfo_CQState.DiscardUnknown(m)
}

var xxx_messageInfo_CQState proto.InternalMessageInfo

func (m *CQState) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CQState) GetLastRunTime() int64 {
	if m != nil && m.LastRunTime != nil {
		return *m.LastRunTime
	}
	return 0
}

type GetContinuousQueryLeaseCommand struct {
	Host                 *string  `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetContinuousQueryLeaseCommand) Reset()         { *m = GetContinuousQueryLeaseCommand{} }
func (m *GetContinuousQueryLeaseCommand) String() string { return proto.CompactTextString(m) }
func (*GetContinuousQueryLeaseCommand) ProtoMessage()    {}
func (*GetContinuousQueryLeaseCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{132}
}
func (m *GetContinuousQueryLeaseCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetContinuousQueryLeaseCommand.Unmarshal(m, b)
}
func (m *GetContinuousQueryLeaseCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetContinuousQueryLeaseCommand.Marshal(b, m, deterministic)
}
func (m *GetContinuousQueryLeaseCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetContinuousQueryLeaseCommand.Merge(m, src)
}
func (m *GetContinuousQueryLeaseCommand) XXX_Size() int {
	return xxx_messageInfo_GetContinuousQueryLeaseCommand.Size(m)
}
func (m *GetContinuousQueryLeaseCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_GetContinuousQueryLeaseCommand.DiscardUnknown(m)
}

var xxx_messageInfo_GetContinuousQueryLeaseCommand proto.InternalMessageInfo

func (m *GetContinuousQueryLeaseCommand) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

var E_GetContinuousQueryLeaseCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*GetContinuousQueryLeaseCommand)(nil),
	Field:         188,
	Name:          "proto.GetContinuousQueryLeaseCommand.command",
	Tag:           "bytes,188,opt,name=command",
	Filename:      "meta.proto",
}

type DropContinuousQueryCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropContinuousQueryCommand) Reset()         { *m = DropContinuousQueryCommand{} }
func (m *DropContinuousQueryCommand) String() string { return proto.CompactTextString(m) }
func (*DropContinuousQueryCommand) ProtoMessage()    {}
func (*DropContinuousQueryCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{133}
}
func (m *DropContinuousQueryCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropContinuousQueryCommand.Unmarshal(m, b)
}
func (m *DropContinuousQueryCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropContinuousQueryCommand.Marshal(b, m, deterministic)
}
func (m *DropContinuousQueryCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropContinuousQueryCommand.Merge(m, src)
}
func (m *DropContinuousQueryCommand) XXX_Size() int {
	return xxx_messageInfo_DropContinuousQueryCommand.Size(m)
}
func (m *DropContinuousQueryCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropContinuousQueryCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropContinuousQueryCommand proto.InternalMessageInfo

func (m *DropContinuousQueryCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DropContinuousQueryCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

var E_DropContinuousQueryCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropContinuousQueryCommand)(nil),
	Field:         189,
	Name:          "proto.DropContinuousQueryCommand.command",
	Tag:           "bytes,189,opt,name=command",
	Filename:      "meta.proto",
}

type NotifyCQLeaseChangedCommand struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyCQLeaseChangedCommand) Reset()         { *m = NotifyCQLeaseChangedCommand{} }
func (m *NotifyCQLeaseChangedCommand) String() string { return proto.CompactTextString(m) }
func (*NotifyCQLeaseChangedCommand) ProtoMessage()    {}
func (*NotifyCQLeaseChangedCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{134}
}
func (m *NotifyCQLeaseChangedCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyCQLeaseChangedCommand.Unmarshal(m, b)
}
func (m *NotifyCQLeaseChangedCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyCQLeaseChangedCommand.Marshal(b, m, deterministic)
}
func (m *NotifyCQLeaseChangedCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyCQLeaseChangedCommand.Merge(m, src)
}
func (m *NotifyCQLeaseChangedCommand) XXX_Size() int {
	return xxx_messageInfo_NotifyCQLeaseChangedCommand.Size(m)
}
func (m *NotifyCQLeaseChangedCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyCQLeaseChangedCommand.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyCQLeaseChangedCommand proto.InternalMessageInfo

var E_NotifyCQLeaseChangedCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*NotifyCQLeaseChangedCommand)(nil),
	Field:         190,
	Name:          "proto.NotifyCQLeaseChangedCommand.command",
	Tag:           "bytes,190,opt,name=command",
	Filename:      "meta.proto",
}

type SetNodeSegregateStatusCommand struct {
	Status               []uint64 `protobuf:"varint,1,rep,name=status" json:"status,omitempty"`
	NodeIds              []uint64 `protobuf:"varint,2,rep,name=nodeIds" json:"nodeIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetNodeSegregateStatusCommand) Reset()         { *m = SetNodeSegregateStatusCommand{} }
func (m *SetNodeSegregateStatusCommand) String() string { return proto.CompactTextString(m) }
func (*SetNodeSegregateStatusCommand) ProtoMessage()    {}
func (*SetNodeSegregateStatusCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{135}
}
func (m *SetNodeSegregateStatusCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetNodeSegregateStatusCommand.Unmarshal(m, b)
}
func (m *SetNodeSegregateStatusCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetNodeSegregateStatusCommand.Marshal(b, m, deterministic)
}
func (m *SetNodeSegregateStatusCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetNodeSegregateStatusCommand.Merge(m, src)
}
func (m *SetNodeSegregateStatusCommand) XXX_Size() int {
	return xxx_messageInfo_SetNodeSegregateStatusCommand.Size(m)
}
func (m *SetNodeSegregateStatusCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetNodeSegregateStatusCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetNodeSegregateStatusCommand proto.InternalMessageInfo

func (m *SetNodeSegregateStatusCommand) GetStatus() []uint64 {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *SetNodeSegregateStatusCommand) GetNodeIds() []uint64 {
	if m != nil {
		return m.NodeIds
	}
	return nil
}

var E_SetNodeSegregateStatusCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetNodeSegregateStatusCommand)(nil),
	Field:         191,
	Name:          "proto.SetNodeSegregateStatusCommand.command",
	Tag:           "bytes,191,opt,name=command",
	Filename:      "meta.proto",
}

type RemoveNodeCommand struct {
	NodeIds              []uint64 `protobuf:"varint,1,rep,name=nodeIds" json:"nodeIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveNodeCommand) Reset()         { *m = RemoveNodeCommand{} }
func (m *RemoveNodeCommand) String() string { return proto.CompactTextString(m) }
func (*RemoveNodeCommand) ProtoMessage()    {}
func (*RemoveNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{136}
}
func (m *RemoveNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveNodeCommand.Unmarshal(m, b)
}
func (m *RemoveNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveNodeCommand.Marshal(b, m, deterministic)
}
func (m *RemoveNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveNodeCommand.Merge(m, src)
}
func (m *RemoveNodeCommand) XXX_Size() int {
	return xxx_messageInfo_RemoveNodeCommand.Size(m)
}
func (m *RemoveNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveNodeCommand proto.InternalMessageInfo

func (m *RemoveNodeCommand) GetNodeIds() []uint64 {
	if m != nil {
		return m.NodeIds
	}
	return nil
}

var E_RemoveNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*RemoveNodeCommand)(nil),
	Field:         192,
	Name:          "proto.RemoveNodeCommand.command",
	Tag:           "bytes,192,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateReplicationCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=database" json:"database,omitempty"`
	RepGroupId           *uint32  `protobuf:"varint,2,req,name=repGroupId" json:"repGroupId,omitempty"`
	MasterId             *uint32  `protobuf:"varint,3,opt,name=masterId" json:"masterId,omitempty"`
	Peers                []*Peer  `protobuf:"bytes,4,rep,name=Peers" json:"Peers,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateReplicationCommand) Reset()         { *m = UpdateReplicationCommand{} }
func (m *UpdateReplicationCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateReplicationCommand) ProtoMessage()    {}
func (*UpdateReplicationCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{137}
}
func (m *UpdateReplicationCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateReplicationCommand.Unmarshal(m, b)
}
func (m *UpdateReplicationCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateReplicationCommand.Marshal(b, m, deterministic)
}
func (m *UpdateReplicationCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateReplicationCommand.Merge(m, src)
}
func (m *UpdateReplicationCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateReplicationCommand.Size(m)
}
func (m *UpdateReplicationCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateReplicationCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateReplicationCommand proto.InternalMessageInfo

func (m *UpdateReplicationCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UpdateReplicationCommand) GetRepGroupId() uint32 {
	if m != nil && m.RepGroupId != nil {
		return *m.RepGroupId
	}
	return 0
}

func (m *UpdateReplicationCommand) GetMasterId() uint32 {
	if m != nil && m.MasterId != nil {
		return *m.MasterId
	}
	return 0
}

func (m *UpdateReplicationCommand) GetPeers() []*Peer {
	if m != nil {
		return m.Peers
	}
	return nil
}

var E_UpdateReplicationCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateReplicationCommand)(nil),
	Field:         193,
	Name:          "proto.UpdateReplicationCommand.command",
	Tag:           "bytes,193,opt,name=command",
	Filename:      "meta.proto",
}

type ObsOptions struct {
	Enabled              *bool    `protobuf:"varint,1,opt,name=Enabled" json:"Enabled,omitempty"`
	BucketName           *string  `protobuf:"bytes,2,opt,name=BucketName" json:"BucketName,omitempty"`
	Ak                   *string  `protobuf:"bytes,3,opt,name=Ak" json:"Ak,omitempty"`
	Sk                   *string  `protobuf:"bytes,4,opt,name=Sk" json:"Sk,omitempty"`
	Endpoint             *string  `protobuf:"bytes,5,opt,name=Endpoint" json:"Endpoint,omitempty"`
	BasePath             *string  `protobuf:"bytes,6,opt,name=BasePath" json:"BasePath,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ObsOptions) Reset()         { *m = ObsOptions{} }
func (m *ObsOptions) String() string { return proto.CompactTextString(m) }
func (*ObsOptions) ProtoMessage()    {}
func (*ObsOptions) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{138}
}
func (m *ObsOptions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ObsOptions.Unmarshal(m, b)
}
func (m *ObsOptions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ObsOptions.Marshal(b, m, deterministic)
}
func (m *ObsOptions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ObsOptions.Merge(m, src)
}
func (m *ObsOptions) XXX_Size() int {
	return xxx_messageInfo_ObsOptions.Size(m)
}
func (m *ObsOptions) XXX_DiscardUnknown() {
	xxx_messageInfo_ObsOptions.DiscardUnknown(m)
}

var xxx_messageInfo_ObsOptions proto.InternalMessageInfo

func (m *ObsOptions) GetEnabled() bool {
	if m != nil && m.Enabled != nil {
		return *m.Enabled
	}
	return false
}

func (m *ObsOptions) GetBucketName() string {
	if m != nil && m.BucketName != nil {
		return *m.BucketName
	}
	return ""
}

func (m *ObsOptions) GetAk() string {
	if m != nil && m.Ak != nil {
		return *m.Ak
	}
	return ""
}

func (m *ObsOptions) GetSk() string {
	if m != nil && m.Sk != nil {
		return *m.Sk
	}
	return ""
}

func (m *ObsOptions) GetEndpoint() string {
	if m != nil && m.Endpoint != nil {
		return *m.Endpoint
	}
	return ""
}

func (m *ObsOptions) GetBasePath() string {
	if m != nil && m.BasePath != nil {
		return *m.BasePath
	}
	return ""
}

type Options struct {
	CaseInSensitive      *bool    `protobuf:"varint,1,opt,name=CaseInSensitive" json:"CaseInSensitive,omitempty"`
	AppendMeta           *bool    `protobuf:"varint,2,opt,name=AppendMeta" json:"AppendMeta,omitempty"`
	WriteThreshold       *int32   `protobuf:"varint,3,opt,name=WriteThreshold" json:"WriteThreshold,omitempty"`
	ReadThreshold        *int32   `protobuf:"varint,4,opt,name=ReadThreshold" json:"ReadThreshold,omitempty"`
	StorageCapacity      *int32   `protobuf:"varint,5,opt,name=StorageCapacity" json:"StorageCapacity,omitempty"`
	SplitChar            *string  `protobuf:"bytes,6,opt,name=SplitChar" json:"SplitChar,omitempty"`
	Ttl                  *int64   `protobuf:"varint,7,opt,name=Ttl" json:"Ttl,omitempty"`
	TagsSplit            *string  `protobuf:"bytes,10,opt,name=TagsSplit" json:"TagsSplit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Options) Reset()         { *m = Options{} }
func (m *Options) String() string { return proto.CompactTextString(m) }
func (*Options) ProtoMessage()    {}
func (*Options) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{139}
}
func (m *Options) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Options.Unmarshal(m, b)
}
func (m *Options) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Options.Marshal(b, m, deterministic)
}
func (m *Options) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Options.Merge(m, src)
}
func (m *Options) XXX_Size() int {
	return xxx_messageInfo_Options.Size(m)
}
func (m *Options) XXX_DiscardUnknown() {
	xxx_messageInfo_Options.DiscardUnknown(m)
}

var xxx_messageInfo_Options proto.InternalMessageInfo

func (m *Options) GetCaseInSensitive() bool {
	if m != nil && m.CaseInSensitive != nil {
		return *m.CaseInSensitive
	}
	return false
}

func (m *Options) GetAppendMeta() bool {
	if m != nil && m.AppendMeta != nil {
		return *m.AppendMeta
	}
	return false
}

func (m *Options) GetWriteThreshold() int32 {
	if m != nil && m.WriteThreshold != nil {
		return *m.WriteThreshold
	}
	return 0
}

func (m *Options) GetReadThreshold() int32 {
	if m != nil && m.ReadThreshold != nil {
		return *m.ReadThreshold
	}
	return 0
}

func (m *Options) GetStorageCapacity() int32 {
	if m != nil && m.StorageCapacity != nil {
		return *m.StorageCapacity
	}
	return 0
}

func (m *Options) GetSplitChar() string {
	if m != nil && m.SplitChar != nil {
		return *m.SplitChar
	}
	return ""
}

func (m *Options) GetTtl() int64 {
	if m != nil && m.Ttl != nil {
		return *m.Ttl
	}
	return 0
}

func (m *Options) GetTagsSplit() string {
	if m != nil && m.TagsSplit != nil {
		return *m.TagsSplit
	}
	return ""
}

type UpdateMeasurementCommand struct {
	Db                   *string  `protobuf:"bytes,1,req,name=Db" json:"Db,omitempty"`
	Rp                   *string  `protobuf:"bytes,2,req,name=Rp" json:"Rp,omitempty"`
	Mst                  *string  `protobuf:"bytes,3,req,name=Mst" json:"Mst,omitempty"`
	Options              *Options `protobuf:"bytes,4,req,name=Options" json:"Options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMeasurementCommand) Reset()         { *m = UpdateMeasurementCommand{} }
func (m *UpdateMeasurementCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateMeasurementCommand) ProtoMessage()    {}
func (*UpdateMeasurementCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{140}
}
func (m *UpdateMeasurementCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMeasurementCommand.Unmarshal(m, b)
}
func (m *UpdateMeasurementCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMeasurementCommand.Marshal(b, m, deterministic)
}
func (m *UpdateMeasurementCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMeasurementCommand.Merge(m, src)
}
func (m *UpdateMeasurementCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateMeasurementCommand.Size(m)
}
func (m *UpdateMeasurementCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMeasurementCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMeasurementCommand proto.InternalMessageInfo

func (m *UpdateMeasurementCommand) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *UpdateMeasurementCommand) GetRp() string {
	if m != nil && m.Rp != nil {
		return *m.Rp
	}
	return ""
}

func (m *UpdateMeasurementCommand) GetMst() string {
	if m != nil && m.Mst != nil {
		return *m.Mst
	}
	return ""
}

func (m *UpdateMeasurementCommand) GetOptions() *Options {
	if m != nil {
		return m.Options
	}
	return nil
}

var E_UpdateMeasurementCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateMeasurementCommand)(nil),
	Field:         194,
	Name:          "proto.UpdateMeasurementCommand.command",
	Tag:           "bytes,194,opt,name=command",
	Filename:      "meta.proto",
}

type DataOps struct {
	Op                   []string `protobuf:"bytes,1,rep,name=Op" json:"Op,omitempty"`
	NewIndex             *int64   `protobuf:"varint,2,opt,name=newIndex" json:"newIndex,omitempty"`
	MaxCQChangeID        *int64   `protobuf:"varint,3,opt,name=MaxCQChangeID" json:"MaxCQChangeID,omitempty"`
	GetOpState           *int32   `protobuf:"varint,4,opt,name=GetOpState" json:"GetOpState,omitempty"`
	Data                 *Data    `protobuf:"bytes,5,opt,name=Data" json:"Data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DataOps) Reset()         { *m = DataOps{} }
func (m *DataOps) String() string { return proto.CompactTextString(m) }
func (*DataOps) ProtoMessage()    {}
func (*DataOps) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{141}
}
func (m *DataOps) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataOps.Unmarshal(m, b)
}
func (m *DataOps) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataOps.Marshal(b, m, deterministic)
}
func (m *DataOps) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataOps.Merge(m, src)
}
func (m *DataOps) XXX_Size() int {
	return xxx_messageInfo_DataOps.Size(m)
}
func (m *DataOps) XXX_DiscardUnknown() {
	xxx_messageInfo_DataOps.DiscardUnknown(m)
}

var xxx_messageInfo_DataOps proto.InternalMessageInfo

func (m *DataOps) GetOp() []string {
	if m != nil {
		return m.Op
	}
	return nil
}

func (m *DataOps) GetNewIndex() int64 {
	if m != nil && m.NewIndex != nil {
		return *m.NewIndex
	}
	return 0
}

func (m *DataOps) GetMaxCQChangeID() int64 {
	if m != nil && m.MaxCQChangeID != nil {
		return *m.MaxCQChangeID
	}
	return 0
}

func (m *DataOps) GetGetOpState() int32 {
	if m != nil && m.GetOpState != nil {
		return *m.GetOpState
	}
	return 0
}

func (m *DataOps) GetData() *Data {
	if m != nil {
		return m.Data
	}
	return nil
}

type CreateSqlNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	GossipAddr           *string  `protobuf:"bytes,2,opt,name=GossipAddr" json:"GossipAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSqlNodeCommand) Reset()         { *m = CreateSqlNodeCommand{} }
func (m *CreateSqlNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateSqlNodeCommand) ProtoMessage()    {}
func (*CreateSqlNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{142}
}
func (m *CreateSqlNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSqlNodeCommand.Unmarshal(m, b)
}
func (m *CreateSqlNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSqlNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateSqlNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSqlNodeCommand.Merge(m, src)
}
func (m *CreateSqlNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateSqlNodeCommand.Size(m)
}
func (m *CreateSqlNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSqlNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSqlNodeCommand proto.InternalMessageInfo

func (m *CreateSqlNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *CreateSqlNodeCommand) GetGossipAddr() string {
	if m != nil && m.GossipAddr != nil {
		return *m.GossipAddr
	}
	return ""
}

var E_CreateSqlNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateSqlNodeCommand)(nil),
	Field:         195,
	Name:          "proto.CreateSqlNodeCommand.command",
	Tag:           "bytes,195,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateSqlNodeStatusCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Status               *int32   `protobuf:"varint,2,req,name=Status" json:"Status,omitempty"`
	Ltime                *uint64  `protobuf:"varint,3,req,name=Ltime" json:"Ltime,omitempty"`
	GossipAddr           *string  `protobuf:"bytes,4,opt,name=GossipAddr" json:"GossipAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSqlNodeStatusCommand) Reset()         { *m = UpdateSqlNodeStatusCommand{} }
func (m *UpdateSqlNodeStatusCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateSqlNodeStatusCommand) ProtoMessage()    {}
func (*UpdateSqlNodeStatusCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{143}
}
func (m *UpdateSqlNodeStatusCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSqlNodeStatusCommand.Unmarshal(m, b)
}
func (m *UpdateSqlNodeStatusCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSqlNodeStatusCommand.Marshal(b, m, deterministic)
}
func (m *UpdateSqlNodeStatusCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSqlNodeStatusCommand.Merge(m, src)
}
func (m *UpdateSqlNodeStatusCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateSqlNodeStatusCommand.Size(m)
}
func (m *UpdateSqlNodeStatusCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSqlNodeStatusCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSqlNodeStatusCommand proto.InternalMessageInfo

func (m *UpdateSqlNodeStatusCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *UpdateSqlNodeStatusCommand) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *UpdateSqlNodeStatusCommand) GetLtime() uint64 {
	if m != nil && m.Ltime != nil {
		return *m.Ltime
	}
	return 0
}

func (m *UpdateSqlNodeStatusCommand) GetGossipAddr() string {
	if m != nil && m.GossipAddr != nil {
		return *m.GossipAddr
	}
	return ""
}

var E_UpdateSqlNodeStatusCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateSqlNodeStatusCommand)(nil),
	Field:         196,
	Name:          "proto.UpdateSqlNodeStatusCommand.command",
	Tag:           "bytes,196,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateNodeTmpIndexCommand struct {
	Role                 *int32   `protobuf:"varint,1,req,name=Role" json:"Role,omitempty"`
	Index                *uint64  `protobuf:"varint,2,req,name=Index" json:"Index,omitempty"`
	NodeId               *uint64  `protobuf:"varint,3,req,name=NodeId" json:"NodeId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNodeTmpIndexCommand) Reset()         { *m = UpdateNodeTmpIndexCommand{} }
func (m *UpdateNodeTmpIndexCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateNodeTmpIndexCommand) ProtoMessage()    {}
func (*UpdateNodeTmpIndexCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{144}
}
func (m *UpdateNodeTmpIndexCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNodeTmpIndexCommand.Unmarshal(m, b)
}
func (m *UpdateNodeTmpIndexCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNodeTmpIndexCommand.Marshal(b, m, deterministic)
}
func (m *UpdateNodeTmpIndexCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNodeTmpIndexCommand.Merge(m, src)
}
func (m *UpdateNodeTmpIndexCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateNodeTmpIndexCommand.Size(m)
}
func (m *UpdateNodeTmpIndexCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNodeTmpIndexCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNodeTmpIndexCommand proto.InternalMessageInfo

func (m *UpdateNodeTmpIndexCommand) GetRole() int32 {
	if m != nil && m.Role != nil {
		return *m.Role
	}
	return 0
}

func (m *UpdateNodeTmpIndexCommand) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

func (m *UpdateNodeTmpIndexCommand) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

var E_UpdateNodeTmpIndexCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateNodeTmpIndexCommand)(nil),
	Field:         197,
	Name:          "proto.UpdateNodeTmpIndexCommand.command",
	Tag:           "bytes,197,opt,name=command",
	Filename:      "meta.proto",
}

type FileInfo struct {
	Sequence             *uint64  `protobuf:"varint,1,opt,name=sequence" json:"sequence,omitempty"`
	Level                *uint32  `protobuf:"varint,2,opt,name=level" json:"level,omitempty"`
	Merge                *uint32  `protobuf:"varint,3,opt,name=merge" json:"merge,omitempty"`
	Extent               *uint32  `protobuf:"varint,4,opt,name=extent" json:"extent,omitempty"`
	MstID                *uint64  `protobuf:"varint,5,opt,name=mstID" json:"mstID,omitempty"`
	ShardID              *uint64  `protobuf:"varint,6,opt,name=shardID" json:"shardID,omitempty"`
	DeletedAt            *int64   `protobuf:"varint,7,opt,name=deletedAt" json:"deletedAt,omitempty"`
	CreatedAt            *int64   `protobuf:"varint,8,opt,name=createdAt" json:"createdAt,omitempty"`
	MinTime              *int64   `protobuf:"varint,9,opt,name=minTime" json:"minTime,omitempty"`
	MaxTime              *int64   `protobuf:"varint,10,opt,name=maxTime" json:"maxTime,omitempty"`
	RowCount             *int64   `protobuf:"varint,11,opt,name=rowCount" json:"rowCount,omitempty"`
	FileSizeBytes        *int64   `protobuf:"varint,12,opt,name=fileSizeBytes" json:"fileSizeBytes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FileInfo) Reset()         { *m = FileInfo{} }
func (m *FileInfo) String() string { return proto.CompactTextString(m) }
func (*FileInfo) ProtoMessage()    {}
func (*FileInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{145}
}
func (m *FileInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FileInfo.Unmarshal(m, b)
}
func (m *FileInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FileInfo.Marshal(b, m, deterministic)
}
func (m *FileInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileInfo.Merge(m, src)
}
func (m *FileInfo) XXX_Size() int {
	return xxx_messageInfo_FileInfo.Size(m)
}
func (m *FileInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FileInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FileInfo proto.InternalMessageInfo

func (m *FileInfo) GetSequence() uint64 {
	if m != nil && m.Sequence != nil {
		return *m.Sequence
	}
	return 0
}

func (m *FileInfo) GetLevel() uint32 {
	if m != nil && m.Level != nil {
		return *m.Level
	}
	return 0
}

func (m *FileInfo) GetMerge() uint32 {
	if m != nil && m.Merge != nil {
		return *m.Merge
	}
	return 0
}

func (m *FileInfo) GetExtent() uint32 {
	if m != nil && m.Extent != nil {
		return *m.Extent
	}
	return 0
}

func (m *FileInfo) GetMstID() uint64 {
	if m != nil && m.MstID != nil {
		return *m.MstID
	}
	return 0
}

func (m *FileInfo) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *FileInfo) GetDeletedAt() int64 {
	if m != nil && m.DeletedAt != nil {
		return *m.DeletedAt
	}
	return 0
}

func (m *FileInfo) GetCreatedAt() int64 {
	if m != nil && m.CreatedAt != nil {
		return *m.CreatedAt
	}
	return 0
}

func (m *FileInfo) GetMinTime() int64 {
	if m != nil && m.MinTime != nil {
		return *m.MinTime
	}
	return 0
}

func (m *FileInfo) GetMaxTime() int64 {
	if m != nil && m.MaxTime != nil {
		return *m.MaxTime
	}
	return 0
}

func (m *FileInfo) GetRowCount() int64 {
	if m != nil && m.RowCount != nil {
		return *m.RowCount
	}
	return 0
}

func (m *FileInfo) GetFileSizeBytes() int64 {
	if m != nil && m.FileSizeBytes != nil {
		return *m.FileSizeBytes
	}
	return 0
}

type InsertFilesCommand struct {
	FileInfos            []*FileInfo `protobuf:"bytes,1,rep,name=FileInfos" json:"FileInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InsertFilesCommand) Reset()         { *m = InsertFilesCommand{} }
func (m *InsertFilesCommand) String() string { return proto.CompactTextString(m) }
func (*InsertFilesCommand) ProtoMessage()    {}
func (*InsertFilesCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{146}
}
func (m *InsertFilesCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertFilesCommand.Unmarshal(m, b)
}
func (m *InsertFilesCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertFilesCommand.Marshal(b, m, deterministic)
}
func (m *InsertFilesCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertFilesCommand.Merge(m, src)
}
func (m *InsertFilesCommand) XXX_Size() int {
	return xxx_messageInfo_InsertFilesCommand.Size(m)
}
func (m *InsertFilesCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertFilesCommand.DiscardUnknown(m)
}

var xxx_messageInfo_InsertFilesCommand proto.InternalMessageInfo

func (m *InsertFilesCommand) GetFileInfos() []*FileInfo {
	if m != nil {
		return m.FileInfos
	}
	return nil
}

var E_InsertFilesCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*InsertFilesCommand)(nil),
	Field:         198,
	Name:          "proto.InsertFilesCommand.command",
	Tag:           "bytes,198,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateMetaNodeStatusCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Status               *int32   `protobuf:"varint,2,req,name=Status" json:"Status,omitempty"`
	Ltime                *uint64  `protobuf:"varint,3,req,name=Ltime" json:"Ltime,omitempty"`
	GossipAddr           *string  `protobuf:"bytes,4,opt,name=GossipAddr" json:"GossipAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateMetaNodeStatusCommand) Reset()         { *m = UpdateMetaNodeStatusCommand{} }
func (m *UpdateMetaNodeStatusCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateMetaNodeStatusCommand) ProtoMessage()    {}
func (*UpdateMetaNodeStatusCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{147}
}
func (m *UpdateMetaNodeStatusCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateMetaNodeStatusCommand.Unmarshal(m, b)
}
func (m *UpdateMetaNodeStatusCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateMetaNodeStatusCommand.Marshal(b, m, deterministic)
}
func (m *UpdateMetaNodeStatusCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateMetaNodeStatusCommand.Merge(m, src)
}
func (m *UpdateMetaNodeStatusCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateMetaNodeStatusCommand.Size(m)
}
func (m *UpdateMetaNodeStatusCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateMetaNodeStatusCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateMetaNodeStatusCommand proto.InternalMessageInfo

func (m *UpdateMetaNodeStatusCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *UpdateMetaNodeStatusCommand) GetStatus() int32 {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return 0
}

func (m *UpdateMetaNodeStatusCommand) GetLtime() uint64 {
	if m != nil && m.Ltime != nil {
		return *m.Ltime
	}
	return 0
}

func (m *UpdateMetaNodeStatusCommand) GetGossipAddr() string {
	if m != nil && m.GossipAddr != nil {
		return *m.GossipAddr
	}
	return ""
}

var E_UpdateMetaNodeStatusCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateMetaNodeStatusCommand)(nil),
	Field:         199,
	Name:          "proto.UpdateMetaNodeStatusCommand.command",
	Tag:           "bytes,199,opt,name=command",
	Filename:      "meta.proto",
}

type ShowClusterCommand struct {
	NodeType             *string  `protobuf:"bytes,1,req,name=nodeType" json:"nodeType,omitempty"`
	ID                   *uint64  `protobuf:"varint,2,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowClusterCommand) Reset()         { *m = ShowClusterCommand{} }
func (m *ShowClusterCommand) String() string { return proto.CompactTextString(m) }
func (*ShowClusterCommand) ProtoMessage()    {}
func (*ShowClusterCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{148}
}
func (m *ShowClusterCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowClusterCommand.Unmarshal(m, b)
}
func (m *ShowClusterCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowClusterCommand.Marshal(b, m, deterministic)
}
func (m *ShowClusterCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowClusterCommand.Merge(m, src)
}
func (m *ShowClusterCommand) XXX_Size() int {
	return xxx_messageInfo_ShowClusterCommand.Size(m)
}
func (m *ShowClusterCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowClusterCommand.DiscardUnknown(m)
}

var xxx_messageInfo_ShowClusterCommand proto.InternalMessageInfo

func (m *ShowClusterCommand) GetNodeType() string {
	if m != nil && m.NodeType != nil {
		return *m.NodeType
	}
	return ""
}

func (m *ShowClusterCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_ShowClusterCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*ShowClusterCommand)(nil),
	Field:         200,
	Name:          "proto.ShowClusterCommand.command",
	Tag:           "bytes,200,opt,name=command",
	Filename:      "meta.proto",
}

type NodeRow struct {
	Timestamp            *int64   `protobuf:"varint,1,req,name=timestamp" json:"timestamp,omitempty"`
	Status               *string  `protobuf:"bytes,2,req,name=status" json:"status,omitempty"`
	HostName             *string  `protobuf:"bytes,3,req,name=hostName" json:"hostName,omitempty"`
	NodeID               *uint64  `protobuf:"varint,4,req,name=nodeID" json:"nodeID,omitempty"`
	NodeType             *string  `protobuf:"bytes,5,req,name=nodeType" json:"nodeType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NodeRow) Reset()         { *m = NodeRow{} }
func (m *NodeRow) String() string { return proto.CompactTextString(m) }
func (*NodeRow) ProtoMessage()    {}
func (*NodeRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{149}
}
func (m *NodeRow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NodeRow.Unmarshal(m, b)
}
func (m *NodeRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NodeRow.Marshal(b, m, deterministic)
}
func (m *NodeRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NodeRow.Merge(m, src)
}
func (m *NodeRow) XXX_Size() int {
	return xxx_messageInfo_NodeRow.Size(m)
}
func (m *NodeRow) XXX_DiscardUnknown() {
	xxx_messageInfo_NodeRow.DiscardUnknown(m)
}

var xxx_messageInfo_NodeRow proto.InternalMessageInfo

func (m *NodeRow) GetTimestamp() int64 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

func (m *NodeRow) GetStatus() string {
	if m != nil && m.Status != nil {
		return *m.Status
	}
	return ""
}

func (m *NodeRow) GetHostName() string {
	if m != nil && m.HostName != nil {
		return *m.HostName
	}
	return ""
}

func (m *NodeRow) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

func (m *NodeRow) GetNodeType() string {
	if m != nil && m.NodeType != nil {
		return *m.NodeType
	}
	return ""
}

type EventRow struct {
	OpId                 *uint64  `protobuf:"varint,1,req,name=opId" json:"opId,omitempty"`
	EventType            *string  `protobuf:"bytes,2,req,name=eventType" json:"eventType,omitempty"`
	Db                   *string  `protobuf:"bytes,3,req,name=db" json:"db,omitempty"`
	PtId                 *uint32  `protobuf:"varint,4,req,name=ptId" json:"ptId,omitempty"`
	SrcNodeId            *uint64  `protobuf:"varint,5,req,name=srcNodeId" json:"srcNodeId,omitempty"`
	DstNodeId            *uint64  `protobuf:"varint,6,req,name=dstNodeId" json:"dstNodeId,omitempty"`
	CurrState            *string  `protobuf:"bytes,7,req,name=currState" json:"currState,omitempty"`
	PreState             *string  `protobuf:"bytes,8,req,name=preState" json:"preState,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EventRow) Reset()         { *m = EventRow{} }
func (m *EventRow) String() string { return proto.CompactTextString(m) }
func (*EventRow) ProtoMessage()    {}
func (*EventRow) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{150}
}
func (m *EventRow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EventRow.Unmarshal(m, b)
}
func (m *EventRow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EventRow.Marshal(b, m, deterministic)
}
func (m *EventRow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EventRow.Merge(m, src)
}
func (m *EventRow) XXX_Size() int {
	return xxx_messageInfo_EventRow.Size(m)
}
func (m *EventRow) XXX_DiscardUnknown() {
	xxx_messageInfo_EventRow.DiscardUnknown(m)
}

var xxx_messageInfo_EventRow proto.InternalMessageInfo

func (m *EventRow) GetOpId() uint64 {
	if m != nil && m.OpId != nil {
		return *m.OpId
	}
	return 0
}

func (m *EventRow) GetEventType() string {
	if m != nil && m.EventType != nil {
		return *m.EventType
	}
	return ""
}

func (m *EventRow) GetDb() string {
	if m != nil && m.Db != nil {
		return *m.Db
	}
	return ""
}

func (m *EventRow) GetPtId() uint32 {
	if m != nil && m.PtId != nil {
		return *m.PtId
	}
	return 0
}

func (m *EventRow) GetSrcNodeId() uint64 {
	if m != nil && m.SrcNodeId != nil {
		return *m.SrcNodeId
	}
	return 0
}

func (m *EventRow) GetDstNodeId() uint64 {
	if m != nil && m.DstNodeId != nil {
		return *m.DstNodeId
	}
	return 0
}

func (m *EventRow) GetCurrState() string {
	if m != nil && m.CurrState != nil {
		return *m.CurrState
	}
	return ""
}

func (m *EventRow) GetPreState() string {
	if m != nil && m.PreState != nil {
		return *m.PreState
	}
	return ""
}

type ShowClusterInfo struct {
	Nodes                []*NodeRow  `protobuf:"bytes,1,rep,name=nodes" json:"nodes,omitempty"`
	Events               []*EventRow `protobuf:"bytes,2,rep,name=events" json:"events,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ShowClusterInfo) Reset()         { *m = ShowClusterInfo{} }
func (m *ShowClusterInfo) String() string { return proto.CompactTextString(m) }
func (*ShowClusterInfo) ProtoMessage()    {}
func (*ShowClusterInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{151}
}
func (m *ShowClusterInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowClusterInfo.Unmarshal(m, b)
}
func (m *ShowClusterInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowClusterInfo.Marshal(b, m, deterministic)
}
func (m *ShowClusterInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowClusterInfo.Merge(m, src)
}
func (m *ShowClusterInfo) XXX_Size() int {
	return xxx_messageInfo_ShowClusterInfo.Size(m)
}
func (m *ShowClusterInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowClusterInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShowClusterInfo proto.InternalMessageInfo

func (m *ShowClusterInfo) GetNodes() []*NodeRow {
	if m != nil {
		return m.Nodes
	}
	return nil
}

func (m *ShowClusterInfo) GetEvents() []*EventRow {
	if m != nil {
		return m.Events
	}
	return nil
}

type IndexDurationCommand struct {
	Index                *uint64  `protobuf:"varint,1,req,name=Index" json:"Index,omitempty"`
	Pts                  []uint32 `protobuf:"varint,2,rep,name=Pts" json:"Pts,omitempty"`
	NodeId               *uint64  `protobuf:"varint,3,opt,name=nodeId" json:"nodeId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IndexDurationCommand) Reset()         { *m = IndexDurationCommand{} }
func (m *IndexDurationCommand) String() string { return proto.CompactTextString(m) }
func (*IndexDurationCommand) ProtoMessage()    {}
func (*IndexDurationCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{152}
}
func (m *IndexDurationCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDurationCommand.Unmarshal(m, b)
}
func (m *IndexDurationCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDurationCommand.Marshal(b, m, deterministic)
}
func (m *IndexDurationCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDurationCommand.Merge(m, src)
}
func (m *IndexDurationCommand) XXX_Size() int {
	return xxx_messageInfo_IndexDurationCommand.Size(m)
}
func (m *IndexDurationCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDurationCommand.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDurationCommand proto.InternalMessageInfo

func (m *IndexDurationCommand) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

func (m *IndexDurationCommand) GetPts() []uint32 {
	if m != nil {
		return m.Pts
	}
	return nil
}

func (m *IndexDurationCommand) GetNodeId() uint64 {
	if m != nil && m.NodeId != nil {
		return *m.NodeId
	}
	return 0
}

var E_IndexDurationCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*IndexDurationCommand)(nil),
	Field:         201,
	Name:          "proto.IndexDurationCommand.command",
	Tag:           "bytes,201,opt,name=command",
	Filename:      "meta.proto",
}

type UpdateIndexInfoTierCommand struct {
	IndexID              *uint64  `protobuf:"varint,1,req,name=IndexID" json:"IndexID,omitempty"`
	Tier                 *uint64  `protobuf:"varint,2,req,name=Tier" json:"Tier,omitempty"`
	DbName               *string  `protobuf:"bytes,3,req,name=DbName" json:"DbName,omitempty"`
	RpName               *string  `protobuf:"bytes,4,req,name=RpName" json:"RpName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateIndexInfoTierCommand) Reset()         { *m = UpdateIndexInfoTierCommand{} }
func (m *UpdateIndexInfoTierCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateIndexInfoTierCommand) ProtoMessage()    {}
func (*UpdateIndexInfoTierCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_3b5ea8fe65782bcc, []int{153}
}
func (m *UpdateIndexInfoTierCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateIndexInfoTierCommand.Unmarshal(m, b)
}
func (m *UpdateIndexInfoTierCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateIndexInfoTierCommand.Marshal(b, m, deterministic)
}
func (m *UpdateIndexInfoTierCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateIndexInfoTierCommand.Merge(m, src)
}
func (m *UpdateIndexInfoTierCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateIndexInfoTierCommand.Size(m)
}
func (m *UpdateIndexInfoTierCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateIndexInfoTierCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateIndexInfoTierCommand proto.InternalMessageInfo

func (m *UpdateIndexInfoTierCommand) GetIndexID() uint64 {
	if m != nil && m.IndexID != nil {
		return *m.IndexID
	}
	return 0
}

func (m *UpdateIndexInfoTierCommand) GetTier() uint64 {
	if m != nil && m.Tier != nil {
		return *m.Tier
	}
	return 0
}

func (m *UpdateIndexInfoTierCommand) GetDbName() string {
	if m != nil && m.DbName != nil {
		return *m.DbName
	}
	return ""
}

func (m *UpdateIndexInfoTierCommand) GetRpName() string {
	if m != nil && m.RpName != nil {
		return *m.RpName
	}
	return ""
}

var E_UpdateIndexInfoTierCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateIndexInfoTierCommand)(nil),
	Field:         202,
	Name:          "proto.UpdateIndexInfoTierCommand.command",
	Tag:           "bytes,202,opt,name=command",
	Filename:      "meta.proto",
}

func init() {
	proto.RegisterEnum("proto.Command_Type", Command_Type_name, Command_Type_value)
	proto.RegisterType((*Data)(nil), "proto.Data")
	proto.RegisterMapType((map[string]*DBPtInfo)(nil), "proto.Data.PtViewEntry")
	proto.RegisterMapType((map[string]uint64)(nil), "proto.Data.QueryIDInitEntry")
	proto.RegisterMapType((map[string]*Replications)(nil), "proto.Data.ReplicaGroupsEntry")
	proto.RegisterType((*Replications)(nil), "proto.Replications")
	proto.RegisterType((*ReplicaGroup)(nil), "proto.ReplicaGroup")
	proto.RegisterType((*Peer)(nil), "proto.Peer")
	proto.RegisterType((*PtOwner)(nil), "proto.PtOwner")
	proto.RegisterType((*PtInfo)(nil), "proto.PtInfo")
	proto.RegisterType((*DBPtInfo)(nil), "proto.DBPtInfo")
	proto.RegisterType((*NodeInfo)(nil), "proto.NodeInfo")
	proto.RegisterType((*DataNode)(nil), "proto.DataNode")
	proto.RegisterType((*DatabaseInfo)(nil), "proto.DatabaseInfo")
	proto.RegisterType((*RetentionPolicySpec)(nil), "proto.RetentionPolicySpec")
	proto.RegisterType((*MeasurementInfo)(nil), "proto.MeasurementInfo")
	proto.RegisterMapType((map[string]int32)(nil), "proto.MeasurementInfo.SchemaEntry")
	proto.RegisterMapType((map[string]*SchemaVal)(nil), "proto.MeasurementInfo.SchemaUseForCleanEntry")
	proto.RegisterMapType((map[uint64]*Idxes)(nil), "proto.MeasurementInfo.ShardIdxesEntry")
	proto.RegisterType((*SchemaVal)(nil), "proto.SchemaVal")
	proto.RegisterType((*RetentionPolicyInfo)(nil), "proto.RetentionPolicyInfo")
	proto.RegisterMapType((map[string]uint32)(nil), "proto.RetentionPolicyInfo.MstVersionsEntry")
	proto.RegisterType((*ContinuousQueryInfo)(nil), "proto.ContinuousQueryInfo")
	proto.RegisterType((*ShardGroupInfo)(nil), "proto.ShardGroupInfo")
	proto.RegisterType((*ShardInfo)(nil), "proto.ShardInfo")
	proto.RegisterType((*ShardKeyInfo)(nil), "proto.ShardKeyInfo")
	proto.RegisterType((*Idxes)(nil), "proto.Idxes")
	proto.RegisterType((*SubscriptionInfo)(nil), "proto.SubscriptionInfo")
	proto.RegisterType((*ShardOwner)(nil), "proto.ShardOwner")
	proto.RegisterType((*UserInfo)(nil), "proto.UserInfo")
	proto.RegisterType((*UserPrivilege)(nil), "proto.UserPrivilege")
	proto.RegisterType((*IndexRelation)(nil), "proto.IndexRelation")
	proto.RegisterType((*IndexList)(nil), "proto.IndexList")
	proto.RegisterType((*RpMeasurementsFieldsInfo)(nil), "proto.RpMeasurementsFieldsInfo")
	proto.RegisterType((*MeasurementFieldsInfo)(nil), "proto.MeasurementFieldsInfo")
	proto.RegisterType((*MeasurementTypeFields)(nil), "proto.MeasurementTypeFields")
	proto.RegisterType((*StreamInfo)(nil), "proto.StreamInfo")
	proto.RegisterType((*StreamInfos)(nil), "proto.StreamInfos")
	proto.RegisterType((*StreamMeasurementInfo)(nil), "proto.StreamMeasurementInfo")
	proto.RegisterType((*StreamCall)(nil), "proto.StreamCall")
	proto.RegisterType((*ColStoreInfo)(nil), "proto.ColStoreInfo")
	proto.RegisterType((*IndexOption)(nil), "proto.IndexOption")
	proto.RegisterType((*IndexOptions)(nil), "proto.IndexOptions")
	proto.RegisterType((*Command)(nil), "proto.Command")
	proto.RegisterExtension(E_CreateDatabaseCommand_Command)
	proto.RegisterType((*CreateDatabaseCommand)(nil), "proto.CreateDatabaseCommand")
	proto.RegisterExtension(E_DropDatabaseCommand_Command)
	proto.RegisterType((*DropDatabaseCommand)(nil), "proto.DropDatabaseCommand")
	proto.RegisterExtension(E_CreateRetentionPolicyCommand_Command)
	proto.RegisterType((*CreateRetentionPolicyCommand)(nil), "proto.CreateRetentionPolicyCommand")
	proto.RegisterExtension(E_DropRetentionPolicyCommand_Command)
	proto.RegisterType((*DropRetentionPolicyCommand)(nil), "proto.DropRetentionPolicyCommand")
	proto.RegisterExtension(E_SetDefaultRetentionPolicyCommand_Command)
	proto.RegisterType((*SetDefaultRetentionPolicyCommand)(nil), "proto.SetDefaultRetentionPolicyCommand")
	proto.RegisterExtension(E_UpdateRetentionPolicyCommand_Command)
	proto.RegisterType((*UpdateRetentionPolicyCommand)(nil), "proto.UpdateRetentionPolicyCommand")
	proto.RegisterExtension(E_CreateShardGroupCommand_Command)
	proto.RegisterType((*CreateShardGroupCommand)(nil), "proto.CreateShardGroupCommand")
	proto.RegisterExtension(E_DeleteShardGroupCommand_Command)
	proto.RegisterType((*DeleteShardGroupCommand)(nil), "proto.DeleteShardGroupCommand")
	proto.RegisterExtension(E_CreateUserCommand_Command)
	proto.RegisterType((*CreateUserCommand)(nil), "proto.CreateUserCommand")
	proto.RegisterExtension(E_DropUserCommand_Command)
	proto.RegisterType((*DropUserCommand)(nil), "proto.DropUserCommand")
	proto.RegisterExtension(E_UpdateUserCommand_Command)
	proto.RegisterType((*UpdateUserCommand)(nil), "proto.UpdateUserCommand")
	proto.RegisterExtension(E_SetPrivilegeCommand_Command)
	proto.RegisterType((*SetPrivilegeCommand)(nil), "proto.SetPrivilegeCommand")
	proto.RegisterExtension(E_SetDataCommand_Command)
	proto.RegisterType((*SetDataCommand)(nil), "proto.SetDataCommand")
	proto.RegisterExtension(E_SetAdminPrivilegeCommand_Command)
	proto.RegisterType((*SetAdminPrivilegeCommand)(nil), "proto.SetAdminPrivilegeCommand")
	proto.RegisterExtension(E_CreateSubscriptionCommand_Command)
	proto.RegisterType((*CreateSubscriptionCommand)(nil), "proto.CreateSubscriptionCommand")
	proto.RegisterExtension(E_DropSubscriptionCommand_Command)
	proto.RegisterType((*DropSubscriptionCommand)(nil), "proto.DropSubscriptionCommand")
	proto.RegisterExtension(E_CreateMetaNodeCommand_Command)
	proto.RegisterType((*CreateMetaNodeCommand)(nil), "proto.CreateMetaNodeCommand")
	proto.RegisterExtension(E_CreateDataNodeCommand_Command)
	proto.RegisterType((*CreateDataNodeCommand)(nil), "proto.CreateDataNodeCommand")
	proto.RegisterType((*DataNodeEvent)(nil), "proto.DataNodeEvent")
	proto.RegisterExtension(E_DeleteMetaNodeCommand_Command)
	proto.RegisterType((*DeleteMetaNodeCommand)(nil), "proto.DeleteMetaNodeCommand")
	proto.RegisterExtension(E_DeleteDataNodeCommand_Command)
	proto.RegisterType((*DeleteDataNodeCommand)(nil), "proto.DeleteDataNodeCommand")
	proto.RegisterType((*Response)(nil), "proto.Response")
	proto.RegisterExtension(E_SetMetaNodeCommand_Command)
	proto.RegisterType((*SetMetaNodeCommand)(nil), "proto.SetMetaNodeCommand")
	proto.RegisterExtension(E_DropShardCommand_Command)
	proto.RegisterType((*DropShardCommand)(nil), "proto.DropShardCommand")
	proto.RegisterExtension(E_MarkDatabaseDeleteCommand_Command)
	proto.RegisterType((*MarkDatabaseDeleteCommand)(nil), "proto.MarkDatabaseDeleteCommand")
	proto.RegisterExtension(E_UpdateShardOwnerCommand_Command)
	proto.RegisterType((*UpdateShardOwnerCommand)(nil), "proto.UpdateShardOwnerCommand")
	proto.RegisterExtension(E_MarkRetentionPolicyDeleteCommand_Command)
	proto.RegisterType((*MarkRetentionPolicyDeleteCommand)(nil), "proto.MarkRetentionPolicyDeleteCommand")
	proto.RegisterExtension(E_CreateMeasurementCommand_Command)
	proto.RegisterType((*CreateMeasurementCommand)(nil), "proto.CreateMeasurementCommand")
	proto.RegisterExtension(E_AlterShardKeyCmd_Command)
	proto.RegisterType((*AlterShardKeyCmd)(nil), "proto.AlterShardKeyCmd")
	proto.RegisterExtension(E_UpdateDbPtStatusCommand_Command)
	proto.RegisterType((*UpdateDbPtStatusCommand)(nil), "proto.UpdateDbPtStatusCommand")
	proto.RegisterExtension(E_ReShardingCommand_Command)
	proto.RegisterType((*ReShardingCommand)(nil), "proto.ReShardingCommand")
	proto.RegisterExtension(E_UpdateSchemaCommand_Command)
	proto.RegisterType((*UpdateSchemaCommand)(nil), "proto.UpdateSchemaCommand")
	proto.RegisterType((*FieldSchema)(nil), "proto.FieldSchema")
	proto.RegisterType((*IndexInfo)(nil), "proto.IndexInfo")
	proto.RegisterType((*IndexGroupInfo)(nil), "proto.IndexGroupInfo")
	proto.RegisterType((*ShardStatus)(nil), "proto.ShardStatus")
	proto.RegisterType((*RpShardStatus)(nil), "proto.RpShardStatus")
	proto.RegisterType((*DBPtStatus)(nil), "proto.DBPtStatus")
	proto.RegisterExtension(E_ReportShardsLoadCommand_Command)
	proto.RegisterType((*ReportShardsLoadCommand)(nil), "proto.ReportShardsLoadCommand")
	proto.RegisterType((*DownSamplePolicyInfo)(nil), "proto.DownSamplePolicyInfo")
	proto.RegisterType((*DownSamplePolicy)(nil), "proto.DownSamplePolicy")
	proto.RegisterType((*DownSampleOperators)(nil), "proto.DownSampleOperators")
	proto.RegisterType((*DownSamplePolicyInfoWithDbRp)(nil), "proto.DownSamplePolicyInfoWithDbRp")
	proto.RegisterType((*DownSamplePoliciesInfoWithDbRp)(nil), "proto.DownSamplePoliciesInfoWithDbRp")
	proto.RegisterType((*ShardDownSampleUpdateInfos)(nil), "proto.ShardDownSampleUpdateInfos")
	proto.RegisterType((*ShardDownSampleUpdateInfo)(nil), "proto.ShardDownSampleUpdateInfo")
	proto.RegisterExtension(E_PruneGroupsCommand_Command)
	proto.RegisterType((*PruneGroupsCommand)(nil), "proto.PruneGroupsCommand")
	proto.RegisterExtension(E_MarkMeasurementDeleteCommand_Command)
	proto.RegisterType((*MarkMeasurementDeleteCommand)(nil), "proto.MarkMeasurementDeleteCommand")
	proto.RegisterExtension(E_DropMeasurementCommand_Command)
	proto.RegisterType((*DropMeasurementCommand)(nil), "proto.DropMeasurementCommand")
	proto.RegisterType((*NodeStartInfo)(nil), "proto.NodeStartInfo")
	proto.RegisterExtension(E_TimeRangeCommand_Command)
	proto.RegisterType((*TimeRangeCommand)(nil), "proto.TimeRangeCommand")
	proto.RegisterExtension(E_ShardDurationCommand_Command)
	proto.RegisterType((*ShardDurationCommand)(nil), "proto.ShardDurationCommand")
	proto.RegisterType((*DurationDescriptor)(nil), "proto.DurationDescriptor")
	proto.RegisterType((*ShardIdentifier)(nil), "proto.ShardIdentifier")
	proto.RegisterType((*IndexIdentifier)(nil), "proto.IndexIdentifier")
	proto.RegisterType((*TimeRangeInfo)(nil), "proto.TimeRangeInfo")
	proto.RegisterType((*IndexDescriptor)(nil), "proto.IndexDescriptor")
	proto.RegisterType((*ShardDurationInfo)(nil), "proto.ShardDurationInfo")
	proto.RegisterType((*IndexDurationInfo)(nil), "proto.IndexDurationInfo")
	proto.RegisterType((*ShardTimeRangeInfo)(nil), "proto.ShardTimeRangeInfo")
	proto.RegisterType((*ShardDurationResponse)(nil), "proto.ShardDurationResponse")
	proto.RegisterType((*IndexDurationResponse)(nil), "proto.IndexDurationResponse")
	proto.RegisterExtension(E_DeleteIndexGroupCommand_Command)
	proto.RegisterType((*DeleteIndexGroupCommand)(nil), "proto.DeleteIndexGroupCommand")
	proto.RegisterExtension(E_UpdateShardInfoTierCommand_Command)
	proto.RegisterType((*UpdateShardInfoTierCommand)(nil), "proto.UpdateShardInfoTierCommand")
	proto.RegisterType((*CardinalityInfo)(nil), "proto.CardinalityInfo")
	proto.RegisterType((*MeasurementCardinalityInfo)(nil), "proto.MeasurementCardinalityInfo")
	proto.RegisterType((*CardinalityResponse)(nil), "proto.CardinalityResponse")
	proto.RegisterExtension(E_UpdateNodeStatusCommand_Command)
	proto.RegisterType((*UpdateNodeStatusCommand)(nil), "proto.UpdateNodeStatusCommand")
	proto.RegisterType((*DbPt)(nil), "proto.DbPt")
	proto.RegisterMapType((map[uint64]*ShardDurationInfo)(nil), "proto.DbPt.ShardsEntry")
	proto.RegisterType((*MigrateEventInfo)(nil), "proto.MigrateEventInfo")
	proto.RegisterExtension(E_CreateEventCommand_Command)
	proto.RegisterType((*CreateEventCommand)(nil), "proto.CreateEventCommand")
	proto.RegisterExtension(E_UpdateEventCommand_Command)
	proto.RegisterType((*UpdateEventCommand)(nil), "proto.UpdateEventCommand")
	proto.RegisterExtension(E_UpdatePtInfoCommand_Command)
	proto.RegisterType((*UpdatePtInfoCommand)(nil), "proto.UpdatePtInfoCommand")
	proto.RegisterExtension(E_RemoveEventCommand_Command)
	proto.RegisterType((*RemoveEventCommand)(nil), "proto.RemoveEventCommand")
	proto.RegisterExtension(E_CreateDownSamplePolicyCommand_Command)
	proto.RegisterType((*CreateDownSamplePolicyCommand)(nil), "proto.CreateDownSamplePolicyCommand")
	proto.RegisterExtension(E_DropDownSamplePolicyCommand_Command)
	proto.RegisterType((*DropDownSamplePolicyCommand)(nil), "proto.DropDownSamplePolicyCommand")
	proto.RegisterExtension(E_GetDownSamplePolicyCommand_Command)
	proto.RegisterType((*GetDownSamplePolicyCommand)(nil), "proto.GetDownSamplePolicyCommand")
	proto.RegisterExtension(E_CreateDbPtViewCommand_Command)
	proto.RegisterType((*CreateDbPtViewCommand)(nil), "proto.CreateDbPtViewCommand")
	proto.RegisterExtension(E_GetMeasurementInfoWithinSameRpCommand_Command)
	proto.RegisterType((*GetMeasurementInfoWithinSameRpCommand)(nil), "proto.GetMeasurementInfoWithinSameRpCommand")
	proto.RegisterExtension(E_UpdateShardDownSampleInfoCommand_Command)
	proto.RegisterType((*UpdateShardDownSampleInfoCommand)(nil), "proto.UpdateShardDownSampleInfoCommand")
	proto.RegisterExtension(E_MarkTakeoverCommand_Command)
	proto.RegisterType((*MarkTakeoverCommand)(nil), "proto.MarkTakeoverCommand")
	proto.RegisterExtension(E_MarkBalancerCommand_Command)
	proto.RegisterType((*MarkBalancerCommand)(nil), "proto.MarkBalancerCommand")
	proto.RegisterExtension(E_CreateStreamCommand_Command)
	proto.RegisterType((*CreateStreamCommand)(nil), "proto.CreateStreamCommand")
	proto.RegisterExtension(E_DropStreamCommand_Command)
	proto.RegisterType((*DropStreamCommand)(nil), "proto.DropStreamCommand")
	proto.RegisterExtension(E_GetMeasurementInfoStoreCommand_Command)
	proto.RegisterType((*GetMeasurementInfoStoreCommand)(nil), "proto.GetMeasurementInfoStoreCommand")
	proto.RegisterExtension(E_VerifyDataNodeCommand_Command)
	proto.RegisterType((*VerifyDataNodeCommand)(nil), "proto.VerifyDataNodeCommand")
	proto.RegisterExtension(E_ExpandGroupsCommand_Command)
	proto.RegisterType((*ExpandGroupsCommand)(nil), "proto.ExpandGroupsCommand")
	proto.RegisterExtension(E_UpdatePtVersionCommand_Command)
	proto.RegisterType((*UpdatePtVersionCommand)(nil), "proto.UpdatePtVersionCommand")
	proto.RegisterExtension(E_GetMeasurementsInfoCommand_Command)
	proto.RegisterType((*GetMeasurementsInfoCommand)(nil), "proto.GetMeasurementsInfoCommand")
	proto.RegisterType((*DatabaseBriefInfo)(nil), "proto.DatabaseBriefInfo")
	proto.RegisterType((*MeasurementsInfo)(nil), "proto.MeasurementsInfo")
	proto.RegisterExtension(E_RegisterQueryIDOffsetCommand_Command)
	proto.RegisterType((*RegisterQueryIDOffsetCommand)(nil), "proto.RegisterQueryIDOffsetCommand")
	proto.RegisterExtension(E_CreateContinuousQueryCommand_Command)
	proto.RegisterType((*CreateContinuousQueryCommand)(nil), "proto.CreateContinuousQueryCommand")
	proto.RegisterExtension(E_Sql2MetaHeartbeatCommand_Command)
	proto.RegisterType((*Sql2MetaHeartbeatCommand)(nil), "proto.Sql2MetaHeartbeatCommand")
	proto.RegisterExtension(E_ContinuousQueryReportCommand_Command)
	proto.RegisterType((*ContinuousQueryReportCommand)(nil), "proto.ContinuousQueryReportCommand")
	proto.RegisterType((*CQState)(nil), "proto.CQState")
	proto.RegisterExtension(E_GetContinuousQueryLeaseCommand_Command)
	proto.RegisterType((*GetContinuousQueryLeaseCommand)(nil), "proto.GetContinuousQueryLeaseCommand")
	proto.RegisterExtension(E_DropContinuousQueryCommand_Command)
	proto.RegisterType((*DropContinuousQueryCommand)(nil), "proto.DropContinuousQueryCommand")
	proto.RegisterExtension(E_NotifyCQLeaseChangedCommand_Command)
	proto.RegisterType((*NotifyCQLeaseChangedCommand)(nil), "proto.NotifyCQLeaseChangedCommand")
	proto.RegisterExtension(E_SetNodeSegregateStatusCommand_Command)
	proto.RegisterType((*SetNodeSegregateStatusCommand)(nil), "proto.SetNodeSegregateStatusCommand")
	proto.RegisterExtension(E_RemoveNodeCommand_Command)
	proto.RegisterType((*RemoveNodeCommand)(nil), "proto.RemoveNodeCommand")
	proto.RegisterExtension(E_UpdateReplicationCommand_Command)
	proto.RegisterType((*UpdateReplicationCommand)(nil), "proto.UpdateReplicationCommand")
	proto.RegisterType((*ObsOptions)(nil), "proto.ObsOptions")
	proto.RegisterType((*Options)(nil), "proto.Options")
	proto.RegisterExtension(E_UpdateMeasurementCommand_Command)
	proto.RegisterType((*UpdateMeasurementCommand)(nil), "proto.UpdateMeasurementCommand")
	proto.RegisterType((*DataOps)(nil), "proto.DataOps")
	proto.RegisterExtension(E_CreateSqlNodeCommand_Command)
	proto.RegisterType((*CreateSqlNodeCommand)(nil), "proto.CreateSqlNodeCommand")
	proto.RegisterExtension(E_UpdateSqlNodeStatusCommand_Command)
	proto.RegisterType((*UpdateSqlNodeStatusCommand)(nil), "proto.UpdateSqlNodeStatusCommand")
	proto.RegisterExtension(E_UpdateNodeTmpIndexCommand_Command)
	proto.RegisterType((*UpdateNodeTmpIndexCommand)(nil), "proto.UpdateNodeTmpIndexCommand")
	proto.RegisterType((*FileInfo)(nil), "proto.FileInfo")
	proto.RegisterExtension(E_InsertFilesCommand_Command)
	proto.RegisterType((*InsertFilesCommand)(nil), "proto.InsertFilesCommand")
	proto.RegisterExtension(E_UpdateMetaNodeStatusCommand_Command)
	proto.RegisterType((*UpdateMetaNodeStatusCommand)(nil), "proto.UpdateMetaNodeStatusCommand")
	proto.RegisterExtension(E_ShowClusterCommand_Command)
	proto.RegisterType((*ShowClusterCommand)(nil), "proto.ShowClusterCommand")
	proto.RegisterType((*NodeRow)(nil), "proto.NodeRow")
	proto.RegisterType((*EventRow)(nil), "proto.EventRow")
	proto.RegisterType((*ShowClusterInfo)(nil), "proto.ShowClusterInfo")
	proto.RegisterExtension(E_IndexDurationCommand_Command)
	proto.RegisterType((*IndexDurationCommand)(nil), "proto.IndexDurationCommand")
	proto.RegisterExtension(E_UpdateIndexInfoTierCommand_Command)
	proto.RegisterType((*UpdateIndexInfoTierCommand)(nil), "proto.UpdateIndexInfoTierCommand")
}

func init() { proto.RegisterFile("meta.proto", fileDescriptor_3b5ea8fe65782bcc) }

var fileDescriptor_3b5ea8fe65782bcc = []byte{
	// 7497 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7d, 0x6b, 0x8c, 0x64, 0xc7,
	0x55, 0xb0, 0x6e, 0x3f, 0x66, 0x7a, 0x6a, 0xb6, 0x67, 0x67, 0x6b, 0x1f, 0x6e, 0x8f, 0xd7, 0xeb,
	0xd9, 0x9b, 0xb5, 0xbd, 0xb1, 0xe3, 0x75, 0x3c, 0x4a, 0x6c, 0xc7, 0x49, 0x9c, 0xec, 0x4c, 0xef,
	0xa3, 0xe3, 0x9d, 0x9d, 0xf6, 0xed, 0xf1, 0xee, 0xf7, 0xc5, 0x21, 0xf8, 0xce, 0x74, 0xcd, 0xcc,
	0xcd, 0xf4, 0x74, 0xb7, 0xef, 0xbd, 0xb3, 0xbb, 0x63, 0x05, 0xc5, 0x49, 0xa4, 0xa0, 0x80, 0x10,
	0x42, 0x88, 0xbc, 0x04, 0x01, 0x42, 0x12, 0x20, 0x10, 0x20, 0x21, 0x21, 0x0f, 0x9c, 0x80, 0xf3,
	0x80, 0x10, 0x21, 0xfe, 0x20, 0x10, 0xbf, 0x90, 0xf8, 0x85, 0x84, 0x00, 0xc1, 0x1f, 0x1e, 0x02,
	0x24, 0x74, 0x4e, 0xbd, 0xef, 0xad, 0x7b, 0x67, 0x77, 0x93, 0x8d, 0xf8, 0xd5, 0xb7, 0x4e, 0xbd,
	0xce, 0x39, 0x75, 0xea, 0xd4, 0xa9, 0x3a, 0xa7, 0xaa, 0x09, 0xd9, 0x61, 0x69, 0x78, 0x66, 0x1c,
	0x8f, 0xd2, 0x11, 0xad, 0xe3, 0x8f, 0xff, 0x1f, 0xd3, 0xa4, 0xd6, 0x0e, 0xd3, 0x90, 0x52, 0x52,
	0x5b, 0x65, 0xf1, 0x4e, 0xcb, 0x9b, 0xaf, 0x9c, 0xae, 0x05, 0xf8, 0x4d, 0x8f, 0x90, 0x7a, 0x67,
	0xd8, 0x67, 0x37, 0x5a, 0x15, 0x04, 0xf2, 0x04, 0x3d, 0x4e, 0xa6, 0x96, 0x06, 0xbb, 0x49, 0xca,
	0xe2, 0x4e, 0xbb, 0x55, 0xc5, 0x1c, 0x0d, 0xa0, 0xf7, 0x93, 0xfa, 0xe5, 0x51, 0x9f, 0x25, 0xad,
	0xda, 0x7c, 0xf5, 0xf4, 0xf4, 0xc2, 0x41, 0xde, 0xdd, 0x19, 0x80, 0x75, 0x86, 0x1b, 0xa3, 0x80,
	0xe7, 0xd2, 0xc7, 0xc8, 0x14, 0x74, 0xbb, 0x16, 0x26, 0x2c, 0x69, 0xd5, 0xb1, 0xe8, 0x61, 0x51,
	0x54, 0xc2, 0xb1, 0xb8, 0x2e, 0x05, 0x2d, 0x3f, 0x97, 0xb0, 0x38, 0x69, 0x4d, 0x58, 0x2d, 0x03,
	0x8c, 0xb7, 0x8c, 0xb9, 0x80, 0xde, 0x72, 0x78, 0x03, 0xfb, 0x6b, 0xb7, 0x26, 0x39, 0x7a, 0x0a,
	0x40, 0x4f, 0x93, 0x83, 0xcb, 0xe1, 0x8d, 0xde, 0x56, 0x18, 0xf7, 0x2f, 0xc4, 0xa3, 0xdd, 0x71,
	0xa7, 0xdd, 0x6a, 0x60, 0x99, 0x2c, 0x98, 0x9e, 0x20, 0x44, 0x82, 0x3a, 0xed, 0xd6, 0x14, 0x16,
	0x32, 0x20, 0xf4, 0x11, 0x4e, 0x01, 0x27, 0x96, 0x58, 0x28, 0x49, 0x78, 0xa0, 0x4b, 0x40, 0xf1,
	0x65, 0x26, 0x8b, 0x4f, 0xbb, 0x79, 0xa3, 0x4b, 0x50, 0x9f, 0x1c, 0x10, 0x3c, 0xed, 0xa6, 0x97,
	0x77, 0x77, 0x5a, 0x33, 0xf3, 0x95, 0xd3, 0xcd, 0xc0, 0x82, 0xd1, 0x47, 0xc9, 0x44, 0x37, 0xbd,
	0x12, 0xb1, 0xeb, 0xad, 0x83, 0xd8, 0xde, 0x5d, 0x46, 0xf7, 0x67, 0x78, 0xce, 0xb9, 0x61, 0x1a,
	0xef, 0x05, 0xa2, 0x18, 0x34, 0x8a, 0x35, 0xbb, 0x2c, 0x86, 0x5e, 0x5a, 0xb3, 0xf3, 0x1e, 0x34,
	0x6a, 0xc2, 0x04, 0x83, 0x70, 0xa4, 0x25, 0x83, 0x0e, 0x29, 0x06, 0x99, 0x60, 0xc1, 0x20, 0x04,
	0x75, 0xda, 0x2d, 0xaa, 0x18, 0x24, 0x20, 0xd0, 0xdb, 0x72, 0x78, 0xe3, 0xdc, 0x35, 0x36, 0x4c,
	0x57, 0xc6, 0x9d, 0x7e, 0xeb, 0xf0, 0xbc, 0x77, 0xba, 0x16, 0x58, 0x30, 0xe8, 0x6d, 0x35, 0xdc,
	0x66, 0x2b, 0xd7, 0x58, 0x7c, 0x6e, 0x18, 0xae, 0x0d, 0x58, 0xbf, 0x75, 0x64, 0xde, 0x3b, 0xdd,
	0x08, 0xb2, 0x60, 0xfa, 0x56, 0xd2, 0x5c, 0x8e, 0x36, 0xe3, 0x30, 0x65, 0x58, 0x3b, 0x69, 0x1d,
	0xb5, 0x68, 0x36, 0xf3, 0x90, 0x97, 0x76, 0x69, 0xe8, 0x68, 0x31, 0x1c, 0x84, 0xc3, 0x75, 0xdd,
	0xd1, 0x31, 0xde, 0x51, 0x06, 0x2c, 0x18, 0xd0, 0x1e, 0x5d, 0x1f, 0xf6, 0xc2, 0x9d, 0xf1, 0x00,
	0xa4, 0xe8, 0x2e, 0xc4, 0x3c, 0x0b, 0xa6, 0x0f, 0x93, 0xc9, 0x5e, 0x1a, 0xb3, 0x70, 0x27, 0x69,
	0xb5, 0x10, 0x99, 0x43, 0x02, 0x19, 0x0e, 0x45, 0x34, 0x64, 0x09, 0x3a, 0x4f, 0xa6, 0x41, 0x78,
	0x78, 0x4e, 0xbb, 0x75, 0x37, 0x36, 0x69, 0x82, 0x84, 0xe0, 0x2e, 0x8d, 0x86, 0xc3, 0x4e, 0xbf,
	0x35, 0x87, 0xf9, 0x1a, 0x40, 0x9f, 0x26, 0xd3, 0xcf, 0xee, 0xb2, 0x78, 0xaf, 0xd3, 0xee, 0x0c,
	0xa3, 0xb4, 0x75, 0x0f, 0x76, 0x78, 0xdc, 0x1c, 0x71, 0x23, 0x9b, 0x0f, 0xbb, 0x59, 0x81, 0xb6,
	0x49, 0x33, 0x60, 0xe3, 0x41, 0xb4, 0x1e, 0xe2, 0xf8, 0x25, 0xad, 0xe3, 0xd8, 0xc2, 0x09, 0xb3,
	0x05, 0xab, 0x00, 0x6f, 0xc3, 0xae, 0x44, 0x5f, 0x47, 0x0e, 0x01, 0xca, 0xbb, 0x6b, 0xc9, 0x7a,
	0x1c, 0x8d, 0xd3, 0x68, 0x34, 0xec, 0xb4, 0x5b, 0xf7, 0x22, 0xae, 0xf9, 0x0c, 0x7a, 0x8a, 0x34,
	0x81, 0x80, 0x67, 0x97, 0xb6, 0xc2, 0xe1, 0x26, 0x30, 0xf2, 0x04, 0x96, 0xb4, 0x81, 0xc0, 0x99,
	0xcb, 0xbb, 0x3b, 0x2b, 0x1b, 0x38, 0xb1, 0x92, 0xd6, 0x7d, 0xf3, 0xde, 0xe9, 0x7a, 0x60, 0x82,
	0x60, 0x48, 0x3a, 0x49, 0xef, 0xd9, 0x4b, 0x51, 0xca, 0xe4, 0xe0, 0xcd, 0xf3, 0xc1, 0xcb, 0x80,
	0xe9, 0xc3, 0xa4, 0xd1, 0x7b, 0x71, 0xc0, 0x27, 0xd9, 0x49, 0xf7, 0x9c, 0x54, 0x05, 0xe8, 0x1c,
	0x69, 0x2c, 0x87, 0x37, 0x96, 0x93, 0xb4, 0xd3, 0x6e, 0xf9, 0x88, 0x99, 0x4a, 0xcf, 0xbd, 0x83,
	0x4c, 0x1b, 0x33, 0x88, 0xce, 0x92, 0xea, 0x36, 0xdb, 0x6b, 0x79, 0xf3, 0xde, 0xe9, 0xa9, 0x00,
	0x3e, 0x41, 0x1b, 0x5d, 0x0b, 0x07, 0xbb, 0xac, 0x55, 0x99, 0xf7, 0xcc, 0x6e, 0x16, 0xbb, 0x5c,
	0xfe, 0x78, 0xee, 0x53, 0x95, 0x27, 0xbd, 0xb9, 0xa7, 0xc9, 0x6c, 0x76, 0x6c, 0x1c, 0x0d, 0x1e,
	0x31, 0x1b, 0xac, 0x99, 0xf5, 0x9f, 0x23, 0x34, 0x3f, 0x32, 0x8e, 0x16, 0x5e, 0x6b, 0xa3, 0x24,
	0xf5, 0xa9, 0xa8, 0x0b, 0x63, 0x92, 0x18, 0xcd, 0xfa, 0x6f, 0x26, 0x07, 0xcc, 0x2c, 0xfa, 0x30,
	0x99, 0x10, 0xa2, 0xe1, 0x59, 0xfa, 0xd8, 0xec, 0x3b, 0x10, 0x45, 0xfc, 0x0f, 0x7b, 0xaa, 0x36,
	0x42, 0xe8, 0x0c, 0xa9, 0x74, 0xda, 0xb8, 0x7a, 0x34, 0x83, 0x4a, 0xa7, 0xcd, 0x99, 0x2b, 0x16,
	0x89, 0x0a, 0x42, 0x55, 0x9a, 0x9e, 0x24, 0xf5, 0x2e, 0x03, 0x4d, 0x5e, 0xc5, 0x8e, 0xa6, 0x45,
	0x47, 0x00, 0x0b, 0x78, 0x0e, 0x3d, 0x46, 0x26, 0x7a, 0x69, 0x98, 0xee, 0xc2, 0x3a, 0x02, 0x95,
	0x45, 0x4a, 0x2d, 0x53, 0x75, 0xbd, 0x4c, 0xf9, 0x0f, 0x91, 0x1a, 0x54, 0xca, 0xa1, 0x40, 0x49,
	0x2d, 0x18, 0x0d, 0x98, 0xe8, 0x1e, 0xbf, 0xfd, 0x93, 0x64, 0xb2, 0x9b, 0xae, 0x5c, 0x1f, 0xb2,
	0x18, 0xba, 0x10, 0xab, 0x04, 0x5f, 0xf3, 0x44, 0xca, 0x7f, 0xd9, 0x03, 0xbd, 0x0a, 0x83, 0x48,
	0x4f, 0x91, 0x3a, 0x96, 0xc5, 0x12, 0xd3, 0x0b, 0x33, 0x12, 0x51, 0xde, 0x42, 0x50, 0x57, 0x0d,
	0x09, 0x5c, 0x2b, 0x59, 0x5c, 0xbb, 0x69, 0xa7, 0x8f, 0x6b, 0x64, 0x33, 0xc0, 0x6f, 0x18, 0xb5,
	0x2b, 0x2c, 0x6e, 0xd5, 0x70, 0x8c, 0xe1, 0x13, 0xb1, 0xbc, 0xd0, 0x69, 0xb7, 0xea, 0xa8, 0x8c,
	0xf1, 0xdb, 0x7f, 0x84, 0x34, 0xa4, 0x20, 0xd1, 0x93, 0xa4, 0xd6, 0x5e, 0xeb, 0xa6, 0x62, 0x50,
	0x9a, 0x0a, 0x05, 0x94, 0x32, 0xcc, 0xf2, 0xff, 0xd9, 0x23, 0x0d, 0xb9, 0x88, 0x18, 0x5c, 0xa8,
	0x49, 0x2e, 0x5c, 0x1c, 0x25, 0x29, 0xe2, 0x36, 0x15, 0xe0, 0x37, 0x6d, 0x91, 0xc9, 0xa0, 0xbb,
	0x74, 0xb6, 0xdf, 0x8f, 0xb1, 0xdb, 0xa9, 0x40, 0x26, 0x21, 0x67, 0x75, 0xa9, 0x8b, 0x15, 0xaa,
	0x3c, 0x47, 0x24, 0x33, 0x23, 0x52, 0x55, 0x54, 0x1e, 0x21, 0xf5, 0x4b, 0xab, 0xd1, 0x0e, 0x6b,
	0x4d, 0x70, 0x23, 0x01, 0x13, 0xb0, 0x38, 0x5c, 0x18, 0x25, 0x49, 0x34, 0xc6, 0x4e, 0x26, 0xb1,
	0x6f, 0x03, 0x02, 0x53, 0xba, 0xc7, 0x36, 0x63, 0xb6, 0x19, 0xa6, 0x4c, 0x34, 0xdb, 0xe0, 0x5a,
	0x36, 0x03, 0x56, 0xa3, 0x48, 0x10, 0x1d, 0x3e, 0x8a, 0xbb, 0xa4, 0x21, 0xe7, 0x33, 0xbd, 0x8f,
	0x54, 0x2e, 0x47, 0x62, 0x80, 0x72, 0x2b, 0x6a, 0xe5, 0x72, 0x04, 0x88, 0xa3, 0x0e, 0x6d, 0x8b,
	0x99, 0x25, 0x52, 0xa0, 0x77, 0xce, 0x0e, 0xa2, 0x6b, 0x4c, 0x64, 0x56, 0xb9, 0x46, 0x36, 0x40,
	0xc0, 0xca, 0xb3, 0x2f, 0xe1, 0x58, 0x4d, 0x05, 0x95, 0xb3, 0x2f, 0xf9, 0x5f, 0xac, 0x92, 0x03,
	0xa6, 0x75, 0x02, 0xb8, 0x5d, 0x0e, 0x77, 0x18, 0xf6, 0x3e, 0x15, 0xe0, 0x37, 0x7d, 0x9c, 0x1c,
	0x6b, 0xb3, 0x8d, 0x70, 0x77, 0x90, 0x06, 0x2c, 0x65, 0x43, 0x98, 0x5b, 0xdd, 0xd1, 0x20, 0x5a,
	0xdf, 0x13, 0x23, 0x50, 0x90, 0x4b, 0x2f, 0x92, 0x43, 0x36, 0x28, 0x62, 0x72, 0x82, 0xcc, 0xa9,
	0x99, 0x68, 0x55, 0x41, 0x0a, 0xf3, 0x95, 0xa0, 0xa5, 0xa5, 0xd1, 0x30, 0x8d, 0x86, 0xbb, 0xa3,
	0xdd, 0x04, 0x34, 0x4f, 0xa4, 0xcc, 0x31, 0xd9, 0x92, 0x9d, 0x2f, 0x5a, 0xca, 0x55, 0xe2, 0x8b,
	0x56, 0xbc, 0xdd, 0x66, 0x03, 0x96, 0xb2, 0x3e, 0xca, 0x4a, 0x23, 0x30, 0x41, 0xf4, 0x51, 0xd2,
	0x40, 0x25, 0xfd, 0x0c, 0xdb, 0x6b, 0x4d, 0x58, 0x6a, 0x47, 0x82, 0xb1, 0x6d, 0x55, 0x88, 0x3e,
	0x40, 0x66, 0xb8, 0xb2, 0x5e, 0x0d, 0x37, 0xcf, 0xc6, 0x71, 0xb8, 0xd7, 0x9a, 0xc4, 0x56, 0x33,
	0x50, 0xd0, 0x1f, 0x42, 0xbf, 0x5c, 0x46, 0xc9, 0xa8, 0x06, 0x2a, 0x0d, 0x0b, 0xef, 0x0a, 0xae,
	0x31, 0x60, 0x05, 0x78, 0xc6, 0xc2, 0xbb, 0xb2, 0x96, 0x88, 0x8c, 0x40, 0x96, 0xf0, 0xbf, 0xec,
	0x91, 0xc3, 0x19, 0xc6, 0xf5, 0xc6, 0x6c, 0xdd, 0x18, 0x3b, 0x4f, 0x8d, 0xdd, 0x1c, 0x69, 0xb4,
	0x77, 0x63, 0xd4, 0x87, 0x28, 0x2c, 0xd5, 0x40, 0xa5, 0xe9, 0x19, 0x42, 0xb5, 0x7d, 0xa8, 0x4a,
	0x55, 0xb1, 0x94, 0x23, 0xc7, 0x22, 0xa0, 0x86, 0x73, 0x5b, 0x13, 0xe0, 0x93, 0x03, 0x57, 0xc3,
	0x78, 0x47, 0xb5, 0x52, 0xc7, 0x56, 0x2c, 0x98, 0xff, 0xf7, 0x13, 0xe4, 0xe0, 0x32, 0x0b, 0x93,
	0xdd, 0x98, 0xed, 0x08, 0xa3, 0xc6, 0x29, 0x6f, 0x8f, 0x91, 0x29, 0xc9, 0x5c, 0x50, 0x40, 0xd5,
	0xa2, 0x21, 0xd0, 0xa5, 0xe8, 0x53, 0x64, 0xa2, 0xb7, 0xbe, 0xc5, 0x76, 0x42, 0x21, 0x5f, 0xbe,
	0x34, 0xa2, 0xec, 0xee, 0xce, 0xf0, 0x42, 0xc2, 0x86, 0xe4, 0x89, 0xac, 0x48, 0xd4, 0xf2, 0x22,
	0xf1, 0x14, 0x69, 0x46, 0x60, 0x02, 0x06, 0x6c, 0xa0, 0xa9, 0x9b, 0x5e, 0x38, 0x22, 0x3a, 0xe9,
	0x98, 0x79, 0x81, 0x5d, 0x14, 0xd4, 0xc6, 0xb9, 0xe1, 0x66, 0x34, 0x64, 0xab, 0x7b, 0x63, 0x86,
	0x02, 0xd5, 0x0c, 0x0c, 0x08, 0x7d, 0x82, 0x1c, 0x58, 0x1a, 0x0d, 0x7a, 0xe9, 0x28, 0xc6, 0x09,
	0x88, 0xb2, 0xa3, 0xe9, 0x35, 0xb3, 0x02, 0xab, 0x20, 0x7d, 0x8c, 0x10, 0x2d, 0x1c, 0x28, 0x50,
	0x4e, 0xa9, 0x31, 0x0a, 0xd1, 0xf3, 0x84, 0x70, 0x5b, 0xbf, 0x7f, 0x83, 0x25, 0xad, 0x29, 0xe4,
	0xd4, 0x03, 0x45, 0x9c, 0x52, 0x05, 0x39, 0xb7, 0x8c, 0x9a, 0x68, 0xbd, 0x0c, 0xa3, 0xd4, 0xb4,
	0x71, 0x08, 0xda, 0x38, 0x59, 0xb0, 0x50, 0xdd, 0xd3, 0xa8, 0x88, 0x2a, 0xb8, 0x59, 0xc9, 0xc8,
	0xb9, 0x5c, 0x80, 0xb2, 0x42, 0x4e, 0x9f, 0x27, 0x87, 0xf8, 0xf8, 0x3c, 0x97, 0xb0, 0xf3, 0xa3,
	0x78, 0x69, 0xc0, 0xc2, 0x61, 0xeb, 0x18, 0xa2, 0xfc, 0x48, 0xe9, 0xe0, 0x1a, 0xe5, 0x39, 0xe6,
	0xf9, 0x76, 0xe6, 0xde, 0x44, 0xa6, 0x0d, 0x49, 0xd8, 0xcf, 0x74, 0xa9, 0x9b, 0xa6, 0xcb, 0x33,
	0xe4, 0x60, 0x86, 0x35, 0x66, 0xf5, 0x1a, 0xaf, 0xee, 0xdb, 0x76, 0xcb, 0x01, 0x29, 0x28, 0x50,
	0xc7, 0x6c, 0xec, 0x0a, 0x39, 0xe6, 0x46, 0xda, 0x81, 0xd2, 0x03, 0x76, 0x9b, 0xb3, 0x72, 0x46,
	0x60, 0xfd, 0x2b, 0xe1, 0xc0, 0x34, 0x84, 0x9e, 0x20, 0x53, 0x0a, 0x0e, 0x4d, 0xad, 0xee, 0x8d,
	0x71, 0x86, 0xd5, 0x03, 0xf8, 0x84, 0x25, 0xf1, 0xdc, 0xb0, 0x8f, 0x4b, 0x1c, 0xa7, 0x4f, 0x26,
	0xfd, 0x7f, 0xab, 0xe7, 0x54, 0x4b, 0xe1, 0x34, 0xb5, 0x55, 0x4b, 0xe5, 0xa6, 0x54, 0x4b, 0xe5,
	0xa6, 0x54, 0x4b, 0xc5, 0x52, 0x2d, 0x4f, 0x91, 0x03, 0xc6, 0x48, 0xcb, 0xbd, 0xf5, 0x31, 0xb7,
	0x10, 0x04, 0x56, 0x59, 0xba, 0x4c, 0xa6, 0x97, 0x93, 0xf4, 0x0a, 0x8b, 0x13, 0x94, 0xb9, 0x19,
	0xac, 0xfa, 0x70, 0xf1, 0xe2, 0x73, 0xc6, 0x28, 0x2d, 0xb6, 0x1c, 0x06, 0x84, 0x3e, 0x41, 0xa6,
	0x35, 0xf2, 0x72, 0xdb, 0x7e, 0xd4, 0xd4, 0x4d, 0x7c, 0x2b, 0x09, 0x88, 0x98, 0x25, 0x61, 0xaf,
	0x67, 0xee, 0x24, 0x92, 0xd6, 0xa4, 0xb5, 0xd7, 0xb3, 0x76, 0x19, 0xb8, 0xd7, 0xb3, 0x4a, 0x67,
	0x55, 0x54, 0x23, 0xaf, 0xa2, 0xe6, 0xc9, 0xf4, 0xc5, 0x51, 0xaa, 0x38, 0x3d, 0x85, 0x9c, 0x36,
	0x41, 0x39, 0x0d, 0x4d, 0xb0, 0x88, 0x05, 0x83, 0x61, 0xd3, 0x1b, 0x62, 0x55, 0x72, 0x9a, 0x0f,
	0x5b, 0x3e, 0x07, 0xf8, 0xa1, 0xa1, 0x49, 0xeb, 0x80, 0xc5, 0x0f, 0x63, 0x6b, 0x8d, 0xfc, 0x30,
	0x4a, 0xd2, 0x15, 0x72, 0x44, 0x6f, 0x3c, 0x35, 0xfb, 0x5b, 0x4d, 0x94, 0xed, 0x7b, 0xe4, 0xd6,
	0xc3, 0x51, 0x24, 0x70, 0x56, 0x84, 0x1d, 0x49, 0x76, 0xe8, 0xf6, 0x9b, 0xd6, 0x4d, 0x73, 0xc6,
	0x84, 0xe4, 0xb0, 0xc3, 0x82, 0x70, 0xca, 0xfd, 0x11, 0x52, 0xc7, 0x02, 0xc2, 0xfa, 0xe1, 0x09,
	0x18, 0x80, 0x4b, 0x61, 0x92, 0x06, 0xbb, 0x43, 0x9c, 0x57, 0x7c, 0x15, 0x35, 0x41, 0xfe, 0x7f,
	0x7b, 0x64, 0xc6, 0x96, 0x91, 0x9c, 0x65, 0x7b, 0x9c, 0x4c, 0xf5, 0xd2, 0x30, 0x4e, 0xc5, 0xd4,
	0x04, 0xb6, 0x6b, 0x80, 0x39, 0x6d, 0xf9, 0x4c, 0x92, 0x49, 0xa8, 0x27, 0x04, 0xe1, 0x6c, 0x2a,
	0x8c, 0x59, 0x0d, 0xa0, 0xa7, 0xc9, 0x84, 0xd0, 0xd2, 0x7c, 0xea, 0xcc, 0x9a, 0x02, 0x8b, 0x3c,
	0x15, 0xf9, 0x40, 0xc4, 0x6a, 0xbc, 0x3b, 0x5c, 0x0f, 0x79, 0x4b, 0x13, 0x9c, 0x08, 0x03, 0x94,
	0x59, 0xce, 0x26, 0x73, 0xcb, 0x59, 0x8b, 0x4c, 0x5e, 0xe3, 0x83, 0xd0, 0x3a, 0x80, 0x99, 0x32,
	0xe9, 0x7f, 0xb4, 0x22, 0x96, 0x75, 0x27, 0xe5, 0x27, 0x48, 0x03, 0xb7, 0x1e, 0x9d, 0x36, 0x5f,
	0xf2, 0x9b, 0x8b, 0x95, 0x96, 0x17, 0x28, 0x18, 0x8c, 0xe5, 0x72, 0xc4, 0x35, 0xc8, 0x54, 0x00,
	0x9f, 0x08, 0x09, 0x6f, 0x20, 0xb5, 0x00, 0x09, 0x6f, 0xe0, 0x4e, 0x2a, 0x62, 0xb1, 0xda, 0x49,
	0x45, 0x0c, 0xad, 0x7f, 0x79, 0x9e, 0xc3, 0xad, 0x79, 0x99, 0x84, 0x45, 0x4c, 0x4b, 0xd2, 0x25,
	0x76, 0x8d, 0x0d, 0xd0, 0xa8, 0xaf, 0x06, 0x59, 0x30, 0xcc, 0x1c, 0xeb, 0xf0, 0x84, 0x9b, 0xf5,
	0x16, 0x8c, 0x2b, 0xb0, 0xb0, 0xbf, 0x32, 0x1c, 0xec, 0xb5, 0xa6, 0x70, 0x7a, 0xaa, 0x34, 0x3f,
	0x56, 0x92, 0x53, 0x15, 0x57, 0xca, 0x46, 0x60, 0x40, 0xfc, 0x80, 0x1c, 0x30, 0xed, 0x1a, 0x68,
	0x4b, 0x59, 0xa0, 0xb0, 0x47, 0x9a, 0x32, 0x8c, 0x4d, 0xa0, 0x11, 0x38, 0x5f, 0xe1, 0x36, 0x1e,
	0xf2, 0x9c, 0x92, 0x5a, 0x6f, 0x53, 0xd9, 0xfb, 0xf8, 0xed, 0xdf, 0x4d, 0xea, 0x7c, 0xad, 0x9e,
	0x25, 0xd5, 0x4e, 0xff, 0x06, 0xb6, 0x53, 0x0f, 0xe0, 0xd3, 0x7f, 0x37, 0x99, 0xcd, 0xea, 0x1b,
	0xa7, 0x9c, 0x53, 0x52, 0x5b, 0x1e, 0xf5, 0x99, 0xdc, 0x66, 0xc1, 0x37, 0xb2, 0x82, 0x25, 0x69,
	0x34, 0xe4, 0x3b, 0x6c, 0xb4, 0xb6, 0xa6, 0x02, 0x0b, 0xe6, 0x9f, 0x12, 0x56, 0x46, 0xf9, 0x9e,
	0xf4, 0x23, 0x1e, 0x69, 0xc8, 0x83, 0xce, 0xa2, 0xee, 0x2f, 0x86, 0xc9, 0x96, 0xda, 0xe5, 0x85,
	0xc9, 0x16, 0x4c, 0xbd, 0xb3, 0xfd, 0x1d, 0x21, 0x07, 0x8d, 0x80, 0x27, 0xa0, 0x8b, 0xe0, 0x3a,
	0xb4, 0x25, 0x6c, 0x37, 0x91, 0xa2, 0x6f, 0x20, 0xa4, 0x1b, 0x47, 0xd7, 0xa2, 0x01, 0xdb, 0x54,
	0x47, 0xb2, 0x47, 0x8c, 0x33, 0x56, 0x95, 0x19, 0x18, 0xe5, 0xfc, 0x0e, 0x69, 0x5a, 0x99, 0xb8,
	0xce, 0x89, 0x2d, 0x92, 0x40, 0x50, 0xa5, 0x61, 0xe2, 0xa9, 0x82, 0x88, 0x69, 0x3d, 0xd0, 0x00,
	0xff, 0x15, 0x8f, 0x34, 0x2d, 0xe3, 0x10, 0x46, 0x23, 0x88, 0xfa, 0x62, 0x47, 0x0f, 0x9f, 0x00,
	0x59, 0x89, 0xfa, 0x5c, 0xe6, 0x03, 0xf8, 0x84, 0x36, 0xb1, 0x12, 0x72, 0x84, 0x33, 0x58, 0x03,
	0xe8, 0xeb, 0x09, 0xc1, 0xc4, 0xa5, 0x28, 0x49, 0xe5, 0x1e, 0x68, 0xd6, 0xd4, 0xb8, 0x90, 0x11,
	0x18, 0x65, 0xc0, 0xc2, 0xc4, 0x94, 0x34, 0xbc, 0xec, 0xb3, 0x69, 0x33, 0x2b, 0xb0, 0x0a, 0xfa,
	0x27, 0x05, 0x22, 0xd0, 0x0c, 0x9e, 0x9c, 0xc3, 0x87, 0x90, 0x48, 0x9e, 0xf0, 0xfb, 0xa4, 0x15,
	0x8c, 0xcd, 0x15, 0xf7, 0x7c, 0xc4, 0x06, 0xfd, 0x04, 0x07, 0xf5, 0x22, 0x99, 0xcd, 0x2c, 0xce,
	0xf2, 0x1c, 0xe6, 0x78, 0x7e, 0xed, 0xd6, 0xf5, 0x82, 0x5c, 0x2d, 0x7f, 0x44, 0x8e, 0x3a, 0x8b,
	0xc2, 0xec, 0x5e, 0x4e, 0x52, 0x43, 0x74, 0x64, 0x92, 0xbe, 0x85, 0x10, 0x98, 0x1b, 0xbc, 0xac,
	0xd8, 0x44, 0x38, 0xba, 0xd5, 0x65, 0x02, 0xa3, 0xbc, 0xbf, 0x64, 0x75, 0xa8, 0x33, 0x40, 0xd4,
	0x44, 0x93, 0x9c, 0x0d, 0x22, 0x65, 0x4c, 0x4b, 0xd0, 0x20, 0xf8, 0xed, 0xbf, 0x5a, 0x21, 0x44,
	0x9f, 0x9b, 0x3a, 0x65, 0x9c, 0x6b, 0xc1, 0x8a, 0xd2, 0x82, 0x6f, 0x20, 0x13, 0xbd, 0x78, 0x7d,
	0x19, 0x8f, 0x2a, 0x2a, 0x06, 0xc6, 0xbc, 0x99, 0xac, 0xa9, 0x23, 0xca, 0x42, 0xad, 0x36, 0x4b,
	0xa0, 0x56, 0xed, 0x66, 0x6a, 0xf1, 0xb2, 0x20, 0xd6, 0x9d, 0x61, 0xca, 0xe2, 0x6b, 0xe1, 0x00,
	0x35, 0x66, 0x35, 0x50, 0x69, 0x18, 0xec, 0x36, 0x1b, 0x84, 0x7b, 0xa8, 0x33, 0xab, 0x01, 0x4f,
	0x00, 0x05, 0xed, 0x68, 0x87, 0xdb, 0x2e, 0x53, 0x01, 0x7e, 0xd3, 0x07, 0x49, 0x7d, 0x29, 0x1c,
	0x0c, 0x60, 0x03, 0x92, 0x3f, 0x2f, 0x86, 0x9c, 0x80, 0xe7, 0x43, 0xe5, 0xa5, 0xd1, 0xb0, 0x8f,
	0xca, 0x71, 0x2a, 0xc0, 0x6f, 0x58, 0x6e, 0x3a, 0x49, 0x8f, 0x0d, 0xd8, 0x7a, 0x7a, 0x76, 0x30,
	0x10, 0x9a, 0xd1, 0x04, 0xf9, 0x8f, 0x93, 0x69, 0xcd, 0x42, 0xec, 0xcd, 0x94, 0x23, 0xc7, 0xe9,
	0x34, 0xcf, 0xf7, 0x5f, 0x24, 0x47, 0x9d, 0xd4, 0x17, 0x1a, 0xb2, 0x72, 0x82, 0x57, 0x32, 0x13,
	0xfc, 0x34, 0x39, 0x98, 0x3d, 0xf4, 0xe0, 0x6b, 0x50, 0x16, 0xec, 0x5f, 0x92, 0xa3, 0x0d, 0xf4,
	0x22, 0xb9, 0xe1, 0x60, 0x20, 0xfb, 0x41, 0xd8, 0x11, 0x52, 0x47, 0x71, 0x91, 0x86, 0x03, 0x26,
	0x50, 0xa7, 0x0d, 0xa2, 0x30, 0x11, 0xed, 0xf2, 0x84, 0xff, 0x0f, 0x9e, 0xbd, 0x2f, 0x84, 0x45,
	0xa4, 0x1b, 0x47, 0x3b, 0x61, 0xbc, 0xa7, 0x97, 0x05, 0x03, 0x02, 0x53, 0xa1, 0x37, 0x8a, 0x53,
	0xc8, 0xac, 0x60, 0xa6, 0x4c, 0x02, 0x97, 0xbb, 0xf1, 0x68, 0xcc, 0xe2, 0x14, 0xab, 0x72, 0x8d,
	0x62, 0x82, 0xe8, 0x29, 0xd2, 0x94, 0xc9, 0x2b, 0x68, 0x1e, 0xd5, 0xb0, 0x8c, 0x0d, 0xa4, 0xaf,
	0x27, 0x87, 0xc1, 0xd8, 0x10, 0x0e, 0x9b, 0xcc, 0x4e, 0xdf, 0x95, 0x45, 0x1f, 0x20, 0x33, 0x4b,
	0xa3, 0x9d, 0x71, 0xb8, 0x0e, 0x29, 0xb5, 0xff, 0xad, 0x07, 0x19, 0xa8, 0x7f, 0x5d, 0x98, 0x91,
	0x5c, 0xf1, 0xc0, 0x24, 0x5b, 0x1d, 0x6d, 0xb3, 0x61, 0x22, 0x4c, 0x37, 0x91, 0x02, 0x16, 0xe0,
	0x57, 0xf4, 0x12, 0x8b, 0x13, 0xb1, 0x02, 0x1a, 0x90, 0x22, 0x04, 0xab, 0x85, 0x08, 0xfa, 0x4f,
	0xda, 0xaa, 0x91, 0x9e, 0xb6, 0xe5, 0x8b, 0xe6, 0x75, 0xa4, 0x14, 0xb0, 0x97, 0x29, 0x99, 0x5c,
	0x1a, 0xed, 0xec, 0x84, 0xc3, 0x3e, 0x7d, 0x90, 0xd4, 0x52, 0x20, 0x0e, 0xc6, 0x7a, 0xc6, 0xd8,
	0xba, 0x63, 0xee, 0x19, 0xa0, 0x30, 0xc0, 0x02, 0xfe, 0xab, 0x87, 0xb8, 0x9a, 0xa0, 0x77, 0x93,
	0xa3, 0x4b, 0x31, 0x0b, 0x53, 0x26, 0xe5, 0x4c, 0x14, 0x9e, 0xad, 0xd2, 0xbb, 0xc8, 0xe1, 0x76,
	0x3c, 0x1a, 0x67, 0x33, 0x6a, 0x74, 0x9e, 0x1c, 0xe7, 0x75, 0x32, 0x82, 0x27, 0x4b, 0xd4, 0xe9,
	0x09, 0x32, 0x07, 0x55, 0x0b, 0xf2, 0x27, 0xe8, 0x29, 0x32, 0xdf, 0x63, 0xa9, 0xfb, 0xb0, 0x4e,
	0x96, 0x9a, 0x84, 0x7e, 0x9e, 0x1b, 0xf7, 0x8b, 0xfb, 0x69, 0xd0, 0x7b, 0xc8, 0x5d, 0x1c, 0x13,
	0x6d, 0xcd, 0xca, 0xcc, 0x29, 0xc8, 0xe4, 0x66, 0x4d, 0x3e, 0x93, 0xd0, 0xa3, 0xe4, 0x10, 0xaf,
	0x09, 0x2b, 0xac, 0x04, 0x37, 0xe9, 0x61, 0x72, 0x10, 0x10, 0x37, 0x81, 0x33, 0x50, 0x96, 0xe3,
	0x61, 0x82, 0x0f, 0x02, 0x7f, 0x7a, 0x2c, 0x55, 0x6b, 0xac, 0xcc, 0x98, 0xa5, 0x94, 0xcc, 0x00,
	0x75, 0x61, 0x1a, 0x4a, 0xd8, 0x21, 0x7a, 0x9c, 0xb4, 0x7a, 0x2c, 0x45, 0x2b, 0x21, 0x57, 0x83,
	0xd2, 0x7b, 0xc9, 0xdd, 0x82, 0x0e, 0xc3, 0x1c, 0x92, 0xd9, 0x47, 0x91, 0x92, 0x78, 0x34, 0x76,
	0x65, 0x1e, 0xd3, 0x23, 0x28, 0x1d, 0x9c, 0x32, 0xab, 0x65, 0x0f, 0xae, 0x99, 0x75, 0x37, 0x64,
	0x71, 0x9a, 0xb2, 0x59, 0x73, 0x90, 0xc5, 0xf9, 0x96, 0x6d, 0xf0, 0x1e, 0x9d, 0x95, 0xad, 0x75,
	0x9c, 0x1e, 0x23, 0xb4, 0xc7, 0xd2, 0x6c, 0x95, 0x7b, 0xe9, 0x11, 0x32, 0x8b, 0xb8, 0xc3, 0x18,
	0x48, 0xe8, 0x09, 0x20, 0x18, 0xcd, 0x4e, 0x21, 0x5b, 0xbc, 0x51, 0x99, 0x7d, 0x1f, 0x10, 0xcc,
	0xb1, 0xd3, 0xe6, 0x9b, 0xcc, 0x7c, 0x0d, 0x08, 0x0f, 0xd4, 0xcd, 0x08, 0x85, 0xdd, 0xc4, 0x83,
	0xc0, 0x70, 0xc9, 0x16, 0xa5, 0x77, 0x65, 0xee, 0x63, 0x80, 0xd5, 0xd9, 0x41, 0xca, 0x62, 0x69,
	0xcd, 0x2e, 0xed, 0xf4, 0x67, 0x17, 0x60, 0xa0, 0x03, 0xde, 0x65, 0x34, 0xdc, 0x94, 0x85, 0xdf,
	0x00, 0x03, 0x2d, 0xb0, 0xc1, 0x93, 0x0c, 0x99, 0xf1, 0x46, 0xc8, 0x08, 0xd8, 0x78, 0x14, 0xa7,
	0x7c, 0xd3, 0x22, 0x33, 0x1e, 0x07, 0x66, 0x74, 0xe3, 0xdd, 0x21, 0xe3, 0x7b, 0x4c, 0x09, 0x7f,
	0x13, 0x48, 0x34, 0xa0, 0x6e, 0xa0, 0x64, 0xa3, 0xfd, 0x14, 0x9d, 0x23, 0xc7, 0x80, 0x5d, 0x0e,
	0xa4, 0xdf, 0x0c, 0x48, 0x83, 0xea, 0x08, 0xc2, 0xa1, 0x96, 0x9d, 0xb7, 0xd0, 0x16, 0x39, 0x82,
	0xdd, 0x4b, 0x55, 0x22, 0x73, 0xde, 0xaa, 0x27, 0x80, 0xde, 0xef, 0xca, 0xcc, 0xa7, 0x61, 0x8a,
	0x1a, 0x2c, 0x06, 0x55, 0x02, 0xbb, 0x14, 0x99, 0xff, 0x36, 0x3d, 0x04, 0x30, 0x9c, 0xdc, 0x5d,
	0x20, 0x33, 0xdf, 0x0e, 0xf4, 0x71, 0xe6, 0xa2, 0x07, 0x58, 0xc2, 0xcf, 0x02, 0x9c, 0x57, 0xb2,
	0xe0, 0x8b, 0x9a, 0x83, 0xdc, 0xb5, 0x22, 0x33, 0x96, 0xa0, 0x42, 0xc0, 0x76, 0x46, 0xd7, 0xec,
	0x0a, 0x6d, 0x7a, 0x92, 0xdc, 0x2b, 0x24, 0x37, 0xb3, 0xc5, 0x96, 0x45, 0xce, 0xd1, 0xfb, 0xc8,
	0x3d, 0xa8, 0x9e, 0x0a, 0x0a, 0x9c, 0x07, 0x0a, 0x2f, 0xb0, 0xb4, 0x28, 0xff, 0x82, 0x31, 0x3b,
	0xd6, 0xb8, 0x3b, 0x52, 0x66, 0x5d, 0xa4, 0xaf, 0x25, 0xf7, 0x5f, 0x00, 0x61, 0xb6, 0x56, 0xec,
	0xab, 0x51, 0xba, 0x15, 0x41, 0x5b, 0x2c, 0x50, 0x7c, 0xec, 0x80, 0x34, 0x1a, 0x7c, 0x34, 0x76,
	0x62, 0x06, 0x9d, 0xef, 0x00, 0x06, 0xc0, 0xc0, 0xaf, 0x86, 0xdb, 0x6c, 0x74, 0x4d, 0xb3, 0xf9,
	0x19, 0x99, 0x21, 0x1d, 0xe5, 0x32, 0xe3, 0x12, 0x64, 0x08, 0x95, 0xc0, 0x97, 0x72, 0x91, 0xb1,
	0x0c, 0x42, 0x8a, 0x13, 0xca, 0x02, 0x5f, 0xa6, 0x3e, 0x39, 0x91, 0x47, 0x19, 0x17, 0x6d, 0x59,
	0x66, 0x05, 0x28, 0xbe, 0xc2, 0xe2, 0x68, 0x63, 0x2f, 0x3b, 0x7d, 0xbb, 0xd0, 0xdd, 0xb9, 0x1b,
	0xe3, 0x70, 0xd8, 0xb7, 0x45, 0xf6, 0x59, 0x10, 0x48, 0x39, 0x74, 0xe2, 0x4c, 0x43, 0xe6, 0x05,
	0xd0, 0x1e, 0x70, 0x78, 0x71, 0x31, 0x8e, 0xd8, 0x86, 0x49, 0x70, 0x4f, 0x30, 0xdf, 0xb4, 0xc7,
	0xcd, 0xfc, 0x55, 0x98, 0x09, 0x01, 0xdb, 0x8c, 0x60, 0x0d, 0x14, 0xfe, 0xdb, 0x95, 0x8d, 0x8d,
	0x84, 0x29, 0x11, 0x78, 0x4e, 0xaf, 0x32, 0x99, 0xd3, 0x10, 0x59, 0xe2, 0x0a, 0xea, 0xd4, 0x17,
	0x07, 0x0b, 0xa0, 0x73, 0x2e, 0xb2, 0x30, 0x4e, 0xd7, 0x58, 0xa8, 0xea, 0x5f, 0xc5, 0xfa, 0x76,
	0x4d, 0x3e, 0x57, 0x65, 0x89, 0xff, 0x27, 0x58, 0x96, 0x29, 0x74, 0x89, 0x19, 0x6b, 0xdd, 0xff,
	0x97, 0x2b, 0x59, 0x01, 0x0e, 0xef, 0x04, 0x29, 0xbc, 0x3c, 0x4a, 0xa3, 0x8d, 0xbd, 0xa5, 0x67,
	0x79, 0x4d, 0xf4, 0xbc, 0x2b, 0x4d, 0xf7, 0x3c, 0x48, 0x72, 0x8f, 0xa5, 0x38, 0x89, 0x6c, 0xe7,
	0x9b, 0x2c, 0xf2, 0x2e, 0xae, 0x76, 0x60, 0x12, 0x98, 0x43, 0xf2, 0x63, 0x40, 0x9e, 0x5c, 0xfe,
	0x94, 0x27, 0x59, 0xe6, 0xbe, 0x1b, 0x34, 0xa8, 0x9e, 0x9f, 0xab, 0x3b, 0x63, 0x9c, 0xe3, 0x32,
	0xfb, 0xc7, 0x41, 0x2b, 0x08, 0xf1, 0xe1, 0x1e, 0x79, 0x99, 0xf3, 0x82, 0x31, 0xf1, 0x79, 0x8e,
	0x8d, 0x4d, 0x08, 0x53, 0xb2, 0x33, 0x4c, 0x58, 0x9c, 0x9e, 0x8f, 0x06, 0x4c, 0xc1, 0xd7, 0x34,
	0x3a, 0x0e, 0xdd, 0xc4, 0x80, 0x0f, 0x32, 0x97, 0x8b, 0x96, 0xdd, 0xec, 0x06, 0xae, 0x0f, 0x5b,
	0xa3, 0xeb, 0xc2, 0xee, 0x91, 0xf0, 0x4d, 0x40, 0x14, 0x51, 0xcf, 0xaa, 0xaf, 0x2d, 0x8d, 0x28,
	0x3f, 0x27, 0xc9, 0x68, 0xa8, 0xe8, 0xa1, 0x46, 0xa3, 0x3f, 0xfb, 0xf2, 0xcb, 0x2f, 0xbf, 0x5c,
	0xf1, 0xff, 0xaa, 0x52, 0x60, 0xc5, 0x38, 0x8d, 0xec, 0x76, 0xde, 0x90, 0xe6, 0x07, 0xd9, 0x65,
	0xae, 0xc0, 0x6c, 0x15, 0x30, 0x01, 0xe5, 0xb9, 0xf0, 0xee, 0x0e, 0x5a, 0x76, 0xcd, 0xc0, 0x80,
	0xd0, 0xfb, 0x49, 0xb5, 0xb7, 0x1d, 0xe1, 0x39, 0x40, 0x81, 0xd3, 0x08, 0xf2, 0x1d, 0x2e, 0xbb,
	0xba, 0xd3, 0x65, 0x77, 0x2b, 0x6e, 0xb9, 0x85, 0xf3, 0x64, 0x72, 0x5d, 0x30, 0x60, 0xc6, 0xb6,
	0x01, 0x5b, 0x9b, 0x58, 0x59, 0xee, 0xcb, 0x9c, 0x4c, 0x0b, 0x64, 0x65, 0x7f, 0xe4, 0xb4, 0x00,
	0x5d, 0x4c, 0x5d, 0x68, 0x17, 0x77, 0xb9, 0x65, 0x31, 0xd7, 0xd1, 0xa0, 0xee, 0xf0, 0x9f, 0xbc,
	0x72, 0xd3, 0xb2, 0xf4, 0x04, 0xc4, 0x39, 0xae, 0x95, 0x5b, 0x1d, 0x57, 0x3c, 0xc0, 0xe4, 0x76,
	0x69, 0x57, 0x1c, 0xee, 0x68, 0xc0, 0xc2, 0x72, 0x31, 0x99, 0x11, 0x92, 0xf9, 0x1a, 0x8b, 0xb3,
	0x6e, 0x2a, 0x34, 0xbd, 0x1f, 0xf7, 0xca, 0x0c, 0xe5, 0x52, 0x6a, 0xe5, 0x20, 0x54, 0x8c, 0x41,
	0x78, 0xa6, 0x18, 0xbb, 0xf7, 0x20, 0x76, 0x27, 0x8d, 0x41, 0xd8, 0x0f, 0xb7, 0xcf, 0x78, 0xfb,
	0x1b, 0xe9, 0xb7, 0x8c, 0xe1, 0xb3, 0xc5, 0x18, 0x6e, 0x23, 0x86, 0x0f, 0xca, 0x99, 0xb2, 0x4f,
	0xcf, 0x1a, 0xcf, 0xaf, 0x54, 0xcb, 0xb7, 0x09, 0xb7, 0x8a, 0x23, 0xec, 0x5f, 0x2f, 0xb3, 0xeb,
	0xe2, 0xcc, 0x0b, 0xc3, 0x34, 0x44, 0xd2, 0xf2, 0x33, 0xd5, 0x32, 0x2e, 0x6c, 0xd3, 0x6f, 0x54,
	0xcf, 0xb8, 0xa4, 0xdd, 0x3e, 0xa8, 0x89, 0x42, 0xf7, 0x36, 0x3a, 0x59, 0xb6, 0x99, 0x60, 0x00,
	0x1e, 0x06, 0xa3, 0x93, 0x45, 0x81, 0xf2, 0x4e, 0x16, 0x6f, 0x7f, 0x27, 0x8b, 0x77, 0xd3, 0x4e,
	0x16, 0xcf, 0xed, 0x64, 0x29, 0x93, 0xfe, 0x81, 0x25, 0xfd, 0x65, 0xe3, 0xa1, 0x47, 0xee, 0x67,
	0x2a, 0x85, 0xdb, 0xb7, 0xd2, 0x41, 0x3b, 0x46, 0x26, 0xac, 0xa8, 0x8f, 0x09, 0x3d, 0x75, 0xc1,
	0x3e, 0x4e, 0xd2, 0x70, 0x67, 0x2c, 0xfc, 0x12, 0x1a, 0x80, 0x1e, 0x0d, 0xe8, 0x06, 0x0f, 0xe6,
	0x6b, 0x3c, 0x76, 0x55, 0x01, 0x32, 0xde, 0x84, 0xba, 0xcb, 0x9b, 0x20, 0xcc, 0x1f, 0xe4, 0x4f,
	0x33, 0x90, 0xc9, 0x85, 0x8b, 0xc5, 0x4c, 0xd9, 0x41, 0xa6, 0x9c, 0xb0, 0x54, 0x42, 0x8e, 0x54,
	0xcd, 0x8f, 0xff, 0xf4, 0x0a, 0x77, 0xac, 0xb7, 0xc5, 0x0f, 0x5f, 0x9c, 0xe6, 0xcb, 0x58, 0x53,
	0x1e, 0x4f, 0x6c, 0xc1, 0x6c, 0x7f, 0x0d, 0x97, 0x48, 0xc3, 0x5f, 0x73, 0x82, 0x10, 0x9e, 0x50,
	0x3e, 0x96, 0x7a, 0x60, 0x40, 0xca, 0x68, 0x1f, 0x5a, 0xb4, 0x17, 0x90, 0xa5, 0x69, 0xff, 0xbc,
	0xe7, 0xd8, 0x90, 0xdf, 0x99, 0xd3, 0xf8, 0x85, 0xc5, 0x62, 0xac, 0x5f, 0x44, 0xac, 0x5b, 0xd6,
	0x88, 0x19, 0x08, 0x69, 0x7c, 0x37, 0x73, 0x07, 0x05, 0xce, 0x65, 0xf1, 0xed, 0xc5, 0x5d, 0xc5,
	0xd8, 0xd5, 0x31, 0x43, 0x23, 0x3b, 0x3b, 0x7a, 0x9f, 0xe3, 0xf0, 0xe1, 0x66, 0xf9, 0x52, 0x46,
	0x69, 0x62, 0x51, 0x9a, 0xeb, 0x42, 0x23, 0xf0, 0x05, 0xcf, 0x79, 0xce, 0x01, 0x12, 0x09, 0xe5,
	0x87, 0x1a, 0x0f, 0x95, 0x2e, 0x3d, 0xc7, 0xb4, 0x1c, 0x15, 0xd5, 0x8c, 0xa3, 0xa2, 0xcc, 0x8e,
	0x48, 0x2d, 0x3b, 0xc2, 0x81, 0x92, 0xc6, 0x39, 0xce, 0x9e, 0xc0, 0xd0, 0xfb, 0x78, 0x28, 0xbe,
	0x88, 0x65, 0x9b, 0x36, 0x02, 0x57, 0x03, 0xcc, 0x58, 0x78, 0x5b, 0x71, 0xc7, 0xbb, 0xd8, 0xf1,
	0x51, 0x63, 0x65, 0xd2, 0x0d, 0xeb, 0x3e, 0x3f, 0xea, 0x15, 0x1f, 0xf1, 0x94, 0x32, 0x4b, 0x09,
	0x6f, 0xc5, 0x10, 0xde, 0x85, 0x4e, 0x31, 0x3e, 0xd7, 0x10, 0x9f, 0xfb, 0x34, 0x3e, 0xce, 0x3e,
	0x2d, 0xbd, 0x52, 0x7c, 0xbc, 0x74, 0xe7, 0xce, 0xa1, 0x95, 0xdb, 0xae, 0x56, 0xe2, 0xb6, 0xab,
	0xe7, 0xdd, 0x76, 0x0b, 0xef, 0x28, 0x26, 0x7d, 0x0f, 0x49, 0x9f, 0xb7, 0x35, 0x6a, 0x9e, 0x28,
	0x4d, 0xfb, 0x37, 0xbc, 0xc2, 0xb3, 0xb3, 0x3b, 0x47, 0x79, 0x99, 0x5e, 0x7c, 0xc9, 0xd6, 0x8b,
	0x6e, 0xd4, 0x34, 0xfe, 0xdf, 0xf6, 0x0a, 0x8e, 0xf7, 0x00, 0xd3, 0x8b, 0xab, 0xab, 0x5d, 0x8c,
	0x01, 0x15, 0x22, 0x25, 0xd3, 0x66, 0x0c, 0x2a, 0x67, 0x7e, 0x26, 0x06, 0x15, 0x73, 0x38, 0x79,
	0x32, 0x89, 0xb1, 0xa0, 0x80, 0x20, 0x5f, 0x25, 0xf0, 0xbb, 0x6c, 0x23, 0xf1, 0x5e, 0xc7, 0x46,
	0x22, 0x83, 0xa2, 0xa6, 0xe2, 0x6b, 0x5e, 0xc1, 0x49, 0xe4, 0x7e, 0x54, 0x94, 0xe0, 0x9a, 0x89,
	0x5b, 0x15, 0x01, 0xa5, 0xd3, 0x32, 0xa0, 0xb4, 0x0c, 0xf7, 0x9f, 0x28, 0xd8, 0x04, 0x39, 0x71,
	0xbf, 0x4a, 0x9a, 0x32, 0x0f, 0x0f, 0xa9, 0x54, 0xd0, 0x2f, 0xa0, 0x7b, 0x40, 0x04, 0xfd, 0x1e,
	0x27, 0x53, 0x98, 0x69, 0xb8, 0xde, 0x34, 0x40, 0x87, 0xf1, 0x56, 0x8d, 0x30, 0x5e, 0x7f, 0x54,
	0x70, 0xce, 0x9a, 0x8d, 0x48, 0x28, 0xa3, 0xe4, 0x7d, 0x16, 0x25, 0xce, 0xe6, 0x34, 0x25, 0xe3,
	0x82, 0xd3, 0xdb, 0x5c, 0x87, 0x17, 0x8a, 0x3b, 0x7c, 0xd9, 0x73, 0xf4, 0x58, 0xc8, 0xbb, 0xf3,
	0x60, 0x14, 0x27, 0xe3, 0xd1, 0x30, 0xc1, 0xf1, 0x59, 0x79, 0x06, 0x3b, 0x69, 0x04, 0x95, 0x95,
	0x67, 0x80, 0x29, 0xe7, 0xe2, 0x78, 0x14, 0x0b, 0xf7, 0x09, 0x4f, 0xe8, 0x6b, 0x51, 0x3c, 0x84,
	0x80, 0x27, 0xfc, 0x6f, 0x7a, 0xae, 0xd3, 0xe5, 0x1f, 0xc9, 0x14, 0x28, 0x59, 0x90, 0xde, 0xcf,
	0x79, 0x71, 0xb7, 0x56, 0xc4, 0x85, 0xac, 0xdf, 0xc8, 0x9f, 0x82, 0xe7, 0xb8, 0x5e, 0xb2, 0x58,
	0x7f, 0x80, 0xf7, 0x74, 0x97, 0xa9, 0x35, 0x8c, 0xa6, 0x74, 0x3f, 0xef, 0x2d, 0x39, 0x57, 0x77,
	0x1a, 0x28, 0x25, 0x5b, 0xc6, 0x0f, 0x7a, 0x96, 0xb2, 0x2d, 0x6c, 0x57, 0xf7, 0xfe, 0x3d, 0xaf,
	0xf0, 0xdc, 0x1e, 0xbd, 0x82, 0x3c, 0x5a, 0x11, 0xfb, 0xaf, 0x06, 0x32, 0x09, 0x39, 0x3c, 0xb8,
	0xa6, 0x2f, 0x66, 0x8e, 0x4c, 0x82, 0x01, 0xd7, 0x5e, 0x13, 0x1b, 0x31, 0x34, 0x6c, 0x79, 0x0a,
	0x0d, 0xbb, 0x31, 0xc2, 0xf9, 0xd0, 0x8a, 0x54, 0xd9, 0x9a, 0xf9, 0x93, 0x9e, 0xa5, 0x77, 0x0b,
	0xb0, 0xd4, 0xa4, 0x7c, 0xd6, 0xdb, 0xdf, 0xcb, 0x70, 0xcb, 0xbb, 0xdf, 0xa0, 0x18, 0xbf, 0x9f,
	0xf6, 0xac, 0xed, 0xef, 0x7e, 0x5d, 0x6b, 0x44, 0xff, 0xa6, 0x5a, 0xec, 0xe8, 0x40, 0x06, 0x2e,
	0x1a, 0x63, 0x2e, 0x52, 0x06, 0x03, 0x2b, 0x26, 0x03, 0x15, 0xd2, 0x55, 0x63, 0x45, 0xbc, 0xc9,
	0x83, 0xac, 0x53, 0xa4, 0xd2, 0x09, 0x4a, 0xc3, 0x91, 0x2b, 0x9d, 0xe0, 0xce, 0xc5, 0x20, 0x2f,
	0x10, 0xc2, 0xbd, 0x33, 0x58, 0xad, 0x61, 0x39, 0x4d, 0xd1, 0xbb, 0xcd, 0x73, 0x03, 0xa3, 0x94,
	0x19, 0x02, 0x3c, 0x55, 0x1e, 0x02, 0x7c, 0xd3, 0x61, 0xc6, 0x65, 0xb6, 0xcb, 0x2f, 0x78, 0x96,
	0xdd, 0x56, 0x34, 0x68, 0x7a, 0x68, 0x5f, 0xf5, 0xf2, 0x5e, 0xaa, 0x1f, 0xe1, 0x90, 0x96, 0x29,
	0xa4, 0x8f, 0xd8, 0x0a, 0x29, 0x8b, 0xa5, 0xa6, 0xe1, 0xfb, 0x4a, 0x25, 0xb4, 0xd7, 0xba, 0xa9,
	0x75, 0x28, 0x8c, 0xde, 0xf5, 0x30, 0xd9, 0xd6, 0x01, 0x59, 0x3c, 0xa5, 0x02, 0xb5, 0xfa, 0x22,
	0x1e, 0x45, 0xa4, 0x40, 0x61, 0xb6, 0x17, 0x05, 0x21, 0x95, 0xf6, 0x22, 0xa4, 0xbb, 0xab, 0x22,
	0x48, 0xb7, 0xd2, 0x5d, 0xd5, 0x2b, 0x4a, 0xdd, 0x58, 0x51, 0xca, 0x94, 0xc2, 0x47, 0x5d, 0x4a,
	0x21, 0x87, 0xa7, 0x26, 0xe6, 0x5f, 0x3c, 0x87, 0x83, 0x70, 0xbf, 0xad, 0xb9, 0x73, 0x54, 0x6e,
	0x72, 0x6b, 0xde, 0x1b, 0x0f, 0x22, 0x1e, 0x82, 0x29, 0x42, 0x29, 0x15, 0x80, 0xce, 0x8b, 0x00,
	0xe0, 0xc5, 0xd1, 0xee, 0xb0, 0x2f, 0xed, 0x68, 0x13, 0xb4, 0xb0, 0x54, 0x4c, 0xf8, 0xc7, 0x3c,
	0x6b, 0xf7, 0x97, 0xa3, 0x49, 0x93, 0xfc, 0x8f, 0x9e, 0xd3, 0xf9, 0x79, 0x5b, 0x44, 0xcf, 0x93,
	0x69, 0x43, 0xdc, 0xc5, 0x40, 0x9a, 0x20, 0xfa, 0x24, 0x69, 0xe2, 0x64, 0x5d, 0x1d, 0xf1, 0xd9,
	0x21, 0xa2, 0xca, 0x5c, 0x13, 0xd9, 0x2e, 0xb8, 0x70, 0xae, 0x98, 0xd8, 0x8f, 0x7b, 0xd6, 0xc6,
	0xd1, 0x41, 0x8d, 0x26, 0x77, 0x9d, 0x4c, 0x1b, 0x9d, 0xc0, 0x10, 0x60, 0xd2, 0x98, 0x6f, 0x1a,
	0xa0, 0x72, 0x95, 0xd1, 0x57, 0x0f, 0x34, 0xc0, 0x8e, 0x91, 0xb5, 0x42, 0xdb, 0xaf, 0x8a, 0x68,
	0x36, 0x67, 0xf8, 0xe9, 0x5c, 0x36, 0xfc, 0xd4, 0x08, 0x3d, 0xb5, 0xc3, 0x37, 0xab, 0xb9, 0xf0,
	0xcd, 0x6f, 0x79, 0x64, 0xc6, 0x8e, 0x75, 0xfe, 0x11, 0xc5, 0xf5, 0x3e, 0x24, 0x62, 0x5b, 0x59,
	0x36, 0xb0, 0x57, 0xd1, 0x19, 0xc8, 0x02, 0xfb, 0x2d, 0x01, 0xfe, 0xfb, 0x3d, 0x21, 0xd9, 0xe2,
	0x8e, 0x9a, 0x32, 0x1c, 0x24, 0x19, 0x32, 0xa9, 0x4e, 0xf4, 0x7a, 0xd1, 0x4b, 0x4c, 0xa8, 0x0a,
	0x0d, 0xc0, 0x09, 0x82, 0x37, 0xad, 0x96, 0x46, 0xbb, 0x42, 0xda, 0xea, 0x81, 0x09, 0xc2, 0x98,
	0xbd, 0xf0, 0x86, 0x31, 0xbd, 0x64, 0xd2, 0x7f, 0x9e, 0x34, 0x83, 0xb1, 0x89, 0x84, 0x16, 0x69,
	0xcf, 0x12, 0xe9, 0x05, 0x11, 0x61, 0x0a, 0xc5, 0x12, 0xe1, 0x6e, 0xa0, 0xa6, 0x42, 0xe5, 0xf5,
	0x03, 0xa3, 0x94, 0xff, 0x02, 0x21, 0xed, 0x45, 0xa9, 0x63, 0x84, 0x52, 0xf3, 0x94, 0x52, 0xe3,
	0x17, 0x1b, 0xe5, 0xbd, 0x4e, 0xfc, 0xa6, 0x67, 0xc8, 0x64, 0x30, 0xe6, 0x5d, 0x54, 0xad, 0xd8,
	0x51, 0x0b, 0xc9, 0x40, 0x16, 0xf2, 0x7f, 0xde, 0x23, 0x77, 0x99, 0x81, 0x09, 0x97, 0x46, 0xa1,
	0xb2, 0x3a, 0xf9, 0xf5, 0xc7, 0x55, 0x28, 0x98, 0x89, 0x5d, 0xd3, 0x48, 0x05, 0xaa, 0x48, 0x99,
	0xf6, 0xfc, 0x84, 0xad, 0x3d, 0x0b, 0x3a, 0xd4, 0x73, 0xeb, 0xbb, 0x9e, 0x3b, 0xd4, 0x9e, 0xbe,
	0x5e, 0x46, 0xee, 0x79, 0xd6, 0x3d, 0x3a, 0x5d, 0x76, 0x65, 0xcc, 0xe2, 0x30, 0x1d, 0xc5, 0x89,
	0x0c, 0xe1, 0xbb, 0x40, 0x68, 0xa6, 0xa5, 0x88, 0xc9, 0xd8, 0xca, 0xbb, 0x0a, 0x42, 0xf6, 0x03,
	0x47, 0x15, 0xeb, 0x44, 0xbf, 0x9a, 0xb9, 0x39, 0xa2, 0x97, 0x27, 0x7e, 0xa3, 0x54, 0xa4, 0xfc,
	0xf7, 0x92, 0xd9, 0x6c, 0xdb, 0xf4, 0x01, 0x32, 0x23, 0xdd, 0xfe, 0x22, 0x90, 0x91, 0x1b, 0xb9,
	0x19, 0x28, 0xe8, 0x7d, 0x10, 0x30, 0x55, 0x8a, 0xcf, 0x40, 0x0b, 0x06, 0x62, 0x7d, 0x35, 0x4c,
	0x59, 0x0c, 0x13, 0x5b, 0x1e, 0x63, 0x2b, 0x80, 0xdf, 0x21, 0x87, 0x1d, 0x8c, 0x01, 0x64, 0xcf,
	0x6e, 0x6e, 0xae, 0x8c, 0x55, 0x38, 0x28, 0x4f, 0x49, 0x3d, 0x6d, 0xec, 0x4b, 0x55, 0xda, 0x7f,
	0x1f, 0x39, 0xee, 0x1a, 0x8f, 0xab, 0x51, 0xba, 0xd5, 0x5e, 0x0b, 0xc6, 0xf4, 0x51, 0x52, 0x43,
	0x6b, 0x8a, 0x9f, 0x99, 0x95, 0x5e, 0x85, 0xc0, 0x82, 0x86, 0xbd, 0x5e, 0x29, 0xb0, 0xd7, 0xab,
	0xe6, 0xec, 0xf1, 0x9f, 0x27, 0x27, 0xf2, 0x63, 0x62, 0xa1, 0xf0, 0x26, 0x3b, 0x0c, 0xee, 0x35,
	0x25, 0x38, 0xc8, 0x3a, 0x32, 0x2e, 0x6e, 0x95, 0xcc, 0x65, 0x42, 0x32, 0xa4, 0x37, 0x79, 0x63,
	0x94, 0xd0, 0xc7, 0xed, 0x86, 0xe7, 0xcd, 0x39, 0xeb, 0xaa, 0x21, 0x5b, 0x1d, 0x91, 0xbb, 0x0b,
	0xcb, 0xd0, 0xd7, 0x91, 0x7a, 0xa7, 0x0f, 0x4b, 0x1b, 0xe7, 0xd8, 0x31, 0xeb, 0x76, 0x03, 0x64,
	0x44, 0x1b, 0x11, 0x8b, 0x03, 0x5e, 0x88, 0x9e, 0x22, 0x4d, 0x23, 0xbe, 0xff, 0x9a, 0x14, 0x06,
	0x1b, 0xe8, 0xff, 0x94, 0xe7, 0x8a, 0x25, 0x02, 0x2d, 0xaa, 0x8d, 0x05, 0xb1, 0xab, 0x36, 0x20,
	0x2a, 0x9e, 0x57, 0x5c, 0x77, 0x2b, 0xdb, 0xc6, 0xfe, 0x92, 0xbd, 0x8d, 0xcd, 0x77, 0xa6, 0xa7,
	0xf0, 0x77, 0xbc, 0xf2, 0x00, 0xa6, 0xdb, 0x72, 0x53, 0xec, 0x6b, 0x16, 0x2c, 0x5c, 0x2e, 0x46,
	0xfe, 0x93, 0x9e, 0xe5, 0x78, 0x2a, 0x43, 0x4e, 0x93, 0xf1, 0x55, 0xaf, 0x28, 0xca, 0xea, 0x0e,
	0x11, 0x50, 0x72, 0x1e, 0xf8, 0xcb, 0x9c, 0x80, 0x7b, 0x8d, 0xad, 0x7d, 0xd9, 0x9e, 0xe0, 0x7f,
	0x3c, 0xd2, 0x14, 0xe1, 0x15, 0x31, 0x8f, 0x23, 0x3e, 0xce, 0xdf, 0x4a, 0xe1, 0xa7, 0x26, 0x7c,
	0x85, 0xd4, 0x00, 0xe3, 0xd2, 0x83, 0x69, 0x4b, 0xb7, 0xc1, 0x56, 0xee, 0xa6, 0x9d, 0x3e, 0x5f,
	0x50, 0x9a, 0x01, 0x4f, 0xd0, 0xc7, 0xc9, 0x94, 0x54, 0x7f, 0x32, 0xa2, 0xbf, 0x65, 0xcd, 0x0c,
	0x91, 0x29, 0x9e, 0x8f, 0x91, 0x45, 0xf5, 0x01, 0x57, 0xdd, 0xbc, 0xa7, 0xfe, 0x14, 0x99, 0x36,
	0x62, 0x83, 0xc4, 0x1d, 0xb5, 0x56, 0xe6, 0x25, 0x1a, 0x95, 0x1f, 0x98, 0x85, 0x01, 0xef, 0x75,
	0xfe, 0x5a, 0xc7, 0x24, 0x57, 0xbe, 0x3c, 0xe5, 0x7f, 0xda, 0xcb, 0x07, 0xc1, 0xdd, 0xd6, 0xa0,
	0x19, 0x66, 0x45, 0xd5, 0x32, 0x2b, 0xca, 0xb6, 0x3d, 0xbf, 0x62, 0x6f, 0x7b, 0xb2, 0x88, 0xe8,
	0x61, 0xfa, 0xa4, 0xe7, 0x8e, 0xca, 0xd3, 0xe7, 0x5b, 0x9e, 0xf9, 0xec, 0xcf, 0x2c, 0xa9, 0x76,
	0x53, 0x69, 0xef, 0xc1, 0x27, 0xa0, 0x3d, 0xe4, 0x7b, 0x20, 0x7e, 0x10, 0x26, 0x52, 0x65, 0x67,
	0x81, 0xbf, 0xea, 0x59, 0x57, 0xd6, 0x5c, 0xdd, 0x9b, 0x67, 0x81, 0x54, 0xe6, 0xb5, 0x19, 0x3f,
	0x7e, 0x1e, 0xc5, 0xc0, 0xc8, 0xd5, 0x88, 0xc5, 0xab, 0x32, 0x86, 0xb8, 0x16, 0xa8, 0x34, 0x5f,
	0xba, 0x8c, 0x60, 0x66, 0xb5, 0x74, 0x19, 0x61, 0xd6, 0x25, 0xcb, 0xa9, 0xff, 0x5f, 0x15, 0x75,
	0x5f, 0x55, 0x6a, 0xc2, 0x12, 0xdb, 0x2e, 0xbb, 0x41, 0xaa, 0x38, 0x36, 0x48, 0xf2, 0xe0, 0xa8,
	0xbd, 0x26, 0xe6, 0x9c, 0x4c, 0xaa, 0x9c, 0x6e, 0x2a, 0xb6, 0x87, 0x32, 0x69, 0x88, 0x43, 0x3d,
	0xeb, 0x3b, 0xe6, 0xce, 0x60, 0x6e, 0x94, 0xa2, 0xa5, 0xaf, 0x00, 0xee, 0x1b, 0x5a, 0xde, 0x1d,
	0xba, 0xa1, 0x65, 0x58, 0xc7, 0x24, 0x77, 0x40, 0x62, 0xd9, 0xef, 0xdc, 0x53, 0xef, 0xb6, 0xdf,
	0x0f, 0x60, 0x9e, 0xda, 0x73, 0xfc, 0xa5, 0x47, 0x0e, 0x72, 0x63, 0xdc, 0xe2, 0xbe, 0xbc, 0x91,
	0xe6, 0xd9, 0x37, 0xd2, 0x7c, 0x11, 0x8d, 0x9e, 0xe1, 0xbe, 0xf5, 0x44, 0xd1, 0x0f, 0x9b, 0xfb,
	0x8a, 0xaa, 0x89, 0x12, 0xaa, 0x26, 0x6d, 0xaa, 0x2e, 0x90, 0xa6, 0x9a, 0x83, 0x52, 0x19, 0xea,
	0x86, 0xbc, 0x92, 0xed, 0x4d, 0xc5, 0xda, 0xde, 0xf8, 0x1f, 0x94, 0xec, 0x31, 0x26, 0xc3, 0x0f,
	0xc6, 0x9e, 0x05, 0x1e, 0x8c, 0x80, 0xa8, 0x89, 0x3b, 0x34, 0x47, 0xb2, 0x6a, 0x83, 0xab, 0x51,
	0x95, 0x84, 0xfd, 0xdb, 0xa1, 0x9c, 0x9e, 0x35, 0xad, 0x0a, 0x6f, 0x7f, 0xab, 0xe2, 0xad, 0xe4,
	0x80, 0x59, 0x5b, 0xec, 0x49, 0xe4, 0xe2, 0x9e, 0x9f, 0xf3, 0x81, 0x55, 0x9c, 0xbe, 0x3d, 0xf7,
	0x30, 0x82, 0xd8, 0x72, 0x14, 0xdd, 0x72, 0xce, 0x16, 0x47, 0x22, 0xac, 0x88, 0xbe, 0x32, 0x22,
	0x32, 0x22, 0xf9, 0x7f, 0x86, 0x88, 0xbf, 0xf5, 0x44, 0xc8, 0x8e, 0x2d, 0x5e, 0xd6, 0xa0, 0x7a,
	0x37, 0x35, 0xa8, 0xf4, 0x71, 0x42, 0xf8, 0x06, 0x5e, 0xbd, 0xf6, 0x96, 0x21, 0xdf, 0x20, 0xc3,
	0x28, 0x49, 0x9f, 0x26, 0x4d, 0x4b, 0x16, 0x84, 0x10, 0x15, 0xaf, 0xc7, 0x76, 0x71, 0x5b, 0xa3,
	0xf1, 0x77, 0x56, 0x34, 0xc0, 0xdf, 0x21, 0x47, 0xad, 0xe2, 0xca, 0x4d, 0x53, 0x6e, 0x4e, 0x58,
	0x06, 0x42, 0xe5, 0xa6, 0x0d, 0x04, 0xe8, 0xce, 0x92, 0x89, 0x1f, 0xbc, 0xbb, 0x9c, 0x88, 0x99,
	0xdd, 0xbd, 0xe2, 0x15, 0x86, 0xbe, 0xdf, 0x6e, 0x24, 0x8d, 0x35, 0xe1, 0xab, 0xf9, 0x09, 0x5f,
	0xb6, 0x53, 0xfe, 0x94, 0xe7, 0x08, 0x86, 0xc9, 0x61, 0x66, 0xf9, 0x51, 0x4a, 0x82, 0xf3, 0x4b,
	0x56, 0x4d, 0x79, 0xef, 0xb8, 0x62, 0xdc, 0x3b, 0xbe, 0x55, 0x27, 0xca, 0xa5, 0x62, 0x3a, 0x7e,
	0xcd, 0xb3, 0xa2, 0x08, 0x8b, 0x51, 0xb4, 0xe2, 0x64, 0x96, 0xf0, 0x68, 0x31, 0x1c, 0x44, 0xe9,
	0xde, 0x6d, 0x4f, 0xa2, 0x79, 0x32, 0x6d, 0x34, 0x23, 0xe8, 0x33, 0x41, 0xfe, 0x7b, 0xc8, 0x9c,
	0x69, 0x37, 0x67, 0xfa, 0x74, 0xb9, 0xfa, 0x9f, 0xcc, 0xb6, 0x69, 0x6a, 0x88, 0x4c, 0x03, 0x76,
	0x5f, 0x2f, 0x90, 0xc3, 0x46, 0x52, 0xc9, 0xf2, 0x13, 0xf6, 0x9e, 0xf2, 0x64, 0x5e, 0xd9, 0x64,
	0x5b, 0xe5, 0xe5, 0xc1, 0xfc, 0x3b, 0x17, 0x4b, 0x47, 0x28, 0x7c, 0x82, 0x12, 0x2d, 0xba, 0x7e,
	0x91, 0x3b, 0xd2, 0xb3, 0x9f, 0xc8, 0xaa, 0x5b, 0x8f, 0x47, 0xa5, 0xa6, 0xd7, 0x39, 0xcd, 0x3f,
	0x1e, 0x55, 0xcb, 0x3e, 0x1e, 0x55, 0x26, 0xc6, 0x9f, 0x76, 0x1d, 0x97, 0xe7, 0xf0, 0xd3, 0x63,
	0xff, 0xef, 0x1e, 0x7f, 0x5e, 0x0b, 0xcf, 0xb8, 0xd6, 0xd4, 0x19, 0xd7, 0x1a, 0xbd, 0x97, 0x54,
	0xba, 0xa9, 0x50, 0x85, 0x99, 0x47, 0xb7, 0x2a, 0xdd, 0x94, 0x3e, 0xaa, 0x5e, 0x09, 0xa8, 0xda,
	0x27, 0x3a, 0x6b, 0xdd, 0x94, 0xab, 0x99, 0x44, 0xbe, 0x9b, 0xc3, 0x1f, 0x0b, 0xc8, 0x6c, 0x34,
	0x6a, 0xd6, 0xe1, 0x76, 0xf9, 0x46, 0x63, 0xae, 0x27, 0x4e, 0x1b, 0x0b, 0x5f, 0x50, 0x39, 0x63,
	0xbf, 0x76, 0x52, 0xac, 0xee, 0x8c, 0x37, 0x1c, 0x3e, 0x53, 0x21, 0xb3, 0xd9, 0x57, 0x13, 0x61,
	0xda, 0x32, 0x4c, 0xf4, 0xc5, 0x6d, 0x42, 0x99, 0x04, 0x25, 0xc8, 0x8c, 0xe8, 0x01, 0xef, 0x74,
	0x3d, 0xd0, 0x00, 0x90, 0xdd, 0xd1, 0x58, 0x6d, 0x04, 0xf0, 0x9b, 0xde, 0x4b, 0xaa, 0xe3, 0x54,
	0x7a, 0x70, 0xa6, 0x0d, 0xfe, 0x04, 0x00, 0x87, 0x06, 0xd7, 0x77, 0xe3, 0x18, 0xc6, 0x85, 0x07,
	0x33, 0xd6, 0x03, 0x0d, 0x00, 0x0d, 0x38, 0x8e, 0x19, 0xcf, 0xe4, 0xd7, 0x20, 0x55, 0x1a, 0xe8,
	0x4f, 0xe2, 0x75, 0xb1, 0xe9, 0x82, 0x4f, 0xe8, 0xbe, 0xcf, 0x92, 0x54, 0x58, 0xb2, 0xf8, 0x4d,
	0x4f, 0x91, 0xe6, 0xfa, 0x16, 0x5b, 0xdf, 0x5e, 0x1a, 0x0d, 0x37, 0x06, 0xd1, 0x7a, 0x2a, 0xcc,
	0x58, 0x1b, 0x08, 0x93, 0x36, 0x54, 0x2f, 0x7e, 0xf5, 0xd1, 0x98, 0xad, 0x05, 0x26, 0xc8, 0xff,
	0x39, 0xcf, 0x75, 0x91, 0x88, 0xbe, 0x51, 0xf0, 0xc3, 0x38, 0x7d, 0x2a, 0x7c, 0x8b, 0x52, 0x97,
	0x2c, 0x3b, 0xe3, 0xf8, 0x8c, 0x7d, 0xc6, 0x91, 0xef, 0x53, 0x4b, 0x2d, 0xe0, 0x94, 0xbf, 0xc4,
	0x74, 0x07, 0x70, 0xfa, 0xac, 0x8d, 0x53, 0xbe, 0x4f, 0xcb, 0x13, 0xe8, 0xba, 0x40, 0x75, 0xab,
	0x13, 0xeb, 0x38, 0x99, 0x42, 0x03, 0x03, 0x1f, 0x28, 0xe5, 0xe2, 0xa4, 0x01, 0xd6, 0x23, 0x74,
	0x9e, 0x7e, 0x6a, 0xaf, 0xcc, 0xb5, 0xf2, 0xeb, 0x2e, 0xd7, 0x8a, 0x85, 0xa2, 0xa6, 0x21, 0x75,
	0x5d, 0xf5, 0xb2, 0x27, 0x45, 0xc5, 0x98, 0x14, 0x65, 0x9c, 0xfb, 0x0d, 0x9b, 0x73, 0xf9, 0x66,
	0x75, 0xaf, 0xff, 0xea, 0xed, 0x73, 0x93, 0xac, 0xf0, 0x01, 0x98, 0x9b, 0x38, 0xf5, 0x74, 0x1f,
	0x67, 0x97, 0x85, 0x90, 0x51, 0x52, 0x1b, 0x1a, 0xde, 0x58, 0xf8, 0x5e, 0x58, 0x29, 0x26, 0xf4,
	0x37, 0x39, 0xa1, 0xa7, 0xec, 0x48, 0x25, 0x37, 0x21, 0x9a, 0xe6, 0xaf, 0x7b, 0xa5, 0x57, 0xe3,
	0xf6, 0xb3, 0x80, 0x62, 0xcb, 0x77, 0xc7, 0x53, 0x30, 0x4e, 0xfd, 0x78, 0x34, 0x3e, 0x3b, 0x18,
	0x08, 0xbf, 0x93, 0x4c, 0x96, 0x05, 0x85, 0x7f, 0x8e, 0xa3, 0xef, 0x9b, 0x57, 0x3f, 0xf6, 0x43,
	0xfe, 0x3d, 0x65, 0xb7, 0xf6, 0xca, 0x8c, 0x93, 0xdf, 0xb2, 0x8d, 0x93, 0xe2, 0x46, 0x74, 0x5f,
	0x1f, 0xf3, 0x0a, 0xae, 0x00, 0x1a, 0x46, 0x93, 0x67, 0x19, 0x4d, 0x27, 0x08, 0x89, 0xf5, 0xad,
	0x1f, 0xfe, 0x76, 0x8f, 0x01, 0x29, 0x8b, 0x9c, 0xfa, 0x6d, 0xcf, 0x15, 0x75, 0x66, 0xf7, 0xab,
	0x51, 0xfb, 0x0b, 0xef, 0x26, 0xaf, 0x20, 0x16, 0xa2, 0x5a, 0xe4, 0x85, 0x15, 0x16, 0x37, 0x2c,
	0x2d, 0x7c, 0x81, 0xad, 0x06, 0x1a, 0xb0, 0x70, 0xb5, 0x98, 0x80, 0xcf, 0x73, 0x02, 0x5e, 0xa7,
	0x19, 0xbc, 0x3f, 0x76, 0x9a, 0xa0, 0x4f, 0x7b, 0xfb, 0x5f, 0x94, 0xbc, 0xb5, 0x03, 0xf4, 0xb2,
	0x70, 0x9a, 0xdf, 0xb1, 0xc3, 0x69, 0xf6, 0xeb, 0xd8, 0xd4, 0x52, 0xae, 0x8b, 0x9a, 0xc0, 0x4c,
	0x86, 0x17, 0xb2, 0xc4, 0x51, 0xbb, 0x48, 0x95, 0xe9, 0xc6, 0xdf, 0xb5, 0x75, 0xa3, 0xa3, 0xd5,
	0x5c, 0xaf, 0x99, 0x5b, 0xa0, 0xb7, 0xd3, 0xeb, 0xef, 0xe5, 0x7b, 0xcd, 0xb4, 0xaa, 0x7b, 0xfd,
	0x59, 0xcf, 0x79, 0xc7, 0x94, 0x3e, 0x66, 0xbe, 0x16, 0x22, 0x86, 0xc2, 0xf1, 0xc0, 0x85, 0x51,
	0xa8, 0x0c, 0xa3, 0x2f, 0xd8, 0x18, 0x39, 0x3a, 0xd4, 0x18, 0x0d, 0x1c, 0x77, 0x5b, 0x9d, 0x61,
	0x6b, 0x25, 0xb1, 0x0d, 0x5f, 0xb4, 0x63, 0x1b, 0x72, 0xed, 0xe9, 0xde, 0x5e, 0xf1, 0xf6, 0xbb,
	0x33, 0x7b, 0xcb, 0x93, 0xcb, 0x78, 0x06, 0xa6, 0x6a, 0x3d, 0x03, 0xb3, 0xd0, 0x2d, 0xc6, 0xf8,
	0xf7, 0x39, 0xc6, 0xf7, 0x17, 0x4e, 0x2c, 0x13, 0x25, 0x8d, 0xfe, 0x8d, 0x82, 0xdb, 0xbc, 0x45,
	0x0f, 0x1d, 0x95, 0x29, 0xa7, 0x2f, 0xd9, 0xca, 0xc9, 0xd9, 0xae, 0xee, 0xf9, 0x5d, 0xce, 0xcb,
	0xc2, 0x65, 0x42, 0xf0, 0x65, 0x5b, 0x08, 0x1c, 0xb5, 0x75, 0xeb, 0x1f, 0xf0, 0x8a, 0xae, 0x1c,
	0xe7, 0xec, 0x9d, 0x19, 0x65, 0xef, 0x34, 0xc1, 0xc0, 0x29, 0xf3, 0xb3, 0xfc, 0x81, 0xed, 0x67,
	0x71, 0x77, 0xa0, 0x91, 0xf8, 0x84, 0x57, 0x76, 0x81, 0xf9, 0x56, 0xe5, 0xa2, 0x6c, 0xdd, 0xfa,
	0x4a, 0x6e, 0xdd, 0x2a, 0xe8, 0x54, 0x23, 0xb7, 0x4d, 0x0e, 0xe5, 0x76, 0x35, 0xce, 0x2d, 0x6e,
	0xfe, 0x76, 0x29, 0xbf, 0x63, 0xe0, 0x78, 0x10, 0x56, 0x2c, 0x62, 0x89, 0x08, 0x56, 0x51, 0x69,
	0xff, 0x8a, 0xf5, 0x78, 0x12, 0x7f, 0xed, 0x68, 0x31, 0x0f, 0x13, 0x9b, 0xde, 0xa2, 0x13, 0xb6,
	0x5c, 0x79, 0x18, 0xe6, 0xd2, 0x2b, 0xe0, 0x56, 0x9c, 0xb5, 0x78, 0x5c, 0xb9, 0xcc, 0x13, 0xf8,
	0x55, 0xdb, 0x13, 0x58, 0xd6, 0xb4, 0xe6, 0xe4, 0x97, 0xbc, 0xf2, 0x5b, 0xe6, 0xb7, 0x7c, 0x79,
	0x50, 0x3d, 0xc9, 0x57, 0x35, 0x9e, 0xe4, 0x2b, 0x43, 0xfb, 0x6b, 0x9e, 0xe3, 0xde, 0xa8, 0x1b,
	0x19, 0x8d, 0xf6, 0x4b, 0xc5, 0x37, 0xdf, 0x9d, 0x6c, 0x2b, 0x89, 0x4a, 0xfc, 0xba, 0x1d, 0x95,
	0x58, 0xd4, 0xac, 0x35, 0x33, 0x4a, 0x2f, 0xd6, 0xd3, 0x87, 0x48, 0x63, 0xe9, 0x59, 0xdc, 0x4d,
	0xca, 0x93, 0x10, 0xd5, 0x27, 0x07, 0x07, 0x2a, 0xbf, 0x8c, 0x31, 0x7f, 0x98, 0x61, 0x4c, 0x49,
	0x97, 0x1a, 0xb9, 0xb7, 0x91, 0x49, 0xd1, 0xb6, 0x73, 0x3e, 0x64, 0x9e, 0x46, 0xe4, 0x4e, 0x00,
	0xeb, 0x69, 0xc4, 0x0f, 0x79, 0xfb, 0x3d, 0x0a, 0xe0, 0x64, 0x70, 0x89, 0x76, 0x7f, 0x25, 0xa7,
	0xdd, 0x4b, 0x1a, 0xb7, 0x15, 0x50, 0xf1, 0xcb, 0x03, 0xb7, 0x7a, 0x77, 0xa5, 0x4c, 0x01, 0x7d,
	0xc3, 0xcb, 0xdd, 0x0d, 0xde, 0x4f, 0xfe, 0x06, 0xa5, 0xaf, 0x1e, 0x94, 0x6d, 0x09, 0xbe, 0x69,
	0x6f, 0x09, 0x4a, 0x5a, 0xd1, 0xbd, 0x7d, 0xca, 0xdb, 0xe7, 0x0d, 0x05, 0x50, 0xbb, 0x09, 0xdf,
	0xba, 0x82, 0xc0, 0xd5, 0x02, 0x91, 0x82, 0xe5, 0x98, 0xfb, 0x4d, 0xf9, 0xe9, 0x71, 0x2d, 0x90,
	0xc9, 0xb2, 0x4d, 0xd7, 0x1f, 0xd9, 0x9b, 0xae, 0xd2, 0x9e, 0xcd, 0x2b, 0x67, 0xf9, 0x47, 0x1c,
	0xcc, 0xfe, 0x3d, 0xbb, 0xff, 0x12, 0x03, 0xe6, 0x8f, 0xb3, 0xc1, 0x99, 0x99, 0x56, 0x75, 0x9f,
	0x7f, 0xe7, 0x15, 0x3f, 0x11, 0x01, 0xd2, 0xd0, 0xcf, 0x68, 0x2e, 0x99, 0x16, 0xdb, 0x18, 0x7e,
	0x72, 0xdd, 0x17, 0xeb, 0xa7, 0x01, 0x81, 0xba, 0x3b, 0xfc, 0x0f, 0x05, 0xfa, 0xe2, 0x69, 0x03,
	0x95, 0xd6, 0x7f, 0x30, 0x50, 0x2b, 0xfa, 0x83, 0x81, 0x32, 0x75, 0xf3, 0xaa, 0xad, 0x6e, 0x8a,
	0xb0, 0xb7, 0x3c, 0xe9, 0xe6, 0xc3, 0xd1, 0xe8, 0xc2, 0xe3, 0x7f, 0x53, 0xe1, 0xf1, 0x7d, 0xa8,
	0xfc, 0x7b, 0x8a, 0x13, 0x84, 0x2c, 0xee, 0xae, 0x6f, 0xb3, 0x54, 0xe8, 0x64, 0x7c, 0x93, 0x4b,
	0x43, 0xf0, 0x7e, 0xd0, 0xb6, 0xb8, 0xd1, 0x5d, 0x39, 0xbb, 0x0d, 0xe9, 0xde, 0xb6, 0x7c, 0x80,
	0xbe, 0xb7, 0x0d, 0x34, 0x9f, 0x1b, 0xf6, 0xc7, 0xa3, 0x68, 0x98, 0x8a, 0x00, 0x62, 0x95, 0x86,
	0xbc, 0xc5, 0x30, 0x61, 0xdd, 0x30, 0xdd, 0xc2, 0x13, 0xb3, 0xa9, 0x40, 0xa5, 0xfd, 0x8f, 0x57,
	0x88, 0x19, 0x27, 0xbe, 0x84, 0xef, 0xd7, 0xf7, 0xd8, 0x30, 0x89, 0xd2, 0xe8, 0x1a, 0x13, 0x58,
	0x66, 0xc1, 0x80, 0xed, 0xd9, 0xf1, 0x98, 0x0d, 0xfb, 0xa0, 0x6c, 0x11, 0xdb, 0x46, 0x60, 0x40,
	0x60, 0xe5, 0xbe, 0x1a, 0x47, 0x29, 0x5b, 0xdd, 0x8a, 0x59, 0xb2, 0x35, 0x1a, 0xf4, 0xc5, 0xba,
	0x9c, 0x81, 0xd2, 0x53, 0xa4, 0x19, 0xb0, 0xb0, 0xaf, 0x8b, 0xd5, 0xb0, 0x98, 0x0d, 0xc4, 0x7f,
	0x04, 0x48, 0x47, 0x71, 0xb8, 0xc9, 0x96, 0xc2, 0x71, 0xb8, 0x1e, 0xa5, 0x7b, 0xe2, 0x54, 0x30,
	0x0b, 0x56, 0x41, 0xc7, 0x4b, 0x5b, 0x61, 0x2c, 0x48, 0xd5, 0x00, 0x7c, 0xc0, 0x39, 0x95, 0xbe,
	0x6f, 0xf8, 0xc4, 0x3b, 0xd7, 0xe1, 0x66, 0x82, 0x45, 0xc4, 0x75, 0x2c, 0x0d, 0xf0, 0xbf, 0xe5,
	0x15, 0x3f, 0x1a, 0xe2, 0x32, 0xe6, 0x82, 0xb1, 0x50, 0x5c, 0x95, 0x60, 0x8c, 0xcf, 0xaa, 0x26,
	0xa9, 0x7a, 0x68, 0x35, 0x49, 0xcd, 0x80, 0xfd, 0x9a, 0xf5, 0xa7, 0x11, 0xb9, 0x17, 0x30, 0x4a,
	0x24, 0xf0, 0x5b, 0x2e, 0x09, 0x2c, 0x0b, 0xb9, 0xf9, 0x45, 0x8f, 0x4c, 0x82, 0x1e, 0x5d, 0x19,
	0x63, 0xb4, 0xe6, 0xca, 0x58, 0x84, 0xd8, 0x55, 0x56, 0xc6, 0x20, 0x18, 0x43, 0x76, 0x5d, 0xba,
	0xf6, 0xf0, 0x45, 0x00, 0x99, 0xce, 0xff, 0x43, 0x0b, 0x7f, 0xfe, 0x2d, 0xf3, 0x0f, 0x2d, 0x27,
	0x08, 0xb9, 0xc0, 0xd2, 0x95, 0x31, 0x3f, 0x8e, 0xe5, 0xa3, 0x67, 0x40, 0xd4, 0xc5, 0xd5, 0xba,
	0x7d, 0xd4, 0xab, 0x2e, 0xae, 0xc2, 0x42, 0xe1, 0x7c, 0xea, 0xa5, 0xf4, 0x76, 0x94, 0xed, 0x05,
	0x10, 0x93, 0xc5, 0xf0, 0x02, 0x94, 0x84, 0x99, 0x7c, 0xdb, 0x0e, 0x33, 0x71, 0x75, 0xed, 0xf4,
	0x64, 0x39, 0x5e, 0x9b, 0xf9, 0x21, 0xbb, 0x32, 0xb2, 0x44, 0x94, 0xac, 0x79, 0xdf, 0x71, 0x7a,
	0xb2, 0x1c, 0x28, 0x6a, 0x52, 0x3e, 0xe7, 0x95, 0xbc, 0xb8, 0xa3, 0x6e, 0x24, 0xf2, 0xb7, 0xcd,
	0xf9, 0x8d, 0x44, 0xf7, 0x5f, 0x7c, 0xe9, 0xdb, 0x0d, 0x55, 0xf3, 0x76, 0x43, 0xd9, 0x4d, 0xac,
	0xef, 0xda, 0x37, 0xb1, 0x0a, 0xb1, 0xd0, 0xc8, 0xfe, 0x79, 0x85, 0x34, 0xce, 0x47, 0xfc, 0x8c,
	0x03, 0x04, 0x21, 0x61, 0x2f, 0xee, 0xb2, 0xe1, 0x3a, 0x13, 0x8e, 0x0d, 0x95, 0x06, 0x1c, 0x07,
	0x18, 0xcf, 0x22, 0xde, 0xa1, 0xc6, 0x04, 0x40, 0x77, 0x58, 0xbc, 0xc9, 0x84, 0xf2, 0xe7, 0x09,
	0x3c, 0x8e, 0xb8, 0x91, 0xb2, 0x61, 0x2a, 0x0f, 0x88, 0x79, 0x0a, 0x4b, 0xe3, 0x1f, 0xfd, 0xd4,
	0xf9, 0x9d, 0x3d, 0x4c, 0x80, 0xa6, 0x4e, 0x84, 0x97, 0x72, 0x02, 0xe1, 0x32, 0x09, 0x3a, 0xa3,
	0xaf, 0x62, 0xc9, 0xb9, 0x2e, 0xd1, 0x00, 0xf4, 0x5d, 0xa0, 0x4c, 0x41, 0x2e, 0xff, 0x77, 0x0a,
	0x0d, 0x80, 0x56, 0x77, 0x22, 0x6e, 0xbd, 0xf1, 0x47, 0x2f, 0x64, 0x12, 0x73, 0x44, 0x34, 0x37,
	0x11, 0x39, 0x3c, 0x89, 0xbb, 0x9b, 0xd1, 0x75, 0x1e, 0x06, 0xce, 0x43, 0x66, 0x54, 0x1a, 0x26,
	0xe9, 0x46, 0x34, 0x60, 0xbd, 0xe8, 0x25, 0xb6, 0xb8, 0x07, 0x16, 0x2b, 0x8f, 0x9b, 0xb1, 0x81,
	0xfe, 0x87, 0x3d, 0xd7, 0xa3, 0x48, 0xf4, 0x11, 0x32, 0x25, 0x99, 0x2c, 0x4d, 0xdd, 0x83, 0xea,
	0xaa, 0xc2, 0x40, 0x38, 0x31, 0x55, 0x89, 0xb2, 0x13, 0xed, 0x3f, 0xb1, 0x4f, 0xb4, 0xf3, 0x7d,
	0x59, 0x37, 0x6a, 0xca, 0x9e, 0x5a, 0xba, 0xc3, 0x73, 0xaa, 0xc4, 0xb4, 0xfb, 0x53, 0xdb, 0xb4,
	0x2b, 0xc1, 0x51, 0x13, 0xf3, 0x21, 0xcf, 0xf5, 0x2c, 0x14, 0xaa, 0x55, 0x10, 0x6f, 0x19, 0x87,
	0x36, 0x15, 0xa8, 0x74, 0xf6, 0xa5, 0xda, 0x32, 0xae, 0x7e, 0x2f, 0x73, 0x41, 0x33, 0xd7, 0x91,
	0x75, 0x16, 0x36, 0x89, 0x7f, 0x61, 0x35, 0xba, 0x0e, 0x12, 0x98, 0xaa, 0x77, 0x44, 0x44, 0x10,
	0x91, 0x02, 0x18, 0xb6, 0xa6, 0xd8, 0xe2, 0x0b, 0x5b, 0x73, 0x8e, 0x34, 0xb6, 0x46, 0xd6, 0xd9,
	0x8f, 0x4a, 0xab, 0x68, 0xbe, 0xb6, 0x78, 0x78, 0x44, 0xa4, 0x2c, 0x3a, 0xeb, 0x36, 0x9d, 0xfe,
	0x5f, 0x7b, 0xa4, 0x81, 0x3e, 0x0d, 0x40, 0x49, 0xfa, 0x00, 0xc5, 0x3f, 0x08, 0xa2, 0x0f, 0x30,
	0xe3, 0x35, 0xc4, 0x90, 0x35, 0xed, 0x35, 0x9c, 0x21, 0x95, 0xbe, 0x8c, 0xcc, 0xaa, 0xf4, 0xd7,
	0xa0, 0x85, 0x71, 0xda, 0xe9, 0x8b, 0x88, 0x2c, 0xfc, 0x86, 0x16, 0x92, 0x78, 0x5d, 0x68, 0x23,
	0x1e, 0xba, 0xa9, 0x01, 0x38, 0x4d, 0x93, 0x54, 0xe4, 0xf2, 0x27, 0xcb, 0x35, 0xc0, 0x76, 0x31,
	0xf2, 0xff, 0x20, 0x2a, 0x70, 0x31, 0x36, 0x38, 0x61, 0x32, 0xed, 0xbf, 0x40, 0x0e, 0x1a, 0x23,
	0x21, 0xff, 0x0b, 0x6a, 0x88, 0xff, 0x2b, 0x66, 0xef, 0x17, 0xc5, 0x80, 0x04, 0x3c, 0x93, 0x3e,
	0x48, 0x26, 0x18, 0xff, 0x7f, 0xba, 0x8a, 0x35, 0xd7, 0x24, 0x97, 0x02, 0x91, 0x8d, 0xd1, 0x97,
	0xae, 0x47, 0xc5, 0xee, 0x64, 0xf4, 0xe5, 0x9f, 0xd9, 0xcb, 0xa2, 0xab, 0x7b, 0xd7, 0xb2, 0xe8,
	0x7a, 0xdb, 0xac, 0x24, 0xf2, 0xec, 0x0e, 0x07, 0x78, 0x7c, 0xdf, 0xb5, 0x2c, 0xba, 0x50, 0x54,
	0xa4, 0x2c, 0x92, 0x77, 0x36, 0xce, 0x9c, 0x79, 0x14, 0xcb, 0xff, 0x6f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x17, 0x8d, 0x90, 0x3b, 0x04, 0x73, 0x00, 0x00,
}
