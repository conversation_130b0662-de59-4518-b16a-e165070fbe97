// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: internal/internal.proto

package influxql

import (
	fmt "fmt"
	proto "github.com/openGemini/openGemini/lib/util/lifted/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Measurements struct {
	Items                []*Measurement `protobuf:"bytes,1,rep,name=Items" json:"Items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *Measurements) Reset()         { *m = Measurements{} }
func (m *Measurements) String() string { return proto.CompactTextString(m) }
func (*Measurements) ProtoMessage()    {}
func (*Measurements) Descriptor() ([]byte, []int) {
	return fileDescriptor_41ca0a4a9dd77d9e, []int{0}
}
func (m *Measurements) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Measurements.Unmarshal(m, b)
}
func (m *Measurements) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Measurements.Marshal(b, m, deterministic)
}
func (m *Measurements) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Measurements.Merge(m, src)
}
func (m *Measurements) XXX_Size() int {
	return xxx_messageInfo_Measurements.Size(m)
}
func (m *Measurements) XXX_DiscardUnknown() {
	xxx_messageInfo_Measurements.DiscardUnknown(m)
}

var xxx_messageInfo_Measurements proto.InternalMessageInfo

func (m *Measurements) GetItems() []*Measurement {
	if m != nil {
		return m.Items
	}
	return nil
}

type Measurement struct {
	Database             *string  `protobuf:"bytes,1,opt,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,2,opt,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	Name                 *string  `protobuf:"bytes,3,opt,name=Name" json:"Name,omitempty"`
	Regex                *string  `protobuf:"bytes,4,opt,name=Regex" json:"Regex,omitempty"`
	IsTarget             *bool    `protobuf:"varint,5,opt,name=IsTarget" json:"IsTarget,omitempty"`
	EngineType           *uint32  `protobuf:"varint,6,opt,name=EngineType" json:"EngineType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Measurement) Reset()         { *m = Measurement{} }
func (m *Measurement) String() string { return proto.CompactTextString(m) }
func (*Measurement) ProtoMessage()    {}
func (*Measurement) Descriptor() ([]byte, []int) {
	return fileDescriptor_41ca0a4a9dd77d9e, []int{1}
}
func (m *Measurement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Measurement.Unmarshal(m, b)
}
func (m *Measurement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Measurement.Marshal(b, m, deterministic)
}
func (m *Measurement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Measurement.Merge(m, src)
}
func (m *Measurement) XXX_Size() int {
	return xxx_messageInfo_Measurement.Size(m)
}
func (m *Measurement) XXX_DiscardUnknown() {
	xxx_messageInfo_Measurement.DiscardUnknown(m)
}

var xxx_messageInfo_Measurement proto.InternalMessageInfo

func (m *Measurement) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *Measurement) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

func (m *Measurement) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *Measurement) GetRegex() string {
	if m != nil && m.Regex != nil {
		return *m.Regex
	}
	return ""
}

func (m *Measurement) GetIsTarget() bool {
	if m != nil && m.IsTarget != nil {
		return *m.IsTarget
	}
	return false
}

func (m *Measurement) GetEngineType() uint32 {
	if m != nil && m.EngineType != nil {
		return *m.EngineType
	}
	return 0
}

func init() {
	proto.RegisterType((*Measurements)(nil), "influxql.Measurements")
	proto.RegisterType((*Measurement)(nil), "influxql.Measurement")
}

func init() { proto.RegisterFile("internal/internal.proto", fileDescriptor_41ca0a4a9dd77d9e) }

var fileDescriptor_41ca0a4a9dd77d9e = []byte{
	// 212 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x8d, 0xc1, 0x4a, 0x03, 0x31,
	0x10, 0x86, 0x89, 0xed, 0xca, 0x3a, 0x55, 0x84, 0x41, 0x31, 0x78, 0x90, 0xd0, 0x53, 0x40, 0x58,
	0xc1, 0xab, 0x57, 0x3d, 0xf4, 0xa0, 0x48, 0xe8, 0x0b, 0x44, 0x19, 0x97, 0x40, 0x76, 0x52, 0x93,
	0x29, 0xb4, 0x8f, 0xe6, 0xdb, 0x89, 0x29, 0x5d, 0x16, 0x6f, 0xff, 0xf7, 0xcd, 0x07, 0x03, 0x37,
	0x81, 0x85, 0x32, 0xfb, 0xf8, 0x70, 0x1c, 0xdd, 0x26, 0x27, 0x49, 0xd8, 0x06, 0xfe, 0x8a, 0xdb,
	0xdd, 0x77, 0x5c, 0x3e, 0xc1, 0xf9, 0x2b, 0xf9, 0xb2, 0xcd, 0x34, 0x10, 0x4b, 0xc1, 0x7b, 0x68,
	0x56, 0x42, 0x43, 0xd1, 0xca, 0xcc, 0xec, 0xe2, 0xf1, 0xba, 0x3b, 0x96, 0xdd, 0x24, 0x73, 0x87,
	0x66, 0xf9, 0xa3, 0x60, 0x31, 0xd1, 0x78, 0x0b, 0xed, 0xb3, 0x17, 0xff, 0xe1, 0x0b, 0x69, 0x65,
	0x94, 0x3d, 0x73, 0x23, 0xa3, 0x85, 0x4b, 0x47, 0x42, 0x2c, 0x21, 0xf1, 0x7b, 0x8a, 0xe1, 0x73,
	0xaf, 0x4f, 0x6a, 0xf2, 0x5f, 0x23, 0xc2, 0xfc, 0xcd, 0x0f, 0xa4, 0x67, 0xf5, 0x5c, 0x37, 0x5e,
	0x41, 0xe3, 0xa8, 0xa7, 0x9d, 0x9e, 0x57, 0x79, 0x80, 0xbf, 0x7f, 0xab, 0xb2, 0xf6, 0xb9, 0x27,
	0xd1, 0x8d, 0x51, 0xb6, 0x75, 0x23, 0xe3, 0x1d, 0xc0, 0x0b, 0xf7, 0x81, 0x69, 0xbd, 0xdf, 0x90,
	0x3e, 0x35, 0xca, 0x5e, 0xb8, 0x89, 0xf9, 0x0d, 0x00, 0x00, 0xff, 0xff, 0x7a, 0xb0, 0xe8, 0x6f,
	0x1c, 0x01, 0x00, 0x00,
}
