// Code generated by goyacc sql.y. DO NOT EDIT.

//line sql.y:2
// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package influxql

import __yyfmt__ "fmt"

//line sql.y:16

import (
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/openGemini/openGemini/lib/util/lifted/vm/protoparser/influx"
)

const DefaultQueryTimeout = time.Duration(0)

func setParseTree(yylex interface{}, stmts Statements) {
	for _, stmt := range stmts {
		yylex.(*YyParser).Query.Statements = append(yylex.(*YyParser).Query.Statements, stmt)
	}
}

func deal_Fill(fill interface{}) (FillOption, interface{}, bool) {
	switch fill.(type) {
	case string:
		switch fill {
		case "null":
			return 0, nil, true
		case "none":
			return 1, nil, true
		case "previous":
			return 3, nil, true
		case "linear":
			return 4, nil, true
		default:
			return -1, nil, false
		}
	case int64:
		return 2, fill.(int64), true
	case float64:
		return 2, fill.(float64), true
	default:
		return -1, nil, false
	}
}

//line sql.y:63
type yySymType struct {
	yys              int
	stmt             Statement
	stmts            Statements
	str              string
	query            Query
	field            *Field
	fields           Fields
	sources          Sources
	source           Source
	sortfs           SortFields
	sortf            *SortField
	ment             *Measurement
	subQuery         *SubQuery
	dimens           Dimensions
	dimen            *Dimension
	int              int
	int64            int64
	float64          float64
	dataType         DataType
	expr             Expr
	tdur             time.Duration
	tdurs            []time.Duration
	bool             bool
	groupByCondition *GroupByCondition
	intSlice         []int
	inter            interface{}
	durations        *Durations
	hints            Hints
	strSlice         []string
	strSlices        [][]string
	location         *time.Location
	indexType        *IndexType
	cqsp             *cqSamplePolicyInfo
	fieldOption      *fieldList
	fieldOptions     []*fieldList
	indexOptions     []*IndexOption
	indexOption      *IndexOption
	databasePolicy   DatabasePolicy
	cmOption         *CreateMeasurementStatementOption
	cte              *CTE
	ctes             CTES
}

const FROM = 57346
const MEASUREMENT = 57347
const INTO = 57348
const ON = 57349
const SELECT = 57350
const WHERE = 57351
const AS = 57352
const GROUP = 57353
const BY = 57354
const ORDER = 57355
const LIMIT = 57356
const OFFSET = 57357
const SLIMIT = 57358
const SOFFSET = 57359
const SHOW = 57360
const CREATE = 57361
const FULL = 57362
const PRIVILEGES = 57363
const OUTER = 57364
const JOIN = 57365
const TO = 57366
const IN = 57367
const NOT = 57368
const EXISTS = 57369
const REVOKE = 57370
const FILL = 57371
const DELETE = 57372
const WITH = 57373
const ENGINETYPE = 57374
const COLUMNSTORE = 57375
const TSSTORE = 57376
const ALL = 57377
const ANY = 57378
const PASSWORD = 57379
const NAME = 57380
const REPLICANUM = 57381
const ALTER = 57382
const USER = 57383
const USERS = 57384
const DATABASES = 57385
const DATABASE = 57386
const MEASUREMENTS = 57387
const RETENTION = 57388
const POLICIES = 57389
const POLICY = 57390
const DURATION = 57391
const DEFAULT = 57392
const SHARD = 57393
const INDEX = 57394
const GRANT = 57395
const HOT = 57396
const WARM = 57397
const TYPE = 57398
const SET = 57399
const FOR = 57400
const GRANTS = 57401
const REPLICATION = 57402
const SERIES = 57403
const DROP = 57404
const CASE = 57405
const WHEN = 57406
const THEN = 57407
const ELSE = 57408
const BEGIN = 57409
const END = 57410
const TRUE = 57411
const FALSE = 57412
const TAG = 57413
const ATTRIBUTE = 57414
const FIELD = 57415
const KEYS = 57416
const VALUES = 57417
const KEY = 57418
const EXPLAIN = 57419
const ANALYZE = 57420
const EXACT = 57421
const CARDINALITY = 57422
const SHARDKEY = 57423
const PRIMARYKEY = 57424
const SORTKEY = 57425
const PROPERTY = 57426
const COMPACT = 57427
const CONTINUOUS = 57428
const DIAGNOSTICS = 57429
const QUERIES = 57430
const QUERIE = 57431
const SHARDS = 57432
const STATS = 57433
const SUBSCRIPTIONS = 57434
const SUBSCRIPTION = 57435
const GROUPS = 57436
const INDEXTYPE = 57437
const INDEXLIST = 57438
const SEGMENT = 57439
const KILL = 57440
const EVERY = 57441
const RESAMPLE = 57442
const DOWNSAMPLE = 57443
const DOWNSAMPLES = 57444
const SAMPLEINTERVAL = 57445
const TIMEINTERVAL = 57446
const STREAM = 57447
const DELAY = 57448
const STREAMS = 57449
const QUERY = 57450
const PARTITION = 57451
const TOKEN = 57452
const TOKENIZERS = 57453
const MATCH = 57454
const LIKE = 57455
const MATCHPHRASE = 57456
const CONFIG = 57457
const CONFIGS = 57458
const CLUSTER = 57459
const IPINRANGE = 57460
const REPLICAS = 57461
const DETAIL = 57462
const DESTINATIONS = 57463
const SCHEMA = 57464
const INDEXES = 57465
const AUTO = 57466
const EXCEPT = 57467
const DESC = 57468
const ASC = 57469
const COMMA = 57470
const SEMICOLON = 57471
const LPAREN = 57472
const RPAREN = 57473
const REGEX = 57474
const EQ = 57475
const NEQ = 57476
const LT = 57477
const LTE = 57478
const GT = 57479
const GTE = 57480
const DOT = 57481
const DOUBLECOLON = 57482
const NEQREGEX = 57483
const EQREGEX = 57484
const IDENT = 57485
const INTEGER = 57486
const DURATIONVAL = 57487
const STRING = 57488
const NUMBER = 57489
const HINT = 57490
const BOUNDPARAM = 57491
const AND = 57492
const OR = 57493
const ADD = 57494
const SUB = 57495
const BITWISE_OR = 57496
const BITWISE_XOR = 57497
const MUL = 57498
const DIV = 57499
const MOD = 57500
const BITWISE_AND = 57501
const UMINUS = 57502

var yyToknames = [...]string{
	"$end",
	"error",
	"$unk",
	"FROM",
	"MEASUREMENT",
	"INTO",
	"ON",
	"SELECT",
	"WHERE",
	"AS",
	"GROUP",
	"BY",
	"ORDER",
	"LIMIT",
	"OFFSET",
	"SLIMIT",
	"SOFFSET",
	"SHOW",
	"CREATE",
	"FULL",
	"PRIVILEGES",
	"OUTER",
	"JOIN",
	"TO",
	"IN",
	"NOT",
	"EXISTS",
	"REVOKE",
	"FILL",
	"DELETE",
	"WITH",
	"ENGINETYPE",
	"COLUMNSTORE",
	"TSSTORE",
	"ALL",
	"ANY",
	"PASSWORD",
	"NAME",
	"REPLICANUM",
	"ALTER",
	"USER",
	"USERS",
	"DATABASES",
	"DATABASE",
	"MEASUREMENTS",
	"RETENTION",
	"POLICIES",
	"POLICY",
	"DURATION",
	"DEFAULT",
	"SHARD",
	"INDEX",
	"GRANT",
	"HOT",
	"WARM",
	"TYPE",
	"SET",
	"FOR",
	"GRANTS",
	"REPLICATION",
	"SERIES",
	"DROP",
	"CASE",
	"WHEN",
	"THEN",
	"ELSE",
	"BEGIN",
	"END",
	"TRUE",
	"FALSE",
	"TAG",
	"ATTRIBUTE",
	"FIELD",
	"KEYS",
	"VALUES",
	"KEY",
	"EXPLAIN",
	"ANALYZE",
	"EXACT",
	"CARDINALITY",
	"SHARDKEY",
	"PRIMARYKEY",
	"SORTKEY",
	"PROPERTY",
	"COMPACT",
	"CONTINUOUS",
	"DIAGNOSTICS",
	"QUERIES",
	"QUERIE",
	"SHARDS",
	"STATS",
	"SUBSCRIPTIONS",
	"SUBSCRIPTION",
	"GROUPS",
	"INDEXTYPE",
	"INDEXLIST",
	"SEGMENT",
	"KILL",
	"EVERY",
	"RESAMPLE",
	"DOWNSAMPLE",
	"DOWNSAMPLES",
	"SAMPLEINTERVAL",
	"TIMEINTERVAL",
	"STREAM",
	"DELAY",
	"STREAMS",
	"QUERY",
	"PARTITION",
	"TOKEN",
	"TOKENIZERS",
	"MATCH",
	"LIKE",
	"MATCHPHRASE",
	"CONFIG",
	"CONFIGS",
	"CLUSTER",
	"IPINRANGE",
	"REPLICAS",
	"DETAIL",
	"DESTINATIONS",
	"SCHEMA",
	"INDEXES",
	"AUTO",
	"EXCEPT",
	"DESC",
	"ASC",
	"COMMA",
	"SEMICOLON",
	"LPAREN",
	"RPAREN",
	"REGEX",
	"EQ",
	"NEQ",
	"LT",
	"LTE",
	"GT",
	"GTE",
	"DOT",
	"DOUBLECOLON",
	"NEQREGEX",
	"EQREGEX",
	"IDENT",
	"INTEGER",
	"DURATIONVAL",
	"STRING",
	"NUMBER",
	"HINT",
	"BOUNDPARAM",
	"AND",
	"OR",
	"ADD",
	"SUB",
	"BITWISE_OR",
	"BITWISE_XOR",
	"MUL",
	"DIV",
	"MOD",
	"BITWISE_AND",
	"UMINUS",
}

var yyStatenames = [...]string{}

const yyEofCode = 1
const yyErrCode = 2
const yyInitialStackSize = 16

//line sql.y:3597

//line yacctab:1
var yyExca = [...]int16{
	-1, 1,
	1, -1,
	-2, 0,
	-1, 73,
	4, 94,
	-2, 141,
	-1, 493,
	113, 159,
	133, 159,
	134, 159,
	135, 159,
	136, 159,
	137, 159,
	138, 159,
	141, 159,
	142, 159,
	-2, 147,
}

const yyPrivate = 57344

const yyLast = 1252

var yyAct = [...]int16{
	521, 536, 976, 917, 814, 949, 446, 831, 940, 728,
	842, 412, 750, 782, 732, 276, 535, 742, 876, 4,
	681, 581, 517, 665, 756, 661, 73, 812, 582, 519,
	403, 530, 217, 444, 239, 245, 338, 465, 256, 156,
	241, 2, 162, 77, 335, 243, 182, 895, 365, 366,
	83, 708, 293, 662, 707, 896, 87, 88, 663, 929,
	250, 249, 171, 172, 176, 173, 169, 170, 174, 175,
	142, 410, 158, 171, 172, 176, 173, 169, 170, 174,
	175, 89, 169, 170, 174, 175, 757, 758, 152, 748,
	759, 91, 493, 637, 593, 159, 760, 83, 160, 91,
	60, 165, 522, 87, 88, 160, 470, 950, 365, 366,
	469, 365, 366, 218, 947, 523, 931, 78, 295, 91,
	641, 642, 177, 283, 181, 915, 284, 921, 223, 224,
	79, 85, 82, 86, 84, 886, 90, 885, 829, 235,
	80, 237, 828, 76, 163, 916, 251, 684, 252, 91,
	159, 986, 253, 160, 809, 83, 365, 366, 91, 763,
	604, 87, 88, 218, 247, 216, 91, 713, 600, 215,
	214, 268, 218, 244, 712, 91, 272, 248, 85, 82,
	86, 84, 216, 90, 711, 710, 215, 80, 257, 218,
	577, 817, 259, 280, 159, 639, 913, 160, 640, 278,
	911, 227, 574, 575, 898, 294, 219, 817, 279, 298,
	332, 299, 238, 285, 286, 287, 288, 289, 290, 291,
	292, 304, 78, 768, 91, 219, 767, 60, 219, 257,
	302, 303, 591, 589, 580, 79, 85, 82, 86, 84,
	159, 90, 578, 160, 457, 80, 219, 271, 76, 348,
	562, 330, 102, 434, 561, 682, 683, 433, 306, 531,
	532, 816, 311, 686, 685, 230, 349, 534, 533, 323,
	149, 399, 147, 322, 158, 980, 185, 820, 918, 116,
	843, 297, 912, 368, 784, 364, 363, 219, 743, 97,
	92, 367, 93, 94, 583, 667, 840, 351, 104, 806,
	805, 797, 753, 752, 738, 697, 101, 696, 95, 369,
	370, 655, 654, 400, 636, 634, 633, 631, 98, 629,
	100, 615, 614, 417, 613, 590, 608, 606, 115, 112,
	113, 114, 119, 105, 592, 108, 436, 103, 579, 109,
	564, 528, 511, 510, 468, 408, 402, 507, 506, 106,
	486, 478, 480, 415, 107, 183, 398, 397, 483, 484,
	396, 393, 743, 110, 111, 392, 391, 388, 386, 117,
	118, 356, 355, 354, 498, 499, 500, 471, 150, 443,
	148, 416, 83, 385, 420, 422, 352, 425, 87, 88,
	120, 496, 347, 346, 485, 96, 487, 345, 340, 333,
	441, 491, 492, 377, 378, 379, 380, 381, 382, 257,
	257, 384, 383, 982, 178, 501, 331, 327, 516, 542,
	257, 515, 308, 180, 179, 300, 219, 612, 541, 270,
	231, 229, 225, 213, 548, 525, 546, 552, 566, 211,
	650, 219, 648, 219, 474, 611, 527, 565, 167, 78,
	178, 91, 695, 475, 529, 616, 602, 573, 872, 180,
	179, 563, 79, 85, 82, 86, 84, 468, 90, 601,
	482, 472, 80, 432, 353, 76, 168, 526, 344, 576,
	871, 721, 514, 513, 442, 847, 524, 524, 846, 544,
	545, 91, 547, 598, 588, 551, 599, 610, 72, 987,
	489, 607, 560, 603, 597, 605, 965, 952, 951, 569,
	571, 572, 83, 946, 621, 638, 930, 624, 87, 88,
	904, 620, 888, 618, 880, 844, 630, 839, 838, 645,
	171, 172, 176, 173, 169, 170, 174, 175, 628, 837,
	835, 834, 651, 669, 744, 740, 367, 644, 673, 739,
	675, 726, 219, 643, 219, 623, 490, 476, 883, 674,
	671, 672, 407, 678, 664, 221, 979, 698, 925, 679,
	219, 894, 668, 786, 727, 706, 694, 649, 646, 78,
	622, 91, 497, 494, 401, 702, 375, 704, 705, 374,
	373, 371, 79, 85, 82, 86, 84, 74, 90, 343,
	751, 653, 80, 360, 362, 76, 72, 981, 966, 942,
	709, 891, 731, 858, 670, 656, 657, 735, 171, 172,
	176, 173, 169, 170, 174, 175, 745, 746, 747, 692,
	693, 836, 771, 772, 830, 723, 60, 770, 700, 701,
	647, 703, 741, 627, 626, 625, 61, 62, 617, 273,
	166, 404, 736, 336, 339, 186, 67, 458, 64, 71,
	810, 189, 755, 749, 153, 766, 730, 754, 65, 232,
	220, 155, 709, 774, 775, 972, 776, 761, 889, 141,
	725, 66, 765, 881, 219, 69, 773, 825, 222, 720,
	63, 124, 880, 339, 718, 779, 796, 785, 778, 219,
	206, 780, 337, 236, 801, 68, 803, 804, 794, 795,
	877, 792, 361, 945, 207, 975, 799, 800, 970, 802,
	962, 359, 813, 504, 437, 819, 70, 123, 824, 524,
	121, 430, 122, 832, 811, 325, 326, 428, 807, 320,
	321, 337, 154, 189, 203, 204, 328, 818, 312, 189,
	860, 791, 781, 60, 827, 196, 197, 198, 244, 200,
	790, 201, 793, 777, 690, 680, 841, 787, 788, 677,
	798, 554, 125, 833, 188, 853, 315, 722, 281, 128,
	282, 257, 3, 849, 459, 845, 764, 126, 848, 194,
	762, 127, 339, 857, 852, 851, 865, 866, 854, 195,
	859, 868, 869, 922, 870, 652, 823, 60, 185, 864,
	226, 861, 862, 187, 867, 318, 319, 61, 62, 409,
	879, 192, 193, 151, 301, 873, 923, 67, 269, 64,
	71, 202, 751, 808, 729, 878, 887, 882, 715, 65,
	587, 586, 275, 884, 585, 890, 584, 146, 258, 855,
	228, 856, 66, 212, 892, 161, 69, 190, 893, 902,
	897, 63, 461, 863, 733, 734, 909, 900, 901, 910,
	596, 310, 903, 143, 453, 456, 68, 454, 455, 908,
	143, 905, 822, 821, 144, 919, 143, 924, 145, 914,
	143, 832, 832, 826, 789, 920, 716, 70, 926, 927,
	688, 689, 934, 935, 928, 676, 305, 557, 609, 191,
	939, 933, 553, 550, 932, 464, 414, 427, 387, 341,
	941, 518, 937, 938, 372, 899, 495, 260, 389, 632,
	508, 505, 906, 907, 948, 954, 488, 875, 874, 956,
	957, 261, 850, 953, 262, 390, 959, 769, 941, 963,
	958, 964, 955, 266, 83, 413, 264, 967, 143, 406,
	87, 88, 659, 660, 537, 538, 971, 413, 539, 405,
	265, 978, 973, 277, 83, 619, 936, 274, 144, 210,
	87, 88, 978, 985, 984, 983, 134, 143, 164, 635,
	164, 144, 144, 419, 421, 60, 424, 426, 395, 737,
	189, 394, 307, 503, 435, 481, 479, 313, 314, 440,
	316, 317, 477, 473, 324, 460, 139, 358, 329, 357,
	350, 78, 132, 91, 309, 129, 267, 131, 263, 234,
	233, 209, 133, 208, 79, 85, 82, 86, 84, 540,
	90, 502, 130, 91, 80, 411, 512, 509, 143, 205,
	199, 157, 595, 594, 79, 85, 82, 86, 84, 463,
	90, 462, 467, 466, 80, 724, 719, 135, 717, 815,
	968, 969, 977, 960, 140, 943, 961, 449, 450, 944,
	974, 99, 136, 137, 783, 445, 138, 658, 447, 451,
	453, 456, 520, 454, 455, 666, 296, 376, 543, 448,
	184, 81, 255, 549, 254, 246, 240, 242, 1, 556,
	75, 559, 59, 55, 54, 53, 58, 57, 568, 570,
	452, 56, 52, 51, 50, 342, 418, 49, 48, 423,
	47, 46, 45, 429, 44, 431, 43, 42, 41, 40,
	438, 39, 439, 38, 37, 36, 35, 34, 33, 32,
	31, 30, 29, 28, 27, 26, 25, 24, 21, 20,
	22, 19, 23, 18, 17, 16, 14, 15, 13, 12,
	714, 7, 11, 10, 9, 8, 334, 6, 5, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 687, 0, 0, 691, 0,
	0, 0, 555, 0, 558, 0, 0, 699, 0, 0,
	0, 567,
}

var yyPact = [...]int16{
	799, -1000, 477, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	449, 247, 686, 981, 983, 842, 237, 235, 745, 627,
	563, -48, 799, 982, 319, 522, 308, 466, 891, 320,
	891, -1000, -1000, 212, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, -1000, 535, 654, 810, 742, 728, -1000, 681, 1046,
	685, 773, 665, 1045, 606, 626, 1026, 1024, -1000, -1000,
	-1000, 970, -1000, -1000, -1000, -1000, -1000, -1000, -1000, -1000,
	-1000, 296, 805, 290, 26, 562, 558, -48, -48, 289,
	983, 802, 288, 121, 287, 561, 1023, 1022, -48, 611,
	-48, 969, -1000, 43, 34, 800, 26, 920, 1021, 949,
	1019, 987, -1000, 770, 286, 103, 987, 521, 967, -1000,
	-1000, -1000, 1044, 962, 43, 984, 319, 707, -20, 891,
	891, 891, 891, 891, 891, 891, 891, -79, -13, 138,
	282, -1000, 758, 744, 744, 34, -1000, 875, 993, 279,
	1017, 983, 668, 993, 993, 701, 993, 736, 660, 130,
	993, 656, 274, 666, 993, 26, -1000, -1000, 273, -48,
	256, 622, 255, 888, 469, 339, 254, -1000, -1000, -1000,
	250, 249, 319, 984, 1013, -1000, 969, -1000, 243, -1000,
	-1000, 335, 230, 229, 228, -1000, 1012, 1010, -1000, -1000,
	593, 584, -1000, -1000, 628, -102, -1000, 34, 284, 461,
	897, 460, 459, 456, -1000, -1000, 270, -90, 225, 887,
	224, 921, 223, 222, 218, 994, 217, 214, -1000, 213,
	-48, -1000, -1000, -48, 454, 969, 526, 957, -1000, 1044,
	-1000, -1000, -1000, -1000, -1000, -1000, -1000, -74, -74, -74,
	-1000, -1000, -74, -1000, 431, -1000, -1000, -1000, -1000, -1000,
	-1000, 891, 753, -1000, 6, 1040, 942, 885, -1000, 210,
	969, 942, 993, 983, 983, 993, 983, 886, 657, 993,
	651, 993, 334, 114, 954, 644, 993, -1000, 993, 983,
	-1000, -1000, -1000, 351, 583, -1000, 1039, 100, 538, 712,
	1008, 825, 884, -48, -33, 332, 1006, 314, 426, 1005,
	-48, -1000, 999, 209, 998, 331, -1000, -48, -48, 43,
	207, 43, 913, 369, 425, 34, 34, -79, -39, 453,
	901, 987, 452, -48, -48, -48, 911, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, -1000, -1000, 996, 642, 907, 205,
	204, -1000, 906, 1043, 200, 199, -1000, 1042, 350, 349,
	-1000, 987, 962, 892, -41, -41, 969, -1000, 378, 198,
	891, 126, 950, 956, 1034, -1000, 942, 950, 983, 969,
	962, 969, 942, 882, 969, 942, 881, 695, 993, 876,
	993, 983, 111, 322, 197, 942, 950, 993, 983, 983,
	969, 962, 59, -1000, -1000, 1039, -1000, 45, 98, 195,
	90, -1000, 151, 797, 795, 792, 791, 721, 89, 182,
	191, -52, -1000, -1000, 838, -1000, -48, 365, 97, 317,
	17, -1000, 17, 184, 319, 183, 877, 987, 306, 181,
	-1000, 179, 178, -1000, 316, -1000, 520, -1000, 43, 965,
	-1000, -1000, -1000, -1000, 92, 450, 424, 987, 517, 516,
	515, -1000, 34, 176, 151, 174, 905, -1000, 173, 172,
	985, -1000, 171, -53, 51, 422, 526, 942, 448, -1000,
	512, 302, 447, 300, -1000, -1000, 962, -1000, 737, -90,
	969, 169, 168, 359, 359, -1000, 946, -91, -91, 152,
	126, 950, -1000, 969, 962, 962, 950, 942, 950, 874,
	693, 942, 950, 689, 122, 869, 870, 688, 983, 969,
	962, 313, 164, 162, -1000, 950, -1000, 983, 969, 962,
	969, 962, 962, 950, -96, -99, -1000, -1000, -1000, -1000,
	-1000, 482, -1000, -1000, 40, 39, 29, 22, -1000, -1000,
	-1000, -1000, 789, 865, 599, 594, 348, -1000, -1000, -1000,
	-1000, 704, 17, -1000, -1000, -1000, 580, 420, 444, 785,
	560, -48, 829, -1000, -1000, -1000, -48, 43, 992, 161,
	418, 414, 219, -1000, 413, -48, -48, -48, -42, 1039,
	544, -1000, 160, -1000, -1000, 159, -1000, -1000, -1000, -1000,
	-1000, -1000, -1000, -1000, 892, 950, -57, -41, 719, 14,
	715, 526, -1000, 942, -1000, -1000, -1000, -1000, -1000, 82,
	79, 932, -1000, -1000, -1000, -1000, 509, 506, -1000, -1000,
	962, 950, 950, -1000, 950, -1000, 687, 122, 950, -1000,
	122, 969, 141, 141, 443, 359, 359, 863, 684, 675,
	122, 969, 962, 962, 950, 158, -1000, -1000, -1000, 969,
	962, 962, 950, 962, 950, 950, -1000, 157, 156, 151,
	-1000, -1000, -1000, -1000, 783, 9, 625, 641, 118, 641,
	134, 849, -1000, -1000, 739, 629, 862, 319, -1000, -3,
	-7, 513, -48, -1000, -1000, -1000, -1000, 34, -1000, -1000,
	-1000, 410, 409, 503, -1000, 408, 397, 396, -1000, -1000,
	-1000, 153, -1000, -1000, 942, 137, 394, -1000, -1000, -1000,
	-57, -1000, -1000, 357, -1000, 892, 950, 925, -1000, -91,
	152, -1000, -1000, 950, -1000, -1000, -1000, 122, 969, -1000,
	969, 942, -1000, 485, -1000, -1000, 141, -1000, -1000, 674,
	122, 122, 969, 962, 950, 950, -1000, -1000, 962, 950,
	950, -1000, 950, -1000, -1000, 347, 325, -1000, -1000, 765,
	917, 916, 620, 151, -1000, 118, 596, 587, 620, -1000,
	428, -1000, -1000, 987, -8, -10, 785, 391, 575, -1000,
	829, -1000, 483, -102, -1000, -1000, 145, -1000, -1000, -1000,
	-1000, 950, -1000, 441, -1000, -1000, -1000, -98, 942, -1000,
	60, -1000, -1000, -1000, 969, 942, 942, 950, 141, 389,
	122, 969, 969, 962, 950, -1000, -1000, 950, -1000, -1000,
	-1000, 56, 139, 52, -1000, -1000, 776, 1, 482, -1000,
	135, 135, 776, -18, 735, 768, -1000, -1000, 856, 438,
	-48, -48, -1000, 137, -87, 385, -29, 950, -1000, 942,
	950, 950, -1000, -1000, -1000, 969, 962, 962, 950, -1000,
	-1000, -1000, -1000, 823, -1000, -1000, -1000, -1000, 481, -1000,
	631, 382, -1000, -31, 785, -38, -1000, -1000, -1000, 377,
	-1000, 376, 137, 950, -1000, -1000, 962, 950, 950, -1000,
	-1000, 823, 135, 637, -1000, 135, 118, -1000, -1000, 375,
	480, -1000, -1000, -1000, -1000, 950, -1000, -1000, -1000, -1000,
	634, -1000, 135, -1000, -1000, 571, -38, -1000, 630, -1000,
	-48, -1000, 436, -1000, -1000, 132, -1000, 479, 280, -38,
	-1000, -48, 7, 368, -1000, -1000, -1000, -1000,
}

var yyPgo = [...]int16{
	0, 782, 1178, 1177, 1176, 1175, 19, 1174, 1173, 1172,
	1171, 1170, 1169, 1168, 1167, 1166, 1165, 1164, 1163, 1162,
	1161, 1160, 1159, 1158, 1157, 1156, 1155, 20, 1154, 1153,
	1152, 1151, 1150, 1149, 1148, 1147, 1146, 1145, 1144, 1143,
	1141, 1139, 1138, 1137, 1136, 1134, 9, 1132, 1131, 1130,
	1128, 1127, 1125, 1124, 1123, 1122, 1121, 1117, 1116, 1115,
	1114, 1113, 1112, 26, 17, 1110, 1108, 41, 679, 34,
	40, 42, 1107, 32, 1106, 45, 31, 70, 1105, 1104,
	35, 1102, 1101, 43, 38, 13, 1100, 46, 1097, 1096,
	23, 11, 1095, 15, 30, 29, 1092, 16, 1, 1087,
	22, 24, 8, 6, 1085, 33, 81, 1084, 813, 12,
	28, 0, 1081, 14, 1080, 21, 27, 3, 1079, 1076,
	7, 1075, 1073, 2, 1072, 1071, 1070, 10, 1069, 4,
	1068, 1066, 1065, 5, 25, 18, 36, 1063, 1062, 37,
	44, 1061, 1059, 1053, 1052, 39, 1051,
}

var yyR1 = [...]uint8{
	0, 66, 67, 67, 67, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 6, 6, 6, 63, 63, 65, 65, 65, 65,
	65, 65, 87, 87, 86, 64, 64, 83, 83, 83,
	83, 83, 83, 83, 83, 83, 83, 83, 83, 83,
	83, 83, 83, 71, 71, 68, 69, 69, 69, 69,
	69, 69, 69, 72, 70, 70, 70, 74, 75, 75,
	75, 75, 75, 73, 73, 73, 93, 93, 94, 94,
	95, 95, 111, 111, 96, 96, 96, 96, 96, 96,
	96, 96, 127, 127, 100, 100, 101, 101, 101, 101,
	77, 77, 79, 79, 78, 78, 80, 80, 80, 80,
	80, 80, 80, 80, 80, 80, 80, 81, 84, 84,
	88, 88, 88, 88, 88, 88, 88, 88, 88, 106,
	82, 82, 82, 82, 82, 82, 82, 82, 82, 82,
	89, 89, 89, 91, 91, 90, 90, 92, 92, 92,
	97, 134, 134, 98, 98, 98, 98, 99, 99, 99,
	99, 2, 2, 3, 3, 140, 140, 140, 140, 140,
	136, 136, 4, 105, 105, 104, 104, 104, 104, 104,
	104, 104, 7, 7, 8, 8, 76, 76, 76, 76,
	9, 9, 10, 10, 5, 5, 5, 11, 11, 102,
	102, 103, 103, 103, 103, 12, 12, 12, 12, 13,
	15, 14, 14, 16, 16, 17, 18, 20, 20, 20,
	22, 22, 21, 21, 21, 23, 23, 19, 24, 24,
	112, 112, 112, 112, 112, 112, 112, 112, 112, 53,
	53, 53, 53, 53, 108, 108, 25, 25, 26, 26,
	26, 26, 27, 27, 27, 27, 27, 85, 85, 107,
	28, 28, 29, 29, 29, 29, 30, 30, 30, 30,
	31, 31, 31, 31, 32, 32, 141, 141, 142, 130,
	130, 131, 131, 131, 116, 116, 135, 135, 135, 143,
	143, 144, 121, 121, 122, 122, 126, 126, 114, 114,
	52, 52, 139, 139, 137, 137, 138, 138, 138, 128,
	128, 129, 129, 117, 117, 109, 109, 118, 119, 123,
	123, 125, 124, 124, 124, 115, 115, 110, 33, 34,
	35, 36, 36, 36, 36, 37, 37, 37, 37, 38,
	38, 39, 39, 40, 41, 41, 42, 132, 132, 132,
	132, 43, 44, 45, 45, 45, 47, 47, 47, 47,
	48, 48, 46, 133, 133, 49, 49, 50, 50, 51,
	54, 55, 120, 120, 113, 113, 59, 59, 60, 61,
	61, 61, 61, 56, 57, 57, 57, 57, 57, 58,
	58, 58, 58, 58, 62, 145, 145, 146,
}

var yyR2 = [...]int8{
	0, 1, 1, 2, 3, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 11, 12, 9, 1, 3, 1, 3, 3, 1,
	3, 3, 1, 2, 4, 1, 3, 3, 3, 3,
	3, 3, 3, 3, 3, 3, 4, 3, 2, 1,
	1, 5, 6, 2, 0, 2, 1, 3, 1, 3,
	3, 5, 1, 6, 3, 5, 3, 1, 5, 4,
	4, 3, 1, 1, 1, 1, 3, 0, 2, 0,
	1, 3, 1, 1, 1, 3, 4, 6, 7, 1,
	3, 1, 4, 0, 4, 0, 1, 1, 1, 2,
	2, 0, 1, 3, 1, 3, 1, 3, 5, 5,
	4, 6, 6, 5, 6, 6, 6, 3, 1, 3,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
	1, 3, 1, 1, 1, 1, 1, 1, 3, 1,
	1, 1, 1, 3, 0, 1, 3, 1, 2, 2,
	2, 1, 1, 4, 2, 2, 0, 4, 2, 2,
	0, 2, 3, 5, 4, 2, 1, 3, 3, 0,
	3, 3, 2, 1, 2, 1, 2, 2, 2, 2,
	1, 2, 9, 6, 7, 4, 2, 2, 2, 2,
	5, 3, 7, 8, 6, 9, 9, 5, 4, 1,
	2, 3, 3, 3, 3, 7, 6, 8, 7, 2,
	3, 4, 3, 3, 2, 7, 6, 6, 7, 6,
	5, 4, 6, 7, 6, 5, 4, 3, 8, 7,
	1, 1, 1, 1, 1, 1, 1, 1, 1, 4,
	8, 7, 7, 6, 2, 0, 7, 6, 11, 10,
	12, 11, 2, 2, 4, 2, 2, 1, 3, 1,
	3, 2, 10, 9, 9, 8, 13, 12, 12, 11,
	10, 9, 9, 8, 5, 5, 0, 6, 10, 0,
	2, 0, 2, 6, 0, 2, 0, 2, 2, 0,
	3, 3, 0, 1, 0, 1, 0, 1, 0, 2,
	2, 0, 2, 1, 2, 2, 2, 3, 2, 3,
	3, 2, 0, 1, 3, 2, 0, 2, 2, 3,
	1, 2, 3, 3, 0, 1, 3, 1, 3, 6,
	4, 9, 8, 8, 7, 9, 8, 8, 7, 2,
	4, 7, 3, 3, 3, 5, 10, 3, 3, 5,
	0, 3, 6, 9, 11, 7, 4, 6, 2, 4,
	2, 4, 10, 1, 3, 8, 6, 2, 4, 3,
	2, 3, 1, 3, 1, 1, 10, 8, 2, 3,
	5, 7, 5, 2, 6, 6, 6, 6, 6, 2,
	6, 6, 10, 10, 3, 1, 3, 5,
}

var yyChk = [...]int16{
	-1000, -66, -67, -1, -6, -2, -3, -10, -5, -7,
	-8, -9, -12, -13, -15, -14, -16, -17, -18, -20,
	-22, -23, -21, -19, -24, -25, -26, -28, -29, -30,
	-31, -32, -33, -34, -35, -36, -37, -38, -39, -40,
	-41, -42, -43, -44, -45, -47, -48, -49, -50, -51,
	-53, -54, -55, -59, -60, -61, -56, -57, -58, -62,
	8, 18, 19, 62, 30, 40, 53, 28, 77, 57,
	98, 31, 129, -63, 148, -65, 156, -83, 130, 143,
	153, -82, 145, 63, 147, 144, 146, 69, 70, -106,
	149, 132, 43, 45, 46, 61, 148, 42, 71, -112,
	73, 59, 5, 90, 51, 86, 102, 107, 88, 92,
	116, 117, 82, 83, 84, 81, 32, 122, 123, 85,
	143, 44, 46, 41, 5, 86, 101, 105, 93, 44,
	61, 46, 41, 51, 5, 86, 101, 102, 105, 35,
	93, -68, -77, 4, 9, 46, 5, 35, 143, 35,
	143, 78, -6, 37, 115, 108, -145, -146, -111, 143,
	146, -1, -71, -77, 6, -63, 128, 140, 10, 156,
	157, 152, 153, 155, 158, 159, 154, -83, 130, 140,
	139, -83, -87, 143, -86, 64, 120, -108, 120, 7,
	47, -108, 79, 80, 61, 71, 74, 75, 76, 4,
	74, 76, 58, 79, 80, 4, 94, 88, 7, 7,
	9, 143, 48, 143, -75, 143, 139, -73, 146, -106,
	108, 7, 130, -111, -111, 143, -68, -77, 48, 143,
	144, 143, 108, 7, 7, -111, 92, -111, -77, -69,
	-74, -70, -72, -75, 130, -80, -78, 130, 143, 27,
	26, 112, 114, 118, -79, -81, -84, -83, 48, -75,
	7, 21, 24, 7, 7, 21, 4, 7, -6, 58,
	143, 144, -6, 128, 10, -68, -93, 11, -69, -71,
	-63, 71, 73, 143, 146, -83, -83, -83, -83, -83,
	-83, -83, -83, 131, -63, 131, -89, 143, 71, 73,
	143, 66, -87, -87, -80, 31, -77, -108, 143, 7,
	-68, -77, 80, -108, -108, 75, -108, -108, 79, 80,
	79, 80, 143, 139, -108, 79, 80, 143, 80, -108,
	-75, 143, -111, 143, -4, -140, 31, 119, -136, 71,
	143, 31, -52, 130, 139, 143, 143, 143, -63, -71,
	7, -77, 143, 139, 143, 143, 143, 7, 7, 128,
	10, 128, 20, -67, -70, 150, 151, -83, -80, 25,
	26, 130, 27, 130, 130, 130, -88, 133, 134, 135,
	136, 137, 138, 142, 141, 113, 143, 31, 143, 7,
	24, 143, 143, 143, 7, 4, 143, 143, 143, -111,
	-145, 130, -77, -94, 125, 12, -68, 131, -83, 66,
	65, 5, -91, 13, 31, 143, -77, -91, -108, -68,
	-77, -68, -77, -108, -68, -77, -68, 31, 80, -108,
	80, -108, 139, 143, 139, -68, -91, 80, -108, -108,
	-68, -77, 133, -140, -105, -104, -103, 49, 60, 38,
	39, 50, 81, 51, 54, 55, 52, 144, 119, 72,
	7, 37, -141, -142, 31, -139, -137, -138, -111, 143,
	139, -73, 139, 7, 130, 139, 131, 7, -111, 7,
	143, 7, 139, -111, -111, -69, 143, -69, 23, 131,
	131, -80, -80, 131, 130, 25, -6, 130, -111, -111,
	-111, -84, 130, 7, 81, 24, 143, 143, 24, 4,
	143, 143, 4, 133, 133, -6, -93, -100, 29, -95,
	-96, -111, 143, 156, -106, -95, -77, 68, 143, -83,
	-76, 133, 134, 142, 141, -97, -98, 14, 15, 12,
	5, -91, -98, -68, -77, -77, -93, -77, -91, -68,
	31, -77, -91, 31, 76, -108, -68, 31, -108, -68,
	-77, 143, 139, 139, 143, -91, -98, -108, -68, -77,
	-68, -77, -77, -93, 143, 144, -105, 145, 144, 143,
	144, -115, -110, 143, 49, 49, 49, 49, -136, 144,
	143, 50, 143, 146, -143, -144, 32, -139, 128, 131,
	71, -111, 139, -73, 143, -73, 143, -63, 143, 31,
	-6, 139, 121, 143, 143, 143, 139, 128, -69, 10,
	-63, -6, 130, 131, -6, 128, 128, 128, -80, 143,
	-115, 143, 24, 143, 143, 4, 143, 146, -111, 144,
	147, 69, 70, 131, -94, -91, 130, 128, 140, 130,
	140, -93, 68, -77, 143, 143, -106, -106, -99, 16,
	17, -134, 144, 149, -134, -90, -92, 143, -76, -98,
	-77, -93, -93, -98, -91, -98, 31, 76, -91, -97,
	76, -27, 133, 134, 25, 142, 141, -68, 31, 31,
	76, -68, -77, -77, -93, 139, 143, 143, -98, -68,
	-77, -77, -93, -77, -93, -93, -98, 150, 150, 128,
	145, 145, 145, 145, -11, 49, 31, -130, 95, -131,
	95, 133, 73, -73, -132, 100, 131, 130, -46, 49,
	106, -111, -113, 35, 36, -111, -69, 7, 143, 131,
	131, -6, -64, 143, 131, -111, -111, -111, 131, -105,
	-109, 56, 143, 143, -100, -97, -101, 143, 144, 147,
	153, -95, 71, 145, 71, -94, -91, 144, 144, 15,
	128, 126, 127, -93, -98, -98, -98, 76, -27, -97,
	-27, -77, -85, -107, 143, -85, 130, -106, -106, 31,
	76, 76, -27, -77, -93, -93, -98, 143, -77, -93,
	-93, -98, -93, -98, -98, 143, 143, -110, 50, 145,
	35, 109, -116, 81, -129, -128, 143, 73, -116, -129,
	143, 34, 33, 67, 99, 58, 31, -63, 145, 145,
	121, -120, -111, -80, 131, 131, 128, 131, 131, 131,
	143, -91, -127, 143, 131, -101, 131, 128, -100, -97,
	17, -134, -90, -98, -27, -77, -77, -91, 128, -85,
	76, -27, -27, -77, -93, -98, -98, -93, -98, -98,
	-98, 133, 133, 60, 21, 21, -135, 90, -115, -129,
	96, 96, -135, 130, -6, 145, 145, -46, 131, 103,
	-113, 128, -64, -97, 130, 145, 153, -91, 144, -77,
	-91, -91, -98, -85, 131, -27, -77, -77, -93, -98,
	-98, 144, 143, 144, -109, 124, 144, -117, 143, -117,
	-109, 145, 68, 58, 31, 130, -120, -120, -127, 146,
	131, 145, -97, -91, -98, -98, -77, -93, -93, -98,
	-102, -103, 128, -121, -118, 82, 131, 145, -46, -133,
	145, 131, 131, -127, -98, -93, -98, -98, -102, -117,
	-122, -119, 83, -117, -129, 131, 128, -98, -126, -125,
	84, -117, 104, -133, -114, 85, -123, -124, -111, 130,
	143, 128, 133, -133, -123, -111, 144, 131,
}

var yyDef = [...]int16{
	0, -2, 1, 2, 5, 6, 7, 8, 9, 10,
	11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
	21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
	31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
	41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
	51, 52, 53, 54, 55, 56, 57, 58, 59, 60,
	0, 0, 0, 0, 141, 0, 0, 0, 0, 0,
	0, 0, 3, -2, 0, 64, 66, 69, 0, 170,
	0, 89, 90, 0, 172, 173, 174, 175, 176, 177,
	179, 169, 201, 285, 0, 285, 0, 249, 0, 0,
	0, 0, 0, 379, 0, 0, 400, 407, 410, 418,
	423, 429, 270, 271, 272, 273, 274, 275, 276, 277,
	278, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	141, 0, 0, 0, 0, 0, 0, 398, 0, 0,
	0, 141, 254, 0, 0, 0, 0, 0, 0, 0,
	0, 0, 301, 0, 0, 0, 0, 435, 0, 122,
	123, 4, 0, 117, 0, 94, 0, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 88, 0, 0, 72, 0, 202, 141, 285, 0,
	231, 141, 0, 285, 285, 0, 285, 285, 0, 0,
	285, 0, 0, 0, 285, 0, 383, 391, 0, 0,
	0, 209, 0, 0, 341, 113, 0, 112, 114, 115,
	0, 0, 0, 94, 0, 250, 141, 252, 0, 267,
	368, 384, 0, 0, 0, 409, 419, 0, 253, 95,
	96, 98, 102, 107, 0, 140, 146, 0, 170, 0,
	0, 0, 0, 0, 144, 142, 0, 158, 0, 382,
	0, 0, 0, 0, 0, 0, 0, 0, 300, 0,
	0, 411, 434, 0, 0, 141, 119, 0, 93, 0,
	65, 67, 68, 70, 71, 77, 78, 79, 80, 81,
	82, 83, 84, 85, 0, 87, 171, 180, 181, 182,
	178, 0, 0, 73, 0, 0, 184, 225, 284, 0,
	141, 184, 285, 141, 141, 285, 141, 0, 0, 285,
	0, 285, 279, 0, 184, 0, 285, 370, 285, 141,
	380, 401, 408, 0, 209, 204, 0, 0, 206, 0,
	0, 0, 316, 0, 0, 0, 0, 0, 0, 0,
	0, 251, 0, 0, 0, 396, 399, 0, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 158, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 160, 161, 162,
	163, 164, 165, 166, 167, 168, 0, 0, 0, 0,
	0, 261, 0, 0, 0, 0, 266, 0, 0, 0,
	436, 0, 117, 135, 0, 0, 141, 86, 0, 0,
	0, 0, 196, 0, 0, 230, 184, 196, 141, 141,
	117, 141, 184, 0, 141, 184, 0, 0, 285, 0,
	285, 141, 0, 0, 0, 184, 196, 285, 141, 141,
	141, 117, 0, 203, 212, 213, 215, 0, 0, 0,
	0, 220, 0, 0, 0, 0, 0, 205, 0, 0,
	0, 0, 314, 315, 329, 340, 343, 0, 0, 113,
	0, 111, 0, 0, 0, 0, 0, 0, 0, 0,
	385, 0, 0, 420, 422, 97, 100, 99, 0, 104,
	106, 143, 145, -2, 0, 0, 0, 0, 0, 0,
	0, 157, 0, 0, 0, 0, 0, 260, 0, 0,
	0, 265, 0, 0, 0, 0, 119, 184, 0, 118,
	120, 124, 122, 129, 131, 116, 117, 91, 0, 74,
	141, 0, 0, 0, 0, 223, 200, 0, 0, 0,
	0, 196, 246, 141, 117, 117, 196, 184, 196, 0,
	0, 184, 196, 0, 0, 0, 0, 0, 141, 141,
	117, 0, 0, 0, 283, 196, 287, 141, 141, 117,
	141, 117, 117, 196, 430, 431, 214, 216, 217, 218,
	219, 221, 365, 367, 0, 0, 0, 0, 207, 208,
	210, 211, 0, 234, 319, 321, 0, 342, 344, 345,
	346, 348, 0, 110, 113, 109, 390, 0, 0, 0,
	406, 0, 0, 256, 392, 397, 0, 0, 0, 0,
	0, 0, 0, 150, 0, 0, 0, 0, 0, 0,
	356, 257, 0, 259, 262, 0, 264, 369, 424, 425,
	426, 427, 428, 437, 135, 196, 0, 0, 0, 0,
	0, 119, 92, 184, 226, 227, 228, 229, 190, 0,
	0, 194, 191, 192, 195, 183, 185, 187, 224, 245,
	117, 196, 196, 378, 196, 248, 0, 0, 196, 269,
	0, 141, 0, 0, 0, 0, 0, 0, 0, 0,
	0, 141, 117, 117, 196, 0, 281, 282, 286, 141,
	117, 117, 196, 117, 196, 196, 374, 0, 0, 0,
	241, 242, 243, 244, 232, 0, 0, 324, 352, 324,
	352, 0, 347, 108, 0, 0, 0, 0, 395, 0,
	0, 0, 0, 414, 415, 421, 101, 0, 105, 148,
	149, 0, 0, 75, 153, 0, 0, 0, 159, 255,
	381, 0, 258, 263, 184, 133, 0, 136, 137, 138,
	0, 121, 125, 0, 130, 135, 196, 198, 199, 0,
	0, 188, 189, 196, 376, 377, 247, 0, 141, 268,
	141, 184, 292, 297, 299, 293, 0, 295, 296, 0,
	0, 0, 141, 117, 196, 196, 305, 280, 117, 196,
	196, 313, 196, 372, 373, 0, 0, 366, 233, 0,
	0, 0, 326, 0, 320, 352, 0, 0, 326, 322,
	0, 330, 331, 0, 0, 0, 0, 0, 0, 405,
	0, 417, 412, 103, 151, 152, 0, 154, 155, 156,
	355, 196, 63, 0, 134, 139, 126, 0, 184, 222,
	0, 193, 186, 375, 141, 184, 184, 196, 0, 0,
	0, 141, 141, 117, 196, 303, 304, 196, 311, 312,
	371, 0, 0, 0, 235, 236, 356, 0, 325, 351,
	0, 0, 356, 0, 0, 387, 388, 393, 0, 0,
	0, 0, 76, 133, 0, 0, 0, 196, 197, 184,
	196, 196, 289, 298, 294, 141, 117, 117, 196, 302,
	310, 433, 432, 238, 317, 327, 328, 349, 353, 350,
	332, 0, 386, 0, 0, 0, 416, 413, 61, 0,
	127, 0, 133, 196, 291, 288, 117, 196, 196, 309,
	237, 239, 0, 334, 333, 0, 352, 389, 394, 0,
	403, 132, 128, 62, 290, 196, 307, 308, 240, 354,
	336, 335, 0, 357, 323, 0, 0, 306, 338, 337,
	364, 358, 0, 404, 318, 0, 361, 360, 0, 0,
	339, 364, 0, 0, 359, 362, 363, 402,
}

var yyTok1 = [...]int8{
	1,
}

var yyTok2 = [...]uint8{
	2, 3, 4, 5, 6, 7, 8, 9, 10, 11,
	12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
	22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
	32, 33, 34, 35, 36, 37, 38, 39, 40, 41,
	42, 43, 44, 45, 46, 47, 48, 49, 50, 51,
	52, 53, 54, 55, 56, 57, 58, 59, 60, 61,
	62, 63, 64, 65, 66, 67, 68, 69, 70, 71,
	72, 73, 74, 75, 76, 77, 78, 79, 80, 81,
	82, 83, 84, 85, 86, 87, 88, 89, 90, 91,
	92, 93, 94, 95, 96, 97, 98, 99, 100, 101,
	102, 103, 104, 105, 106, 107, 108, 109, 110, 111,
	112, 113, 114, 115, 116, 117, 118, 119, 120, 121,
	122, 123, 124, 125, 126, 127, 128, 129, 130, 131,
	132, 133, 134, 135, 136, 137, 138, 139, 140, 141,
	142, 143, 144, 145, 146, 147, 148, 149, 150, 151,
	152, 153, 154, 155, 156, 157, 158, 159, 160,
}

var yyTok3 = [...]int8{
	0,
}

var yyErrorMessages = [...]struct {
	state int
	token int
	msg   string
}{}

//line yaccpar:1

/*	parser for yacc output	*/

var (
	yyDebug        = 0
	yyErrorVerbose = true
)

type yyLexer interface {
	Lex(lval *yySymType) int
	Error(s string)
}

type yyParser interface {
	Parse(yyLexer) int
	Lookahead() int
}

type yyParserImpl struct {
	lval  yySymType
	stack [yyInitialStackSize]yySymType
	char  int
}

func (p *yyParserImpl) Lookahead() int {
	return p.char
}

func yyNewParser() yyParser {
	return &yyParserImpl{}
}

const yyFlag = -1000

func yyTokname(c int) string {
	if c >= 1 && c-1 < len(yyToknames) {
		if yyToknames[c-1] != "" {
			return yyToknames[c-1]
		}
	}
	return __yyfmt__.Sprintf("tok-%v", c)
}

func yyStatname(s int) string {
	if s >= 0 && s < len(yyStatenames) {
		if yyStatenames[s] != "" {
			return yyStatenames[s]
		}
	}
	return __yyfmt__.Sprintf("state-%v", s)
}

func yyErrorMessage(state, lookAhead int) string {
	const TOKSTART = 4

	if !yyErrorVerbose {
		return "syntax error"
	}

	for _, e := range yyErrorMessages {
		if e.state == state && e.token == lookAhead {
			return "syntax error: " + e.msg
		}
	}

	res := "syntax error: unexpected " + yyTokname(lookAhead)

	// To match Bison, suggest at most four expected tokens.
	expected := make([]int, 0, 4)

	// Look for shiftable tokens.
	base := int(yyPact[state])
	for tok := TOKSTART; tok-1 < len(yyToknames); tok++ {
		if n := base + tok; n >= 0 && n < yyLast && int(yyChk[int(yyAct[n])]) == tok {
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}
	}

	if yyDef[state] == -2 {
		i := 0
		for yyExca[i] != -1 || int(yyExca[i+1]) != state {
			i += 2
		}

		// Look for tokens that we accept or reduce.
		for i += 2; yyExca[i] >= 0; i += 2 {
			tok := int(yyExca[i])
			if tok < TOKSTART || yyExca[i+1] == 0 {
				continue
			}
			if len(expected) == cap(expected) {
				return res
			}
			expected = append(expected, tok)
		}

		// If the default action is to accept or reduce, give up.
		if yyExca[i+1] != 0 {
			return res
		}
	}

	for i, tok := range expected {
		if i == 0 {
			res += ", expecting "
		} else {
			res += " or "
		}
		res += yyTokname(tok)
	}
	return res
}

func yylex1(lex yyLexer, lval *yySymType) (char, token int) {
	token = 0
	char = lex.Lex(lval)
	if char <= 0 {
		token = int(yyTok1[0])
		goto out
	}
	if char < len(yyTok1) {
		token = int(yyTok1[char])
		goto out
	}
	if char >= yyPrivate {
		if char < yyPrivate+len(yyTok2) {
			token = int(yyTok2[char-yyPrivate])
			goto out
		}
	}
	for i := 0; i < len(yyTok3); i += 2 {
		token = int(yyTok3[i+0])
		if token == char {
			token = int(yyTok3[i+1])
			goto out
		}
	}

out:
	if token == 0 {
		token = int(yyTok2[1]) /* unknown char */
	}
	if yyDebug >= 3 {
		__yyfmt__.Printf("lex %s(%d)\n", yyTokname(token), uint(char))
	}
	return char, token
}

func yyParse(yylex yyLexer) int {
	return yyNewParser().Parse(yylex)
}

func (yyrcvr *yyParserImpl) Parse(yylex yyLexer) int {
	var yyn int
	var yyVAL yySymType
	var yyDollar []yySymType
	_ = yyDollar // silence set and not used
	yyS := yyrcvr.stack[:]

	Nerrs := 0   /* number of errors */
	Errflag := 0 /* error recovery flag */
	yystate := 0
	yyrcvr.char = -1
	yytoken := -1 // yyrcvr.char translated into internal numbering
	defer func() {
		// Make sure we report no lookahead when not parsing.
		yystate = -1
		yyrcvr.char = -1
		yytoken = -1
	}()
	yyp := -1
	goto yystack

ret0:
	return 0

ret1:
	return 1

yystack:
	/* put a state and value onto the stack */
	if yyDebug >= 4 {
		__yyfmt__.Printf("char %v in %v\n", yyTokname(yytoken), yyStatname(yystate))
	}

	yyp++
	if yyp >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyS[yyp] = yyVAL
	yyS[yyp].yys = yystate

yynewstate:
	yyn = int(yyPact[yystate])
	if yyn <= yyFlag {
		goto yydefault /* simple state */
	}
	if yyrcvr.char < 0 {
		yyrcvr.char, yytoken = yylex1(yylex, &yyrcvr.lval)
	}
	yyn += yytoken
	if yyn < 0 || yyn >= yyLast {
		goto yydefault
	}
	yyn = int(yyAct[yyn])
	if int(yyChk[yyn]) == yytoken { /* valid shift */
		yyrcvr.char = -1
		yytoken = -1
		yyVAL = yyrcvr.lval
		yystate = yyn
		if Errflag > 0 {
			Errflag--
		}
		goto yystack
	}

yydefault:
	/* default state action */
	yyn = int(yyDef[yystate])
	if yyn == -2 {
		if yyrcvr.char < 0 {
			yyrcvr.char, yytoken = yylex1(yylex, &yyrcvr.lval)
		}

		/* look through exception table */
		xi := 0
		for {
			if yyExca[xi+0] == -1 && int(yyExca[xi+1]) == yystate {
				break
			}
			xi += 2
		}
		for xi += 2; ; xi += 2 {
			yyn = int(yyExca[xi+0])
			if yyn < 0 || yyn == yytoken {
				break
			}
		}
		yyn = int(yyExca[xi+1])
		if yyn < 0 {
			goto ret0
		}
	}
	if yyn == 0 {
		/* error ... attempt to resume parsing */
		switch Errflag {
		case 0: /* brand new error */
			yylex.Error(yyErrorMessage(yystate, yytoken))
			Nerrs++
			if yyDebug >= 1 {
				__yyfmt__.Printf("%s", yyStatname(yystate))
				__yyfmt__.Printf(" saw %s\n", yyTokname(yytoken))
			}
			fallthrough

		case 1, 2: /* incompletely recovered error ... try again */
			Errflag = 3

			/* find a state where "error" is a legal shift action */
			for yyp >= 0 {
				yyn = int(yyPact[yyS[yyp].yys]) + yyErrCode
				if yyn >= 0 && yyn < yyLast {
					yystate = int(yyAct[yyn]) /* simulate a shift of "error" */
					if int(yyChk[yystate]) == yyErrCode {
						goto yystack
					}
				}

				/* the current p has no shift on "error", pop stack */
				if yyDebug >= 2 {
					__yyfmt__.Printf("error recovery pops state %d\n", yyS[yyp].yys)
				}
				yyp--
			}
			/* there is no state on the stack with an error shift ... abort */
			goto ret1

		case 3: /* no shift yet; clobber input char */
			if yyDebug >= 2 {
				__yyfmt__.Printf("error recovery discards %s\n", yyTokname(yytoken))
			}
			if yytoken == yyEofCode {
				goto ret1
			}
			yyrcvr.char = -1
			yytoken = -1
			goto yynewstate /* try again in the same state */
		}
	}

	/* reduction by production yyn */
	if yyDebug >= 2 {
		__yyfmt__.Printf("reduce %v in:\n\t%v\n", yyn, yyStatname(yystate))
	}

	yynt := yyn
	yypt := yyp
	_ = yypt // guard against "declared and not used"

	yyp -= int(yyR2[yyn])
	// yyp is now the index of $0. Perform the default action. Iff the
	// reduced production is ε, $1 is possibly out of range.
	if yyp+1 >= len(yyS) {
		nyys := make([]yySymType, len(yyS)*2)
		copy(nyys, yyS)
		yyS = nyys
	}
	yyVAL = yyS[yyp+1]

	/* consult goto table to find next state */
	yyn = int(yyR1[yyn])
	yyg := int(yyPgo[yyn])
	yyj := yyg + yyS[yyp].yys + 1

	if yyj >= yyLast {
		yystate = int(yyAct[yyg])
	} else {
		yystate = int(yyAct[yyj])
		if int(yyChk[yystate]) != -yyn {
			yystate = int(yyAct[yyg])
		}
	}
	// dummy call; replaced with literal code
	switch yynt {

	case 1:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:190
		{
			setParseTree(yylex, yyDollar[1].stmts)
		}
	case 2:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:196
		{
			yyVAL.stmts = []Statement{yyDollar[1].stmt}
		}
	case 3:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:200
		{
			if len(yyDollar[1].stmts) >= 1 {
				yyVAL.stmts = yyDollar[1].stmts
			} else {
				yylex.Error("excrescent semicolo")
			}
		}
	case 4:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:208
		{
			yyVAL.stmts = append(yyDollar[1].stmts, yyDollar[3].stmt)
		}
	case 5:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:216
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 6:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:220
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 7:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:224
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 8:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:228
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 9:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:232
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 10:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:236
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 11:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:240
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 12:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:244
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 13:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:248
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 14:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:252
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 15:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:256
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 16:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:260
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 17:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:264
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 18:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:268
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 19:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:272
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 20:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:276
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 21:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:280
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 22:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:284
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 23:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:288
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 24:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:292
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 25:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:296
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 26:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:300
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 27:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:304
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 28:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:308
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 29:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:312
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 30:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:316
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 31:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:320
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 32:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:324
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 33:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:328
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 34:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:332
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 35:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:336
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 36:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:340
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 37:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:344
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 38:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:348
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 39:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:352
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 40:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:356
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 41:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:360
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 42:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:364
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 43:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:368
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 44:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:372
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 45:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:376
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 46:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:380
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 47:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:384
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 48:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:388
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 49:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:392
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 50:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:396
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 51:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:400
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 52:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:404
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 53:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:408
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 54:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:412
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 55:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:416
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 56:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:420
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 57:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:424
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 58:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:428
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 59:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:432
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 60:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:436
		{
			yyVAL.stmt = yyDollar[1].stmt
		}
	case 61:
		yyDollar = yyS[yypt-11 : yypt+1]
//line sql.y:442
		{
			stmt := &SelectStatement{}
			stmt.Fields = yyDollar[2].fields
			stmt.Sources = yyDollar[4].sources
			stmt.Dimensions = yyDollar[6].dimens
			stmt.ExceptDimensions = yyDollar[7].dimens
			stmt.Condition = yyDollar[5].expr
			stmt.SortFields = yyDollar[9].sortfs
			stmt.Limit = yyDollar[10].intSlice[0]
			stmt.Offset = yyDollar[10].intSlice[1]
			stmt.SLimit = yyDollar[10].intSlice[2]
			stmt.SOffset = yyDollar[10].intSlice[3]

			tempfill, tempfillvalue, fillflag := deal_Fill(yyDollar[8].inter)
			if fillflag == false {
				yylex.Error("Invalid characters in fill")
			} else {
				stmt.Fill, stmt.FillValue = tempfill, tempfillvalue
			}
			stmt.IsRawQuery = true
			WalkFunc(stmt.Fields, func(n Node) {
				if _, ok := n.(*Call); ok {
					stmt.IsRawQuery = false
				}
			})
			stmt.Location = yyDollar[11].location
			if len(yyDollar[3].sources) > 1 {
				yylex.Error("into clause only support one measurement")
			} else if len(yyDollar[3].sources) == 1 {
				mst, ok := yyDollar[3].sources[0].(*Measurement)
				if !ok {
					yylex.Error("into clause only support measurement clause")
				}
				mst.IsTarget = true
				stmt.Target = &Target{
					Measurement: mst,
				}
			}
			yyVAL.stmt = stmt
		}
	case 62:
		yyDollar = yyS[yypt-12 : yypt+1]
//line sql.y:483
		{
			stmt := &SelectStatement{}
			stmt.Hints = yyDollar[2].hints
			stmt.Fields = yyDollar[3].fields
			stmt.Sources = yyDollar[5].sources
			stmt.Dimensions = yyDollar[7].dimens
			stmt.ExceptDimensions = yyDollar[8].dimens
			stmt.Condition = yyDollar[6].expr
			stmt.SortFields = yyDollar[10].sortfs
			stmt.Limit = yyDollar[11].intSlice[0]
			stmt.Offset = yyDollar[11].intSlice[1]
			stmt.SLimit = yyDollar[11].intSlice[2]
			stmt.SOffset = yyDollar[11].intSlice[3]

			tempfill, tempfillvalue, fillflag := deal_Fill(yyDollar[9].inter)
			if fillflag == false {
				yylex.Error("Invalid characters in fill")
			} else {
				stmt.Fill, stmt.FillValue = tempfill, tempfillvalue
			}
			stmt.IsRawQuery = true
			WalkFunc(stmt.Fields, func(n Node) {
				if _, ok := n.(*Call); ok {
					stmt.IsRawQuery = false
				}
			})
			stmt.Location = yyDollar[12].location
			if len(yyDollar[4].sources) > 1 {
				yylex.Error("into clause only support one measurement")
			} else if len(yyDollar[4].sources) == 1 {
				mst, ok := yyDollar[4].sources[0].(*Measurement)
				if !ok {
					yylex.Error("into clause only support measurement clause")
				}
				mst.IsTarget = true
				stmt.Target = &Target{
					Measurement: mst,
				}
			}
			yyVAL.stmt = stmt
		}
	case 63:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:525
		{
			stmt := &SelectStatement{}
			stmt.Fields = yyDollar[2].fields
			stmt.Dimensions = yyDollar[4].dimens
			stmt.ExceptDimensions = yyDollar[5].dimens
			stmt.Condition = yyDollar[3].expr
			stmt.SortFields = yyDollar[7].sortfs
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			stmt.SLimit = yyDollar[8].intSlice[2]
			stmt.SOffset = yyDollar[8].intSlice[3]

			tempfill, tempfillvalue, fillflag := deal_Fill(yyDollar[6].inter)
			if fillflag == false {
				yylex.Error("Invalid characters in fill")
			} else {
				stmt.Fill, stmt.FillValue = tempfill, tempfillvalue
			}
			stmt.IsRawQuery = true
			WalkFunc(stmt.Fields, func(n Node) {
				if _, ok := n.(*Call); ok {
					stmt.IsRawQuery = false
				}
			})
			stmt.Location = yyDollar[9].location
			yyVAL.stmt = stmt
		}
	case 64:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:556
		{
			yyVAL.fields = []*Field{yyDollar[1].field}
		}
	case 65:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:560
		{
			yyVAL.fields = append([]*Field{yyDollar[1].field}, yyDollar[3].fields...)
		}
	case 66:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:566
		{
			yyVAL.field = &Field{Expr: &Wildcard{Type: Token(yyDollar[1].int)}}
		}
	case 67:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:570
		{
			yyVAL.field = &Field{Expr: &Wildcard{Type: TAG}}
		}
	case 68:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:574
		{
			yyVAL.field = &Field{Expr: &Wildcard{Type: FIELD}}
		}
	case 69:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:578
		{
			yyVAL.field = &Field{Expr: yyDollar[1].expr}
		}
	case 70:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:582
		{
			yyVAL.field = &Field{Expr: yyDollar[1].expr, Alias: yyDollar[3].str}
		}
	case 71:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:586
		{
			yyVAL.field = &Field{Expr: yyDollar[1].expr, Alias: yyDollar[3].str}
		}
	case 72:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:592
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 73:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:596
		{
			c := yyDollar[1].expr.(*CaseWhenExpr)
			c.Conditions = append(c.Conditions, yyDollar[2].expr.(*CaseWhenExpr).Conditions...)
			c.Assigners = append(c.Assigners, yyDollar[2].expr.(*CaseWhenExpr).Assigners...)
			yyVAL.expr = c
		}
	case 74:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:605
		{
			c := &CaseWhenExpr{}
			c.Conditions = []Expr{yyDollar[2].expr}
			c.Assigners = []Expr{yyDollar[4].expr}
			yyVAL.expr = c
		}
	case 75:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:614
		{
			yyVAL.fields = []*Field{&Field{Expr: &VarRef{Val: yyDollar[1].str}}}
		}
	case 76:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:618
		{
			yyVAL.fields = append([]*Field{&Field{Expr: &VarRef{Val: yyDollar[1].str}}}, yyDollar[3].fields...)
		}
	case 77:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:624
		{
			yyVAL.expr = &BinaryExpr{Op: Token(MUL), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 78:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:628
		{
			yyVAL.expr = &BinaryExpr{Op: Token(DIV), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 79:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:632
		{
			yyVAL.expr = &BinaryExpr{Op: Token(ADD), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 80:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:636
		{
			yyVAL.expr = &BinaryExpr{Op: Token(SUB), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 81:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:640
		{
			yyVAL.expr = &BinaryExpr{Op: Token(BITWISE_XOR), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 82:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:644
		{
			yyVAL.expr = &BinaryExpr{Op: Token(MOD), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 83:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:648
		{
			yyVAL.expr = &BinaryExpr{Op: Token(BITWISE_AND), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 84:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:652
		{
			yyVAL.expr = &BinaryExpr{Op: Token(BITWISE_OR), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 85:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:656
		{
			yyVAL.expr = &ParenExpr{Expr: yyDollar[2].expr}
		}
	case 86:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:660
		{
			if strings.ToLower(yyDollar[1].str) == "cast" {
				if len(yyDollar[3].fields) != 1 {
					yylex.Error("The cast format is incorrect.")
				} else {
					name := "Unknown"
					if strings.ToLower(yyDollar[3].fields[0].Alias) == "bool" {
						name = "cast_bool"
					}
					if strings.ToLower(yyDollar[3].fields[0].Alias) == "float" {
						name = "cast_float64"
					}
					if strings.ToLower(yyDollar[3].fields[0].Alias) == "int" {
						name = "cast_int64"
					}
					if strings.ToLower(yyDollar[3].fields[0].Alias) == "string" {
						name = "cast_string"
					}
					cols := &Call{Name: strings.ToLower(name), Args: []Expr{}}
					cols.Args = append(cols.Args, yyDollar[3].fields[0].Expr)
					yyVAL.expr = cols
				}
			} else {
				cols := &Call{Name: strings.ToLower(yyDollar[1].str), Args: []Expr{}}
				for i := range yyDollar[3].fields {
					cols.Args = append(cols.Args, yyDollar[3].fields[i].Expr)
				}
				yyVAL.expr = cols
			}
		}
	case 87:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:691
		{
			cols := &Call{Name: strings.ToLower(yyDollar[1].str)}
			yyVAL.expr = cols
		}
	case 88:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:696
		{
			switch s := yyDollar[2].expr.(type) {
			case *NumberLiteral:
				s.Val = -1 * s.Val
				yyVAL.expr = yyDollar[2].expr
			case *IntegerLiteral:
				s.Val = -1 * s.Val
				yyVAL.expr = yyDollar[2].expr
			default:
				yyVAL.expr = &BinaryExpr{Op: Token(MUL), LHS: &IntegerLiteral{Val: -1}, RHS: yyDollar[2].expr}
			}

		}
	case 89:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:710
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 90:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:714
		{
			yyVAL.expr = &DurationLiteral{Val: yyDollar[1].tdur}
		}
	case 91:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:718
		{
			c := yyDollar[2].expr.(*CaseWhenExpr)
			c.Assigners = append(c.Assigners, yyDollar[4].expr)
			yyVAL.expr = c
		}
	case 92:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:724
		{
			yyVAL.expr = &VarRef{}
		}
	case 93:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:730
		{
			yyVAL.sources = yyDollar[2].sources
		}
	case 94:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:734
		{
			yyVAL.sources = nil
		}
	case 95:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:740
		{
			yyVAL.sources = yyDollar[2].sources
		}
	case 96:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:746
		{
			yyVAL.sources = []Source{yyDollar[1].ment}
		}
	case 97:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:750
		{
			yyVAL.sources = append([]Source{yyDollar[1].ment}, yyDollar[3].sources...)
		}
	case 98:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:754
		{
			yyVAL.sources = yyDollar[1].sources

		}
	case 99:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:759
		{
			yyVAL.sources = append(yyDollar[1].sources, yyDollar[3].sources...)
		}
	case 100:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:763
		{
			yyDollar[1].ment.Alias = yyDollar[3].str
			yyVAL.sources = []Source{yyDollar[1].ment}
		}
	case 101:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:768
		{
			yyDollar[1].ment.Alias = yyDollar[3].str
			yyVAL.sources = append([]Source{yyDollar[1].ment}, yyDollar[5].sources...)
		}
	case 102:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:773
		{
			yyVAL.sources = []Source{yyDollar[1].source}
		}
	case 103:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:779
		{
			join := &Join{}
			if len(yyDollar[1].sources) != 1 || len(yyDollar[4].sources) != 1 {
				yylex.Error("only support one query for join")
			}
			join.LSrc = yyDollar[1].sources[0]
			join.RSrc = yyDollar[4].sources[0]
			join.Condition = yyDollar[6].expr
			yyVAL.source = join
		}
	case 104:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:792
		{
			all_subquerys := []Source{}
			for _, temp_stmt := range yyDollar[2].stmts {
				stmt, ok := temp_stmt.(*SelectStatement)
				if !ok {
					yylex.Error("expexted SelectStatement")
				}
				build_SubQuery := &SubQuery{Statement: stmt}
				all_subquerys = append(all_subquerys, build_SubQuery)
			}
			yyVAL.sources = all_subquerys
		}
	case 105:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:805
		{
			if len(yyDollar[2].stmts) != 1 {
				yylex.Error("expexted SelectStatement length")
			}
			all_subquerys := []Source{}
			stmt, ok := yyDollar[2].stmts[0].(*SelectStatement)
			if !ok {
				yylex.Error("expexted SelectStatement")
			}
			build_SubQuery := &SubQuery{
				Statement: stmt,
				Alias:     yyDollar[5].str,
			}
			all_subquerys = append(all_subquerys, build_SubQuery)
			yyVAL.sources = all_subquerys
		}
	case 106:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:822
		{
			yyVAL.sources = yyDollar[2].sources
		}
	case 107:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:828
		{
			yyVAL.ment = yyDollar[1].ment
		}
	case 108:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:834
		{
			mst := yyDollar[5].ment
			mst.Database = yyDollar[1].str
			mst.RetentionPolicy = yyDollar[3].str
			yyVAL.ment = mst
		}
	case 109:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:841
		{
			mst := yyDollar[4].ment
			mst.RetentionPolicy = yyDollar[2].str
			yyVAL.ment = mst
		}
	case 110:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:847
		{
			mst := yyDollar[4].ment
			mst.Database = yyDollar[1].str
			yyVAL.ment = mst
		}
	case 111:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:853
		{
			mst := yyDollar[3].ment
			mst.RetentionPolicy = yyDollar[1].str
			yyVAL.ment = mst
		}
	case 112:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:859
		{
			yyVAL.ment = yyDollar[1].ment
		}
	case 113:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:865
		{
			yyVAL.ment = &Measurement{Name: yyDollar[1].str}
		}
	case 114:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:869
		{
			yyVAL.ment = &Measurement{Name: yyDollar[1].str}
		}
	case 115:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:873
		{
			re, err := regexp.Compile(yyDollar[1].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}

			yyVAL.ment = &Measurement{Regex: &RegexLiteral{Val: re}}
		}
	case 116:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:884
		{
			yyVAL.dimens = yyDollar[3].dimens
		}
	case 117:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:888
		{
			yyVAL.dimens = nil
		}
	case 118:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:894
		{
			yyVAL.dimens = yyDollar[2].dimens
		}
	case 119:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:898
		{
			yyVAL.dimens = nil
		}
	case 120:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:904
		{
			yyVAL.dimens = []*Dimension{yyDollar[1].dimen}
		}
	case 121:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:908
		{
			yyVAL.dimens = append([]*Dimension{yyDollar[1].dimen}, yyDollar[3].dimens...)
		}
	case 122:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:914
		{
			yyVAL.str = yyDollar[1].str
		}
	case 123:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:918
		{
			yyVAL.str = yyDollar[1].str
		}
	case 124:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:924
		{
			yyVAL.dimen = &Dimension{Expr: &VarRef{Val: yyDollar[1].str}}
		}
	case 125:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:928
		{
			yyVAL.dimen = &Dimension{Expr: &VarRef{Val: yyDollar[1].str}}
		}
	case 126:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:932
		{
			if strings.ToLower(yyDollar[1].str) != "time" {
				yylex.Error("Invalid group by combination for no-time tag and time duration")
			}

			yyVAL.dimen = &Dimension{Expr: &Call{Name: "time", Args: []Expr{&DurationLiteral{Val: yyDollar[3].tdur}}}}
		}
	case 127:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:940
		{
			if strings.ToLower(yyDollar[1].str) != "time" {
				yylex.Error("Invalid group by combination for no-time tag and time duration")
			}

			yyVAL.dimen = &Dimension{Expr: &Call{Name: "time", Args: []Expr{&DurationLiteral{Val: yyDollar[3].tdur}, &DurationLiteral{Val: yyDollar[5].tdur}}}}
		}
	case 128:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:948
		{
			if strings.ToLower(yyDollar[1].str) != "time" {
				yylex.Error("Invalid group by combination for no-time tag and time duration")
			}

			yyVAL.dimen = &Dimension{Expr: &Call{Name: "time", Args: []Expr{&DurationLiteral{Val: yyDollar[3].tdur}, &DurationLiteral{Val: time.Duration(-yyDollar[6].tdur)}}}}
		}
	case 129:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:956
		{
			yyVAL.dimen = &Dimension{Expr: &Wildcard{Type: Token(yyDollar[1].int)}}
		}
	case 130:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:960
		{
			yyVAL.dimen = &Dimension{Expr: &Wildcard{Type: Token(yyDollar[1].int)}}
		}
	case 131:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:964
		{
			re, err := regexp.Compile(yyDollar[1].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			yyVAL.dimen = &Dimension{Expr: &RegexLiteral{Val: re}}
		}
	case 132:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:975
		{
			if strings.ToLower(yyDollar[1].str) != "tz" {
				yylex.Error("Expect tz")
			}
			loc, err := time.LoadLocation(yyDollar[3].str)
			if err != nil {
				yylex.Error("nable to find time zone")
			}
			yyVAL.location = loc
		}
	case 133:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:986
		{
			yyVAL.location = nil
		}
	case 134:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:992
		{
			yyVAL.inter = yyDollar[3].inter
		}
	case 135:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:996
		{
			yyVAL.inter = "null"
		}
	case 136:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1002
		{
			yyVAL.inter = yyDollar[1].str
		}
	case 137:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1006
		{
			yyVAL.inter = yyDollar[1].int64
		}
	case 138:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1010
		{
			yyVAL.inter = yyDollar[1].float64
		}
	case 139:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1014
		{
			switch s := yyDollar[2].inter.(type) {
			case int64:
				yyVAL.inter = -1 * s
			case float64:
				yyVAL.inter = -1 * s
			default:
				yyVAL.inter = yyDollar[2].inter
			}
		}
	case 140:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1027
		{
			yyVAL.expr = yyDollar[2].expr
		}
	case 141:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:1031
		{
			yyVAL.expr = nil
		}
	case 142:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1037
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 143:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1041
		{
			yyVAL.expr = &BinaryExpr{Op: Token(yyDollar[2].int), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 144:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1047
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 145:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1051
		{
			yyVAL.expr = &BinaryExpr{Op: Token(yyDollar[2].int), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 146:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1057
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 147:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1061
		{
			yyVAL.expr = &ParenExpr{Expr: yyDollar[2].expr}
		}
	case 148:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1065
		{
			ident := &VarRef{Val: yyDollar[1].str}
			var expr, e Expr
			for i := range yyDollar[4].fields {
				expr = &BinaryExpr{LHS: ident, Op: Token(EQ), RHS: yyDollar[4].fields[i].Expr}
				if e == nil {
					e = expr
				} else {
					e = &BinaryExpr{LHS: e, Op: Token(OR), RHS: expr}
				}
			}
			yyVAL.expr = e
		}
	case 149:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1079
		{
			yyVAL.expr = &InCondition{Stmt: yyDollar[4].stmt.(*SelectStatement), Column: &VarRef{Val: yyDollar[1].str}}
		}
	case 150:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1083
		{
			yyVAL.expr = &BinaryExpr{}
		}
	case 151:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1087
		{
			yyVAL.expr = &BinaryExpr{}
		}
	case 152:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1091
		{
			yyVAL.expr = &BinaryExpr{}
		}
	case 153:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1095
		{
			yyVAL.expr = &BinaryExpr{}
		}
	case 154:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1099
		{
			yyVAL.expr = &BinaryExpr{
				LHS: &VarRef{Val: yyDollar[3].str},
				RHS: &StringLiteral{Val: yyDollar[5].str},
				Op:  MATCH,
			}
		}
	case 155:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1107
		{
			yyVAL.expr = &BinaryExpr{
				LHS: &VarRef{Val: yyDollar[3].str},
				RHS: &StringLiteral{Val: yyDollar[5].str},
				Op:  MATCHPHRASE,
			}
		}
	case 156:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1115
		{
			yyVAL.expr = &BinaryExpr{
				LHS: &VarRef{Val: yyDollar[3].str},
				RHS: &StringLiteral{Val: yyDollar[5].str},
				Op:  IPINRANGE,
			}
		}
	case 157:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1125
		{
			if yyDollar[2].int == NEQREGEX {
				switch yyDollar[3].expr.(type) {
				case *RegexLiteral:
				default:
					yylex.Error("expected regular expression")
				}
			}
			yyVAL.expr = &BinaryExpr{Op: Token(yyDollar[2].int), LHS: yyDollar[1].expr, RHS: yyDollar[3].expr}
		}
	case 158:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1138
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 159:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1142
		{
			yyVAL.expr = &ParenExpr{Expr: yyDollar[2].expr}
		}
	case 160:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1148
		{
			yyVAL.int = EQ
		}
	case 161:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1152
		{
			yyVAL.int = NEQ
		}
	case 162:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1156
		{
			yyVAL.int = LT
		}
	case 163:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1160
		{
			yyVAL.int = LTE
		}
	case 164:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1164
		{
			yyVAL.int = GT
		}
	case 165:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1168
		{
			yyVAL.int = GTE
		}
	case 166:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1172
		{
			yyVAL.int = EQREGEX
		}
	case 167:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1176
		{
			yyVAL.int = NEQREGEX
		}
	case 168:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1180
		{
			yyVAL.int = LIKE
		}
	case 169:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1186
		{
			yyVAL.str = yyDollar[1].str
		}
	case 170:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1192
		{
			yyVAL.expr = &VarRef{Val: yyDollar[1].str}
		}
	case 171:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1196
		{
			yyVAL.expr = &VarRef{Val: yyDollar[1].str, Type: yyDollar[3].dataType}
		}
	case 172:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1200
		{
			yyVAL.expr = &NumberLiteral{Val: yyDollar[1].float64}
		}
	case 173:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1204
		{
			yyVAL.expr = &IntegerLiteral{Val: yyDollar[1].int64}
		}
	case 174:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1208
		{
			yyVAL.expr = &StringLiteral{Val: yyDollar[1].str}
		}
	case 175:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1212
		{
			yyVAL.expr = &BooleanLiteral{Val: true}
		}
	case 176:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1216
		{
			yyVAL.expr = &BooleanLiteral{Val: false}
		}
	case 177:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1220
		{
			re, err := regexp.Compile(yyDollar[1].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			yyVAL.expr = &RegexLiteral{Val: re}
		}
	case 178:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1228
		{
			yyVAL.expr = &VarRef{Val: yyDollar[1].str + "." + yyDollar[3].str, Type: Tag}
		}
	case 179:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1232
		{
			yyVAL.expr = yyDollar[1].expr
		}
	case 180:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1238
		{
			switch strings.ToLower(yyDollar[1].str) {
			case "float":
				yyVAL.dataType = Float
			case "integer":
				yyVAL.dataType = Integer
			case "string":
				yyVAL.dataType = String
			case "boolean":
				yyVAL.dataType = Boolean
			case "time":
				yyVAL.dataType = Time
			case "duration":
				yyVAL.dataType = Duration
			case "unsigned":
				yyVAL.dataType = Unsigned
			default:
				yylex.Error("wrong field dataType")
			}
		}
	case 181:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1259
		{
			yyVAL.dataType = Tag
		}
	case 182:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1263
		{
			yyVAL.dataType = AnyField
		}
	case 183:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1269
		{
			yyVAL.sortfs = yyDollar[3].sortfs
		}
	case 184:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:1273
		{
			yyVAL.sortfs = nil
		}
	case 185:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1279
		{
			yyVAL.sortfs = []*SortField{yyDollar[1].sortf}
		}
	case 186:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1283
		{
			yyVAL.sortfs = append([]*SortField{yyDollar[1].sortf}, yyDollar[3].sortfs...)
		}
	case 187:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1289
		{
			yyVAL.sortf = &SortField{Name: yyDollar[1].str, Ascending: true}
		}
	case 188:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1293
		{
			yyVAL.sortf = &SortField{Name: yyDollar[1].str, Ascending: false}
		}
	case 189:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1297
		{
			yyVAL.sortf = &SortField{Name: yyDollar[1].str, Ascending: true}
		}
	case 190:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1303
		{
			yyVAL.intSlice = append(yyDollar[1].intSlice, yyDollar[2].intSlice...)
		}
	case 191:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1309
		{
			yyVAL.int64 = yyDollar[1].int64
		}
	case 192:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1314
		{
			if n, ok := yyDollar[1].expr.(*IntegerLiteral); ok {
				yyVAL.int64 = n.Val
			} else {
				yylex.Error("unsupported type, expect integer type")
			}
		}
	case 193:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1324
		{
			yyVAL.intSlice = []int{int(yyDollar[2].int64), int(yyDollar[4].int64)}
		}
	case 194:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1328
		{
			yyVAL.intSlice = []int{int(yyDollar[2].int64), 0}
		}
	case 195:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1332
		{
			yyVAL.intSlice = []int{0, int(yyDollar[2].int64)}
		}
	case 196:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:1336
		{
			yyVAL.intSlice = []int{0, 0}
		}
	case 197:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1342
		{
			yyVAL.intSlice = []int{int(yyDollar[2].int64), int(yyDollar[4].int64)}
		}
	case 198:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1346
		{
			yyVAL.intSlice = []int{int(yyDollar[2].int64), 0}
		}
	case 199:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1350
		{
			yyVAL.intSlice = []int{0, int(yyDollar[2].int64)}
		}
	case 200:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:1354
		{
			yyVAL.intSlice = []int{0, 0}
		}
	case 201:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1360
		{
			yyVAL.stmt = &ShowDatabasesStatement{ShowDetail: false}
		}
	case 202:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1364
		{
			yyVAL.stmt = &ShowDatabasesStatement{ShowDetail: true}
		}
	case 203:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1370
		{
			sms := yyDollar[4].stmt

			sms.(*CreateDatabaseStatement).Name = yyDollar[3].str
			sms.(*CreateDatabaseStatement).DatabaseAttr = yyDollar[5].databasePolicy
			yyVAL.stmt = sms
		}
	case 204:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1378
		{
			stmt := &CreateDatabaseStatement{}
			stmt.RetentionPolicyCreate = false
			stmt.Name = yyDollar[3].str
			stmt.DatabaseAttr = yyDollar[4].databasePolicy
			yyVAL.stmt = stmt
		}
	case 205:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1388
		{
			yyVAL.databasePolicy = DatabasePolicy{Replicas: uint32(yyDollar[2].int64), EnableTagArray: false}
		}
	case 206:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1393
		{
			yyVAL.databasePolicy = DatabasePolicy{EnableTagArray: yyDollar[1].bool}
		}
	case 207:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1398
		{
			yyVAL.databasePolicy = DatabasePolicy{Replicas: uint32(yyDollar[2].int64), EnableTagArray: yyDollar[3].bool}
		}
	case 208:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1403
		{
			yyVAL.databasePolicy = DatabasePolicy{Replicas: uint32(yyDollar[3].int64), EnableTagArray: yyDollar[1].bool}
		}
	case 209:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:1407
		{
			yyVAL.databasePolicy = DatabasePolicy{EnableTagArray: false}
		}
	case 210:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1413
		{
			if strings.ToLower(yyDollar[3].str) != "array" {
				yylex.Error("unsupport type")
			}
			yyVAL.bool = true
		}
	case 211:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1420
		{
			yyVAL.bool = false
		}
	case 212:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1427
		{
			stmt := &CreateDatabaseStatement{}
			stmt.RetentionPolicyCreate = true
			stmt.RetentionPolicyDuration = yyDollar[2].durations.PolicyDuration
			stmt.RetentionPolicyReplication = yyDollar[2].durations.Replication
			stmt.RetentionPolicyName = yyDollar[2].durations.PolicyName
			stmt.ShardKey = yyDollar[2].durations.ShardKey
			sort.Strings(stmt.ShardKey)

			if yyDollar[2].durations.rpdefault == true {
				yylex.Error("no default")
			}

			if yyDollar[2].durations.ShardGroupDuration == -1 || yyDollar[2].durations.ShardGroupDuration == 0 {
				stmt.RetentionPolicyShardGroupDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.RetentionPolicyShardGroupDuration = yyDollar[2].durations.ShardGroupDuration
			}

			if yyDollar[2].durations.HotDuration == -1 || yyDollar[2].durations.HotDuration == 0 {
				stmt.RetentionPolicyHotDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.RetentionPolicyHotDuration = yyDollar[2].durations.HotDuration
			}

			if yyDollar[2].durations.WarmDuration == -1 || yyDollar[2].durations.WarmDuration == 0 {
				stmt.RetentionPolicyWarmDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.RetentionPolicyWarmDuration = yyDollar[2].durations.WarmDuration
			}

			if yyDollar[2].durations.IndexGroupDuration == -1 || yyDollar[2].durations.IndexGroupDuration == 0 {
				stmt.RetentionPolicyIndexGroupDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.RetentionPolicyIndexGroupDuration = yyDollar[2].durations.IndexGroupDuration
			}
			yyVAL.stmt = stmt
		}
	case 213:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1470
		{
			yyVAL.durations = yyDollar[1].durations
		}
	case 214:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1474
		{
			if yyDollar[1].durations.ShardGroupDuration < 0 || yyDollar[2].durations.ShardGroupDuration < 0 {
				if yyDollar[2].durations.ShardGroupDuration >= 0 {
					yyDollar[1].durations.ShardGroupDuration = yyDollar[2].durations.ShardGroupDuration
				}
			} else {
				yylex.Error("Repeat Shard Group Duration")
			}

			if len(yyDollar[1].durations.ShardKey) != 0 && len(yyDollar[2].durations.ShardKey) != 0 {
				yylex.Error("Repeat ShardKey")
			} else if len(yyDollar[2].durations.ShardKey) != 0 {
				yyDollar[1].durations.ShardKey = yyDollar[2].durations.ShardKey
			}

			if yyDollar[1].durations.HotDuration < 0 || yyDollar[2].durations.HotDuration < 0 {
				if yyDollar[2].durations.HotDuration >= 0 {
					yyDollar[1].durations.HotDuration = yyDollar[2].durations.HotDuration
				}
			} else {
				yylex.Error("Repeat Hot Duration")
			}

			if yyDollar[1].durations.WarmDuration < 0 || yyDollar[2].durations.WarmDuration < 0 {
				if yyDollar[2].durations.WarmDuration >= 0 {
					yyDollar[1].durations.WarmDuration = yyDollar[2].durations.WarmDuration
				}
			} else {
				yylex.Error("Repeat Warm Duration")
			}

			if yyDollar[1].durations.IndexGroupDuration < 0 || yyDollar[2].durations.IndexGroupDuration < 0 {
				if yyDollar[2].durations.IndexGroupDuration >= 0 {
					yyDollar[1].durations.IndexGroupDuration = yyDollar[2].durations.IndexGroupDuration
				}
			} else {
				yylex.Error("Repeat Index Group Duration")
			}

			if yyDollar[1].durations.PolicyDuration == nil || yyDollar[2].durations.PolicyDuration == nil {
				if yyDollar[2].durations.PolicyDuration != nil {
					yyDollar[1].durations.PolicyDuration = yyDollar[2].durations.PolicyDuration
				}
			} else {
				yylex.Error("Repeat Policy Duration")
			}

			if yyDollar[1].durations.Replication == nil || yyDollar[2].durations.Replication == nil {
				if yyDollar[2].durations.Replication != nil {
					yyDollar[1].durations.Replication = yyDollar[2].durations.Replication
				}
			} else {
				yylex.Error("Repeat Policy Replication")
			}

			if len(yyDollar[1].durations.PolicyName) == 0 || len(yyDollar[2].durations.PolicyName) == 0 {
				if len(yyDollar[2].durations.PolicyName) != 0 {
					yyDollar[1].durations.PolicyName = yyDollar[2].durations.PolicyName
				}
			} else {
				yylex.Error("Repeat Policy Name")
			}

			if yyDollar[1].durations.rpdefault == false || yyDollar[2].durations.rpdefault == false {
				if yyDollar[2].durations.rpdefault == true {
					yyDollar[1].durations.rpdefault = yyDollar[2].durations.rpdefault
				}
			} else {
				yylex.Error("Repeat rpdefault")
			}
			yyVAL.durations = yyDollar[1].durations
		}
	case 215:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1549
		{
			yyVAL.durations = yyDollar[1].durations
		}
	case 216:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1553
		{
			duration := yyDollar[2].tdur
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, PolicyDuration: &duration}
		}
	case 217:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1558
		{
			replicaN := int(yyDollar[2].int64)
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, Replication: &replicaN}
		}
	case 218:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1563
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, PolicyName: yyDollar[2].str}
		}
	case 219:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1567
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, ReplicaNum: uint32(yyDollar[2].int64)}
		}
	case 220:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1571
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, rpdefault: true}
		}
	case 221:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1575
		{
			if len(yyDollar[2].strSlice) == 0 {
				yylex.Error("ShardKey should not be nil")
			}
			yyVAL.durations = &Durations{ShardKey: yyDollar[2].strSlice, ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1, rpdefault: false}
		}
	case 222:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:1586
		{
			sms := &ShowMeasurementsStatement{}
			sms.Database = yyDollar[3].str
			sms.Source = yyDollar[6].ment
			sms.Condition = yyDollar[7].expr
			sms.SortFields = yyDollar[8].sortfs
			sms.Limit = yyDollar[9].intSlice[0]
			sms.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = sms
		}
	case 223:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1597
		{
			sms := &ShowMeasurementsStatement{}
			sms.Database = yyDollar[3].str
			sms.Condition = yyDollar[4].expr
			sms.SortFields = yyDollar[5].sortfs
			sms.Limit = yyDollar[6].intSlice[0]
			sms.Offset = yyDollar[6].intSlice[1]
			yyVAL.stmt = sms
		}
	case 224:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1609
		{
			sms := &ShowMeasurementsDetailStatement{}
			sms.Database = yyDollar[4].str
			sms.Source = yyDollar[7].ment
			yyVAL.stmt = sms
		}
	case 225:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1616
		{
			sms := &ShowMeasurementsDetailStatement{}
			sms.Database = yyDollar[4].str
			yyVAL.stmt = sms
		}
	case 226:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1625
		{
			yyVAL.ment = &Measurement{Name: yyDollar[2].str}
		}
	case 227:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1629
		{
			yyVAL.ment = &Measurement{Name: yyDollar[2].str}
		}
	case 228:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1633
		{
			re, err := regexp.Compile(yyDollar[2].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			yyVAL.ment = &Measurement{Regex: &RegexLiteral{Val: re}}
		}
	case 229:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1641
		{
			re, err := regexp.Compile(yyDollar[2].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			yyVAL.ment = &Measurement{Regex: &RegexLiteral{Val: re}}
		}
	case 230:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1653
		{
			yyVAL.stmt = &ShowRetentionPoliciesStatement{
				Database: yyDollar[5].str,
			}
		}
	case 231:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1659
		{
			yyVAL.stmt = &ShowRetentionPoliciesStatement{}
		}
	case 232:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1666
		{
			stmt := yyDollar[7].stmt.(*CreateRetentionPolicyStatement)
			stmt.Name = yyDollar[4].str
			stmt.Database = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 233:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:1673
		{
			stmt := yyDollar[7].stmt.(*CreateRetentionPolicyStatement)
			stmt.Name = yyDollar[4].str
			stmt.Database = yyDollar[6].str
			stmt.Default = true
			yyVAL.stmt = stmt
		}
	case 234:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1683
		{
			stmt := &CreateUserStatement{}
			stmt.Name = yyDollar[3].str
			stmt.Password = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 235:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:1690
		{
			stmt := &CreateUserStatement{}
			stmt.Name = yyDollar[3].str
			stmt.Password = yyDollar[6].str
			stmt.Admin = true
			yyVAL.stmt = stmt
		}
	case 236:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:1698
		{
			stmt := &CreateUserStatement{}
			stmt.Name = yyDollar[3].str
			stmt.Password = yyDollar[6].str
			stmt.Rwuser = true
			yyVAL.stmt = stmt
		}
	case 237:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1709
		{
			stmt := &CreateRetentionPolicyStatement{}
			stmt.Duration = yyDollar[2].tdur
			stmt.Replication = int(yyDollar[4].int64)

			if yyDollar[5].durations.ShardGroupDuration == -1 || yyDollar[5].durations.ShardGroupDuration == 0 {
				stmt.ShardGroupDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.ShardGroupDuration = yyDollar[5].durations.ShardGroupDuration
			}

			if yyDollar[5].durations.HotDuration == -1 || yyDollar[5].durations.HotDuration == 0 {
				stmt.HotDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.HotDuration = yyDollar[5].durations.HotDuration
			}

			if yyDollar[5].durations.WarmDuration == -1 || yyDollar[5].durations.WarmDuration == 0 {
				stmt.WarmDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.WarmDuration = yyDollar[5].durations.WarmDuration
			}

			if yyDollar[5].durations.IndexGroupDuration == -1 || yyDollar[5].durations.IndexGroupDuration == 0 {
				stmt.IndexGroupDuration = time.Duration(DefaultQueryTimeout)
			} else {
				stmt.IndexGroupDuration = yyDollar[5].durations.IndexGroupDuration
			}

			yyVAL.stmt = stmt
		}
	case 238:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1741
		{
			stmt := &CreateRetentionPolicyStatement{}
			stmt.Duration = yyDollar[2].tdur
			stmt.Replication = int(yyDollar[4].int64)
			yyVAL.stmt = stmt
		}
	case 239:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:1751
		{
			yyVAL.durations = yyDollar[1].durations
		}
	case 240:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1755
		{
			if yyDollar[1].durations.ShardGroupDuration < 0 || yyDollar[2].durations.ShardGroupDuration < 0 {
				if yyDollar[2].durations.ShardGroupDuration >= 0 {
					yyDollar[1].durations.ShardGroupDuration = yyDollar[2].durations.ShardGroupDuration
				}
			} else {
				yylex.Error("Repeat Shard Group Duration")
			}

			if yyDollar[1].durations.HotDuration < 0 || yyDollar[2].durations.HotDuration < 0 {
				if yyDollar[2].durations.HotDuration >= 0 {
					yyDollar[1].durations.HotDuration = yyDollar[2].durations.HotDuration
				}
			} else {
				yylex.Error("Repeat Hot Duration")
			}

			if yyDollar[1].durations.WarmDuration < 0 || yyDollar[2].durations.WarmDuration < 0 {
				if yyDollar[2].durations.WarmDuration >= 0 {
					yyDollar[1].durations.WarmDuration = yyDollar[2].durations.WarmDuration
				}
			} else {
				yylex.Error("Repeat Warm Duration")
			}

			if yyDollar[1].durations.IndexGroupDuration < 0 || yyDollar[2].durations.IndexGroupDuration < 0 {
				if yyDollar[2].durations.IndexGroupDuration >= 0 {
					yyDollar[1].durations.IndexGroupDuration = yyDollar[2].durations.IndexGroupDuration
				}
			} else {
				yylex.Error("Repeat Index Group Duration")
			}
			yyVAL.durations = yyDollar[1].durations
		}
	case 241:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1793
		{
			yyVAL.durations = &Durations{ShardGroupDuration: yyDollar[3].tdur, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: -1}
		}
	case 242:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1797
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: yyDollar[3].tdur, WarmDuration: -1, IndexGroupDuration: -1}
		}
	case 243:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1801
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: yyDollar[3].tdur, IndexGroupDuration: -1}
		}
	case 244:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1805
		{
			yyVAL.durations = &Durations{ShardGroupDuration: -1, HotDuration: -1, WarmDuration: -1, IndexGroupDuration: yyDollar[3].tdur}
		}
	case 245:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1813
		{
			stmt := &ShowSeriesStatement{}
			stmt.Database = yyDollar[3].str
			stmt.Sources = yyDollar[4].sources
			stmt.Condition = yyDollar[5].expr
			stmt.SortFields = yyDollar[6].sortfs
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 246:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1824
		{
			stmt := &ShowSeriesStatement{}
			stmt.Database = yyDollar[3].str
			stmt.Condition = yyDollar[4].expr
			stmt.SortFields = yyDollar[5].sortfs
			stmt.Limit = yyDollar[6].intSlice[0]
			stmt.Offset = yyDollar[6].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 247:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:1834
		{
			stmt := &ShowSeriesStatement{}
			stmt.Hints = yyDollar[2].hints
			stmt.Database = yyDollar[4].str
			stmt.Sources = yyDollar[5].sources
			stmt.Condition = yyDollar[6].expr
			stmt.SortFields = yyDollar[7].sortfs
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 248:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1846
		{
			stmt := &ShowSeriesStatement{}
			stmt.Hints = yyDollar[2].hints
			stmt.Database = yyDollar[4].str
			stmt.Condition = yyDollar[5].expr
			stmt.SortFields = yyDollar[6].sortfs
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 249:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1859
		{
			yyVAL.stmt = &ShowUsersStatement{}
		}
	case 250:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1865
		{
			stmt := &DropDatabaseStatement{}
			stmt.Name = yyDollar[3].str
			yyVAL.stmt = stmt
		}
	case 251:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1873
		{
			stmt := &DropSeriesStatement{}
			stmt.Sources = yyDollar[3].sources
			stmt.Condition = yyDollar[4].expr
			yyVAL.stmt = stmt
		}
	case 252:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1880
		{
			stmt := &DropSeriesStatement{}
			stmt.Condition = yyDollar[3].expr
			yyVAL.stmt = stmt
		}
	case 253:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:1888
		{
			stmt := &DeleteSeriesStatement{}
			stmt.Sources = yyDollar[2].sources
			stmt.Condition = yyDollar[3].expr
			yyVAL.stmt = stmt
		}
	case 254:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:1895
		{
			stmt := &DeleteSeriesStatement{}
			stmt.Condition = yyDollar[2].expr
			yyVAL.stmt = stmt
		}
	case 255:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1904
		{
			stmt := &AlterRetentionPolicyStatement{}
			stmt.Name = yyDollar[4].str
			stmt.Database = yyDollar[6].str
			stmt.Duration = yyDollar[7].durations.PolicyDuration
			stmt.Replication = yyDollar[7].durations.Replication
			stmt.Default = yyDollar[7].durations.rpdefault
			if yyDollar[7].durations.ShardGroupDuration == -1 {
				stmt.ShardGroupDuration = nil
			} else {
				stmt.ShardGroupDuration = &yyDollar[7].durations.ShardGroupDuration
			}
			if yyDollar[7].durations.HotDuration == -1 {
				stmt.HotDuration = nil
			} else {
				stmt.HotDuration = &yyDollar[7].durations.HotDuration
			}
			if yyDollar[7].durations.WarmDuration == -1 {
				stmt.WarmDuration = nil
			} else {
				stmt.WarmDuration = &yyDollar[7].durations.WarmDuration
			}
			if yyDollar[7].durations.IndexGroupDuration == -1 {
				stmt.IndexGroupDuration = nil
			} else {
				stmt.IndexGroupDuration = &yyDollar[7].durations.IndexGroupDuration
			}

			if len(yyDollar[7].durations.PolicyName) > 0 || yyDollar[7].durations.ReplicaNum != 0 {
				yylex.Error("PolicyName and ReplicaNum")
			}
			yyVAL.stmt = stmt
		}
	case 256:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1942
		{
			stmt := &DropRetentionPolicyStatement{}
			stmt.Name = yyDollar[4].str
			stmt.Database = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 257:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1951
		{
			stmt := &GrantStatement{}
			stmt.Privilege = AllPrivileges
			stmt.On = yyDollar[4].str
			stmt.User = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 258:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:1959
		{
			stmt := &GrantStatement{}
			stmt.Privilege = AllPrivileges
			stmt.On = yyDollar[5].str
			stmt.User = yyDollar[7].str
			yyVAL.stmt = stmt
		}
	case 259:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1967
		{
			stmt := &GrantStatement{}
			switch strings.ToLower(yyDollar[2].str) {
			case "read":
				stmt.Privilege = ReadPrivilege
			case "write":
				stmt.Privilege = WritePrivilege
			default:
				yylex.Error("wrong Privilege")
			}
			stmt.On = yyDollar[4].str
			stmt.User = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 260:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:1984
		{
			yyVAL.stmt = &GrantAdminStatement{User: yyDollar[5].str}
		}
	case 261:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:1988
		{
			yyVAL.stmt = &GrantAdminStatement{User: yyDollar[4].str}
		}
	case 262:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:1994
		{
			stmt := &RevokeStatement{}
			stmt.Privilege = AllPrivileges
			stmt.On = yyDollar[4].str
			stmt.User = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 263:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:2002
		{
			stmt := &RevokeStatement{}
			stmt.Privilege = AllPrivileges
			stmt.On = yyDollar[5].str
			stmt.User = yyDollar[7].str
			yyVAL.stmt = stmt
		}
	case 264:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:2010
		{
			stmt := &RevokeStatement{}
			switch strings.ToLower(yyDollar[2].str) {
			case "read":
				stmt.Privilege = ReadPrivilege
			case "write":
				stmt.Privilege = WritePrivilege
			default:
				yylex.Error("wrong Privilege")
			}
			stmt.On = yyDollar[4].str
			stmt.User = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 265:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:2027
		{
			yyVAL.stmt = &RevokeAdminStatement{User: yyDollar[5].str}
		}
	case 266:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:2031
		{
			yyVAL.stmt = &RevokeAdminStatement{User: yyDollar[4].str}
		}
	case 267:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2037
		{
			yyVAL.stmt = &DropUserStatement{Name: yyDollar[3].str}
		}
	case 268:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:2043
		{
			stmt := &ShowTagKeysStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Sources = yyDollar[5].sources
			stmt.Condition = yyDollar[6].expr
			stmt.SortFields = yyDollar[7].sortfs
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			stmt.SLimit = yyDollar[8].intSlice[2]
			stmt.SOffset = yyDollar[8].intSlice[3]
			yyVAL.stmt = stmt

		}
	case 269:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:2057
		{
			stmt := &ShowTagKeysStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Condition = yyDollar[5].expr
			stmt.SortFields = yyDollar[6].sortfs
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			stmt.SLimit = yyDollar[7].intSlice[2]
			stmt.SOffset = yyDollar[7].intSlice[3]
			yyVAL.stmt = stmt
		}
	case 270:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2071
		{
			yyVAL.str = "PRIMARYKEY"
		}
	case 271:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2075
		{
			yyVAL.str = "SORTKEY"
		}
	case 272:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2079
		{
			yyVAL.str = "PROPERTY"
		}
	case 273:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2083
		{
			yyVAL.str = "SHARDKEY"
		}
	case 274:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2087
		{
			yyVAL.str = "ENGINETYPE"
		}
	case 275:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2091
		{
			yyVAL.str = "SCHEMA"
		}
	case 276:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2095
		{
			yyVAL.str = "INDEXES"
		}
	case 277:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2099
		{
			yyVAL.str = "COMPACT"
		}
	case 278:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2103
		{
			yylex.Error("SHOW command error, only support PRIMARYKEY, SORTKEY, SHARDKEY, ENGINETYPE, INDEXES, SCHEMA, COMPACT")
		}
	case 279:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:2109
		{
			stmt := &ShowMeasurementKeysStatement{}
			stmt.Name = yyDollar[2].str
			stmt.Measurement = yyDollar[4].str
			yyVAL.stmt = stmt
		}
	case 280:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:2116
		{
			stmt := &ShowMeasurementKeysStatement{}
			stmt.Name = yyDollar[2].str
			stmt.Database = yyDollar[4].str
			stmt.Rp = yyDollar[6].str
			stmt.Measurement = yyDollar[8].str
			yyVAL.stmt = stmt
		}
	case 281:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:2125
		{
			stmt := &ShowMeasurementKeysStatement{}
			stmt.Name = yyDollar[2].str
			stmt.Database = yyDollar[4].str
			stmt.Measurement = yyDollar[7].str
			yyVAL.stmt = stmt
		}
	case 282:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:2133
		{
			stmt := &ShowMeasurementKeysStatement{}
			stmt.Name = yyDollar[2].str
			stmt.Rp = yyDollar[5].str
			stmt.Measurement = yyDollar[7].str
			yyVAL.stmt = stmt
		}
	case 283:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:2141
		{
			stmt := &ShowMeasurementKeysStatement{}
			stmt.Name = yyDollar[2].str
			stmt.Measurement = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 284:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2150
		{
			yyVAL.str = yyDollar[2].str
		}
	case 285:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2154
		{
			yyVAL.str = ""
		}
	case 286:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:2160
		{
			stmt := &ShowFieldKeysStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Sources = yyDollar[5].sources
			stmt.SortFields = yyDollar[6].sortfs
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 287:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:2170
		{
			stmt := &ShowFieldKeysStatement{}
			stmt.Database = yyDollar[4].str
			stmt.SortFields = yyDollar[5].sortfs
			stmt.Limit = yyDollar[6].intSlice[0]
			stmt.Offset = yyDollar[6].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 288:
		yyDollar = yyS[yypt-11 : yypt+1]
//line sql.y:2182
		{
			stmt := yyDollar[8].stmt.(*ShowTagValuesStatement)
			stmt.TagKeyCondition = nil
			stmt.Database = yyDollar[4].str
			stmt.Sources = yyDollar[5].sources
			stmt.Condition = yyDollar[9].expr
			stmt.SortFields = yyDollar[10].sortfs
			stmt.Limit = yyDollar[11].intSlice[0]
			stmt.Offset = yyDollar[11].intSlice[1]
			yyVAL.stmt = stmt

		}
	case 289:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:2195
		{
			stmt := yyDollar[7].stmt.(*ShowTagValuesStatement)
			stmt.TagKeyCondition = nil
			stmt.Database = yyDollar[4].str
			stmt.Condition = yyDollar[8].expr
			stmt.SortFields = yyDollar[9].sortfs
			stmt.Limit = yyDollar[10].intSlice[0]
			stmt.Offset = yyDollar[10].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 290:
		yyDollar = yyS[yypt-12 : yypt+1]
//line sql.y:2206
		{
			stmt := yyDollar[9].stmt.(*ShowTagValuesStatement)
			stmt.Hints = yyDollar[2].hints
			stmt.TagKeyCondition = nil
			stmt.Database = yyDollar[5].str
			stmt.Sources = yyDollar[6].sources
			stmt.Condition = yyDollar[10].expr
			stmt.SortFields = yyDollar[11].sortfs
			stmt.Limit = yyDollar[12].intSlice[0]
			stmt.Offset = yyDollar[12].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 291:
		yyDollar = yyS[yypt-11 : yypt+1]
//line sql.y:2219
		{
			stmt := yyDollar[8].stmt.(*ShowTagValuesStatement)
			stmt.Hints = yyDollar[2].hints
			stmt.TagKeyCondition = nil
			stmt.Database = yyDollar[5].str
			stmt.Condition = yyDollar[9].expr
			stmt.SortFields = yyDollar[10].sortfs
			stmt.Limit = yyDollar[11].intSlice[0]
			stmt.Offset = yyDollar[11].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 292:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2233
		{
			stmt := &ShowTagValuesStatement{}
			stmt.Op = EQ
			stmt.TagKeyExpr = yyDollar[2].expr.(*ListLiteral)
			yyVAL.stmt = stmt
		}
	case 293:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2240
		{
			stmt := &ShowTagValuesStatement{}
			stmt.Op = NEQ
			stmt.TagKeyExpr = yyDollar[2].expr.(*ListLiteral)
			yyVAL.stmt = stmt
		}
	case 294:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:2247
		{
			stmt := &ShowTagValuesStatement{}
			stmt.Op = IN
			stmt.TagKeyExpr = yyDollar[3].expr.(*ListLiteral)
			yyVAL.stmt = stmt
		}
	case 295:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2254
		{
			stmt := &ShowTagValuesStatement{}
			stmt.Op = EQREGEX
			re, err := regexp.Compile(yyDollar[2].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			stmt.TagKeyExpr = &RegexLiteral{Val: re}
			yyVAL.stmt = stmt
		}
	case 296:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2265
		{
			stmt := &ShowTagValuesStatement{}
			stmt.Op = NEQREGEX
			re, err := regexp.Compile(yyDollar[2].str)
			if err != nil {
				yylex.Error("Invalid regexprs")
			}
			stmt.TagKeyExpr = &RegexLiteral{Val: re}
			yyVAL.stmt = stmt
		}
	case 297:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2279
		{
			temp := []string{yyDollar[1].str}
			yyVAL.expr = &ListLiteral{Vals: temp}
		}
	case 298:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2284
		{
			yyDollar[3].expr.(*ListLiteral).Vals = append(yyDollar[3].expr.(*ListLiteral).Vals, yyDollar[1].str)
			yyVAL.expr = yyDollar[3].expr
		}
	case 299:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2291
		{
			yyVAL.str = yyDollar[1].str
		}
	case 300:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2299
		{
			stmt := &ExplainStatement{}
			stmt.Statement = yyDollar[3].stmt.(*SelectStatement)
			stmt.Analyze = true
			yyVAL.stmt = stmt
		}
	case 301:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2306
		{
			stmt := &ExplainStatement{}
			stmt.Statement = yyDollar[2].stmt.(*SelectStatement)
			stmt.Analyze = false
			yyVAL.stmt = stmt
		}
	case 302:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:2316
		{
			stmt := &ShowTagKeyCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt.Sources = yyDollar[7].sources
			stmt.Condition = yyDollar[8].expr
			stmt.Dimensions = yyDollar[9].dimens
			stmt.Limit = yyDollar[10].intSlice[0]
			stmt.Offset = yyDollar[10].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 303:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:2328
		{
			stmt := &ShowTagKeyCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 304:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:2339
		{
			stmt := &ShowTagKeyCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = false
			stmt.Sources = yyDollar[6].sources
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 305:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:2351
		{
			stmt := &ShowTagKeyCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 306:
		yyDollar = yyS[yypt-13 : yypt+1]
//line sql.y:2367
		{
			stmt := &ShowTagValuesCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt.Sources = yyDollar[7].sources
			stmt_temp := yyDollar[10].stmt.(*ShowTagValuesStatement)
			stmt.Op = stmt_temp.Op
			stmt.TagKeyExpr = stmt_temp.TagKeyExpr
			stmt.Condition = yyDollar[11].expr
			stmt.Dimensions = yyDollar[12].dimens
			stmt.Limit = yyDollar[13].intSlice[0]
			stmt.Offset = yyDollar[13].intSlice[1]
			stmt.TagKeyCondition = nil
			yyVAL.stmt = stmt

		}
	case 307:
		yyDollar = yyS[yypt-12 : yypt+1]
//line sql.y:2384
		{
			stmt := &ShowTagValuesCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt_temp := yyDollar[9].stmt.(*ShowTagValuesStatement)
			stmt.Op = stmt_temp.Op
			stmt.TagKeyExpr = stmt_temp.TagKeyExpr
			stmt.Condition = yyDollar[10].expr
			stmt.Dimensions = yyDollar[11].dimens
			stmt.Limit = yyDollar[12].intSlice[0]
			stmt.Offset = yyDollar[12].intSlice[1]
			stmt.TagKeyCondition = nil
			yyVAL.stmt = stmt
		}
	case 308:
		yyDollar = yyS[yypt-12 : yypt+1]
//line sql.y:2399
		{
			stmt := &ShowTagValuesCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = false
			stmt.Sources = yyDollar[6].sources
			stmt_temp := yyDollar[9].stmt.(*ShowTagValuesStatement)
			stmt.Op = stmt_temp.Op
			stmt.TagKeyExpr = stmt_temp.TagKeyExpr
			stmt.Condition = yyDollar[10].expr
			stmt.Dimensions = yyDollar[11].dimens
			stmt.Limit = yyDollar[12].intSlice[0]
			stmt.Offset = yyDollar[12].intSlice[1]
			stmt.TagKeyCondition = nil
			yyVAL.stmt = stmt

		}
	case 309:
		yyDollar = yyS[yypt-11 : yypt+1]
//line sql.y:2416
		{
			stmt := &ShowTagValuesCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt_temp := yyDollar[8].stmt.(*ShowTagValuesStatement)
			stmt.Op = stmt_temp.Op
			stmt.TagKeyExpr = stmt_temp.TagKeyExpr
			stmt.Condition = yyDollar[9].expr
			stmt.Dimensions = yyDollar[10].dimens
			stmt.Limit = yyDollar[11].intSlice[0]
			stmt.Offset = yyDollar[11].intSlice[1]
			stmt.TagKeyCondition = nil
			yyVAL.stmt = stmt
		}
	case 310:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:2434
		{
			stmt := &ShowFieldKeyCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt.Sources = yyDollar[7].sources
			stmt.Condition = yyDollar[8].expr
			stmt.Dimensions = yyDollar[9].dimens
			stmt.Limit = yyDollar[10].intSlice[0]
			stmt.Offset = yyDollar[10].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 311:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:2446
		{
			stmt := &ShowFieldKeyCardinalityStatement{}
			stmt.Database = yyDollar[6].str
			stmt.Exact = true
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 312:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:2457
		{
			stmt := &ShowFieldKeyCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = false
			stmt.Sources = yyDollar[6].sources
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 313:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:2469
		{
			stmt := &ShowFieldKeyCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 314:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:2483
		{
			stmt := &CreateMeasurementStatement{}
			stmt.Database = yyDollar[3].ment.Database
			stmt.Name = yyDollar[3].ment.Name
			stmt.RetentionPolicy = yyDollar[3].ment.RetentionPolicy
			if yyDollar[4].stmt != nil {
				stmt.Fields = yyDollar[4].stmt.(*CreateMeasurementStatement).Fields
				stmt.Tags = yyDollar[4].stmt.(*CreateMeasurementStatement).Tags
				stmt.IndexOption = yyDollar[4].stmt.(*CreateMeasurementStatement).IndexOption
			}
			if yyDollar[5].cmOption.NumOfShards != 0 && yyDollar[5].cmOption.Type == "range" {
				yylex.Error("Not support to set num-of-shards for range sharding")
			}
			stmt.IndexType = yyDollar[5].cmOption.IndexType
			stmt.IndexList = yyDollar[5].cmOption.IndexList
			stmt.ShardKey = yyDollar[5].cmOption.ShardKey
			stmt.NumOfShards = yyDollar[5].cmOption.NumOfShards
			stmt.Type = yyDollar[5].cmOption.Type
			stmt.EngineType = yyDollar[5].cmOption.EngineType

			yyVAL.stmt = stmt
		}
	case 315:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:2506
		{
			stmt := &CreateMeasurementStatement{}
			stmt.Database = yyDollar[3].ment.Database
			stmt.Name = yyDollar[3].ment.Name
			stmt.RetentionPolicy = yyDollar[3].ment.RetentionPolicy
			if yyDollar[4].stmt != nil {
				stmt.Fields = yyDollar[4].stmt.(*CreateMeasurementStatement).Fields
				stmt.Tags = yyDollar[4].stmt.(*CreateMeasurementStatement).Tags
				stmt.IndexOption = yyDollar[4].stmt.(*CreateMeasurementStatement).IndexOption
			}

			// check if PrimaryKey & SortKey is IN Tags/Fields/time
			for _, key := range yyDollar[5].cmOption.PrimaryKey {
				_, inTag := stmt.Tags[key]
				_, inField := stmt.Fields[key]
				if !inTag && !inField && key != "time" {
					if len(yyDollar[5].cmOption.PrimaryKey) != len(yyDollar[5].cmOption.SortKey) {
						yylex.Error("Invalid PrimaryKey")
					} else {
						yylex.Error("Invalid PrimaryKey/SortKey")
					}
					return 1
				}
			}
			for _, key := range yyDollar[5].cmOption.SortKey {
				_, inTag := stmt.Tags[key]
				_, inField := stmt.Fields[key]
				if !inTag && !inField && key != "time" {
					if len(yyDollar[5].cmOption.PrimaryKey) != len(yyDollar[5].cmOption.SortKey) {
						yylex.Error("Invalid SortKey")
					} else {
						yylex.Error("Invalid PrimaryKey/SortKey")
					}
					return 1
				}
			}
			// check if ShardKey is IN Tags/Fields
			for _, key := range yyDollar[5].cmOption.ShardKey {
				_, inTag := stmt.Tags[key]
				_, inField := stmt.Fields[key]
				if !inTag && !inField {
					yylex.Error("Invalid ShardKey")
					return 1
				}
			}
			// check if primary key is left prefix of sort key
			if len(yyDollar[5].cmOption.PrimaryKey) > len(yyDollar[5].cmOption.SortKey) {
				yylex.Error("PrimaryKey should be left prefix of SortKey")
				return 1
			}
			for i, v := range yyDollar[5].cmOption.PrimaryKey {
				if v != yyDollar[5].cmOption.SortKey[i] {
					yylex.Error("PrimaryKey should be left prefix of SortKey")
					return 1
				}
			}
			// check if indexlist of secondary is IN Tags/Fields
			for i := range yyDollar[5].cmOption.IndexType {
				indextype := yyDollar[5].cmOption.IndexType[i]
				if indextype == "timecluster" {
					continue
				}
				indexlist := yyDollar[5].cmOption.IndexList[i]
				for _, col := range indexlist {
					_, inTag := stmt.Tags[col]
					_, inField := stmt.Fields[col]
					if !inTag && !inField {
						yylex.Error("Invalid indexlist")
					}
				}
			}
			if yyDollar[5].cmOption.NumOfShards != 0 && yyDollar[5].cmOption.Type == "range" {
				yylex.Error("Not support to set num-of-shards for range sharding")
			}

			stmt.EngineType = yyDollar[5].cmOption.EngineType
			stmt.IndexType = yyDollar[5].cmOption.IndexType
			stmt.IndexList = yyDollar[5].cmOption.IndexList
			stmt.TimeClusterDuration = yyDollar[5].cmOption.TimeClusterDuration
			stmt.ShardKey = yyDollar[5].cmOption.ShardKey
			stmt.NumOfShards = yyDollar[5].cmOption.NumOfShards
			stmt.Type = yyDollar[5].cmOption.Type
			stmt.PrimaryKey = yyDollar[5].cmOption.PrimaryKey
			stmt.SortKey = yyDollar[5].cmOption.SortKey
			stmt.Property = yyDollar[5].cmOption.Property
			stmt.CompactType = yyDollar[5].cmOption.CompactType
			yyVAL.stmt = stmt
		}
	case 316:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2596
		{
			option := &CreateMeasurementStatementOption{}
			option.Type = "hash"
			option.EngineType = "tsstore"
			yyVAL.cmOption = option
		}
	case 317:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:2603
		{
			option := &CreateMeasurementStatementOption{}
			if yyDollar[3].indexType != nil {
				option.IndexType = yyDollar[3].indexType.types
				option.IndexList = yyDollar[3].indexType.lists
			}
			if yyDollar[4].strSlice != nil {
				option.ShardKey = yyDollar[4].strSlice
			}
			option.NumOfShards = yyDollar[5].int64
			option.Type = yyDollar[6].str
			option.EngineType = yyDollar[2].str
			yyVAL.cmOption = option
		}
	case 318:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:2620
		{
			option := &CreateMeasurementStatementOption{}
			if yyDollar[3].indexType != nil {
				option.IndexType = yyDollar[3].indexType.types
				option.IndexList = yyDollar[3].indexType.lists
				option.TimeClusterDuration = yyDollar[3].indexType.timeClusterDuration
			}
			if yyDollar[4].strSlice != nil {
				option.ShardKey = yyDollar[4].strSlice
			}
			option.NumOfShards = yyDollar[5].int64
			option.Type = yyDollar[6].str
			option.EngineType = yyDollar[2].str
			if yyDollar[7].strSlice != nil {
				option.PrimaryKey = yyDollar[7].strSlice
			} else if yyDollar[8].strSlice != nil {
				option.PrimaryKey = yyDollar[8].strSlice
			}

			if yyDollar[8].strSlice != nil {
				option.SortKey = yyDollar[8].strSlice
			} else if yyDollar[7].strSlice != nil {
				option.SortKey = yyDollar[7].strSlice
			}
			if yyDollar[9].strSlices != nil {
				option.Property = yyDollar[9].strSlices
			}
			option.CompactType = yyDollar[10].str
			yyVAL.cmOption = option
		}
	case 319:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2652
		{
			yyVAL.indexType = nil
		}
	case 320:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2656
		{
			validIndexType := map[string]struct{}{}
			validIndexType["text"] = struct{}{}
			validIndexType["field"] = struct{}{}
			if yyDollar[2].indexType == nil {
				yyVAL.indexType = nil
			} else {
				for _, indexType := range yyDollar[2].indexType.types {
					if _, ok := validIndexType[strings.ToLower(indexType)]; !ok {
						yylex.Error("Invalid index type for TSSTORE")
					}
				}
				yyVAL.indexType = yyDollar[2].indexType
			}
		}
	case 321:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2673
		{
			yyVAL.indexType = nil
		}
	case 322:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2677
		{
			validIndexType := map[string]struct{}{}
			validIndexType["bloomfilter"] = struct{}{}
			validIndexType["bloomfilter_ip"] = struct{}{}
			validIndexType["minmax"] = struct{}{}
			validIndexType["text"] = struct{}{}
			if yyDollar[2].indexType == nil {
				yyVAL.indexType = nil
			} else {
				for _, indexType := range yyDollar[2].indexType.types {
					if _, ok := validIndexType[strings.ToLower(indexType)]; !ok {
						yylex.Error("Invalid index type for COLUMNSTORE")
					}
				}
				yyVAL.indexType = yyDollar[2].indexType
			}
		}
	case 323:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:2695
		{
			indexType := strings.ToLower(yyDollar[2].str)
			if indexType != "timecluster" {
				yylex.Error("expect TIMECLUSTER for INDEXTYPE")
				return 1
			}
			indextype := &IndexType{
				types:               []string{indexType},
				lists:               [][]string{{"time"}},
				timeClusterDuration: yyDollar[4].tdur,
			}
			validIndexType := map[string]struct{}{}
			validIndexType["bloomfilter"] = struct{}{}
			validIndexType["bloomfilter_ip"] = struct{}{}
			validIndexType["minmax"] = struct{}{}
			if yyDollar[6].indexType == nil {
				yyVAL.indexType = indextype
			} else {
				for _, indexType := range yyDollar[6].indexType.types {
					if _, ok := validIndexType[strings.ToLower(indexType)]; !ok {
						yylex.Error("Invalid index type for COLUMNSTORE")
					}
				}
				indextype.types = append(indextype.types, yyDollar[6].indexType.types...)
				indextype.lists = append(indextype.lists, yyDollar[6].indexType.lists...)
				yyVAL.indexType = indextype
			}
		}
	case 324:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2725
		{
			yyVAL.strSlice = nil
		}
	case 325:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2729
		{
			shardKey := yyDollar[2].strSlice
			sort.Strings(shardKey)
			yyVAL.strSlice = shardKey
		}
	case 326:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2736
		{
			yyVAL.int64 = 0
		}
	case 327:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2740
		{
			yyVAL.int64 = -1
		}
	case 328:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2744
		{
			if yyDollar[2].int64 == 0 {
				yylex.Error("syntax error: NUM OF SHARDS SHOULD LARGER THAN 0")
			}
			yyVAL.int64 = yyDollar[2].int64
		}
	case 329:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2752
		{
			yyVAL.str = "tsstore" // default engine type
		}
	case 330:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2756
		{
			yyVAL.str = "tsstore"
		}
	case 331:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2762
		{
			yyVAL.str = "columnstore"
		}
	case 332:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2767
		{
			yyVAL.strSlice = nil
		}
	case 333:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2770
		{
			yyVAL.strSlice = yyDollar[1].strSlice
		}
	case 334:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2775
		{
			yyVAL.strSlice = nil
		}
	case 335:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2778
		{
			yyVAL.strSlice = yyDollar[1].strSlice
		}
	case 336:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2783
		{
			yyVAL.strSlices = nil
		}
	case 337:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2786
		{
			yyVAL.strSlices = yyDollar[1].strSlices
		}
	case 338:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2791
		{
			yyVAL.str = "row"
		}
	case 339:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2795
		{
			compactionType := strings.ToLower(yyDollar[2].str)
			if compactionType != "row" && compactionType != "block" {
				yylex.Error("expect ROW or BLOCK for COMPACT type")
				return 1
			}
			yyVAL.str = compactionType
		}
	case 340:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2806
		{
			stmt := &CreateMeasurementStatement{
				Tags:   make(map[string]int32),
				Fields: make(map[string]int32),
			}
			for i := range yyDollar[2].fieldOptions {
				fType := yyDollar[2].fieldOptions[i].tagOrField
				if fType == "tag" {
					stmt.Tags[yyDollar[2].fieldOptions[i].fieldName] = influx.Field_Type_Tag
				} else if fType == "field" {
					fieldType := strings.ToLower(yyDollar[2].fieldOptions[i].fieldType)
					fieldName := yyDollar[2].fieldOptions[i].fieldName
					if fieldType == "int64" {
						stmt.Fields[fieldName] = influx.Field_Type_Int
					} else if fieldType == "float64" {
						stmt.Fields[fieldName] = influx.Field_Type_Float
					} else if fieldType == "string" {
						stmt.Fields[fieldName] = influx.Field_Type_String
					} else if fieldType == "bool" {
						stmt.Fields[fieldName] = influx.Field_Type_Boolean
					} else {
						yylex.Error("expect FLOAT64, INT64, BOOL, STRING for column data type")
						return 1 // syntax error
					}
				}
			}
			yyVAL.stmt = stmt
		}
	case 341:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2835
		{
			yyVAL.stmt = nil
		}
	case 342:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2841
		{
			fields := []*fieldList{yyDollar[1].fieldOption}
			yyVAL.fieldOptions = append(fields, yyDollar[2].fieldOptions...)
		}
	case 343:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2847
		{
			yyVAL.fieldOptions = []*fieldList{yyDollar[1].fieldOption}
		}
	case 344:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2853
		{
			yyVAL.fieldOption = yyDollar[1].fieldOption
		}
	case 345:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2858
		{
			yyVAL.fieldOption = yyDollar[1].fieldOption
		}
	case 346:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2864
		{
			yyVAL.fieldOption = &fieldList{
				fieldName:  yyDollar[1].str,
				fieldType:  "string",
				tagOrField: "tag",
			}
		}
	case 347:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2873
		{
			yyVAL.fieldOption = &fieldList{
				fieldName:  yyDollar[1].str,
				fieldType:  yyDollar[2].str,
				tagOrField: "field",
			}
		}
	case 348:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2882
		{
			yyVAL.fieldOption = &fieldList{
				fieldName:  yyDollar[1].str,
				fieldType:  yyDollar[2].str,
				tagOrField: "field",
			}
		}
	case 349:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2892
		{
			yyVAL.indexType = &IndexType{
				types: []string{yyDollar[1].str},
				lists: [][]string{yyDollar[3].strSlice},
			}
		}
	case 350:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2900
		{
			yyVAL.indexType = &IndexType{
				types: []string{"field"},
				lists: [][]string{yyDollar[3].strSlice},
			}
		}
	case 351:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2909
		{
			indextype := yyDollar[1].indexType
			if yyDollar[2].indexType != nil {
				indextype.types = append(indextype.types, yyDollar[2].indexType.types...)
				indextype.lists = append(indextype.lists, yyDollar[2].indexType.lists...)
			}
			yyVAL.indexType = indextype
		}
	case 352:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2918
		{
			yyVAL.indexType = nil
		}
	case 353:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2924
		{
			yyVAL.strSlice = []string{yyDollar[1].str}
		}
	case 354:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2928
		{

			yyVAL.strSlice = append([]string{yyDollar[1].str}, yyDollar[3].strSlice...)
		}
	case 355:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2935
		{
			shardType := strings.ToLower(yyDollar[2].str)
			if shardType != "hash" && shardType != "range" {
				yylex.Error("expect HASH or RANGE for TYPE")
				return 1
			}
			yyVAL.str = shardType
		}
	case 356:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2944
		{
			yyVAL.str = "hash"
		}
	case 357:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2950
		{
			yyVAL.strSlice = yyDollar[2].strSlice
		}
	case 358:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2956
		{
			yyVAL.strSlice = yyDollar[2].strSlice
		}
	case 359:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2962
		{
			m := yyDollar[1].strSlices
			if yyDollar[3].strSlices != nil {
				m[0] = append(m[0], yyDollar[3].strSlices[0]...)
				m[1] = append(m[1], yyDollar[3].strSlices[1]...)
			}
			yyVAL.strSlices = m
		}
	case 360:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2972
		{
			yyVAL.strSlices = yyDollar[1].strSlices
		}
	case 361:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:2978
		{
			yyVAL.strSlices = yyDollar[2].strSlices
		}
	case 362:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2984
		{
			yyVAL.strSlices = [][]string{{yyDollar[1].str}, {yyDollar[3].str}}
		}
	case 363:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:2988
		{
			yyVAL.strSlices = [][]string{{yyDollar[1].str}, {fmt.Sprintf("%d", yyDollar[3].int64)}}
		}
	case 364:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:2992
		{
			yyVAL.strSlices = nil
		}
	case 365:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:2998
		{
			yyVAL.strSlice = []string{yyDollar[1].str}
		}
	case 366:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3002
		{
			yyVAL.strSlice = append(yyDollar[1].strSlice, yyDollar[3].str)
		}
	case 367:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3007
		{
			yyVAL.str = yyDollar[1].str
		}
	case 368:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3013
		{
			stmt := &DropShardStatement{}
			stmt.ID = uint64(yyDollar[3].int64)
			yyVAL.stmt = stmt
		}
	case 369:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3021
		{
			stmt := &SetPasswordUserStatement{}
			stmt.Name = yyDollar[4].str
			stmt.Password = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 370:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3032
		{
			stmt := &ShowGrantsForUserStatement{}
			stmt.Name = yyDollar[4].str
			yyVAL.stmt = stmt
		}
	case 371:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:3040
		{
			stmt := &ShowMeasurementCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Sources = yyDollar[6].sources
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 372:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3052
		{
			stmt := &ShowMeasurementCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 373:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3063
		{
			stmt := &ShowMeasurementCardinalityStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Exact = false
			stmt.Sources = yyDollar[5].sources
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 374:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:3075
		{
			stmt := &ShowMeasurementCardinalityStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Exact = false
			stmt.Condition = yyDollar[5].expr
			stmt.Dimensions = yyDollar[6].dimens
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 375:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:3089
		{
			stmt := &ShowSeriesCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Sources = yyDollar[6].sources
			stmt.Condition = yyDollar[7].expr
			stmt.Dimensions = yyDollar[8].dimens
			stmt.Limit = yyDollar[9].intSlice[0]
			stmt.Offset = yyDollar[9].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 376:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3101
		{
			stmt := &ShowSeriesCardinalityStatement{}
			stmt.Database = yyDollar[5].str
			stmt.Exact = true
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 377:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3112
		{
			stmt := &ShowSeriesCardinalityStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Exact = false
			stmt.Sources = yyDollar[5].sources
			stmt.Condition = yyDollar[6].expr
			stmt.Dimensions = yyDollar[7].dimens
			stmt.Limit = yyDollar[8].intSlice[0]
			stmt.Offset = yyDollar[8].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 378:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:3124
		{
			stmt := &ShowSeriesCardinalityStatement{}
			stmt.Database = yyDollar[4].str
			stmt.Exact = false
			stmt.Condition = yyDollar[5].expr
			stmt.Dimensions = yyDollar[6].dimens
			stmt.Limit = yyDollar[7].intSlice[0]
			stmt.Offset = yyDollar[7].intSlice[1]
			yyVAL.stmt = stmt
		}
	case 379:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3138
		{
			stmt := &ShowShardsStatement{}
			yyVAL.stmt = stmt
		}
	case 380:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3143
		{
			stmt := &ShowShardsStatement{mstInfo: yyDollar[4].ment}
			yyVAL.stmt = stmt
		}
	case 381:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:3151
		{
			stmt := &AlterShardKeyStatement{}
			stmt.Database = yyDollar[3].ment.Database
			stmt.Name = yyDollar[3].ment.Name
			stmt.RetentionPolicy = yyDollar[3].ment.RetentionPolicy
			stmt.ShardKey = yyDollar[6].strSlice
			sort.Strings(stmt.ShardKey)
			stmt.Type = yyDollar[7].str
			yyVAL.stmt = stmt
		}
	case 382:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3162
		{
			stmt := &AlterShardKeyStatement{}
			stmt.Database = yyDollar[3].ment.Database
			stmt.Name = yyDollar[3].ment.Name
			stmt.RetentionPolicy = yyDollar[3].ment.RetentionPolicy
			stmt.Type = "hash"
			yyVAL.stmt = stmt
		}
	case 383:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3176
		{
			stmt := &ShowShardGroupsStatement{}
			yyVAL.stmt = stmt
		}
	case 384:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3183
		{
			stmt := &DropMeasurementStatement{}
			stmt.Name = yyDollar[3].str
			stmt.RpName = ""
			yyVAL.stmt = stmt
		}
	case 385:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:3190
		{
			stmt := &DropMeasurementStatement{}
			stmt.Name = yyDollar[5].str
			stmt.RpName = yyDollar[3].str
			yyVAL.stmt = stmt
		}
	case 386:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:3200
		{
			stmt := &CreateContinuousQueryStatement{
				Name:     yyDollar[4].str,
				Database: yyDollar[6].str,
				Source:   yyDollar[9].stmt.(*SelectStatement),
			}
			if yyDollar[7].cqsp != nil {
				stmt.ResampleEvery = yyDollar[7].cqsp.ResampleEvery
				stmt.ResampleFor = yyDollar[7].cqsp.ResampleFor
			}
			yyVAL.stmt = stmt
		}
	case 387:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3215
		{
			yyVAL.cqsp = &cqSamplePolicyInfo{
				ResampleEvery: yyDollar[3].tdur,
			}
		}
	case 388:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3221
		{
			yyVAL.cqsp = &cqSamplePolicyInfo{
				ResampleFor: yyDollar[3].tdur,
			}
		}
	case 389:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:3227
		{
			yyVAL.cqsp = &cqSamplePolicyInfo{
				ResampleEvery: yyDollar[3].tdur,
				ResampleFor:   yyDollar[5].tdur,
			}
		}
	case 390:
		yyDollar = yyS[yypt-0 : yypt+1]
//line sql.y:3234
		{
			yyVAL.cqsp = nil
		}
	case 391:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3240
		{
			yyVAL.stmt = &ShowContinuousQueriesStatement{}
		}
	case 392:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3246
		{
			yyVAL.stmt = &DropContinuousQueryStatement{
				Name:     yyDollar[4].str,
				Database: yyDollar[6].str,
			}
		}
	case 393:
		yyDollar = yyS[yypt-9 : yypt+1]
//line sql.y:3254
		{
			stmt := yyDollar[9].stmt.(*CreateDownSampleStatement)
			stmt.RpName = yyDollar[4].str
			stmt.Ops = yyDollar[6].fields
			yyVAL.stmt = stmt
		}
	case 394:
		yyDollar = yyS[yypt-11 : yypt+1]
//line sql.y:3261
		{
			stmt := yyDollar[11].stmt.(*CreateDownSampleStatement)
			stmt.RpName = yyDollar[6].str
			stmt.DbName = yyDollar[4].str
			stmt.Ops = yyDollar[8].fields
			yyVAL.stmt = stmt
		}
	case 395:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:3269
		{
			stmt := yyDollar[7].stmt.(*CreateDownSampleStatement)
			stmt.Ops = yyDollar[4].fields
			yyVAL.stmt = stmt
		}
	case 396:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3277
		{
			yyVAL.stmt = &DropDownSampleStatement{
				RpName: yyDollar[4].str,
			}
		}
	case 397:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3283
		{
			yyVAL.stmt = &DropDownSampleStatement{
				DbName: yyDollar[4].str,
				RpName: yyDollar[6].str,
			}
		}
	case 398:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3290
		{
			yyVAL.stmt = &DropDownSampleStatement{
				DropAll: true,
			}
		}
	case 399:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3296
		{
			yyVAL.stmt = &DropDownSampleStatement{
				DbName:  yyDollar[4].str,
				DropAll: true,
			}
		}
	case 400:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3305
		{
			yyVAL.stmt = &ShowDownSampleStatement{}
		}
	case 401:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3309
		{
			yyVAL.stmt = &ShowDownSampleStatement{
				DbName: yyDollar[4].str,
			}
		}
	case 402:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:3317
		{
			yyVAL.stmt = &CreateDownSampleStatement{
				Duration:       yyDollar[2].tdur,
				SampleInterval: yyDollar[5].tdurs,
				TimeInterval:   yyDollar[9].tdurs,
			}
		}
	case 403:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3327
		{
			yyVAL.tdurs = []time.Duration{yyDollar[1].tdur}
		}
	case 404:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3331
		{
			yyVAL.tdurs = append([]time.Duration{yyDollar[1].tdur}, yyDollar[3].tdurs...)
		}
	case 405:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3338
		{
			stmt := &CreateStreamStatement{
				Name:  yyDollar[3].str,
				Query: yyDollar[6].stmt,
				Delay: yyDollar[8].tdur,
			}
			if len(yyDollar[4].sources) > 1 {
				yylex.Error("into clause only support one target")
			}
			if len(yyDollar[4].sources) == 1 {
				mst, ok := yyDollar[4].sources[0].(*Measurement)
				if !ok {
					yylex.Error("into clause only support measurement clause")
				}
				mst.IsTarget = true
				stmt.Target = &Target{
					Measurement: mst,
				}
			}
			yyVAL.stmt = stmt
		}
	case 406:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3360
		{
			stmt := &CreateStreamStatement{
				Name:  yyDollar[3].str,
				Query: yyDollar[6].stmt,
			}
			if len(yyDollar[4].sources) > 1 {
				yylex.Error("into clause only support one target")
			}
			if len(yyDollar[4].sources) == 1 {
				mst, ok := yyDollar[4].sources[0].(*Measurement)
				if !ok {
					yylex.Error("into clause only support measurement clause")
				}
				mst.IsTarget = true
				stmt.Target = &Target{
					Measurement: mst,
				}
			}
			yyVAL.stmt = stmt
		}
	case 407:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3383
		{
			yyVAL.stmt = &ShowStreamsStatement{}
		}
	case 408:
		yyDollar = yyS[yypt-4 : yypt+1]
//line sql.y:3387
		{
			yyVAL.stmt = &ShowStreamsStatement{Database: yyDollar[4].str}
		}
	case 409:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3393
		{
			yyVAL.stmt = &DropStreamsStatement{Name: yyDollar[3].str}
		}
	case 410:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3398
		{
			yyVAL.stmt = &ShowQueriesStatement{}
		}
	case 411:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3403
		{
			yyVAL.stmt = &KillQueryStatement{QueryID: uint64(yyDollar[3].int64)}
		}
	case 412:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3409
		{
			yyVAL.strSlice = []string{yyDollar[1].str}
		}
	case 413:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3413
		{
			yyVAL.strSlice = append([]string{yyDollar[1].str}, yyDollar[3].strSlice...)
		}
	case 414:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3419
		{
			yyVAL.str = "ALL"
		}
	case 415:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3423
		{
			yyVAL.str = "ANY"
		}
	case 416:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:3429
		{
			yyVAL.stmt = &CreateSubscriptionStatement{Name: yyDollar[3].str, Database: yyDollar[5].str, RetentionPolicy: yyDollar[7].str, Destinations: yyDollar[10].strSlice, Mode: yyDollar[9].str}
		}
	case 417:
		yyDollar = yyS[yypt-8 : yypt+1]
//line sql.y:3433
		{
			yyVAL.stmt = &CreateSubscriptionStatement{Name: yyDollar[3].str, Database: yyDollar[5].str, RetentionPolicy: "", Destinations: yyDollar[8].strSlice, Mode: yyDollar[7].str}
		}
	case 418:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3439
		{
			yyVAL.stmt = &ShowSubscriptionsStatement{}
		}
	case 419:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3445
		{
			yyVAL.stmt = &DropSubscriptionStatement{Name: "", Database: "", RetentionPolicy: ""}
		}
	case 420:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:3449
		{
			yyVAL.stmt = &DropSubscriptionStatement{Name: "", Database: yyDollar[5].str, RetentionPolicy: ""}
		}
	case 421:
		yyDollar = yyS[yypt-7 : yypt+1]
//line sql.y:3453
		{
			yyVAL.stmt = &DropSubscriptionStatement{Name: yyDollar[3].str, Database: yyDollar[5].str, RetentionPolicy: yyDollar[7].str}
		}
	case 422:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:3457
		{
			yyVAL.stmt = &DropSubscriptionStatement{Name: yyDollar[3].str, Database: yyDollar[5].str, RetentionPolicy: ""}
		}
	case 423:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3463
		{
			stmt := &ShowConfigsStatement{}
			yyVAL.stmt = stmt
		}
	case 424:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3470
		{
			stmt := &SetConfigStatement{}
			stmt.Component = yyDollar[3].str
			stmt.Key = yyDollar[4].str
			stmt.Value = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 425:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3478
		{
			stmt := &SetConfigStatement{}
			stmt.Component = yyDollar[3].str
			stmt.Key = yyDollar[4].str
			stmt.Value = yyDollar[6].int64
			yyVAL.stmt = stmt
		}
	case 426:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3486
		{
			stmt := &SetConfigStatement{}
			stmt.Component = yyDollar[3].str
			stmt.Key = yyDollar[4].str
			stmt.Value = yyDollar[6].float64
			yyVAL.stmt = stmt
		}
	case 427:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3494
		{
			stmt := &SetConfigStatement{}
			stmt.Component = yyDollar[3].str
			stmt.Key = yyDollar[4].str
			stmt.Value = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 428:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3502
		{
			stmt := &SetConfigStatement{}
			stmt.Component = yyDollar[3].str
			stmt.Key = yyDollar[4].str
			stmt.Value = yyDollar[6].str
			yyVAL.stmt = stmt
		}
	case 429:
		yyDollar = yyS[yypt-2 : yypt+1]
//line sql.y:3512
		{
			stmt := &ShowClusterStatement{}
			stmt.NodeID = 0
			yyVAL.stmt = stmt
		}
	case 430:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3518
		{
			stmt := &ShowClusterStatement{}
			stmt.NodeID = 0
			if strings.ToLower(yyDollar[4].str) == "nodetype" {
				stmt.NodeType = yyDollar[6].str
			} else {
				yylex.Error("Invalid where clause")
			}
			yyVAL.stmt = stmt
		}
	case 431:
		yyDollar = yyS[yypt-6 : yypt+1]
//line sql.y:3529
		{
			stmt := &ShowClusterStatement{}
			if strings.ToLower(yyDollar[4].str) == "nodeid" {
				stmt.NodeID = yyDollar[6].int64
			} else {
				yylex.Error("Invalid where clause")
			}
			yyVAL.stmt = stmt
		}
	case 432:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:3539
		{
			stmt := &ShowClusterStatement{}
			if strings.ToLower(yyDollar[4].str) == "nodeid" {
				stmt.NodeID = yyDollar[6].int64
			} else {
				yylex.Error("Invalid where clause")
			}
			if strings.ToLower(yyDollar[8].str) == "nodetype" {
				stmt.NodeType = yyDollar[10].str
			} else {
				yylex.Error("Invalid where clause")
			}
			yyVAL.stmt = stmt
		}
	case 433:
		yyDollar = yyS[yypt-10 : yypt+1]
//line sql.y:3554
		{
			stmt := &ShowClusterStatement{}
			if strings.ToLower(yyDollar[4].str) == "nodetype" {
				stmt.NodeType = yyDollar[6].str
			} else {
				yylex.Error("Invalid where clause")
			}
			if strings.ToLower(yyDollar[8].str) == "nodeid" {
				stmt.NodeID = yyDollar[10].int64
			} else {
				yylex.Error("Invalid where clause")
			}
			yyVAL.stmt = stmt
		}
	case 434:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3571
		{
			yyVAL.stmt = &WithSelectStatement{
				CTEs:  yyDollar[2].ctes,
				Query: yyDollar[3].stmt.(*SelectStatement),
			}
		}
	case 435:
		yyDollar = yyS[yypt-1 : yypt+1]
//line sql.y:3580
		{
			yyVAL.ctes = []*CTE{yyDollar[1].cte}
		}
	case 436:
		yyDollar = yyS[yypt-3 : yypt+1]
//line sql.y:3584
		{
			yyVAL.ctes = append([]*CTE{yyDollar[1].cte}, yyDollar[3].ctes...)
		}
	case 437:
		yyDollar = yyS[yypt-5 : yypt+1]
//line sql.y:3590
		{
			yyVAL.cte = &CTE{
				Alias: yyDollar[1].str,
				Query: yyDollar[4].stmt.(*SelectStatement),
			}
		}
	}
	goto yystack /* stack new state and value */
}
