// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.14.0
// source: internal.proto

package executor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AggType int32

const (
	AggType_TagSet        AggType = 0
	AggType_CountDistinct AggType = 1
	AggType_Normal        AggType = 2
)

// Enum value maps for AggType.
var (
	AggType_name = map[int32]string{
		0: "TagSet",
		1: "CountDistinct",
		2: "Normal",
	}
	AggType_value = map[string]int32{
		"TagSet":        0,
		"CountDistinct": 1,
		"Normal":        2,
	}
)

func (x AggType) Enum() *AggType {
	p := new(AggType)
	*p = x
	return p
}

func (x AggType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AggType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_proto_enumTypes[0].Descriptor()
}

func (AggType) Type() protoreflect.EnumType {
	return &file_internal_proto_enumTypes[0]
}

func (x AggType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AggType.Descriptor instead.
func (AggType) EnumDescriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{0}
}

type LogicPlanType int32

const (
	LogicPlanType_LogicalExchange          LogicPlanType = 0
	LogicPlanType_LogicalLimit             LogicPlanType = 1
	LogicPlanType_LogicalIndexScan         LogicPlanType = 2
	LogicPlanType_LogicalAggregate         LogicPlanType = 3
	LogicPlanType_LogicalMerge             LogicPlanType = 4
	LogicPlanType_LogicalSortMerge         LogicPlanType = 5
	LogicPlanType_LogicalFilter            LogicPlanType = 6
	LogicPlanType_LogicalDedupe            LogicPlanType = 7
	LogicPlanType_LogicalInterval          LogicPlanType = 8
	LogicPlanType_LogicalSeries            LogicPlanType = 9
	LogicPlanType_LogicalReader            LogicPlanType = 10
	LogicPlanType_LogicalTagSubset         LogicPlanType = 11
	LogicPlanType_LogicalFill              LogicPlanType = 12
	LogicPlanType_LogicalAlign             LogicPlanType = 13
	LogicPlanType_LogicalMst               LogicPlanType = 14
	LogicPlanType_LogicalProject           LogicPlanType = 15
	LogicPlanType_LogicalSlidingWindow     LogicPlanType = 16
	LogicPlanType_LogicalFilterBlank       LogicPlanType = 17
	LogicPlanType_LogicalHttpSender        LogicPlanType = 18
	LogicPlanType_LogicalFullJoin          LogicPlanType = 19
	LogicPlanType_LogicalWriteIntoStorage  LogicPlanType = 20
	LogicPlanType_LogicalSequenceAggregate LogicPlanType = 21
	LogicPlanType_LogicalSplitGroup        LogicPlanType = 22
	LogicPlanType_LogicalHoltWinters       LogicPlanType = 23
	LogicPlanType_LogicalSubQuery          LogicPlanType = 24
	LogicPlanType_LogicalGroupBy           LogicPlanType = 25
	LogicPlanType_LogicalOrderBy           LogicPlanType = 26
	LogicPlanType_LogicalHttpSenderHint    LogicPlanType = 27
	LogicPlanType_LogicalTarget            LogicPlanType = 28
	LogicPlanType_LogicalDummyShard        LogicPlanType = 29
	LogicPlanType_LogicalTSSPScan          LogicPlanType = 30
	LogicPlanType_LogicalSortAppend        LogicPlanType = 31
	LogicPlanType_LogicalSort              LogicPlanType = 32
	LogicPlanType_LogicalHashMerge         LogicPlanType = 33
	LogicPlanType_LogicalSparseIndexScan   LogicPlanType = 34
	LogicPlanType_LogicalColumnStoreReader LogicPlanType = 35
	LogicPlanType_LogicalHashAgg           LogicPlanType = 36
	LogicPlanType_LogicalJoin              LogicPlanType = 37
	LogicPlanType_LogicalBinOp             LogicPlanType = 38
	LogicPlanType_LogicalPromSubquery      LogicPlanType = 39
	LogicPlanType_LogicalPromSort          LogicPlanType = 40
)

// Enum value maps for LogicPlanType.
var (
	LogicPlanType_name = map[int32]string{
		0:  "LogicalExchange",
		1:  "LogicalLimit",
		2:  "LogicalIndexScan",
		3:  "LogicalAggregate",
		4:  "LogicalMerge",
		5:  "LogicalSortMerge",
		6:  "LogicalFilter",
		7:  "LogicalDedupe",
		8:  "LogicalInterval",
		9:  "LogicalSeries",
		10: "LogicalReader",
		11: "LogicalTagSubset",
		12: "LogicalFill",
		13: "LogicalAlign",
		14: "LogicalMst",
		15: "LogicalProject",
		16: "LogicalSlidingWindow",
		17: "LogicalFilterBlank",
		18: "LogicalHttpSender",
		19: "LogicalFullJoin",
		20: "LogicalWriteIntoStorage",
		21: "LogicalSequenceAggregate",
		22: "LogicalSplitGroup",
		23: "LogicalHoltWinters",
		24: "LogicalSubQuery",
		25: "LogicalGroupBy",
		26: "LogicalOrderBy",
		27: "LogicalHttpSenderHint",
		28: "LogicalTarget",
		29: "LogicalDummyShard",
		30: "LogicalTSSPScan",
		31: "LogicalSortAppend",
		32: "LogicalSort",
		33: "LogicalHashMerge",
		34: "LogicalSparseIndexScan",
		35: "LogicalColumnStoreReader",
		36: "LogicalHashAgg",
		37: "LogicalJoin",
		38: "LogicalBinOp",
		39: "LogicalPromSubquery",
		40: "LogicalPromSort",
	}
	LogicPlanType_value = map[string]int32{
		"LogicalExchange":          0,
		"LogicalLimit":             1,
		"LogicalIndexScan":         2,
		"LogicalAggregate":         3,
		"LogicalMerge":             4,
		"LogicalSortMerge":         5,
		"LogicalFilter":            6,
		"LogicalDedupe":            7,
		"LogicalInterval":          8,
		"LogicalSeries":            9,
		"LogicalReader":            10,
		"LogicalTagSubset":         11,
		"LogicalFill":              12,
		"LogicalAlign":             13,
		"LogicalMst":               14,
		"LogicalProject":           15,
		"LogicalSlidingWindow":     16,
		"LogicalFilterBlank":       17,
		"LogicalHttpSender":        18,
		"LogicalFullJoin":          19,
		"LogicalWriteIntoStorage":  20,
		"LogicalSequenceAggregate": 21,
		"LogicalSplitGroup":        22,
		"LogicalHoltWinters":       23,
		"LogicalSubQuery":          24,
		"LogicalGroupBy":           25,
		"LogicalOrderBy":           26,
		"LogicalHttpSenderHint":    27,
		"LogicalTarget":            28,
		"LogicalDummyShard":        29,
		"LogicalTSSPScan":          30,
		"LogicalSortAppend":        31,
		"LogicalSort":              32,
		"LogicalHashMerge":         33,
		"LogicalSparseIndexScan":   34,
		"LogicalColumnStoreReader": 35,
		"LogicalHashAgg":           36,
		"LogicalJoin":              37,
		"LogicalBinOp":             38,
		"LogicalPromSubquery":      39,
		"LogicalPromSort":          40,
	}
)

func (x LogicPlanType) Enum() *LogicPlanType {
	p := new(LogicPlanType)
	*p = x
	return p
}

func (x LogicPlanType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogicPlanType) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_proto_enumTypes[1].Descriptor()
}

func (LogicPlanType) Type() protoreflect.EnumType {
	return &file_internal_proto_enumTypes[1]
}

func (x LogicPlanType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogicPlanType.Descriptor instead.
func (LogicPlanType) EnumDescriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{1}
}

type ProcessorOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                  string          `protobuf:"bytes,23,opt,name=Name,proto3" json:"Name,omitempty"`
	Expr                  string          `protobuf:"bytes,1,opt,name=Expr,proto3" json:"Expr,omitempty"`
	Aux                   []*VarRef       `protobuf:"bytes,2,rep,name=Aux,proto3" json:"Aux,omitempty"`
	Sources               []*Measurement  `protobuf:"bytes,3,rep,name=Sources,proto3" json:"Sources,omitempty"`
	Interval              *Interval       `protobuf:"bytes,4,opt,name=Interval,proto3" json:"Interval,omitempty"`
	Dimensions            []string        `protobuf:"bytes,5,rep,name=Dimensions,proto3" json:"Dimensions,omitempty"`
	GroupBy               map[string]bool `protobuf:"bytes,19,rep,name=GroupBy,proto3" json:"GroupBy,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	Fill                  int32           `protobuf:"varint,6,opt,name=Fill,proto3" json:"Fill,omitempty"`
	FillValue             float64         `protobuf:"fixed64,7,opt,name=FillValue,proto3" json:"FillValue,omitempty"`
	Condition             string          `protobuf:"bytes,8,opt,name=Condition,proto3" json:"Condition,omitempty"`
	StartTime             int64           `protobuf:"varint,9,opt,name=StartTime,proto3" json:"StartTime,omitempty"`
	EndTime               int64           `protobuf:"varint,10,opt,name=EndTime,proto3" json:"EndTime,omitempty"`
	Location              string          `protobuf:"bytes,21,opt,name=Location,proto3" json:"Location,omitempty"`
	Ascending             bool            `protobuf:"varint,11,opt,name=Ascending,proto3" json:"Ascending,omitempty"`
	Limit                 int64           `protobuf:"varint,12,opt,name=Limit,proto3" json:"Limit,omitempty"`
	Offset                int64           `protobuf:"varint,13,opt,name=Offset,proto3" json:"Offset,omitempty"`
	SLimit                int64           `protobuf:"varint,14,opt,name=SLimit,proto3" json:"SLimit,omitempty"`
	SOffset               int64           `protobuf:"varint,15,opt,name=SOffset,proto3" json:"SOffset,omitempty"`
	StripName             bool            `protobuf:"varint,22,opt,name=StripName,proto3" json:"StripName,omitempty"`
	Dedupe                bool            `protobuf:"varint,16,opt,name=Dedupe,proto3" json:"Dedupe,omitempty"`
	MaxSeriesN            int64           `protobuf:"varint,18,opt,name=MaxSeriesN,proto3" json:"MaxSeriesN,omitempty"`
	Ordered               bool            `protobuf:"varint,20,opt,name=Ordered,proto3" json:"Ordered,omitempty"`
	ChunkSize             int64           `protobuf:"varint,25,opt,name=ChunkSize,proto3" json:"ChunkSize,omitempty"`
	MaxParallel           int64           `protobuf:"varint,26,opt,name=MaxParallel,proto3" json:"MaxParallel,omitempty"`
	Query                 string          `protobuf:"bytes,27,opt,name=Query,proto3" json:"Query,omitempty"`
	HintType              int64           `protobuf:"varint,28,opt,name=HintType,proto3" json:"HintType,omitempty"`
	EnableBinaryTreeMerge int64           `protobuf:"varint,29,opt,name=EnableBinaryTreeMerge,proto3" json:"EnableBinaryTreeMerge,omitempty"`
	QueryId               uint64          `protobuf:"varint,30,opt,name=QueryId,proto3" json:"QueryId,omitempty"`
	SeriesKey             []byte          `protobuf:"bytes,31,opt,name=SeriesKey,proto3" json:"SeriesKey,omitempty"`
	GroupByAllDims        bool            `protobuf:"varint,32,opt,name=GroupByAllDims,proto3" json:"GroupByAllDims,omitempty"`
	EngineType            uint32          `protobuf:"varint,33,opt,name=EngineType,proto3" json:"EngineType,omitempty"`
	SortFields            string          `protobuf:"bytes,34,opt,name=SortFields,proto3" json:"SortFields,omitempty"`
	HasFieldWildcard      bool            `protobuf:"varint,35,opt,name=HasFieldWildcard,proto3" json:"HasFieldWildcard,omitempty"`
	LogQueryCurrId        string          `protobuf:"bytes,36,opt,name=LogQueryCurrId,proto3" json:"LogQueryCurrId,omitempty"`
	IncQuery              bool            `protobuf:"varint,37,opt,name=IncQuery,proto3" json:"IncQuery,omitempty"`
	IterID                int32           `protobuf:"varint,38,opt,name=IterID,proto3" json:"IterID,omitempty"`
	PromQuery             bool            `protobuf:"varint,39,opt,name=PromQuery,proto3" json:"PromQuery,omitempty"`
	Step                  int64           `protobuf:"varint,40,opt,name=Step,proto3" json:"Step,omitempty"`
	Range                 int64           `protobuf:"varint,41,opt,name=Range,proto3" json:"Range,omitempty"`
	LookBackDelta         int64           `protobuf:"varint,42,opt,name=LookBackDelta,proto3" json:"LookBackDelta,omitempty"`
	QueryOffset           int64           `protobuf:"varint,43,opt,name=QueryOffset,proto3" json:"QueryOffset,omitempty"`
	Without               bool            `protobuf:"varint,44,opt,name=Without,proto3" json:"Without,omitempty"`
	PromRemoteRead        bool            `protobuf:"varint,45,opt,name=PromRemoteRead,proto3" json:"PromRemoteRead,omitempty"`
}

func (x *ProcessorOptions) Reset() {
	*x = ProcessorOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessorOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessorOptions) ProtoMessage() {}

func (x *ProcessorOptions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessorOptions.ProtoReflect.Descriptor instead.
func (*ProcessorOptions) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessorOptions) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProcessorOptions) GetExpr() string {
	if x != nil {
		return x.Expr
	}
	return ""
}

func (x *ProcessorOptions) GetAux() []*VarRef {
	if x != nil {
		return x.Aux
	}
	return nil
}

func (x *ProcessorOptions) GetSources() []*Measurement {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *ProcessorOptions) GetInterval() *Interval {
	if x != nil {
		return x.Interval
	}
	return nil
}

func (x *ProcessorOptions) GetDimensions() []string {
	if x != nil {
		return x.Dimensions
	}
	return nil
}

func (x *ProcessorOptions) GetGroupBy() map[string]bool {
	if x != nil {
		return x.GroupBy
	}
	return nil
}

func (x *ProcessorOptions) GetFill() int32 {
	if x != nil {
		return x.Fill
	}
	return 0
}

func (x *ProcessorOptions) GetFillValue() float64 {
	if x != nil {
		return x.FillValue
	}
	return 0
}

func (x *ProcessorOptions) GetCondition() string {
	if x != nil {
		return x.Condition
	}
	return ""
}

func (x *ProcessorOptions) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ProcessorOptions) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *ProcessorOptions) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *ProcessorOptions) GetAscending() bool {
	if x != nil {
		return x.Ascending
	}
	return false
}

func (x *ProcessorOptions) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ProcessorOptions) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *ProcessorOptions) GetSLimit() int64 {
	if x != nil {
		return x.SLimit
	}
	return 0
}

func (x *ProcessorOptions) GetSOffset() int64 {
	if x != nil {
		return x.SOffset
	}
	return 0
}

func (x *ProcessorOptions) GetStripName() bool {
	if x != nil {
		return x.StripName
	}
	return false
}

func (x *ProcessorOptions) GetDedupe() bool {
	if x != nil {
		return x.Dedupe
	}
	return false
}

func (x *ProcessorOptions) GetMaxSeriesN() int64 {
	if x != nil {
		return x.MaxSeriesN
	}
	return 0
}

func (x *ProcessorOptions) GetOrdered() bool {
	if x != nil {
		return x.Ordered
	}
	return false
}

func (x *ProcessorOptions) GetChunkSize() int64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *ProcessorOptions) GetMaxParallel() int64 {
	if x != nil {
		return x.MaxParallel
	}
	return 0
}

func (x *ProcessorOptions) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ProcessorOptions) GetHintType() int64 {
	if x != nil {
		return x.HintType
	}
	return 0
}

func (x *ProcessorOptions) GetEnableBinaryTreeMerge() int64 {
	if x != nil {
		return x.EnableBinaryTreeMerge
	}
	return 0
}

func (x *ProcessorOptions) GetQueryId() uint64 {
	if x != nil {
		return x.QueryId
	}
	return 0
}

func (x *ProcessorOptions) GetSeriesKey() []byte {
	if x != nil {
		return x.SeriesKey
	}
	return nil
}

func (x *ProcessorOptions) GetGroupByAllDims() bool {
	if x != nil {
		return x.GroupByAllDims
	}
	return false
}

func (x *ProcessorOptions) GetEngineType() uint32 {
	if x != nil {
		return x.EngineType
	}
	return 0
}

func (x *ProcessorOptions) GetSortFields() string {
	if x != nil {
		return x.SortFields
	}
	return ""
}

func (x *ProcessorOptions) GetHasFieldWildcard() bool {
	if x != nil {
		return x.HasFieldWildcard
	}
	return false
}

func (x *ProcessorOptions) GetLogQueryCurrId() string {
	if x != nil {
		return x.LogQueryCurrId
	}
	return ""
}

func (x *ProcessorOptions) GetIncQuery() bool {
	if x != nil {
		return x.IncQuery
	}
	return false
}

func (x *ProcessorOptions) GetIterID() int32 {
	if x != nil {
		return x.IterID
	}
	return 0
}

func (x *ProcessorOptions) GetPromQuery() bool {
	if x != nil {
		return x.PromQuery
	}
	return false
}

func (x *ProcessorOptions) GetStep() int64 {
	if x != nil {
		return x.Step
	}
	return 0
}

func (x *ProcessorOptions) GetRange() int64 {
	if x != nil {
		return x.Range
	}
	return 0
}

func (x *ProcessorOptions) GetLookBackDelta() int64 {
	if x != nil {
		return x.LookBackDelta
	}
	return 0
}

func (x *ProcessorOptions) GetQueryOffset() int64 {
	if x != nil {
		return x.QueryOffset
	}
	return 0
}

func (x *ProcessorOptions) GetWithout() bool {
	if x != nil {
		return x.Without
	}
	return false
}

func (x *ProcessorOptions) GetPromRemoteRead() bool {
	if x != nil {
		return x.PromRemoteRead
	}
	return false
}

type Measurement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database        string         `protobuf:"bytes,1,opt,name=Database,proto3" json:"Database,omitempty"`
	RetentionPolicy string         `protobuf:"bytes,2,opt,name=RetentionPolicy,proto3" json:"RetentionPolicy,omitempty"`
	Name            string         `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`
	Regex           string         `protobuf:"bytes,4,opt,name=Regex,proto3" json:"Regex,omitempty"`
	IsTarget        bool           `protobuf:"varint,5,opt,name=IsTarget,proto3" json:"IsTarget,omitempty"`
	SystemIterator  string         `protobuf:"bytes,6,opt,name=SystemIterator,proto3" json:"SystemIterator,omitempty"`
	EngineType      uint32         `protobuf:"varint,7,opt,name=EngineType,proto3" json:"EngineType,omitempty"`
	IndexRelation   *IndexRelation `protobuf:"bytes,8,opt,name=indexRelation,proto3" json:"indexRelation,omitempty"`
	ObsOptions      *ObsOptions    `protobuf:"bytes,9,opt,name=ObsOptions,proto3" json:"ObsOptions,omitempty"`
	IsTimeSorted    bool           `protobuf:"varint,10,opt,name=IsTimeSorted,proto3" json:"IsTimeSorted,omitempty"`
}

func (x *Measurement) Reset() {
	*x = Measurement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Measurement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Measurement) ProtoMessage() {}

func (x *Measurement) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Measurement.ProtoReflect.Descriptor instead.
func (*Measurement) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{1}
}

func (x *Measurement) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *Measurement) GetRetentionPolicy() string {
	if x != nil {
		return x.RetentionPolicy
	}
	return ""
}

func (x *Measurement) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Measurement) GetRegex() string {
	if x != nil {
		return x.Regex
	}
	return ""
}

func (x *Measurement) GetIsTarget() bool {
	if x != nil {
		return x.IsTarget
	}
	return false
}

func (x *Measurement) GetSystemIterator() string {
	if x != nil {
		return x.SystemIterator
	}
	return ""
}

func (x *Measurement) GetEngineType() uint32 {
	if x != nil {
		return x.EngineType
	}
	return 0
}

func (x *Measurement) GetIndexRelation() *IndexRelation {
	if x != nil {
		return x.IndexRelation
	}
	return nil
}

func (x *Measurement) GetObsOptions() *ObsOptions {
	if x != nil {
		return x.ObsOptions
	}
	return nil
}

func (x *Measurement) GetIsTimeSorted() bool {
	if x != nil {
		return x.IsTimeSorted
	}
	return false
}

type IndexRelation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rid          uint32          `protobuf:"varint,1,opt,name=Rid,proto3" json:"Rid,omitempty"`
	Oids         []uint32        `protobuf:"varint,2,rep,packed,name=Oids,proto3" json:"Oids,omitempty"`
	IndexNames   []string        `protobuf:"bytes,3,rep,name=IndexNames,proto3" json:"IndexNames,omitempty"`
	IndexLists   []*IndexList    `protobuf:"bytes,4,rep,name=IndexLists,proto3" json:"IndexLists,omitempty"`
	IndexOptions []*IndexOptions `protobuf:"bytes,5,rep,name=IndexOptions,proto3" json:"IndexOptions,omitempty"`
}

func (x *IndexRelation) Reset() {
	*x = IndexRelation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexRelation) ProtoMessage() {}

func (x *IndexRelation) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexRelation.ProtoReflect.Descriptor instead.
func (*IndexRelation) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{2}
}

func (x *IndexRelation) GetRid() uint32 {
	if x != nil {
		return x.Rid
	}
	return 0
}

func (x *IndexRelation) GetOids() []uint32 {
	if x != nil {
		return x.Oids
	}
	return nil
}

func (x *IndexRelation) GetIndexNames() []string {
	if x != nil {
		return x.IndexNames
	}
	return nil
}

func (x *IndexRelation) GetIndexLists() []*IndexList {
	if x != nil {
		return x.IndexLists
	}
	return nil
}

func (x *IndexRelation) GetIndexOptions() []*IndexOptions {
	if x != nil {
		return x.IndexOptions
	}
	return nil
}

type IndexList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IList []string `protobuf:"bytes,1,rep,name=IList,proto3" json:"IList,omitempty"`
}

func (x *IndexList) Reset() {
	*x = IndexList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexList) ProtoMessage() {}

func (x *IndexList) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexList.ProtoReflect.Descriptor instead.
func (*IndexList) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{3}
}

func (x *IndexList) GetIList() []string {
	if x != nil {
		return x.IList
	}
	return nil
}

type IndexOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Infos []*IndexOption `protobuf:"bytes,1,rep,name=Infos,proto3" json:"Infos,omitempty"`
}

func (x *IndexOptions) Reset() {
	*x = IndexOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexOptions) ProtoMessage() {}

func (x *IndexOptions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexOptions.ProtoReflect.Descriptor instead.
func (*IndexOptions) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{4}
}

func (x *IndexOptions) GetInfos() []*IndexOption {
	if x != nil {
		return x.Infos
	}
	return nil
}

type IndexOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Tokens              string `protobuf:"bytes,1,opt,name=Tokens,proto3" json:"Tokens,omitempty"`
	Tokenizers          string `protobuf:"bytes,2,opt,name=Tokenizers,proto3" json:"Tokenizers,omitempty"`
	TimeClusterDuration int64  `protobuf:"varint,3,opt,name=TimeClusterDuration,proto3" json:"TimeClusterDuration,omitempty"`
}

func (x *IndexOption) Reset() {
	*x = IndexOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IndexOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexOption) ProtoMessage() {}

func (x *IndexOption) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexOption.ProtoReflect.Descriptor instead.
func (*IndexOption) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{5}
}

func (x *IndexOption) GetTokens() string {
	if x != nil {
		return x.Tokens
	}
	return ""
}

func (x *IndexOption) GetTokenizers() string {
	if x != nil {
		return x.Tokenizers
	}
	return ""
}

func (x *IndexOption) GetTimeClusterDuration() int64 {
	if x != nil {
		return x.TimeClusterDuration
	}
	return 0
}

type ObsOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled    bool   `protobuf:"varint,1,opt,name=Enabled,proto3" json:"Enabled,omitempty"`
	BucketName string `protobuf:"bytes,2,opt,name=BucketName,proto3" json:"BucketName,omitempty"`
	Ak         string `protobuf:"bytes,3,opt,name=Ak,proto3" json:"Ak,omitempty"`
	Sk         string `protobuf:"bytes,4,opt,name=Sk,proto3" json:"Sk,omitempty"`
	Endpoint   string `protobuf:"bytes,5,opt,name=Endpoint,proto3" json:"Endpoint,omitempty"`
	BasePath   string `protobuf:"bytes,6,opt,name=BasePath,proto3" json:"BasePath,omitempty"`
}

func (x *ObsOptions) Reset() {
	*x = ObsOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ObsOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObsOptions) ProtoMessage() {}

func (x *ObsOptions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObsOptions.ProtoReflect.Descriptor instead.
func (*ObsOptions) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{6}
}

func (x *ObsOptions) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *ObsOptions) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *ObsOptions) GetAk() string {
	if x != nil {
		return x.Ak
	}
	return ""
}

func (x *ObsOptions) GetSk() string {
	if x != nil {
		return x.Sk
	}
	return ""
}

func (x *ObsOptions) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *ObsOptions) GetBasePath() string {
	if x != nil {
		return x.BasePath
	}
	return ""
}

type Interval struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration int64 `protobuf:"varint,1,opt,name=Duration,proto3" json:"Duration,omitempty"`
	Offset   int64 `protobuf:"varint,2,opt,name=Offset,proto3" json:"Offset,omitempty"`
}

func (x *Interval) Reset() {
	*x = Interval{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Interval) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Interval) ProtoMessage() {}

func (x *Interval) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Interval.ProtoReflect.Descriptor instead.
func (*Interval) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{7}
}

func (x *Interval) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Interval) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type IteratorStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesN int64 `protobuf:"varint,1,opt,name=SeriesN,proto3" json:"SeriesN,omitempty"`
	PointN  int64 `protobuf:"varint,2,opt,name=PointN,proto3" json:"PointN,omitempty"`
}

func (x *IteratorStats) Reset() {
	*x = IteratorStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IteratorStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IteratorStats) ProtoMessage() {}

func (x *IteratorStats) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IteratorStats.ProtoReflect.Descriptor instead.
func (*IteratorStats) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{8}
}

func (x *IteratorStats) GetSeriesN() int64 {
	if x != nil {
		return x.SeriesN
	}
	return 0
}

func (x *IteratorStats) GetPointN() int64 {
	if x != nil {
		return x.PointN
	}
	return 0
}

type VarRef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Val  string `protobuf:"bytes,1,opt,name=Val,proto3" json:"Val,omitempty"`
	Type int32  `protobuf:"varint,2,opt,name=Type,proto3" json:"Type,omitempty"`
}

func (x *VarRef) Reset() {
	*x = VarRef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VarRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VarRef) ProtoMessage() {}

func (x *VarRef) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VarRef.ProtoReflect.Descriptor instead.
func (*VarRef) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{9}
}

func (x *VarRef) GetVal() string {
	if x != nil {
		return x.Val
	}
	return ""
}

func (x *VarRef) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type QueryParam struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SeriesKey   string  `protobuf:"bytes,1,opt,name=SeriesKey,proto3" json:"SeriesKey,omitempty"`
	TagsAsKey   string  `protobuf:"bytes,2,opt,name=TagsAsKey,proto3" json:"TagsAsKey,omitempty"`
	QueryFields int32   `protobuf:"varint,3,opt,name=QueryFields,proto3" json:"QueryFields,omitempty"`
	QueryPct    float32 `protobuf:"fixed32,4,opt,name=QueryPct,proto3" json:"QueryPct,omitempty"`
}

func (x *QueryParam) Reset() {
	*x = QueryParam{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryParam) ProtoMessage() {}

func (x *QueryParam) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryParam.ProtoReflect.Descriptor instead.
func (*QueryParam) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{10}
}

func (x *QueryParam) GetSeriesKey() string {
	if x != nil {
		return x.SeriesKey
	}
	return ""
}

func (x *QueryParam) GetTagsAsKey() string {
	if x != nil {
		return x.TagsAsKey
	}
	return ""
}

func (x *QueryParam) GetQueryFields() int32 {
	if x != nil {
		return x.QueryFields
	}
	return 0
}

func (x *QueryParam) GetQueryPct() float32 {
	if x != nil {
		return x.QueryPct
	}
	return 0
}

type Unnest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expr    string   `protobuf:"bytes,1,opt,name=Expr,proto3" json:"Expr,omitempty"`
	Aliases []string `protobuf:"bytes,2,rep,name=Aliases,proto3" json:"Aliases,omitempty"`
	DstType []int32  `protobuf:"varint,3,rep,packed,name=DstType,proto3" json:"DstType,omitempty"`
}

func (x *Unnest) Reset() {
	*x = Unnest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Unnest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Unnest) ProtoMessage() {}

func (x *Unnest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Unnest.ProtoReflect.Descriptor instead.
func (*Unnest) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{11}
}

func (x *Unnest) GetExpr() string {
	if x != nil {
		return x.Expr
	}
	return ""
}

func (x *Unnest) GetAliases() []string {
	if x != nil {
		return x.Aliases
	}
	return nil
}

func (x *Unnest) GetDstType() []int32 {
	if x != nil {
		return x.DstType
	}
	return nil
}

type QuerySchema struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryFields string    `protobuf:"bytes,2,opt,name=QueryFields,proto3" json:"QueryFields,omitempty"`
	ColumnNames []string  `protobuf:"bytes,3,rep,name=ColumnNames,proto3" json:"ColumnNames,omitempty"`
	Unnests     []*Unnest `protobuf:"bytes,5,rep,name=Unnests,proto3" json:"Unnests,omitempty"`
}

func (x *QuerySchema) Reset() {
	*x = QuerySchema{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QuerySchema) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySchema) ProtoMessage() {}

func (x *QuerySchema) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySchema.ProtoReflect.Descriptor instead.
func (*QuerySchema) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{12}
}

func (x *QuerySchema) GetQueryFields() string {
	if x != nil {
		return x.QueryFields
	}
	return ""
}

func (x *QuerySchema) GetColumnNames() []string {
	if x != nil {
		return x.ColumnNames
	}
	return nil
}

func (x *QuerySchema) GetUnnests() []*Unnest {
	if x != nil {
		return x.Unnests
	}
	return nil
}

type Chunk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string       `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Tags          []*ChunkTags `protobuf:"bytes,2,rep,name=Tags,proto3" json:"Tags,omitempty"`
	TagIndex      []int64      `protobuf:"varint,3,rep,packed,name=TagIndex,proto3" json:"TagIndex,omitempty"`
	Time          []int64      `protobuf:"varint,4,rep,packed,name=Time,proto3" json:"Time,omitempty"`
	IntervalIndex []int64      `protobuf:"varint,5,rep,packed,name=IntervalIndex,proto3" json:"IntervalIndex,omitempty"`
	Columns       []*Column    `protobuf:"bytes,6,rep,name=Columns,proto3" json:"Columns,omitempty"`
}

func (x *Chunk) Reset() {
	*x = Chunk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chunk) ProtoMessage() {}

func (x *Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chunk.ProtoReflect.Descriptor instead.
func (*Chunk) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{13}
}

func (x *Chunk) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Chunk) GetTags() []*ChunkTags {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Chunk) GetTagIndex() []int64 {
	if x != nil {
		return x.TagIndex
	}
	return nil
}

func (x *Chunk) GetTime() []int64 {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *Chunk) GetIntervalIndex() []int64 {
	if x != nil {
		return x.IntervalIndex
	}
	return nil
}

func (x *Chunk) GetColumns() []*Column {
	if x != nil {
		return x.Columns
	}
	return nil
}

type ChunkTags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subset []byte `protobuf:"bytes,1,opt,name=Subset,proto3" json:"Subset,omitempty"`
}

func (x *ChunkTags) Reset() {
	*x = ChunkTags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChunkTags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChunkTags) ProtoMessage() {}

func (x *ChunkTags) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChunkTags.ProtoReflect.Descriptor instead.
func (*ChunkTags) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{14}
}

func (x *ChunkTags) GetSubset() []byte {
	if x != nil {
		return x.Subset
	}
	return nil
}

type Column struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataType      int64     `protobuf:"varint,1,opt,name=DataType,proto3" json:"DataType,omitempty"`
	FloatValues   []float64 `protobuf:"fixed64,2,rep,packed,name=FloatValues,proto3" json:"FloatValues,omitempty"`
	IntegerValues []int64   `protobuf:"varint,3,rep,packed,name=IntegerValues,proto3" json:"IntegerValues,omitempty"`
	StringBytes   []byte    `protobuf:"bytes,4,opt,name=StringBytes,proto3" json:"StringBytes,omitempty"`
	Offset        []uint32  `protobuf:"varint,8,rep,packed,name=Offset,proto3" json:"Offset,omitempty"`
	BooleanValues []bool    `protobuf:"varint,5,rep,packed,name=BooleanValues,proto3" json:"BooleanValues,omitempty"`
	Times         []int64   `protobuf:"varint,6,rep,packed,name=Times,proto3" json:"Times,omitempty"`
	NilsV2        []byte    `protobuf:"bytes,7,opt,name=NilsV2,proto3" json:"NilsV2,omitempty"`
}

func (x *Column) Reset() {
	*x = Column{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Column) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Column) ProtoMessage() {}

func (x *Column) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Column.ProtoReflect.Descriptor instead.
func (*Column) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{15}
}

func (x *Column) GetDataType() int64 {
	if x != nil {
		return x.DataType
	}
	return 0
}

func (x *Column) GetFloatValues() []float64 {
	if x != nil {
		return x.FloatValues
	}
	return nil
}

func (x *Column) GetIntegerValues() []int64 {
	if x != nil {
		return x.IntegerValues
	}
	return nil
}

func (x *Column) GetStringBytes() []byte {
	if x != nil {
		return x.StringBytes
	}
	return nil
}

func (x *Column) GetOffset() []uint32 {
	if x != nil {
		return x.Offset
	}
	return nil
}

func (x *Column) GetBooleanValues() []bool {
	if x != nil {
		return x.BooleanValues
	}
	return nil
}

func (x *Column) GetTimes() []int64 {
	if x != nil {
		return x.Times
	}
	return nil
}

func (x *Column) GetNilsV2() []byte {
	if x != nil {
		return x.NilsV2
	}
	return nil
}

type ExprOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Expr string `protobuf:"bytes,1,opt,name=Expr,proto3" json:"Expr,omitempty"`
	Ref  string `protobuf:"bytes,2,opt,name=Ref,proto3" json:"Ref,omitempty"`
}

func (x *ExprOptions) Reset() {
	*x = ExprOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExprOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExprOptions) ProtoMessage() {}

func (x *ExprOptions) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExprOptions.ProtoReflect.Descriptor instead.
func (*ExprOptions) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{16}
}

func (x *ExprOptions) GetExpr() string {
	if x != nil {
		return x.Expr
	}
	return ""
}

func (x *ExprOptions) GetRef() string {
	if x != nil {
		return x.Ref
	}
	return ""
}

type QueryNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      LogicPlanType  `protobuf:"varint,1,opt,name=Name,proto3,enum=executor.LogicPlanType" json:"Name,omitempty"`
	Inputs    [][]byte       `protobuf:"bytes,3,rep,name=Inputs,proto3" json:"Inputs,omitempty"`
	Ops       []*ExprOptions `protobuf:"bytes,6,rep,name=Ops,proto3" json:"Ops,omitempty"`
	Exchange  uint32         `protobuf:"varint,7,opt,name=Exchange,proto3" json:"Exchange,omitempty"`
	Limit     int64          `protobuf:"varint,8,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset    int64          `protobuf:"varint,9,opt,name=Offset,proto3" json:"Offset,omitempty"`
	LimitType int64          `protobuf:"varint,10,opt,name=limitType,proto3" json:"limitType,omitempty"`
	AggType   AggType        `protobuf:"varint,11,opt,name=AggType,proto3,enum=executor.AggType" json:"AggType,omitempty"`
}

func (x *QueryNode) Reset() {
	*x = QueryNode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *QueryNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryNode) ProtoMessage() {}

func (x *QueryNode) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryNode.ProtoReflect.Descriptor instead.
func (*QueryNode) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{17}
}

func (x *QueryNode) GetName() LogicPlanType {
	if x != nil {
		return x.Name
	}
	return LogicPlanType_LogicalExchange
}

func (x *QueryNode) GetInputs() [][]byte {
	if x != nil {
		return x.Inputs
	}
	return nil
}

func (x *QueryNode) GetOps() []*ExprOptions {
	if x != nil {
		return x.Ops
	}
	return nil
}

func (x *QueryNode) GetExchange() uint32 {
	if x != nil {
		return x.Exchange
	}
	return 0
}

func (x *QueryNode) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *QueryNode) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *QueryNode) GetLimitType() int64 {
	if x != nil {
		return x.LimitType
	}
	return 0
}

func (x *QueryNode) GetAggType() AggType {
	if x != nil {
		return x.AggType
	}
	return AggType_TagSet
}

type RemoteQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Database  string     `protobuf:"bytes,1,opt,name=Database,proto3" json:"Database,omitempty"`
	PtID      uint32     `protobuf:"varint,2,opt,name=PtID,proto3" json:"PtID,omitempty"`
	ShardIDs  []uint64   `protobuf:"varint,3,rep,packed,name=ShardIDs,proto3" json:"ShardIDs,omitempty"`
	Opt       []byte     `protobuf:"bytes,4,opt,name=Opt,proto3" json:"Opt,omitempty"`
	NodeID    uint64     `protobuf:"varint,5,opt,name=NodeID,proto3" json:"NodeID,omitempty"`
	Analyze   bool       `protobuf:"varint,6,opt,name=analyze,proto3" json:"analyze,omitempty"`
	QueryNode []byte     `protobuf:"bytes,7,opt,name=QueryNode,proto3" json:"QueryNode,omitempty"`
	PtQuerys  []*PtQuery `protobuf:"bytes,8,rep,name=PtQuerys,proto3" json:"PtQuerys,omitempty"`
}

func (x *RemoteQuery) Reset() {
	*x = RemoteQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RemoteQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteQuery) ProtoMessage() {}

func (x *RemoteQuery) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteQuery.ProtoReflect.Descriptor instead.
func (*RemoteQuery) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{18}
}

func (x *RemoteQuery) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *RemoteQuery) GetPtID() uint32 {
	if x != nil {
		return x.PtID
	}
	return 0
}

func (x *RemoteQuery) GetShardIDs() []uint64 {
	if x != nil {
		return x.ShardIDs
	}
	return nil
}

func (x *RemoteQuery) GetOpt() []byte {
	if x != nil {
		return x.Opt
	}
	return nil
}

func (x *RemoteQuery) GetNodeID() uint64 {
	if x != nil {
		return x.NodeID
	}
	return 0
}

func (x *RemoteQuery) GetAnalyze() bool {
	if x != nil {
		return x.Analyze
	}
	return false
}

func (x *RemoteQuery) GetQueryNode() []byte {
	if x != nil {
		return x.QueryNode
	}
	return nil
}

func (x *RemoteQuery) GetPtQuerys() []*PtQuery {
	if x != nil {
		return x.PtQuerys
	}
	return nil
}

type ShardInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ID      uint64 `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Path    string `protobuf:"bytes,2,opt,name=Path,proto3" json:"Path,omitempty"`
	Version uint32 `protobuf:"varint,3,opt,name=Version,proto3" json:"Version,omitempty"`
}

func (x *ShardInfo) Reset() {
	*x = ShardInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ShardInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShardInfo) ProtoMessage() {}

func (x *ShardInfo) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShardInfo.ProtoReflect.Descriptor instead.
func (*ShardInfo) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{19}
}

func (x *ShardInfo) GetID() uint64 {
	if x != nil {
		return x.ID
	}
	return 0
}

func (x *ShardInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ShardInfo) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type PtQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PtID       uint32       `protobuf:"varint,1,opt,name=PtID,proto3" json:"PtID,omitempty"`
	ShardInfos []*ShardInfo `protobuf:"bytes,2,rep,name=ShardInfos,proto3" json:"ShardInfos,omitempty"`
}

func (x *PtQuery) Reset() {
	*x = PtQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_internal_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PtQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PtQuery) ProtoMessage() {}

func (x *PtQuery) ProtoReflect() protoreflect.Message {
	mi := &file_internal_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PtQuery.ProtoReflect.Descriptor instead.
func (*PtQuery) Descriptor() ([]byte, []int) {
	return file_internal_proto_rawDescGZIP(), []int{20}
}

func (x *PtQuery) GetPtID() uint32 {
	if x != nil {
		return x.PtID
	}
	return 0
}

func (x *PtQuery) GetShardInfos() []*ShardInfo {
	if x != nil {
		return x.ShardInfos
	}
	return nil
}

var File_internal_proto protoreflect.FileDescriptor

var file_internal_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x22, 0x92, 0x0b, 0x0a, 0x10, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x6f, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x45, 0x78, 0x70, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x45, 0x78, 0x70, 0x72, 0x12, 0x22, 0x0a, 0x03, 0x41, 0x75, 0x78, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e,
	0x56, 0x61, 0x72, 0x52, 0x65, 0x66, 0x52, 0x03, 0x41, 0x75, 0x78, 0x12, 0x2f, 0x0a, 0x07, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x07, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x2e, 0x0a, 0x08,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a,
	0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x44, 0x69, 0x6d, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x07,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x6f, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42,
	0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x46, 0x69, 0x6c, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x46,
	0x69, 0x6c, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x46, 0x69, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x41, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x53, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x53, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x53, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x72, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x53, 0x74, 0x72, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x4d, 0x61, 0x78, 0x53, 0x65,
	0x72, 0x69, 0x65, 0x73, 0x4e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x4d, 0x61, 0x78,
	0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x12, 0x18, 0x0a, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x65, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x65,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x4d, 0x61, 0x78, 0x50, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65,
	0x6c, 0x12, 0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x69, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x48, 0x69, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x61, 0x72, 0x79, 0x54, 0x72, 0x65, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x15, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x61, 0x72, 0x79,
	0x54, 0x72, 0x65, 0x65, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x49, 0x64, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4b, 0x65, 0x79,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4b, 0x65,
	0x79, 0x12, 0x26, 0x0a, 0x0e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x41, 0x6c, 0x6c, 0x44,
	0x69, 0x6d, 0x73, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x42, 0x79, 0x41, 0x6c, 0x6c, 0x44, 0x69, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x6f, 0x72,
	0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53,
	0x6f, 0x72, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x48, 0x61, 0x73,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x57, 0x69, 0x6c, 0x64, 0x63, 0x61, 0x72, 0x64, 0x18, 0x23, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x10, 0x48, 0x61, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x57, 0x69, 0x6c,
	0x64, 0x63, 0x61, 0x72, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x43, 0x75, 0x72, 0x72, 0x49, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4c,
	0x6f, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x75, 0x72, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x49, 0x6e, 0x63, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x25, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x49, 0x6e, 0x63, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x74, 0x65,
	0x72, 0x49, 0x44, 0x18, 0x26, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x49, 0x74, 0x65, 0x72, 0x49,
	0x44, 0x12, 0x1c, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x27,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x50, 0x72, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x53, 0x74, 0x65, 0x70, 0x18, 0x28, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x53,
	0x74, 0x65, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x29, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x4c, 0x6f, 0x6f,
	0x6b, 0x42, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x4c, 0x6f, 0x6f, 0x6b, 0x42, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x12,
	0x20, 0x0a, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x18, 0x2c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x50,
	0x72, 0x6f, 0x6d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x52, 0x65, 0x61, 0x64, 0x18, 0x2d, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0e, 0x50, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x52,
	0x65, 0x61, 0x64, 0x1a, 0x3a, 0x0a, 0x0c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xfa, 0x02, 0x0a, 0x0b, 0x4d, 0x65, 0x61, 0x73, 0x75, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x52,
	0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x52, 0x65, 0x67,
	0x65, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x52, 0x65, 0x67, 0x65, 0x78, 0x12,
	0x1a, 0x0a, 0x08, 0x49, 0x73, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x49, 0x73, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x74, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0a, 0x4f, 0x62, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f,
	0x72, 0x2e, 0x4f, 0x62, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x4f, 0x62,
	0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x49, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x22, 0xc6, 0x01, 0x0a,
	0x0d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x52, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x52, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x4f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x04,
	0x4f, 0x69, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x12, 0x33, 0x0a, 0x0a, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4c, 0x69, 0x73,
	0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x0a, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x21, 0x0a, 0x09, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x05, 0x49, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x3b, 0x0a, 0x0c, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2b, 0x0a, 0x05, 0x49, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x6f, 0x72, 0x2e, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x77, 0x0a, 0x0b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x69, 0x7a, 0x65, 0x72, 0x73, 0x12, 0x30, 0x0a, 0x13,
	0x54, 0x69, 0x6d, 0x65, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9e,
	0x01, 0x0a, 0x0a, 0x4f, 0x62, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x41, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x41, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x53, 0x6b, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x53, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x42, 0x61, 0x73, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22,
	0x3e, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22,
	0x41, 0x0a, 0x0d, 0x49, 0x74, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4e, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x4e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x50, 0x6f, 0x69, 0x6e,
	0x74, 0x4e, 0x22, 0x2e, 0x0a, 0x06, 0x56, 0x61, 0x72, 0x52, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03,
	0x56, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x56, 0x61, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x12,
	0x1c, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x41, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x54, 0x61, 0x67, 0x73, 0x41, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x20, 0x0a,
	0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12,
	0x1a, 0x0a, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x63, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x08, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x63, 0x74, 0x22, 0x50, 0x0a, 0x06, 0x55,
	0x6e, 0x6e, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x45, 0x78, 0x70, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x45, 0x78, 0x70, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x6c, 0x69,
	0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x41, 0x6c, 0x69, 0x61,
	0x73, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x07, 0x44, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x7d, 0x0a,
	0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x20, 0x0a, 0x0b,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x20,
	0x0a, 0x0b, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x2a, 0x0a, 0x07, 0x55, 0x6e, 0x6e, 0x65, 0x73, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x55, 0x6e, 0x6e,
	0x65, 0x73, 0x74, 0x52, 0x07, 0x55, 0x6e, 0x6e, 0x65, 0x73, 0x74, 0x73, 0x22, 0xc6, 0x01, 0x0a,
	0x05, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x54, 0x61,
	0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x2e, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x54, 0x61, 0x67, 0x73, 0x52, 0x04, 0x54,
	0x61, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x12, 0x0a, 0x04, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x04, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x2a, 0x0a, 0x07, 0x43, 0x6f, 0x6c,
	0x75, 0x6d, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x52, 0x07, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x73, 0x22, 0x23, 0x0a, 0x09, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x54, 0x61,
	0x67, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x06, 0x53, 0x75, 0x62, 0x73, 0x65, 0x74, 0x22, 0xfa, 0x01, 0x0a, 0x06, 0x43,
	0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0b, 0x46, 0x6c, 0x6f, 0x61, 0x74, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x65, 0x72, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x49, 0x6e, 0x74, 0x65,
	0x67, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x4f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x4f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x08, 0x52, 0x0d, 0x42, 0x6f, 0x6f, 0x6c,
	0x65, 0x61, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x05, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x4e, 0x69, 0x6c, 0x73, 0x56, 0x32, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x4e, 0x69, 0x6c, 0x73, 0x56, 0x32, 0x22, 0x33, 0x0a, 0x0b, 0x45, 0x78, 0x70, 0x72, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x45, 0x78, 0x70, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x45, 0x78, 0x70, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x52, 0x65,
	0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x52, 0x65, 0x66, 0x22, 0x8e, 0x02, 0x0a,
	0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x2b, 0x0a, 0x04, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x06, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x73, 0x12,
	0x27, 0x0a, 0x03, 0x4f, 0x70, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x03, 0x4f, 0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x2b, 0x0a, 0x07, 0x41, 0x67, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x41, 0x67, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x41, 0x67, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0xea, 0x01,
	0x0a, 0x0b, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x74, 0x49,
	0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x50, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a,
	0x08, 0x53, 0x68, 0x61, 0x72, 0x64, 0x49, 0x44, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52,
	0x08, 0x53, 0x68, 0x61, 0x72, 0x64, 0x49, 0x44, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x4f, 0x70, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x4f, 0x70, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x09, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x08, 0x50,
	0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x52, 0x08, 0x50, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79, 0x73, 0x22, 0x49, 0x0a, 0x09, 0x53, 0x68,
	0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x52, 0x0a, 0x07, 0x50, 0x74, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x50, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x50, 0x74, 0x49, 0x44, 0x12, 0x33, 0x0a, 0x0a, 0x53, 0x68, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x6f, 0x72, 0x2e, 0x53, 0x68, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x53,
	0x68, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2a, 0x34, 0x0a, 0x07, 0x41, 0x67, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x61, 0x67, 0x53, 0x65, 0x74, 0x10, 0x00,
	0x12, 0x11, 0x0a, 0x0d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x63,
	0x74, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x02, 0x2a,
	0x84, 0x07, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x50, 0x6c, 0x61, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x63, 0x61, 0x6e, 0x10, 0x02, 0x12, 0x14,
	0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4d,
	0x65, 0x72, 0x67, 0x65, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x53, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d,
	0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x10, 0x06, 0x12,
	0x11, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65,
	0x10, 0x07, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x76, 0x61, 0x6c, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x53, 0x65, 0x72, 0x69, 0x65, 0x73, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x64, 0x65, 0x72, 0x10, 0x0a, 0x12, 0x14, 0x0a,
	0x10, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x54, 0x61, 0x67, 0x53, 0x75, 0x62, 0x73, 0x65,
	0x74, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x69,
	0x6c, 0x6c, 0x10, 0x0c, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x41,
	0x6c, 0x69, 0x67, 0x6e, 0x10, 0x0d, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x4d, 0x73, 0x74, 0x10, 0x0e, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x0f, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6c, 0x69, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x10, 0x10, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x42, 0x6c, 0x61, 0x6e, 0x6b, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11,
	0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x48, 0x74, 0x74, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x10, 0x12, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x46, 0x75,
	0x6c, 0x6c, 0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x13, 0x12, 0x1b, 0x0a, 0x17, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x74, 0x6f, 0x53, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x10, 0x14, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c,
	0x53, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x10, 0x15, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x70,
	0x6c, 0x69, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x10, 0x16, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x48, 0x6f, 0x6c, 0x74, 0x57, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x73,
	0x10, 0x17, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x75, 0x62,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x10, 0x18, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x10, 0x19, 0x12, 0x12, 0x0a, 0x0e, 0x4c,
	0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x10, 0x1a, 0x12,
	0x19, 0x0a, 0x15, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x48, 0x74, 0x74, 0x70, 0x53, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x48, 0x69, 0x6e, 0x74, 0x10, 0x1b, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x6f,
	0x67, 0x69, 0x63, 0x61, 0x6c, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x10, 0x1c, 0x12, 0x15, 0x0a,
	0x11, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x44, 0x75, 0x6d, 0x6d, 0x79, 0x53, 0x68, 0x61,
	0x72, 0x64, 0x10, 0x1d, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x54,
	0x53, 0x53, 0x50, 0x53, 0x63, 0x61, 0x6e, 0x10, 0x1e, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x6f, 0x67,
	0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x41, 0x70, 0x70, 0x65, 0x6e, 0x64, 0x10, 0x1f,
	0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x10,
	0x20, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x48, 0x61, 0x73, 0x68,
	0x4d, 0x65, 0x72, 0x67, 0x65, 0x10, 0x21, 0x12, 0x1a, 0x0a, 0x16, 0x4c, 0x6f, 0x67, 0x69, 0x63,
	0x61, 0x6c, 0x53, 0x70, 0x61, 0x72, 0x73, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x53, 0x63, 0x61,
	0x6e, 0x10, 0x22, 0x12, 0x1c, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x43, 0x6f,
	0x6c, 0x75, 0x6d, 0x6e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x61, 0x64, 0x65, 0x72, 0x10,
	0x23, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x48, 0x61, 0x73, 0x68,
	0x41, 0x67, 0x67, 0x10, 0x24, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c,
	0x4a, 0x6f, 0x69, 0x6e, 0x10, 0x25, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61,
	0x6c, 0x42, 0x69, 0x6e, 0x4f, 0x70, 0x10, 0x26, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x6f, 0x67, 0x69,
	0x63, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6d, 0x53, 0x75, 0x62, 0x71, 0x75, 0x65, 0x72, 0x79, 0x10,
	0x27, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x69, 0x63, 0x61, 0x6c, 0x50, 0x72, 0x6f, 0x6d,
	0x53, 0x6f, 0x72, 0x74, 0x10, 0x28, 0x42, 0x0d, 0x5a, 0x0b, 0x2e, 0x2f, 0x3b, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_proto_rawDescOnce sync.Once
	file_internal_proto_rawDescData = file_internal_proto_rawDesc
)

func file_internal_proto_rawDescGZIP() []byte {
	file_internal_proto_rawDescOnce.Do(func() {
		file_internal_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_proto_rawDescData)
	})
	return file_internal_proto_rawDescData
}

var file_internal_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_internal_proto_goTypes = []interface{}{
	(AggType)(0),             // 0: executor.AggType
	(LogicPlanType)(0),       // 1: executor.LogicPlanType
	(*ProcessorOptions)(nil), // 2: executor.ProcessorOptions
	(*Measurement)(nil),      // 3: executor.Measurement
	(*IndexRelation)(nil),    // 4: executor.IndexRelation
	(*IndexList)(nil),        // 5: executor.IndexList
	(*IndexOptions)(nil),     // 6: executor.IndexOptions
	(*IndexOption)(nil),      // 7: executor.IndexOption
	(*ObsOptions)(nil),       // 8: executor.ObsOptions
	(*Interval)(nil),         // 9: executor.Interval
	(*IteratorStats)(nil),    // 10: executor.IteratorStats
	(*VarRef)(nil),           // 11: executor.VarRef
	(*QueryParam)(nil),       // 12: executor.QueryParam
	(*Unnest)(nil),           // 13: executor.Unnest
	(*QuerySchema)(nil),      // 14: executor.QuerySchema
	(*Chunk)(nil),            // 15: executor.Chunk
	(*ChunkTags)(nil),        // 16: executor.ChunkTags
	(*Column)(nil),           // 17: executor.Column
	(*ExprOptions)(nil),      // 18: executor.ExprOptions
	(*QueryNode)(nil),        // 19: executor.QueryNode
	(*RemoteQuery)(nil),      // 20: executor.RemoteQuery
	(*ShardInfo)(nil),        // 21: executor.ShardInfo
	(*PtQuery)(nil),          // 22: executor.PtQuery
	nil,                      // 23: executor.ProcessorOptions.GroupByEntry
}
var file_internal_proto_depIdxs = []int32{
	11, // 0: executor.ProcessorOptions.Aux:type_name -> executor.VarRef
	3,  // 1: executor.ProcessorOptions.Sources:type_name -> executor.Measurement
	9,  // 2: executor.ProcessorOptions.Interval:type_name -> executor.Interval
	23, // 3: executor.ProcessorOptions.GroupBy:type_name -> executor.ProcessorOptions.GroupByEntry
	4,  // 4: executor.Measurement.indexRelation:type_name -> executor.IndexRelation
	8,  // 5: executor.Measurement.ObsOptions:type_name -> executor.ObsOptions
	5,  // 6: executor.IndexRelation.IndexLists:type_name -> executor.IndexList
	6,  // 7: executor.IndexRelation.IndexOptions:type_name -> executor.IndexOptions
	7,  // 8: executor.IndexOptions.Infos:type_name -> executor.IndexOption
	13, // 9: executor.QuerySchema.Unnests:type_name -> executor.Unnest
	16, // 10: executor.Chunk.Tags:type_name -> executor.ChunkTags
	17, // 11: executor.Chunk.Columns:type_name -> executor.Column
	1,  // 12: executor.QueryNode.Name:type_name -> executor.LogicPlanType
	18, // 13: executor.QueryNode.Ops:type_name -> executor.ExprOptions
	0,  // 14: executor.QueryNode.AggType:type_name -> executor.AggType
	22, // 15: executor.RemoteQuery.PtQuerys:type_name -> executor.PtQuery
	21, // 16: executor.PtQuery.ShardInfos:type_name -> executor.ShardInfo
	17, // [17:17] is the sub-list for method output_type
	17, // [17:17] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_internal_proto_init() }
func file_internal_proto_init() {
	if File_internal_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_internal_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessorOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Measurement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexRelation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IndexOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ObsOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Interval); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IteratorStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VarRef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryParam); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Unnest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QuerySchema); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Chunk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChunkTags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Column); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExprOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*QueryNode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RemoteQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ShardInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PtQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_internal_proto_goTypes,
		DependencyIndexes: file_internal_proto_depIdxs,
		EnumInfos:         file_internal_proto_enumTypes,
		MessageInfos:      file_internal_proto_msgTypes,
	}.Build()
	File_internal_proto = out.File
	file_internal_proto_rawDesc = nil
	file_internal_proto_goTypes = nil
	file_internal_proto_depIdxs = nil
}
