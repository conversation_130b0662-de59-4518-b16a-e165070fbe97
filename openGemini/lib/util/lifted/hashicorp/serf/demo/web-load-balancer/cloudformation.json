{"AWSTemplateFormatVersion": "2010-09-09", "Mappings": {"AWSNATAMI": {"us-east-1": {"AMI": "ami-c6699baf"}, "us-west-2": {"AMI": "ami-52ff7262"}, "us-west-1": {"AMI": "ami-3bcc9e7e"}, "eu-west-1": {"AMI": "ami-0b5b6c7f"}, "ap-southeast-1": {"AMI": "ami-02eb9350"}, "ap-southeast-2": {"AMI": "ami-ab990e91"}, "ap-northeast-1": {"AMI": "ami-14d86d15"}, "sa-east-1": {"AMI": "ami-0439e619"}}, "AWSINSTAMI": {"us-east-1": {"AMI": "ami-a73264ce"}, "us-west-2": {"AMI": "ami-6aad335a"}, "us-west-1": {"AMI": "ami-acf9cde9"}, "eu-west-1": {"AMI": "ami-8e987ef9"}, "ap-southeast-1": {"AMI": "ami-b84e04ea"}, "ap-southeast-2": {"AMI": "ami-3d128f07"}, "ap-northeast-1": {"AMI": "ami-3f32ac3e"}, "sa-east-1": {"AMI": "ami-35258228"}}}, "Parameters": {"WebNodes": {"Type": "String", "Default": "5", "Description": "Number of web servers to launch."}}, "Outputs": {"LoadBalancerIP": {"Value": {"Ref": "LoadBalancerIP"}}}, "Resources": {"Gateway": {"Type": "AWS::EC2::InternetGateway"}, "VPC": {"Type": "AWS::EC2::VPC", "Properties": {"CidrBlock": "10.0.0.0/16", "EnableDnsHostnames": true}}, "VPCGateway": {"Type": "AWS::EC2::VPCGatewayAttachment", "Properties": {"InternetGatewayId": {"Ref": "Gateway"}, "VpcId": {"Ref": "VPC"}}}, "PublicSubnet": {"Type": "AWS::EC2::Subnet", "Properties": {"CidrBlock": "10.0.0.0/24", "VpcId": {"Ref": "VPC"}}}, "PrivateSubnet": {"Type": "AWS::EC2::Subnet", "Properties": {"CidrBlock": "********/24", "VpcId": {"Ref": "VPC"}}}, "PrivateSubnetRoute": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "PrivateRouteTable"}, "SubnetId": {"Ref": "PrivateSubnet"}}}, "PrivateRouteTable": {"Type": "AWS::EC2::RouteTable", "Properties": {"VpcId": {"Ref": "VPC"}}}, "PrivateRouteGlobal": {"Type": "AWS::EC2::Route", "Properties": {"RouteTableId": {"Ref": "PrivateRouteTable"}, "DestinationCidrBlock": "0.0.0.0/0", "InstanceId": {"Ref": "NATDevice"}}, "DependsOn": "PublicRouteGlobal"}, "PublicSubnetRoute": {"Type": "AWS::EC2::SubnetRouteTableAssociation", "Properties": {"RouteTableId": {"Ref": "PublicRouteTable"}, "SubnetId": {"Ref": "PublicSubnet"}}}, "PublicRouteTable": {"Type": "AWS::EC2::RouteTable", "Properties": {"VpcId": {"Ref": "VPC"}}}, "PublicRouteGlobal": {"Type": "AWS::EC2::Route", "Properties": {"RouteTableId": {"Ref": "PublicRouteTable"}, "DestinationCidrBlock": "0.0.0.0/0", "GatewayId": {"Ref": "Gateway"}}}, "NATIPAddress": {"Type": "AWS::EC2::EIP", "Properties": {"Domain": "vpc", "InstanceId": {"Ref": "NATDevice"}}, "DependsOn": "VPCGateway"}, "NATDevice": {"Type": "AWS::EC2::Instance", "Properties": {"SubnetId": {"Ref": "PublicSubnet"}, "SourceDestCheck": "false", "ImageId": {"Fn::FindInMap": ["AWSNATAMI", {"Ref": "AWS::Region"}, "AMI"]}, "SecurityGroupIds": [{"Ref": "InstanceSecurityGroup"}], "Tags": [{"Key": "Name", "Value": "Serf Demo NAT Device"}]}}, "LoadBalancer": {"Type": "AWS::EC2::Instance", "Properties": {"ImageId": {"Fn::FindInMap": ["AWSINSTAMI", {"Ref": "AWS::Region"}, "AMI"]}, "PrivateIpAddress": "********", "SecurityGroupIds": [{"Ref": "InstanceSecurityGroup"}], "SubnetId": {"Ref": "PublicSubnet"}, "Tags": [{"Key": "Name", "Value": "Serf Demo LB"}], "UserData": "IyEvYmluL2Jhc2gKTk9ERV9TRVRVUF9VUkw9Imh0dHBzOi8vcmF3LmdpdGh1Yi5jb20vaGFzaGljb3JwL3NlcmYvbWFzdGVyL2RlbW8vd2ViLWxvYWQtYmFsYW5jZXIvc2V0dXBfbG9hZF9iYWxhbmNlci5zaCIKClNFUkZfU0VUVVBfVVJMPSJodHRwczovL3Jhdy5naXRodWIuY29tL2hhc2hpY29ycC9zZXJmL21hc3Rlci9kZW1vL3dlYi1sb2FkLWJhbGFuY2VyL3NldHVwX3NlcmYuc2giCgojIFNldHVwIHRoZSBub2RlIGl0c2VsZgp3Z2V0IC1PIC0gJE5PREVfU0VUVVBfVVJMIHwgYmFzaAoKIyBTZXR1cCB0aGUgc2VyZiBhZ2VudApleHBvcnQgU0VSRl9ST0xFPSJsYiIKd2dldCAtTyAtICRTRVJGX1NFVFVQX1VSTCB8IGJhc2gK"}, "DependsOn": "PublicRouteGlobal"}, "LoadBalancerIP": {"Type": "AWS::EC2::EIP", "Properties": {"InstanceId": {"Ref": "LoadBalancer"}, "Domain": "vpc"}, "DependsOn": "VPCGateway"}, "WebGroup": {"Type": "AWS::AutoScaling::AutoScalingGroup", "Properties": {"AvailabilityZones": [{"Fn::GetAtt": ["PrivateSubnet", "AvailabilityZone"]}], "LaunchConfigurationName": {"Ref": "WebLaunchConfig"}, "DesiredCapacity": {"Ref": "WebNodes"}, "MinSize": {"Ref": "WebNodes"}, "MaxSize": {"Ref": "WebNodes"}, "VPCZoneIdentifier": [{"Ref": "PrivateSubnet"}]}, "DependsOn": ["NATDevice", "NATIPAddress", "PrivateRouteGlobal"]}, "WebLaunchConfig": {"Type": "AWS::AutoScaling::LaunchConfiguration", "Properties": {"ImageId": {"Fn::FindInMap": ["AWSINSTAMI", {"Ref": "AWS::Region"}, "AMI"]}, "InstanceType": "m1.small", "SecurityGroups": [{"Ref": "InstanceSecurityGroup"}], "UserData": "IyEvYmluL2Jhc2gKTk9ERV9TRVRVUF9VUkw9Imh0dHBzOi8vcmF3LmdpdGh1Yi5jb20vaGFzaGljb3JwL3NlcmYvbWFzdGVyL2RlbW8vd2ViLWxvYWQtYmFsYW5jZXIvc2V0dXBfd2ViX3NlcnZlci5zaCIKClNFUkZfU0VUVVBfVVJMPSJodHRwczovL3Jhdy5naXRodWIuY29tL2hhc2hpY29ycC9zZXJmL21hc3Rlci9kZW1vL3dlYi1sb2FkLWJhbGFuY2VyL3NldHVwX3NlcmYuc2giCgojIFNldHVwIHRoZSBub2RlIGl0c2VsZgp3Z2V0IC1PIC0gJE5PREVfU0VUVVBfVVJMIHwgYmFzaAoKIyBTZXR1cCB0aGUgc2VyZiBhZ2VudApleHBvcnQgU0VSRl9ST0xFPSJ3ZWIiCndnZXQgLU8gLSAkU0VSRl9TRVRVUF9VUkwgfCBiYXNoCg=="}}, "InstanceSecurityGroup": {"Type": "AWS::EC2::SecurityGroup", "Properties": {"GroupDescription": "Serf demo security group", "VpcId": {"Ref": "VPC"}, "SecurityGroupIngress": [{"IpProtocol": "icmp", "FromPort": "-1", "ToPort": "-1", "CidrIp": "0.0.0.0/0"}, {"IpProtocol": "tcp", "FromPort": "22", "ToPort": "22", "CidrIp": "0.0.0.0/0"}, {"IpProtocol": "tcp", "FromPort": "80", "ToPort": "80", "CidrIp": "0.0.0.0/0"}, {"IpProtocol": "tcp", "FromPort": "9999", "ToPort": "9999", "CidrIp": "0.0.0.0/0"}]}}, "InstanceSecurityGroupSelfRule": {"Type": "AWS::EC2::SecurityGroupIngress", "Properties": {"GroupId": {"Ref": "InstanceSecurityGroup"}, "IpProtocol": "-1", "FromPort": "0", "ToPort": "65535", "SourceSecurityGroupId": {"Ref": "InstanceSecurityGroup"}}}}}