# Serf Agent (systemd service unit)
[Unit]
Description=Serf Agent
After=syslog.target
After=network.target

[Service]
Type=simple
# Ensure the configuration directory exists.
ExecStartPre=/usr/bin/install -d -g root -o root /etc/serf/
ExecStart=/usr/local/bin/serf agent -config-dir=/etc/serf/
# Use SIGINT instead of SIGTERM so serf can depart the cluster.
KillSignal=SIGINT
# Restart on success, failure, and any emitted signals like HUP.
Restart=always
# Wait ten seconds before respawn attempts.
RestartSec=10

[Install]
WantedBy=multi-user.target
