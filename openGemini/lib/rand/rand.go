// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package rand

import (
	"crypto/rand"
	"encoding/binary"
	rand2 "math/rand"
)

const (
	rngMask = (1 << 63) - 1
)

/* #nosec */
var defaultRand = rand2.New(&Source{})

type Source struct {
}

func (s Source) Int63() int64 {
	var i uint64
	err := binary.Read(rand.Reader, binary.LittleEndian, &i)
	if err != nil {
		return 0
	}
	return int64(i & rngMask)
}

func (s Source) Seed(int64) {

}

func Int63() int64 {
	return defaultRand.Int63()
}

func Intn(n int) int {
	return defaultRand.Intn(n)
}

func Int63n(n int64) int64 {
	return defaultRand.Int63n(n)
}

func Float64() float64 {
	return defaultRand.Float64()
}

func Int31n(n int32) int32 {
	return defaultRand.Int31n(n)
}
