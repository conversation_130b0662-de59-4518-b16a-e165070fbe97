// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package sysinfo

import (
	"fmt"
	"os"
)

func TotalMemory() (uint64, error) {
	return 8 * 1024 * 1024 * 1024, nil
}

func Suicide() {
	pid := os.Getpid()
	p, err := os.FindProcess(pid)
	if err != nil {
		fmt.Printf("cannot find current process: %v\n", err)
	}
	if err = p.Kill(); err != nil {
		fmt.Printf("error process syscall.Kill:%v\n", err)
	}
}
