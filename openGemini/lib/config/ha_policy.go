/*
Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package config

import "github.com/openGemini/openGemini/lib/errno"

type HAPolicy uint8

const (
	WriteAvailableFirst HAPolicy = iota
	SharedStorage
	Replication
	PolicyEnd
)

const (
	WAFPolicy = "write-available-first"
	SSPolicy  = "shared-storage"
	RepPolicy = "replication"
)

var policies = map[string]HAPolicy{
	WAFPolicy: WriteAvailableFirst,
	SSPolicy:  SharedStorage,
	RepPolicy: Replication,
}

var policy HAPolicy

func SetHaPolicy(haPolicy string) error {
	ok := false
	policy, ok = policies[haPolicy]
	if !ok {
		return errno.NewError(errno.InvalidHaPolicy)
	}
	return nil
}

func GetHaPolicy() HAPolicy {
	return policy
}

func IsReplication() bool {
	return policy == Replication
}

func IsSharedStorage() bool {
	return policy == SharedStorage
}

var hardWrite bool

func SetHardWrite(hardWriteConf bool) {
	hardWrite = hardWriteConf
}

func IsHardWrite() bool {
	return hardWrite
}
