// Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package config

type SFS struct {
	DataDir string `toml:"store-data-dir"`
}

var sfsConfig = &SFS{}

func NewSFS() *SFS {
	return &SFS{}
}

func (s *SFS) GetDataDir() string {
	return s.DataDir
}

func GetSFSSConfig() *SFS {
	return sfsConfig
}

func GetDataDir() string {
	return sfsConfig.DataDir
}

func SetSFSConfig(dataDir string) {
	sfsConfig.DataDir = dataDir
}
