// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package mlf_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/openGemini/openGemini/lib/compress/mlf"
	"github.com/openGemini/openGemini/lib/rand"
)

func TestCodec(t *testing.T) {
	var values [][]float64
	values = append(values, []float64{0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.05, 0.02, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.06, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.04, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.03, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.04, 0.02, 0.01, 0.03, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.04, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.05, 0.02, 0.02, 0.02, 0.02, 0.06, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.05, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.03, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.04, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.07, 0.01, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.01, 0.03, 0.01, 0.04, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.04, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.06, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.05, 0.02, 0.02, 0.02, 0.03, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.01, 0.02, 0.01, 0.05, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.03, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.03, 0.01, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.06, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.05, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.03, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.01, 0.01, 0.02, 0.01, 0.02, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.06, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.01, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.01, 0.02, 0.02, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.08, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.01, 0.02, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.01, 0.02, 0.02, 0.04, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.03, 0.02, 0.03, 0.01, 0.01, 0.01, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.05, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.01, 0.02, 0.02, 0.02, 0.07, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02})
	values = append(values, []float64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.02, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})
	values = append(values, []float64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})
	values = append(values, []float64{347825.67, 417043.47, 245127.68, 222727.34, 304815.12, 235449.79, 356157.36, 193551.88, 258867.77, 229786.82, 297521.74, 322992.43, 237291.68, 185545.54, 397370.18, 278911.26, 268605, 257500.43, 329741.22, 214250.75, 302599.9, 300568.01, 387193.03, 212229.41, 310504.44, 452619.88, 468398.64, 473590.58, 553941.43, 302795.54, 358757.3, 297600.08, 234206.41, 243489.35, 493094.59, 384792.36, 421842.39, 511633.09, 510388.35, 266643.1, 419192.9, 711190.9, 631030.23, 517596.05, 465079.79, 423334.81, 676096.14, 419330.75, 529720.56, 537256.19, 708575.32, 677460.88, 669287.07, 705920.76, 550140.3, 944416.82, 755661.71, 1.30344873e+06, 703023.25, 591960.49, 1.04289687e+06, 921018.33, 788966.96, 1.28257045e+06, 1.42160213e+06, 2.69651908e+06, 1.3541689e+06, 1.14771819e+06, 2.19034438e+06, 2.40593495e+06, 2.02026147e+06, 3.65407458e+06, 1.79470766e+06, 2.17766528e+06, 1.4374566e+06, 1.22481981e+06, 2.25298126e+06, 2.21353425e+06, 3.7894888e+06, 2.37910363e+06, 2.52214992e+06, 1.84014823e+06, 1.76969873e+06, 1.75119209e+06, 3.18397093e+06, 4.9616607e+06, 2.08788458e+06, 2.08697796e+06, 2.22657264e+06, 1.69446123e+06, 2.11732142e+06, 2.57867315e+06, 2.43820788e+06, 2.08180322e+06, 1.56423814e+06, 2.61504366e+06, 2.79920432e+06, 1.96516444e+06, 2.02950792e+06, 1.93261879e+06, 3.16423158e+06, 2.56673095e+06, 1.95861851e+06, 2.23033571e+06, 3.07257613e+06, 2.50178192e+06, 1.78220099e+06, 2.64933975e+06, 3.87485109e+06, 3.0450041e+06, 2.42284371e+06, 3.23069032e+06, 2.96853839e+06, 3.60808618e+06, 2.6084696e+06, 2.28116008e+06, 2.26011371e+06, 3.54694259e+06, 3.33941564e+06, 2.76425682e+06, 2.71971977e+06, 3.71030652e+06, 2.54524371e+06, 3.56957412e+06, 2.88087063e+06, 3.22658597e+06, 2.57201108e+06, 2.65675016e+06, 3.46881534e+06, 4.0562257e+06, 2.60860484e+06, 3.03117651e+06, 3.86570178e+06, 2.53881327e+06, 2.07682559e+06, 4.44044232e+06, 3.27740324e+06, 3.28736042e+06, 2.22359943e+06, 2.65673694e+06, 2.60481681e+06, 3.25378993e+06, 3.12573208e+06, 2.69433686e+06, 2.53570943e+06, 3.06700354e+06, 3.39486183e+06, 2.79469449e+06, 2.85412868e+06, 3.93690622e+06, 2.543113e+06, 2.36447188e+06, 2.07150705e+06, 1.82778292e+06, 3.08440597e+06, 2.32900835e+06, 3.27734461e+06, 3.13219151e+06, 2.66098549e+06, 3.03005729e+06, 2.72814659e+06, 2.70288177e+06, 3.54280549e+06, 2.93607676e+06, 1.78982271e+06, 2.21613523e+06, 2.91318387e+06, 2.87405087e+06, 3.61371611e+06, 3.89794311e+06, 4.38114417e+06, 2.58856806e+06, 4.20199583e+06, 2.91670305e+06, 2.2392368e+06, 2.86393206e+06, 2.67345352e+06, 2.2061398e+06, 2.15551076e+06, 4.77088567e+06, 2.85512124e+06, 2.81340064e+06, 3.4885111e+06, 3.63155454e+06, 5.38672434e+06, 2.67275954e+06, 3.21274957e+06, 2.39921036e+06, 2.86121674e+06, 2.42633635e+06, 1.91891923e+06, 2.28660989e+06, 2.7198714e+06, 3.4548414e+06, 1.89060746e+06, 2.19800602e+06, 3.0995187e+06, 2.88323394e+06, 2.28130506e+06, 3.67648274e+06, 3.71732086e+06, 2.90720821e+06, 2.99123053e+06, 2.05261155e+06, 3.01956895e+06, 2.90781698e+06, 2.66107465e+06, 2.88986309e+06, 4.30053279e+06, 3.26226683e+06, 3.03636253e+06, 2.84701552e+06, 3.33289013e+06, 2.63731107e+06, 2.68625744e+06, 2.14660065e+06, 2.21313728e+06, 4.14337918e+06, 2.1611933e+06, 3.03016893e+06, 3.1863518e+06, 2.21321757e+06, 2.31929874e+06, 2.94547619e+06, 2.65408276e+06, 3.84403327e+06, 2.06885817e+06, 2.10335192e+06, 2.74379303e+06, 2.01658082e+06, 2.3149195e+06, 2.46844998e+06, 1.87004889e+06, 1.59472112e+06, 2.25521752e+06, 2.94175055e+06, 2.67432127e+06, 2.27570512e+06, 3.01109492e+06, 2.65106131e+06, 2.47330123e+06, 2.34391925e+06, 1.70425623e+06, 2.50992753e+06, 2.4660162e+06, 1.58141994e+06, 1.34116622e+06, 1.4896744e+06, 1.58393605e+06, 2.04321723e+06, 2.44422139e+06, 1.96159065e+06, 1.83739218e+06, 1.64103496e+06, 1.66021293e+06, 1.60891687e+06, 1.65176256e+06, 1.91491696e+06, 1.61766941e+06, 1.58707778e+06, 1.89218947e+06, 1.57930197e+06, 1.57015129e+06, 2.13767924e+06, 1.30261337e+06, 1.44146755e+06, 1.40615347e+06, 1.34663057e+06, 1.28208042e+06, 1.54276262e+06, 1.64338548e+06, 1.65010899e+06, 2.28908164e+06, 2.61260034e+06, 1.97885994e+06, 1.79122352e+06, 2.2873777e+06, 1.84838672e+06, 1.74287741e+06, 2.08156897e+06, 1.87846651e+06, 1.89900149e+06, 1.85390023e+06, 1.5732252e+06, 1.3822195e+06, 2.12734917e+06, 2.01810161e+06, 1.39442797e+06, 1.42933487e+06, 1.61501156e+06, 1.72405923e+06, 1.4815506e+06, 1.2382554e+06, 1.8969767e+06, 1.77727268e+06, 1.64226996e+06, 1.67480144e+06, 1.78134738e+06, 1.39849789e+06, 1.91498407e+06, 2.33465522e+06, 1.35492109e+06, 2.08472373e+06, 1.26195199e+06, 1.31933752e+06, 1.63442269e+06, 1.65681361e+06, 1.34288681e+06, 904600.44, 1.46793448e+06, 1.64602041e+06, 1.44899671e+06, 1.04669968e+06, 1.31596947e+06, 1.18743421e+06, 1.56095194e+06, 1.10478534e+06, 1.38627157e+06, 1.55701749e+06, 1.32469963e+06, 1.54976951e+06, 1.86750835e+06, 1.36928877e+06, 1.5424944e+06, 1.68371235e+06, 1.46073225e+06, 1.35178586e+06, 1.51577668e+06, 1.05407993e+06, 1.21676836e+06, 1.81432884e+06, 1.38907063e+06, 1.23102293e+06, 1.29826673e+06, 1.77925076e+06, 1.2572613e+06, 1.74840613e+06, 1.37949811e+06, 1.42356981e+06, 1.71804977e+06, 1.54167755e+06, 1.70884712e+06, 1.95433815e+06, 1.72533682e+06, 1.55962076e+06, 1.65253414e+06, 1.60510633e+06, 336013.6, 613599.45, 479085.57, 657012.74, 833364.84, 1.38585464e+06, 610007.26, 498649.17, 469249.37, 1.19248507e+06, 586718.75, 554464.66, 682903.73, 755312.75, 841299.97, 1.14318114e+06, 1.53431837e+06, 2.04093469e+06, 1.31204489e+06, 1.58865794e+06, 3.81777878e+06, 4.75352269e+06, 5.0553882e+06, 5.39225872e+06, 4.80716877e+06, 5.12085426e+06, 4.50889683e+06, 3.72369759e+06, 2.56097507e+06, 3.02915346e+06, 3.43278686e+06, 2.19533707e+06, 2.65247969e+06, 4.35201139e+06, 2.63956458e+06, 3.90037956e+06, 3.02959811e+06, 2.48895704e+06, 3.16343516e+06, 1.160016146e+07, 3.82184495e+06, 1.989233127e+07, 4.25810942e+06, 2.82023663e+06, 2.98696191e+06, 2.66953001e+06, 3.04807992e+06, 2.16079262e+06, 3.28724864e+06, 2.81541533e+06, 4.34301361e+06, 3.85025805e+06, 2.44862617e+06, 2.53458858e+06, 2.6840086e+06, 3.33907065e+06, 2.27500359e+06, 3.0490326e+06, 3.34614462e+06, 3.31006521e+06, 3.21427407e+06, 1.99727209e+06, 3.074455537857347e+17, 2.50293357e+06, 2.64451414e+06, 3.33007309e+06, 2.09707363e+06, 2.83740367e+06, 2.68122503e+06, 3.19194108e+06, 4.18778217e+06, 3.67072122e+06, 5.26749974e+06, 4.37275891e+06, 4.35211656e+06, 4.27512263e+06, 3.88584916e+06, 3.92996033e+06, 3.21410924e+06, 3.02536256e+06, 2.79530919e+06, 2.56682188e+06, 4.0511643e+06, 3.15261048e+06, 3.03236346e+06, 2.64566358e+06, 1.96289324e+06, 3.29017962e+06, 2.83656084e+06, 2.67071986e+06, 2.64717623e+06, 2.35454387e+06, 2.79849101e+06, 2.46093031e+06, 1.7880059e+06, 2.52675879e+06, 3.37043028e+06, 2.30647253e+06, 3.04588059e+06, 2.83641467e+06, 3.06819629e+06, 3.05025658e+06, 2.18285357e+06, 2.48828403e+06, 2.16669829e+06, 1.61601028e+06, 2.73089098e+06, 2.71672444e+06, 3.15147316e+06, 3.06633327e+06, 2.5821259e+06, 2.51139477e+06, 3.28904497e+06, 1.72715783e+06, 1.97964106e+06, 1.60121092e+06, 3.22988795e+06, 3.02261788e+06, 3.28601295e+06, 2.36888832e+06, 2.72868803e+06, 2.05185604e+06, 2.47432862e+06, 3.47011625e+06, 3.21408954e+06, 3.05871918e+06, 1.88781025e+06, 2.69418112e+06, 3.13844771e+06, 1.61577152e+06, 2.66136204e+06, 1.83300278e+06, 2.65317832e+06, 2.71672165e+06, 2.78830776e+06, 3.86310089e+06, 2.4962519e+06, 3.02459495e+06, 1.99276163e+06, 3.81107871e+06, 3.03031604e+06, 2.82405147e+06, 3.60443623e+06, 2.56919828e+06, 3.57945853e+06, 3.28953078e+06, 3.05978424e+06, 3.30486323e+06, 3.24259317e+06, 3.75916959e+06, 4.08251504e+06, 3.37583171e+06, 2.89554665e+06, 3.11860799e+06, 2.6996877e+06, 2.42826814e+06, 3.76060813e+06, 2.47781877e+06, 2.17583559e+06, 3.89767238e+06, 2.99199487e+06, 3.04768406e+06, 3.34860539e+06, 2.7302737e+06, 3.3863018e+06, 3.54754803e+06, 2.43744503e+06, 2.14489225e+06, 1.89619021e+06, 2.76873528e+06, 3.32008778e+06, 2.1301009e+06, 3.25434094e+06, 2.3228784e+06, 2.16424632e+06, 3.07815169e+06, 2.28226964e+06, 4.08453774e+06, 3.75562951e+06, 2.49733105e+06, 2.30553519e+06, 2.51689094e+06, 2.1794364e+06, 1.9619271e+06, 2.31748843e+06, 2.90639996e+06, 2.01233617e+06, 1.50233245e+06, 2.07633486e+06, 2.12836411e+06, 4.03637497e+06, 5.6349284e+06, 3.04306631e+06, 2.73272432e+06, 2.86717276e+06, 3.31416932e+06, 4.51654067e+06, 2.98801252e+06, 2.40063581e+06, 3.31624598e+06, 4.77355001e+06, 2.97027024e+06, 3.03047048e+06, 4.72781682e+06, 3.51002547e+06, 3.02634298e+06, 4.76624831e+06, 4.87161913e+06, 3.28884374e+06, 4.15900076e+06, 2.80493837e+06, 2.79959422e+06, 3.78074556e+06, 2.7840345e+06, 3.69296965e+06, 3.37131482e+06, 3.72405504e+06, 4.30216981e+06, 4.04276127e+06, 2.64280282e+06, 3.47980904e+06, 3.36101991e+06, 4.68252795e+06, 3.36894245e+06, 3.62032613e+06, 3.31915351e+06, 4.75922552e+06, 4.42569927e+06, 3.1709935e+06, 3.1465283e+06, 3.76437022e+06, 2.675776e+06, 2.12618798e+06, 2.90192042e+06, 3.19077519e+06, 3.68067758e+06, 2.06941711e+06, 2.3432541e+06, 2.18022073e+06, 2.54342648e+06, 2.25627863e+06, 1.84637577e+06, 2.57808423e+06, 2.66931467e+06, 2.70658499e+06, 2.50219231e+06, 2.77184499e+06, 2.59477641e+06, 2.63304407e+06, 2.27307776e+06, 2.00314938e+06, 2.1398918e+06, 2.1760387e+06, 1.71797137e+06, 2.67519592e+06, 2.02693557e+06, 2.03075014e+06, 1.88893326e+06, 2.05624383e+06, 1.83361642e+06, 2.50121554e+06, 2.39414001e+06, 2.38102214e+06, 2.25563375e+06, 2.24843758e+06, 2.17436066e+06, 1.71921582e+06, 1.93236318e+06, 2.22815951e+06, 2.68185692e+06, 2.32406437e+06, 1.77608817e+06, 2.86460207e+06, 2.39790775e+06, 2.02751213e+06, 2.47487517e+06, 2.14861601e+06, 1.89084162e+06, 1.45589678e+06, 1.65043716e+06, 1.75731123e+06, 2.04818891e+06, 1.91663364e+06, 1.9423747e+06, 2.41951221e+06, 2.26307808e+06, 1.71494461e+06, 1.44163915e+06, 1.79809317e+06, 1.9015543e+06, 1.59610733e+06, 1.55151663e+06, 1.81498624e+06, 1.75969746e+06, 1.80497949e+06, 1.82867454e+06, 2.38392302e+06, 1.72001809e+06, 1.12512233e+06, 1.5114638e+06, 1.50458961e+06, 1.70302339e+06, 1.58219812e+06, 1.4760054e+06, 1.91479751e+06, 1.74831039e+06, 2.15511428e+06, 2.26776329e+06, 2.12422584e+06, 2.10913474e+06, 2.74611343e+06, 1.9288678e+06, 2.10604165e+06, 2.05485469e+06, 2.27875013e+06, 3.08439723e+06, 2.66392209e+06, 1.81797558e+06, 2.87923892e+06, 2.14514866e+06, 1.66179389e+06, 1.99559376e+06, 2.71573407e+06, 2.9070089e+06, 2.79919059e+06, 2.49670521e+06, 1.99614918e+06, 1.43787262e+06, 1.52292823e+06, 2.07564955e+06, 1.64785394e+06, 2.00513979e+06, 2.27757578e+06, 2.64070594e+06, 1.90669895e+06, 2.97089845e+06, 2.37720557e+06, 3.28132119e+06, 2.04945689e+06, 2.81489906e+06, 2.78939539e+06, 2.43397451e+06, 2.48511841e+06, 2.04475191e+06, 3.23273613e+06, 3.51760534e+06, 1.52743314e+06, 3.14576668e+06, 3.34085533e+06, 3.24006019e+06, 2.60979114e+06, 3.20084673e+06, 2.15628609e+06, 2.02047529e+06, 3.22578758e+06, 2.55101994e+06, 2.53433262e+06, 2.09495423e+06, 3.08388975e+06, 2.04357986e+06, 3.94917476e+06, 2.31751785e+06, 2.01408386e+06, 2.0884983e+06, 2.44090338e+06, 2.46322254e+06, 2.87609515e+06, 1.94706836e+06, 3.8951128e+06, 2.52767557e+06, 2.63364312e+06, 3.81641606e+06, 5.52225267e+06, 2.3970308e+06, 2.30477094e+06, 3.24181697e+06, 2.76906744e+06, 2.72946223e+06, 2.90108873e+06, 2.72294151e+06, 2.13814643e+06, 2.77948493e+06, 2.95356105e+06, 2.30886822e+06, 2.87291139e+06, 2.88333405e+06, 3.28038727e+06, 2.98330727e+06, 2.62876864e+06, 4.22415183e+06, 2.61266445e+06, 2.69968568e+06, 2.43174104e+06, 2.96785011e+06, 2.45165406e+06, 2.20706863e+06, 2.6960544e+06, 2.86158256e+06, 3.10204004e+06, 3.97407654e+06, 2.02851631e+06, 2.95837673e+06, 2.8586676e+06, 2.84534712e+06, 2.0703405e+06, 2.02181504e+06, 2.35834474e+06, 2.74446037e+06, 2.4633387e+06, 3.25607372e+06, 2.8591171e+06, 4.09440587e+06, 3.7872113e+06, 2.74540665e+06, 2.31489725e+06, 2.36033344e+06, 2.98101315e+06, 2.23533925e+06, 3.23280128e+06, 1.88889839e+06, 1.98620769e+06, 2.63829816e+06, 3.26803741e+06, 2.49190297e+06, 2.32205423e+06, 2.54396382e+06, 2.71114252e+06, 2.05664364e+06, 2.456977e+06, 2.70314442e+06, 2.35755478e+06, 2.01236646e+06, 3.05945313e+06, 2.65940192e+06, 2.05160575e+06, 2.58283532e+06, 2.63420821e+06, 3.03253607e+06, 2.51676239e+06, 2.46717967e+06, 2.39736717e+06, 1.92784937e+06, 2.40485894e+06, 2.78780948e+06, 2.72593614e+06, 2.30733078e+06, 3.00093868e+06, 3.58961874e+06, 2.26185793e+06, 2.99831749e+06, 2.05716489e+06, 7.71427651e+06, 2.38426858e+06, 2.10897584e+06, 2.41545415e+06, 2.96406611e+06, 2.77726555e+06, 2.59775007e+06, 2.77048003e+06, 2.92670767e+06, 2.27334878e+06, 1.93082846e+06, 2.49544638e+06, 2.59651834e+06, 2.22972886e+06, 2.07450272e+06, 2.46386472e+06, 2.20444988e+06, 2.16023425e+06, 2.56589998e+06, 3.22455102e+06, 2.77515029e+06, 2.2982969e+06, 2.14806414e+06, 2.71059143e+06, 2.70468275e+06, 1.95734195e+06, 1.95952889e+06, 2.04400573e+06, 3.0059549e+06, 1.91657034e+06, 2.03642468e+06, 2.08477082e+06, 2.74354494e+06, 2.5599333e+06, 2.36502048e+06, 2.55459339e+06, 2.90366298e+06, 3.19635411e+06, 2.60046786e+06, 2.39063437e+06, 2.34830952e+06, 2.10784202e+06, 1.93358202e+06, 1.89249228e+06, 2.37463957e+06, 1.98161307e+06, 2.17582049e+06, 2.08137929e+06, 2.23868129e+06, 3.3857395e+06, 1.86385497e+06, 1.9918676e+06, 2.33214747e+06, 2.1672397e+06, 2.62126033e+06, 2.66313325e+06, 1.65137937e+06, 2.19055368e+06, 2.16750909e+06, 2.49141166e+06, 2.97164303e+06, 2.40265517e+06, 1.93880127e+06, 1.47519658e+06, 1.84156088e+06, 1.72258744e+06, 1.93968466e+06, 1.80882194e+06, 2.08274784e+06, 1.59002672e+06, 2.19582588e+06, 2.27567827e+06, 1.73038874e+06, 1.67790765e+06, 1.71251966e+06, 1.69540261e+06, 1.86966594e+06, 1.7525253e+06, 1.68575977e+06, 1.63192105e+06, 1.83787477e+06, 2.20166508e+06, 1.93749986e+06, 1.83244896e+06, 2.0888083e+06, 2.30900639e+06, 1.91627973e+06, 1.52780262e+06, 1.8013676e+06, 2.00971749e+06, 1.84914698e+06, 1.92002112e+06, 2.04558458e+06, 1.38088615e+06, 1.85760049e+06, 1.580062e+06, 2.07719871e+06, 1.44903365e+06, 1.27817472e+06, 1.93295693e+06, 2.37083535e+06, 1.79060708e+06, 1.7892255e+06, 1.91639683e+06, 2.27755591e+06, 1.84436527e+06, 1.827967e+06, 1.62962399e+06, 1.52536303e+06, 1.83138991e+06, 1.40084595e+06, 1.63471735e+06, 1.501038e+06, 1.73607388e+06, 1.61450958e+06, 1.45799229e+06, 2.07198662e+06, 1.6212342e+06, 1.59853565e+06, 1.9202318e+06, 1.64274548e+06, 1.99997453e+06, 1.34998912e+06, 1.81193418e+06, 1.45218766e+06, 1.97996487e+06, 2.04226795e+06, 1.57390991e+06, 1.60663086e+06, 1.52059916e+06, 2.22282584e+06, 1.70544672e+06, 1.89624459e+06, 1.48852397e+06, 1.85036407e+06, 1.77687907e+06, 1.69325634e+06, 1.42745026e+06, 1.62811312e+06, 1.82889074e+06, 2.43966687e+06, 1.57166918e+06, 1.72776199e+06, 2.44044423e+06, 1.8242669e+06, 2.22826392e+06, 1.75821431e+06, 1.72855598e+06, 2.10935208e+06, 2.22064555e+06, 1.79251544e+06, 1.85849765e+06, 2.15731225e+06, 1.90678584e+06, 1.56790199e+06, 2.04226939e+06, 1.45089598e+06, 1.81427723e+06, 1.81284874e+06, 1.59886035e+06, 1.62601801e+06, 1.71789356e+06, 2.05793655e+06, 1.52783055e+06, 1.84679929e+06, 1.68806633e+06, 2.06032669e+06, 1.46443682e+06, 1.92797885e+06, 1.6110586e+06, 1.98976351e+06, 1.80054407e+06, 1.82304082e+06, 1.78806098e+06, 1.64984091e+06, 1.74333987e+06, 1.66337027e+06, 1.73494369e+06, 1.85774666e+06, 1.92859362e+06, 1.93056159e+06, 1.87347587e+06, 1.9324098e+06, 1.60843656e+06, 1.91406366e+06, 1.94705447e+06, 1.77131625e+06, 2.04083454e+06, 1.78839231e+06, 2.01058765e+06, 1.82090917e+06, 1.26222308e+06, 1.93053869e+06, 1.72143605e+06, 1.92822551e+06, 2.00292664e+06, 1.74433111e+06, 1.57929527e+06, 1.43913471e+06, 2.02305507e+06, 1.68415372e+06, 2.208182e+06})
	values = append(values, []float64{1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 0, 1, 0, 0.5, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0.5, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0.5, 1, 1, 1, 2, 0, 1, 0, 0.5, 1, 1, 1, 1, 0, 1, 1, 0.5, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 2, 1, 1, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 0, 0.5, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0.5, 1, 1, 1, 1, 1, 1, 1, 0.5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0.5, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0.5, 1, 1, 1, 0, 1, 1, 1, 0.5, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 2, 2})
	values = append(values, []float64{12133.1312121212, 323232.2323232323, 0.1212121121212, 0.3333333333222222})
	values = append(values, []float64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0})
	values = append(values, []float64{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 323232.2323232323, 0, 0, 0, 0, 0, 0})
	values = append(values, []float64{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1})
	values = append(values, []float64{12133.1312121212, 323232.2323232323, 0.1212121121212, 0.3333333333222222, 1, 2, 3, 4, 5, 0, 0, 0, 0, 7})
	values = append(values, []float64{2.427203167593362e+17, 117.84, 6.0880343477589064e+16})
	values = append(values, []float64{2.427203167593362e+17, 117.84, 6.0880343477589064e+16, 0, 0})
	values = append(values, []float64{0, 117.84, 0})
	values = append(values, []float64{0, 0, 0, 0, 0, 0, 0, 0, -1, 0, 0, 0, 0, 0, 0, 0, 0, 0})
	values = append(values, []float64{-1, -1, -18, -1, -1, -8, -1, -1, -1, -1})
	values = append(values, []float64{-1, -1, -18, -1, -1, -8, 1, 1, 1, 1, 323232.2323232323, 1})
	values = append(values, []float64{1.9005287, 1.6105287, 1.1105287, 0.8205287, 0.55052865, 0.22052866, -0.38947132, -0.69947135, 0.18052867, 0.93052864, 1.4405286, 2.0905285, 2.9105287, 3.5305285, 3.7905285, 3.5405285, 3.4605286, 2.8805285, 2.1005285, 0.85052866, 0.38052866, 0.040528655, -0.17947134, -0.75947136, -1.5894713, -2.2894714, -2.9194715, -3.6094716, -3.5494714, -3.8294713, -4.0394716, -3.8194714, -2.8394713, -1.3894713, 0.050528657, 1.1705287, 2.2505286, 2.5705285, 2.6105285, 2.1405284, 1.6905286, 0.90052867, 0.37052867, 0.12052866, -0.30947134, -0.7294713, -0.69947135, -0.62947136, -0.5594713, -0.28947133, 0.0005286578, -0.9194713, -0.62947136, -0.40947133, -0.22947133, 0.48052865, 1.5005287, 2.6005285, 3.6205285, 4.6305285, 5.6305285, 5.6305285, 5.1905284, 3.8205285, 3.2505286, 2.5005286, -0.059471346, -1.1794714, -1.0294713, -1.6194713, -1.2394713, -1.0494714, -0.63947135, -0.58947134, -0.30947134, 0.66052866, 0.92052865, 0.43052867, 0.39052868, -0.10947134, 0.44052866, 2.4205287, 4.8505287, 7.3705287, 8.490529, 8.820529, 9.100529, 9.200529, 7.6805286, 6.2205286, 3.7705286, 3.2405286, 3.8405285, 4.3805285, 4.3305287, 4.1905284, 4.0605288, 3.2605286, 2.6005285, 2.8505285, 3.5405285, 5.2405286, 6.1305285, 8.400529, 8.410529, 8.950529, 10.160529, 10.570529, 10.950529, 10.540529, 10.290529, 9.500529, 9.000529, 8.250529, 8.360529, 8.200529, 8.000528, 8.060529, 7.8705287, 7.5305285, 6.9805284, 7.5805287, 8.540529, 6.2205286, 6.1205287, 6.040529, 6.0105286, 6.3605285, 7.2305284, 7.6205287, 7.6605287, 7.9605284, 8.0505295, 8.020529, 7.7805285, 5.8605285, 6.3205285, 6.4005284, 6.4405284, 6.4305286, 6.3805285, 6.2205286, 6.1405287, 6.0905285, 6.1005287, 6.1305285, 6.1705284, 6.3705287, 6.6905284, 6.9905286, 7.2705283, 7.8105288, 9.3005295, 11.740529, 12.390529, 9.520529, 9.570529, 9.250529, 8.570529, 7.0705285, 6.1405287, 4.9905286, 3.6105285, 3.5805285, 3.7705286, 4.1905284, 4.1905284, 4.1705284, 4.0905285, 3.9705286, 3.8405285, 4.040529, 3.8105285, 3.5405285, 3.2705286, 3.1205285, 3.1205285, 4.1905284, 5.290529, 6.4605284, 7.4105287, 7.9205284, 7.9105287, 7.3905287, 6.3105288, 5.6205287, 4.3405285, 3.2105286, 2.7705286, 2.3605285, 2.2005286, 2.5705285, 2.8005285, 3.0405285, 2.6805286, 5.1705284, 5.4905286, 3.6905286, 2.9505286, 2.9805286, 3.4805286, 3.9105287, 5.5005283, 8.990529, 9.320529, 9.790529, 10.320529, 9.630529, 8.360529, 7.4205284, 6.9105287, 6.1705284, 6.4605284, 7.4705286, 7.7405286, 7.0005283, 6.0005283, 5.4105287, 5.3805285, 6.7805285, 6.5605288, 6.5105286, 6.4105287, 6.7805285, 8.780529, 10.120529, 10.710529, 10.260529, 11.3005295, 11.690529, 11.150529, 9.450529, 8.180529, 6.7205286, 5.1705284, 3.8105285, 2.5805285, 1.8905287, 1.3405286, 1.0405287, 0.8905287, 0.8305287, 1.1605287, 2.9605286, 3.7905285, 0.6405287, 0.68052864, 1.9705287, 4.9805284, 7.5305285, 8.8005295, 9.190529, 9.680529, 10.990529, 10.570529, 9.210529, 7.2405286, 5.9005284, 5.0205283, 4.7105284, 3.6805286, 3.8505285, 3.6805286, 4.8905287, 4.5305285, 5.6105285, 5.1505284, 4.6305285, 4.4205284, 4.6605287, 5.0005283, 5.0505285, 4.9705286, 5.1205287, 5.2705283, 5.5105286, 5.5805287, 5.7405286, 5.6205287, 5.2105284, 4.540529, 3.9605286, 3.4705286, 2.6505284, 1.7405287, 0.39052868, -0.19947135, -0.33947134, 0.32052866, 0.29052866, 0.14052865, 0.73052865, 0.90052867, 1.1405287, 1.0605286, 1.1905286, 2.4305286, 3.6905286, 4.6905284, 5.6105285, 6.2005286, 6.3705287, 6.1205287, 5.3405285, 3.9805286, 2.5405285, 1.7405287, 0.78052866, 0.38052866, -0.059471346, -0.07947134, 0.26052865, 0.53052866, 0.62052864, 0.42052865, 1.2905287, 0.67052865, 0.12052866, 0.030528657, 0.38052866, 1.5305287, 3.8305285, 5.7505283, 7.1805286, 8.330529, 8.950529, 8.670529, 7.5605288, 6.2105284, 6.0005283, 6.5605288, 6.7805285, 6.0705285, 6.3005285, 6.6105285, 6.0505285, 6.1005287, 5.5805287, 5.2105284, 1.8805287, 1.7805287, 2.0905285, 1.6505287, 1.9405286, 4.3305287, 6.4205284, 8.560529, 10.080529, 10.170529, 9.390529, 9.230529, 8.380529, 7.790529, 8.220529, 8.0505295, 7.9905286, 7.9705286, 7.3305287, 7.2005286, 8.410529, 8.440529, 7.9705286, 7.8305287, 5.540529, 5.4705286, 5.3805285, 5.2305284, 5.4105287, 6.4005284, 7.7505283, 8.750529, 8.320529, 8.660529, 8.630529, 8.330529, 7.6505284, 6.9405284, 6.3905287, 5.3805285, 4.7305284, 4.2205286, 3.5805285, 2.8805285, 2.7405286, 3.0505285, 3.6505284, 3.9505286, 6.3305287, 6.1505284, 5.1205287, 4.0805287, 3.3805285, 4.7405286, 7.6605287, 8.570529, 8.940529, 9.640529, 9.920529, 9.360529, 8.3005295, 6.0605288, 5.2305284, 5.5705285, 7.0805287, 7.3905287, 6.8605285, 7.8905287, 9.220529, 9.740529, 9.510529, 9.580529, 8.220529, 8.170529, 8.290529, 8.400529, 8.900529, 9.580529, 10.280529, 10.780529, 10.850529, 10.440529, 10.350529, 10.400529, 10.350529, 10.030529, 9.880529, 9.590529, 9.890529, 10.340529, 10.530529, 11.120529, 11.070529, 11.010529, 10.430529, 10.000529, 9.760529, 9.3005295, 9.740529, 9.260529, 9.290529, 10.430529, 11.700529, 12.590529, 13.090529, 13.250529, 13.130529, 12.580529, 11.8005295, 10.480529, 8.890529, 8.030529, 7.5805287, 7.2605286, 6.8905287, 6.3905287, 5.8805285, 5.3605285, 5.1805286, 4.6205287, 6.4205284, 6.0605288, 5.2305284, 5.8605285, 6.0805287, 7.6405287, 9.540529, 10.580529, 11.200529, 11.650529, 11.680529, 11.330529, 10.370529, 9.8005295, 9.060529, 7.5205283, 5.6305285, 4.8505287, 4.3205285, 4.3905287, 4.8905287, 5.2805285, 5.2605286, 4.020529, 1.5605286, 1.4105287, 1.2705287, 1.1405287, 2.4005284, 5.5905285, 7.7705283, 9.370529, 11.530529, 11.950529, 12.160529, 11.880529, 10.140529, 7.9505286, 6.9505286, 7.1005287, 7.3105288, 7.8205285, 8.660529, 8.710529, 8.150529, 8.180529, 8.3005295, 8.500529, 7.9105287, 8.390529, 7.8005285, 7.3105288, 7.4705286, 7.6205287, 7.6005287, 7.7805285, 7.7005286, 6.0605288, 6.0505285, 6.0305285, 5.8005285, 5.3305287, 4.4705286, 4.000529, 3.3805285, 2.8705285, 2.7905285, 2.4805286, 2.2805285, 1.9705287, 1.5905286, 1.2205287, 0.18052867, -0.9894713, -1.4994713, -1.3594713, -0.68947136, 1.0105287, 3.4605286, 5.0805287, 6.4805284, 7.4205284, 7.790529, 7.6505284, 7.0305285, 5.7205286, 5.0205283, 4.6905284, 4.2805285, 4.3105288, 4.5605288, 4.000529, 3.6705287, 3.6405284, 3.8205285, 3.6005285, 2.3005285, 2.0905285, 1.8705287, 1.7605287, 2.8505285, 5.5705285, 8.130529, 9.8005295, 10.880529, 11.540529, 11.850529, 11.570529, 10.420529, 9.290529, 8.380529, 7.5905285, 6.8605285, 6.3005285, 6.3305287, 5.9905286, 5.4905286, 5.4205284, 5.5605288, 5.4405284, 4.0705285, 3.6805286, 3.2005286, 2.7005286, 2.7905285, 3.7805285, 4.9105287, 5.7705283, 6.3505287, 7.040529, 7.3205285, 7.1805286, 6.7005286, 6.2705283, 5.790529, 4.7805285, 4.2005286, 3.7305286, 3.4305286, 1.7405287, 1.6505287, 1.0005287, 0.34052867, 0.21052867, 1.0905286, 0.9605287, 0.8905287, 0.8205287, 1.4905287, 2.5305285, 3.4205287, 4.7205286, 5.7305284, 6.8905287, 7.6305285, 7.3805285, 7.1305285, 7.2505283, 7.0005283, 6.6605287, 6.3805285, 6.2505283, 6.3805285, 6.9505286, 7.0905285, 7.1005287, 6.6205287, 6.3005285, 3.9605286, 3.0505285, 2.7305286, 4.0105286, 4.000529, 5.4605284, 6.9905286, 8.350529, 9.490529, 10.140529, 10.330529, 9.960529, 8.980529, 7.4905286, 6.6105285, 6.3805285, 6.6205287, 6.3705287, 6.2405286, 6.0005283, 4.7505283, 3.5505285, 3.3905284, 3.3805285, 3.5305285, 3.3705285, 3.1505284, 2.9705286, 3.6105285, 6.1905284, 8.230529, 9.670529, 10.540529, 11.200529, 11.420529, 11.110529, 9.960529, 8.900529, 7.9505286, 7.1305285, 6.6105285, 6.1505284, 5.8005285, 5.6305285, 5.4205284, 5.2305284, 5.0705285, 4.7505283, 4.2005286, 3.8905284, 3.5805285, 3.3305285, 4.1805286, 6.9105287, 9.0505295, 10.470529, 10.280529, 11.0505295, 11.460529, 11.450529, 10.790529, 10.000529, 9.470529, 9.060529, 8.860529, 8.540529, 8.260529, 7.9405284, 7.4205284, 6.9205284, 6.4205284, 5.7705283, 4.7205286, 3.4405286, 3.1205285, 3.7605286, 4.1605287, 5.2805285, 6.1505284, 6.5005283, 5.6005287, 6.4205284, 7.1505284, 7.1005287, 6.4505286, 5.7405286, 4.9305286, 4.040529, 3.0105286, 2.1505284, 1.9305286, 2.0405285, 1.8405286, 1.7705287, 1.7105286, 1.5605286, 0.9505287, 0.72052866, 0.60052866, 0.53052866, 0.9405287, 1.5905286, 2.1705287, 2.6705287, 3.9605286, 4.6605287, 4.9505286, 5.4105287, 5.6805286, 4.9905286, 4.5305285, 3.7505286, 4.1205287, 4.4405284, 4.6805286, 5.0205283, 5.3205285, 5.4505286, 5.7705283, 5.5205283, 5.4505286, 4.790529, 3.3605285, 3.0605285, 3.7805285, 4.6005287, 6.5305285, 7.8505287, 8.410529, 9.420529, 9.830529, 10.060529, 9.340529, 8.870529, 9.380529, 8.810529, 8.850529, 8.790529, 2.9605286, 2.8005285, 1.4005287, 1.6305287, 1.8805287, 2.0005286, 3.2705286, 2.7505286, 2.4805286, 2.1605287, 2.2805285, 2.2105286, 2.8505285, 3.6905286, 4.5005283, 4.7605286, 4.8105288, 4.5005283, 3.7605286, 2.8405285, 2.3605285, 2.0205286, 1.6505287, 1.1605287, 0.6905287, -0.009471342, -0.76947135, -1.0294713, -0.7994713, -0.9894713, -1.1894714, -1.4894713, -1.7294713, -1.8694713, -1.0794713, 0.49052867, 2.2705286, 3.7805285, 5.3105288, 7.0005283, 8.100529, 8.230529, 7.4105287, 5.4005284, 4.0105286, 4.2005286, 3.2905285, 1.6605287, 0.43052867, -0.57947135, -1.5194713, -1.1494713, -2.0594714, -1.5894713, 0.7605287, 1.2805287, 1.0705286, 1.9205287, 2.1105285, 1.8605287, 4.3305287, 6.7005286, 6.1405287, 6.2705283, 6.0105286, 6.2405286, 5.8005285, 5.1805286, 4.8405285, 5.0905285, 5.2505283, 4.4805284, 4.1405287, 2.8205285, 1.5405287, 1.9605286, 3.2905285, 3.4905286, 2.3005285, 1.8905287, 2.2205286, 2.7405286, 2.4005284, 4.6505284, 6.9805284, 9.160529, 10.590529, 9.350529, 6.8405285, 6.3105288, 8.280529, 8.620529, 8.610529, 9.610529, 9.960529, 10.320529, 10.690529, 10.670529, 10.640529, 10.170529, 9.270529, 9.8005295, 10.860529, 10.760529, 9.750529, 9.0505295, 9.220529, 10.210529, 11.380529, 11.970529, 11.290529, 11.230529, 10.850529, 10.580529, 9.850529, 9.460529, 8.840529, 7.4305286, 7.0805287, 6.3505287, 5.6705284, 5.0205283, 4.8505287, 4.9205284, 5.1005287, 5.0505285, 4.5505285, 4.3805285, 4.1205287, 4.0105286, 4.0505285, 3.9305286, 4.3405285, 5.3305287, 6.4905286, 7.4005284, 7.9205284, 8.020529, 7.6905284, 6.4005284, 5.4305286, 4.7505283, 4.5005283, 3.8105285, 2.8505285, 2.3705285, 1.9005287, 1.8505287, 1.2205287, 0.12052866, 0.30052868, 0.070528656, 0.26052865, 0.48052865, 0.7505287, 2.1405284, 4.4005284, 6.8005285, 8.110529, 9.030529, 9.850529, 10.330529, 10.510529, 9.810529, 6.1105285, 4.7305284, 3.8305285, 2.9205287, 2.5105286, 2.2505286, 2.1505284, 2.0105286, 1.7805287, 1.7405287, 1.0105287, 0.7505287, 0.73052865, 0.43052867, 1.2005286, 2.8805285, 4.5805287, 6.3505287, 7.8405285, 8.600529, 8.310529, 7.2205286, 6.5105286, 6.4305286, 6.2705283, 5.4005284, 4.8505287, 4.4605284, 3.4205287, 2.3105285, 2.2905285, 2.9105287, 2.8405285, 2.3105285, 1.5405287, 0.91052866, 0.37052867, -0.27947134, 0.030528657, 1.4905287, 2.9605286, 4.3805285, 4.9005284, 5.4705286, 6.2605286, 6.7505283, 6.5605288, 6.2505283, 5.8505287, 4.5005283, 4.7305284, 3.6905286, 2.9805286, 2.6105285, 1.9305286, 1.5605286, 0.91052866, 0.54052866, -0.36947134, -0.5394713, -0.58947134, -0.65947133, 0.46052867, 0.81052864, 0.21052867, 1.2105286, 2.6105285, 6.0005283, 7.0005283, 7.8305287, 8.5505295})
	values = append(values, []float64{4503584600157858, 4503616379538772, 4503616379994506, 4503735783968335, 4503885172757585, 4503916018127305, 4503974996833236, 4504034885551075, 4504182321316272, 4504215562714710, 4504275989437998, 4504334941144606, 4504482519911516, 4504516638320467, 4504574955018463, 4504635229739929})
	codecValues(values)
}

func TestCodecSameValue(t *testing.T) {
	samples := []float64{0, 0.1, 100, 1 << 40, 1<<40 + 0.01, 0.00000001, 1.00000001, -1, -1.1}
	values := make([][]float64, len(samples))

	for j := 0; j < len(samples); j++ {
		for i := 0; i < 1000; i++ {
			values[j] = append(values[j], samples[j])
		}
	}

	codecValues(values)
}

func codecValues(values [][]float64) {
	enc := &mlf.Compressor{}
	dec := &mlf.Decompressor{}
	var tmp []float64
	for i := 0; i < len(values); i++ {
		items := values[i]
		if len(items) == 0 {
			continue
		}

		tmp = append(tmp[:0], items...)
		buf := []byte{1, 1, 1, 1, 1, 1}

		enc.Prepare(tmp)
		buf = enc.Encode([]byte{1, 1, 1, 1, 1, 1}, tmp)
		other := dec.Decode(buf[6:])

		if len(tmp) != len(other) {
			fmt.Printf("diff len: exp=%d; got=%d \n", len(tmp), len(other))
			panic(1)
		}

		fmt.Println("ratio", float64(len(buf))/float64(len(values[i])*8), len(buf), len(values[i])*8)
		for j := 0; j < len(items); j++ {
			if other[j] != items[j] {
				fmt.Printf("not equal[%d], exp: %12f, got: %12f \n", j, items[j], other[j])
				panic(1)
			}
		}
	}
}

func buildData() []float64 {
	var values []float64
	for i := 0; i < 1000; i++ {
		f := float64(rand.Int63()%10000) / 100
		values = append(values, f)
	}

	return values
}

func TestBenchmarkEncode(t *testing.T) {
	values := buildData()

	const N = 100000
	var buf = make([]byte, 9000)
	enc := &mlf.Compressor{}
	_ = enc
	begin := time.Now()
	var tmp []float64
	for i := 0; i < N; i++ {
		tmp = append(tmp[:0], values...)
		enc.Prepare(tmp)
		buf = enc.Encode(buf[:0], tmp)
	}
	fmt.Println(len(buf), float64(len(buf))/80)
	fmt.Println(time.Since(begin), time.Since(begin)/N)
}

func TestBenchmarkDecode(t *testing.T) {
	values := buildData()

	const N = 200000
	var buf []byte
	enc := &mlf.Compressor{}
	enc.Prepare(values)
	buf = enc.Encode(buf[:0], values)
	dec := &mlf.Decompressor{}
	begin := time.Now()

	var swap []byte
	for i := 0; i < N; i++ {
		swap = append(swap[:0], buf...)
		dec.Decode(swap)
	}
	fmt.Println(len(buf), float64(len(buf))/80)
	fmt.Println(time.Since(begin), time.Since(begin)/N)
}
