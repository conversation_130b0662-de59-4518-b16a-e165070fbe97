// Generated by tmpl
// https://github.com/benb<PERSON><PERSON><PERSON>/tmpl
//
// DO NOT EDIT!
// Source: sort_item.go.tmpl

// Copyright 2023 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package record

import (
	"errors"
	"math"
)

type SortItem interface {
	Compare(i, j int) int
	CompareSingleValue(data interface{}, positionX, positionY int) (int, error)
	Swap(i, j int)
}

type FloatSlice struct {
	V []float64
}

func (sli *FloatSlice) Swap(i, j int) {
	sli.V[i], sli.V[j] = sli.V[j], sli.V[i]
}

type IntegerSlice struct {
	V []int64
}

func (sli *IntegerSlice) Swap(i, j int) {
	sli.V[i], sli.V[j] = sli.V[j], sli.V[i]
}

type StringSlice struct {
	V []string
}

func (sli *StringSlice) Swap(i, j int) {
	sli.V[i], sli.V[j] = sli.V[j], sli.V[i]
}

type BooleanSlice struct {
	V []bool
}

func (sli *BooleanSlice) Swap(i, j int) {
	sli.V[i], sli.V[j] = sli.V[j], sli.V[i]
}

func (sli *FloatSlice) Compare(i, j int) int {
	if sli.V[i] > sli.V[j] {
		return -1
	} else if sli.V[i] == sli.V[j] {
		return 0
	}
	return 1
}

func (sli *FloatSlice) CompareSingleValue(data interface{}, positionX, positionY int) (int, error) {
	cm, ok := data.(*FloatSlice)
	if !ok {
		return 0, errors.New("complex binary expression unsupported")
	}

	if sli.V[positionX] > cm.V[positionY] {
		return -1, nil
	} else if sli.V[positionX] == cm.V[positionY] {
		return 0, nil
	}
	return 1, nil
}

func (sli *IntegerSlice) Compare(i, j int) int {
	if sli.V[i] > sli.V[j] {
		return -1
	} else if sli.V[i] == sli.V[j] {
		return 0
	}
	return 1
}

func (sli *IntegerSlice) CompareSingleValue(data interface{}, positionX, positionY int) (int, error) {
	cm, ok := data.(*IntegerSlice)
	if !ok {
		return 0, errors.New("complex binary expression unsupported")
	}

	if sli.V[positionX] > cm.V[positionY] {
		return -1, nil
	} else if sli.V[positionX] == cm.V[positionY] {
		return 0, nil
	}
	return 1, nil
}

func (sli *StringSlice) Compare(i, j int) int {
	if sli.V[i] > sli.V[j] {
		return -1
	} else if sli.V[i] == sli.V[j] {
		return 0
	}
	return 1
}

func (sli *StringSlice) CompareSingleValue(data interface{}, positionX, positionY int) (int, error) {
	cm, ok := data.(*StringSlice)
	if !ok {
		return 0, errors.New("complex binary expression unsupported")
	}

	if sli.V[positionX] > cm.V[positionY] {
		return -1, nil
	} else if sli.V[positionX] == cm.V[positionY] {
		return 0, nil
	}
	return 1, nil
}

func (sli *BooleanSlice) Compare(i, j int) int {
	if sli.V[i] == sli.V[j] {
		return 0
	} else if sli.V[i] {
		return -1
	}
	return 1
}

func (sli *BooleanSlice) CompareSingleValue(data interface{}, positionX, positionY int) (int, error) {
	cm, ok := data.(*BooleanSlice)
	if !ok {
		return 0, errors.New("complex binary expression unsupported")
	}

	if sli.V[positionX] == cm.V[positionY] {
		return 0, nil
	} else if sli.V[positionX] {
		return -1, nil
	}
	return 1, nil
}

func (sli *BooleanSlice) PadBoolSlice(cv *ColVal) {
	if cv.NilCount == 0 {
		sli.V = append(sli.V, cv.BooleanValues()...)
		return
	}

	value := cv.BooleanValues()
	sli.V = make([]bool, 0, cv.Len)
	var nilCount int
	for i := 0; i < cv.Len; i++ {
		if cv.IsNil(i) {
			sli.V = append(sli.V, false)
			nilCount++
			continue
		}
		sli.V = append(sli.V, value[i-nilCount])
	}
}

func (sli *BooleanSlice) PadBoolSliceWithLimit(cv *ColVal, compareLength int) {
	for i := 0; i < compareLength; i++ {
		v, _ := cv.BooleanValue(i)
		sli.V = append(sli.V, v)
	}
}

func (sli *StringSlice) PadStringSlice(cv *ColVal) {
	sli.V = make([]string, 0, cv.Len)
	if cv.NilCount == 0 {
		sli.V = cv.StringValues(sli.V)
		return
	}

	var v string
	for i := 0; i < cv.Len; i++ {
		if cv.IsNil(i) {
			sli.V = append(sli.V, "")
			continue
		}
		v, _ = cv.StringValueSafe(i)
		sli.V = append(sli.V, v)
	}
}

func (sli *StringSlice) PadStringSliceWithLimit(cv *ColVal, compareLength int) {
	for i := 0; i < compareLength; i++ {
		v, _ := cv.StringValueSafe(i)
		sli.V = append(sli.V, v)
	}
}

func (sli *FloatSlice) PadFloatSlice(cv *ColVal) {
	if cv.NilCount == 0 {
		sli.V = append(sli.V, cv.FloatValues()...)
		return
	}

	value := cv.FloatValues()
	sli.V = make([]float64, 0, cv.Len)
	var nilCount int
	for i := 0; i < cv.Len; i++ {
		if cv.IsNil(i) {
			sli.V = append(sli.V, -math.MaxFloat64)
			nilCount++
			continue
		}
		sli.V = append(sli.V, value[i-nilCount])
	}
}

func (sli *FloatSlice) PadFloatSliceWithLimit(cv *ColVal, compareLength int) {
	for i := 0; i < compareLength; i++ {
		v, _ := cv.FloatValue(i)
		sli.V = append(sli.V, v)
	}
}

func (sli *IntegerSlice) PadIntSlice(cv *ColVal) {
	if cv.NilCount == 0 {
		sli.V = append(sli.V, cv.IntegerValues()...)
		return
	}

	value := cv.IntegerValues()
	sli.V = make([]int64, 0, cv.Len)
	var nilCount int
	for i := 0; i < cv.Len; i++ {
		if cv.IsNil(i) {
			sli.V = append(sli.V, math.MinInt64)
			nilCount++
			continue
		}
		sli.V = append(sli.V, value[i-nilCount])
	}
}

func (sli *IntegerSlice) PadIntSliceWithLimit(cv *ColVal, compareLength int) {
	for i := 0; i < compareLength; i++ {
		v, _ := cv.IntegerValue(i)
		sli.V = append(sli.V, v)
	}
}
