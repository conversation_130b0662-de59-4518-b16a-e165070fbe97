// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: statistics.tmpl

// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package statistics

import (
	"sync"
	"sync/atomic"
	"time"

	"github.com/openGemini/openGemini/lib/statisticsPusher/statistics/opsStat"
)

type DownSampleStatistics struct {
	itemErrors int64
	itemActive int64

	mu  sync.RWMutex
	buf []byte

	tags map[string]string
}

var instanceDownSampleStatistics = &DownSampleStatistics{}

func NewDownSampleStatistics() *DownSampleStatistics {
	return instanceDownSampleStatistics
}

func (s *DownSampleStatistics) Init(tags map[string]string) {
	s.tags = make(map[string]string)
	for k, v := range tags {
		s.tags[k] = v
	}
}

func (s *DownSampleStatistics) Collect(buffer []byte) ([]byte, error) {
	data := map[string]interface{}{
		"Errors": s.itemErrors,
		"Active": s.itemActive,
	}

	buffer = AddPointToBuffer("downSample", s.tags, data, buffer)
	if len(s.buf) > 0 {
		s.mu.Lock()
		buffer = append(buffer, s.buf...)
		s.buf = s.buf[:0]
		s.mu.Unlock()
	}

	return buffer, nil
}

func (s *DownSampleStatistics) CollectOps() []opsStat.OpsStatistic {
	data := map[string]interface{}{
		"Errors": s.itemErrors,
		"Active": s.itemActive,
	}

	return []opsStat.OpsStatistic{
		{
			Name:   "downSample",
			Tags:   s.tags,
			Values: data,
		},
	}
}

func (s *DownSampleStatistics) AddErrors(i int64) {
	atomic.AddInt64(&s.itemErrors, i)
}

func (s *DownSampleStatistics) AddActive(i int64) {
	atomic.AddInt64(&s.itemActive, i)
}

func (s *DownSampleStatistics) Push(item *DownSampleStatItem) {
	if !item.Validate() {
		return
	}

	data := item.Values()
	tags := item.Tags()
	AllocTagMap(tags, s.tags)

	s.mu.Lock()
	s.buf = AddPointToBuffer("downSample", tags, data, s.buf)
	s.mu.Unlock()
}

type DownSampleStatItem struct {
	validateHandle func(item *DownSampleStatItem) bool

	Level               int64
	OriginalFileCount   int64
	OriginalFileSize    int64
	DownSampleFileCount int64
	DownSampleFileSize  int64

	ShardID string
	TaskID  string

	begin    time.Time
	duration int64
}

func (s *DownSampleStatItem) Duration() int64 {
	if s.duration == 0 {
		s.duration = time.Since(s.begin).Milliseconds()
	}
	return s.duration
}

func (s *DownSampleStatItem) Push() {
	NewDownSampleStatistics().Push(s)
}

func (s *DownSampleStatItem) Validate() bool {
	if s.validateHandle == nil {
		return true
	}
	return s.validateHandle(s)
}

func (s *DownSampleStatItem) Values() map[string]interface{} {
	return map[string]interface{}{
		"Level":               s.Level,
		"OriginalFileCount":   s.OriginalFileCount,
		"OriginalFileSize":    s.OriginalFileSize,
		"DownSampleFileCount": s.DownSampleFileCount,
		"DownSampleFileSize":  s.DownSampleFileSize,
		"Duration":            s.Duration(),
	}
}

func (s *DownSampleStatItem) Tags() map[string]string {
	return map[string]string{
		"ShardID": s.ShardID,
		"TaskID":  s.TaskID,
	}
}
