// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: statistics.tmpl

// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package statistics

import (
	"sync"
	"sync/atomic"
)

type RecordStatistics struct {
	itemIntervalRecordPoolInUse            int64
	itemIntervalRecordPoolGet              int64
	itemIntervalRecordPoolGetReUse         int64
	itemIntervalRecordPoolAbort            int64
	itemFileCursorPoolInUse                int64
	itemFileCursorPoolGet                  int64
	itemFileCursorPoolGetReUse             int64
	itemFileCursorPoolAbort                int64
	itemFileLoopCursorPoolInUse            int64
	itemFileLoopCursorPoolGet              int64
	itemFileLoopCursorPoolGetReUse         int64
	itemFileLoopCursorPoolAbort            int64
	itemFileCursorValidRowPoolInUse        int64
	itemFileCursorValidRowPoolGet          int64
	itemFileCursorValidRowPoolGetReUse     int64
	itemFileCursorValidRowPoolAbort        int64
	itemFileCursorFilterRecordPoolInUse    int64
	itemFileCursorFilterRecordPoolGet      int64
	itemFileCursorFilterRecordPoolGetReUse int64
	itemFileCursorFilterRecordPoolAbort    int64
	itemAggPoolInUse                       int64
	itemAggPoolGet                         int64
	itemAggPoolGetReUse                    int64
	itemAggPoolAbort                       int64
	itemTsmMergePoolInUse                  int64
	itemTsmMergePoolGet                    int64
	itemTsmMergePoolGetReUse               int64
	itemTsmMergePoolAbort                  int64
	itemTsspSequencePoolInUse              int64
	itemTsspSequencePoolGet                int64
	itemTsspSequencePoolGetReUse           int64
	itemTsspSequencePoolAbort              int64
	itemSequenceAggPoolInUse               int64
	itemSequenceAggPoolGet                 int64
	itemSequenceAggPoolGetReUse            int64
	itemSequenceAggPoolAbort               int64
	itemCircularRecordPool                 int64
	itemSeriesPoolInUse                    int64
	itemSeriesPoolGet                      int64
	itemSeriesPoolAbort                    int64
	itemSeriesPoolGetReUse                 int64
	itemSeriesLoopPoolInUse                int64
	itemSeriesLoopPoolGet                  int64
	itemSeriesLoopPoolAbort                int64
	itemSeriesLoopPoolGetReUse             int64
	itemLogstoreInUse                      int64
	itemLogstoreGet                        int64
	itemLogstoreAbort                      int64
	itemLogstoreReUse                      int64

	mu  sync.RWMutex
	buf []byte

	tags map[string]string
}

var instanceRecordStatistics = &RecordStatistics{}

func NewRecordStatistics() *RecordStatistics {
	return instanceRecordStatistics
}

func (s *RecordStatistics) Init(tags map[string]string) {
	s.tags = make(map[string]string)
	for k, v := range tags {
		s.tags[k] = v
	}
}

func (s *RecordStatistics) Collect(buffer []byte) ([]byte, error) {
	data := map[string]interface{}{
		"IntervalRecordPoolInUse":            s.itemIntervalRecordPoolInUse,
		"IntervalRecordPoolGet":              s.itemIntervalRecordPoolGet,
		"IntervalRecordPoolGetReUse":         s.itemIntervalRecordPoolGetReUse,
		"IntervalRecordPoolAbort":            s.itemIntervalRecordPoolAbort,
		"FileCursorPoolInUse":                s.itemFileCursorPoolInUse,
		"FileCursorPoolGet":                  s.itemFileCursorPoolGet,
		"FileCursorPoolGetReUse":             s.itemFileCursorPoolGetReUse,
		"FileCursorPoolAbort":                s.itemFileCursorPoolAbort,
		"FileLoopCursorPoolInUse":            s.itemFileLoopCursorPoolInUse,
		"FileLoopCursorPoolGet":              s.itemFileLoopCursorPoolGet,
		"FileLoopCursorPoolGetReUse":         s.itemFileLoopCursorPoolGetReUse,
		"FileLoopCursorPoolAbort":            s.itemFileLoopCursorPoolAbort,
		"FileCursorValidRowPoolInUse":        s.itemFileCursorValidRowPoolInUse,
		"FileCursorValidRowPoolGet":          s.itemFileCursorValidRowPoolGet,
		"FileCursorValidRowPoolGetReUse":     s.itemFileCursorValidRowPoolGetReUse,
		"FileCursorValidRowPoolAbort":        s.itemFileCursorValidRowPoolAbort,
		"FileCursorFilterRecordPoolInUse":    s.itemFileCursorFilterRecordPoolInUse,
		"FileCursorFilterRecordPoolGet":      s.itemFileCursorFilterRecordPoolGet,
		"FileCursorFilterRecordPoolGetReUse": s.itemFileCursorFilterRecordPoolGetReUse,
		"FileCursorFilterRecordPoolAbort":    s.itemFileCursorFilterRecordPoolAbort,
		"AggPoolInUse":                       s.itemAggPoolInUse,
		"AggPoolGet":                         s.itemAggPoolGet,
		"AggPoolGetReUse":                    s.itemAggPoolGetReUse,
		"AggPoolAbort":                       s.itemAggPoolAbort,
		"TsmMergePoolInUse":                  s.itemTsmMergePoolInUse,
		"TsmMergePoolGet":                    s.itemTsmMergePoolGet,
		"TsmMergePoolGetReUse":               s.itemTsmMergePoolGetReUse,
		"TsmMergePoolAbort":                  s.itemTsmMergePoolAbort,
		"TsspSequencePoolInUse":              s.itemTsspSequencePoolInUse,
		"TsspSequencePoolGet":                s.itemTsspSequencePoolGet,
		"TsspSequencePoolGetReUse":           s.itemTsspSequencePoolGetReUse,
		"TsspSequencePoolAbort":              s.itemTsspSequencePoolAbort,
		"SequenceAggPoolInUse":               s.itemSequenceAggPoolInUse,
		"SequenceAggPoolGet":                 s.itemSequenceAggPoolGet,
		"SequenceAggPoolGetReUse":            s.itemSequenceAggPoolGetReUse,
		"SequenceAggPoolAbort":               s.itemSequenceAggPoolAbort,
		"CircularRecordPool":                 s.itemCircularRecordPool,
		"SeriesPoolInUse":                    s.itemSeriesPoolInUse,
		"SeriesPoolGet":                      s.itemSeriesPoolGet,
		"SeriesPoolAbort":                    s.itemSeriesPoolAbort,
		"SeriesPoolGetReUse":                 s.itemSeriesPoolGetReUse,
		"SeriesLoopPoolInUse":                s.itemSeriesLoopPoolInUse,
		"SeriesLoopPoolGet":                  s.itemSeriesLoopPoolGet,
		"SeriesLoopPoolAbort":                s.itemSeriesLoopPoolAbort,
		"SeriesLoopPoolGetReUse":             s.itemSeriesLoopPoolGetReUse,
		"LogstoreInUse":                      s.itemLogstoreInUse,
		"LogstoreGet":                        s.itemLogstoreGet,
		"LogstoreAbort":                      s.itemLogstoreAbort,
		"LogstoreReUse":                      s.itemLogstoreReUse,
	}

	buffer = AddPointToBuffer("record", s.tags, data, buffer)
	if len(s.buf) > 0 {
		s.mu.Lock()
		buffer = append(buffer, s.buf...)
		s.buf = s.buf[:0]
		s.mu.Unlock()
	}

	return buffer, nil
}

func (s *RecordStatistics) AddIntervalRecordPoolInUse(i int64) {
	atomic.AddInt64(&s.itemIntervalRecordPoolInUse, i)
}

func (s *RecordStatistics) AddIntervalRecordPoolGet(i int64) {
	atomic.AddInt64(&s.itemIntervalRecordPoolGet, i)
}

func (s *RecordStatistics) AddIntervalRecordPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemIntervalRecordPoolGetReUse, i)
}

func (s *RecordStatistics) AddIntervalRecordPoolAbort(i int64) {
	atomic.AddInt64(&s.itemIntervalRecordPoolAbort, i)
}

func (s *RecordStatistics) AddFileCursorPoolInUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorPoolInUse, i)
}

func (s *RecordStatistics) AddFileCursorPoolGet(i int64) {
	atomic.AddInt64(&s.itemFileCursorPoolGet, i)
}

func (s *RecordStatistics) AddFileCursorPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorPoolGetReUse, i)
}

func (s *RecordStatistics) AddFileCursorPoolAbort(i int64) {
	atomic.AddInt64(&s.itemFileCursorPoolAbort, i)
}

func (s *RecordStatistics) AddFileLoopCursorPoolInUse(i int64) {
	atomic.AddInt64(&s.itemFileLoopCursorPoolInUse, i)
}

func (s *RecordStatistics) AddFileLoopCursorPoolGet(i int64) {
	atomic.AddInt64(&s.itemFileLoopCursorPoolGet, i)
}

func (s *RecordStatistics) AddFileLoopCursorPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemFileLoopCursorPoolGetReUse, i)
}

func (s *RecordStatistics) AddFileLoopCursorPoolAbort(i int64) {
	atomic.AddInt64(&s.itemFileLoopCursorPoolAbort, i)
}

func (s *RecordStatistics) AddFileCursorValidRowPoolInUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorValidRowPoolInUse, i)
}

func (s *RecordStatistics) AddFileCursorValidRowPoolGet(i int64) {
	atomic.AddInt64(&s.itemFileCursorValidRowPoolGet, i)
}

func (s *RecordStatistics) AddFileCursorValidRowPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorValidRowPoolGetReUse, i)
}

func (s *RecordStatistics) AddFileCursorValidRowPoolAbort(i int64) {
	atomic.AddInt64(&s.itemFileCursorValidRowPoolAbort, i)
}

func (s *RecordStatistics) AddFileCursorFilterRecordPoolInUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorFilterRecordPoolInUse, i)
}

func (s *RecordStatistics) AddFileCursorFilterRecordPoolGet(i int64) {
	atomic.AddInt64(&s.itemFileCursorFilterRecordPoolGet, i)
}

func (s *RecordStatistics) AddFileCursorFilterRecordPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemFileCursorFilterRecordPoolGetReUse, i)
}

func (s *RecordStatistics) AddFileCursorFilterRecordPoolAbort(i int64) {
	atomic.AddInt64(&s.itemFileCursorFilterRecordPoolAbort, i)
}

func (s *RecordStatistics) AddAggPoolInUse(i int64) {
	atomic.AddInt64(&s.itemAggPoolInUse, i)
}

func (s *RecordStatistics) AddAggPoolGet(i int64) {
	atomic.AddInt64(&s.itemAggPoolGet, i)
}

func (s *RecordStatistics) AddAggPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemAggPoolGetReUse, i)
}

func (s *RecordStatistics) AddAggPoolAbort(i int64) {
	atomic.AddInt64(&s.itemAggPoolAbort, i)
}

func (s *RecordStatistics) AddTsmMergePoolInUse(i int64) {
	atomic.AddInt64(&s.itemTsmMergePoolInUse, i)
}

func (s *RecordStatistics) AddTsmMergePoolGet(i int64) {
	atomic.AddInt64(&s.itemTsmMergePoolGet, i)
}

func (s *RecordStatistics) AddTsmMergePoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemTsmMergePoolGetReUse, i)
}

func (s *RecordStatistics) AddTsmMergePoolAbort(i int64) {
	atomic.AddInt64(&s.itemTsmMergePoolAbort, i)
}

func (s *RecordStatistics) AddTsspSequencePoolInUse(i int64) {
	atomic.AddInt64(&s.itemTsspSequencePoolInUse, i)
}

func (s *RecordStatistics) AddTsspSequencePoolGet(i int64) {
	atomic.AddInt64(&s.itemTsspSequencePoolGet, i)
}

func (s *RecordStatistics) AddTsspSequencePoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemTsspSequencePoolGetReUse, i)
}

func (s *RecordStatistics) AddTsspSequencePoolAbort(i int64) {
	atomic.AddInt64(&s.itemTsspSequencePoolAbort, i)
}

func (s *RecordStatistics) AddSequenceAggPoolInUse(i int64) {
	atomic.AddInt64(&s.itemSequenceAggPoolInUse, i)
}

func (s *RecordStatistics) AddSequenceAggPoolGet(i int64) {
	atomic.AddInt64(&s.itemSequenceAggPoolGet, i)
}

func (s *RecordStatistics) AddSequenceAggPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemSequenceAggPoolGetReUse, i)
}

func (s *RecordStatistics) AddSequenceAggPoolAbort(i int64) {
	atomic.AddInt64(&s.itemSequenceAggPoolAbort, i)
}

func (s *RecordStatistics) AddCircularRecordPool(i int64) {
	atomic.AddInt64(&s.itemCircularRecordPool, i)
}

func (s *RecordStatistics) AddSeriesPoolInUse(i int64) {
	atomic.AddInt64(&s.itemSeriesPoolInUse, i)
}

func (s *RecordStatistics) AddSeriesPoolGet(i int64) {
	atomic.AddInt64(&s.itemSeriesPoolGet, i)
}

func (s *RecordStatistics) AddSeriesPoolAbort(i int64) {
	atomic.AddInt64(&s.itemSeriesPoolAbort, i)
}

func (s *RecordStatistics) AddSeriesPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemSeriesPoolGetReUse, i)
}

func (s *RecordStatistics) AddSeriesLoopPoolInUse(i int64) {
	atomic.AddInt64(&s.itemSeriesLoopPoolInUse, i)
}

func (s *RecordStatistics) AddSeriesLoopPoolGet(i int64) {
	atomic.AddInt64(&s.itemSeriesLoopPoolGet, i)
}

func (s *RecordStatistics) AddSeriesLoopPoolAbort(i int64) {
	atomic.AddInt64(&s.itemSeriesLoopPoolAbort, i)
}

func (s *RecordStatistics) AddSeriesLoopPoolGetReUse(i int64) {
	atomic.AddInt64(&s.itemSeriesLoopPoolGetReUse, i)
}

func (s *RecordStatistics) AddLogstoreInUse(i int64) {
	atomic.AddInt64(&s.itemLogstoreInUse, i)
}

func (s *RecordStatistics) AddLogstoreGet(i int64) {
	atomic.AddInt64(&s.itemLogstoreGet, i)
}

func (s *RecordStatistics) AddLogstoreAbort(i int64) {
	atomic.AddInt64(&s.itemLogstoreAbort, i)
}

func (s *RecordStatistics) AddLogstoreReUse(i int64) {
	atomic.AddInt64(&s.itemLogstoreReUse, i)
}
