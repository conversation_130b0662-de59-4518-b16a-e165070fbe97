// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: statistics.tmpl

// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package statistics

import (
	"sync"
	"sync/atomic"
	"time"

	"github.com/openGemini/openGemini/lib/statisticsPusher/statistics/opsStat"
)

type MergeStatistics struct {
	itemCurrentOutOfOrderFile int64
	itemSkipTotal             int64
	itemMergeSelfTotal        int64
	itemErrors                int64
	itemActive                int64

	mu  sync.RWMutex
	buf []byte

	tags map[string]string
}

var instanceMergeStatistics = &MergeStatistics{}

func NewMergeStatistics() *MergeStatistics {
	return instanceMergeStatistics
}

func (s *MergeStatistics) Init(tags map[string]string) {
	s.tags = make(map[string]string)
	for k, v := range tags {
		s.tags[k] = v
	}
}

func (s *MergeStatistics) Collect(buffer []byte) ([]byte, error) {
	data := map[string]interface{}{
		"CurrentOutOfOrderFile": s.itemCurrentOutOfOrderFile,
		"SkipTotal":             s.itemSkipTotal,
		"MergeSelfTotal":        s.itemMergeSelfTotal,
		"Errors":                s.itemErrors,
		"Active":                s.itemActive,
	}

	buffer = AddPointToBuffer("merge", s.tags, data, buffer)
	if len(s.buf) > 0 {
		s.mu.Lock()
		buffer = append(buffer, s.buf...)
		s.buf = s.buf[:0]
		s.mu.Unlock()
	}

	return buffer, nil
}

func (s *MergeStatistics) CollectOps() []opsStat.OpsStatistic {
	data := map[string]interface{}{
		"CurrentOutOfOrderFile": s.itemCurrentOutOfOrderFile,
		"SkipTotal":             s.itemSkipTotal,
		"MergeSelfTotal":        s.itemMergeSelfTotal,
		"Errors":                s.itemErrors,
		"Active":                s.itemActive,
	}

	return []opsStat.OpsStatistic{
		{
			Name:   "merge",
			Tags:   s.tags,
			Values: data,
		},
	}
}

func (s *MergeStatistics) AddCurrentOutOfOrderFile(i int64) {
	atomic.AddInt64(&s.itemCurrentOutOfOrderFile, i)
}

func (s *MergeStatistics) AddSkipTotal(i int64) {
	atomic.AddInt64(&s.itemSkipTotal, i)
}

func (s *MergeStatistics) AddMergeSelfTotal(i int64) {
	atomic.AddInt64(&s.itemMergeSelfTotal, i)
}

func (s *MergeStatistics) AddErrors(i int64) {
	atomic.AddInt64(&s.itemErrors, i)
}

func (s *MergeStatistics) AddActive(i int64) {
	atomic.AddInt64(&s.itemActive, i)
}

func (s *MergeStatistics) SetCurrentOutOfOrderFile(i int64) {
	s.itemCurrentOutOfOrderFile = i
}

func (s *MergeStatistics) Push(item *MergeStatItem) {
	if !item.Validate() {
		return
	}

	data := item.Values()
	tags := item.Tags()
	AllocTagMap(tags, s.tags)

	s.mu.Lock()
	s.buf = AddPointToBuffer("merge", tags, data, s.buf)
	s.mu.Unlock()
}

type MergeStatItem struct {
	validateHandle func(item *MergeStatItem) bool

	OutOfOrderFileCount  int64
	OutOfOrderFileSize   int64
	OrderFileCount       int64
	OrderFileSize        int64
	MergedFileCount      int64
	MergedFileSize       int64
	OrderSeriesCount     int64
	IntersectSeriesCount int64

	Measurement string
	ShardID     string

	begin    time.Time
	duration int64
}

func (s *MergeStatItem) Duration() int64 {
	if s.duration == 0 {
		s.duration = time.Since(s.begin).Milliseconds()
	}
	return s.duration
}

func (s *MergeStatItem) Push() {
	NewMergeStatistics().Push(s)
}

func (s *MergeStatItem) Validate() bool {
	if s.validateHandle == nil {
		return true
	}
	return s.validateHandle(s)
}

func (s *MergeStatItem) Values() map[string]interface{} {
	return map[string]interface{}{
		"OutOfOrderFileCount":  s.OutOfOrderFileCount,
		"OutOfOrderFileSize":   s.OutOfOrderFileSize,
		"OrderFileCount":       s.OrderFileCount,
		"OrderFileSize":        s.OrderFileSize,
		"MergedFileCount":      s.MergedFileCount,
		"MergedFileSize":       s.MergedFileSize,
		"OrderSeriesCount":     s.OrderSeriesCount,
		"IntersectSeriesCount": s.IntersectSeriesCount,
		"Duration":             s.Duration(),
	}
}

func (s *MergeStatItem) Tags() map[string]string {
	return map[string]string{
		"Measurement": s.Measurement,
		"ShardID":     s.ShardID,
	}
}
