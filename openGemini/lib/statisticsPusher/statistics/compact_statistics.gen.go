// Copyright 2022 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benbjohnson/tmpl
//
// Source: statistics.tmpl

package statistics

import (
	"sync"
	"sync/atomic"

	"github.com/openGemini/openGemini/lib/statisticsPusher/statistics/opsStat"
)

type CompactStatistics struct {
	itemActive             int64
	itemErrors             int64
	itemMaxMemoryUsed      int64
	itemRecordPoolGetTotal int64
	itemRecordPoolHitTotal int64

	mu  sync.RWMutex
	buf []byte

	tags map[string]string
}

var instanceCompactStatistics = &CompactStatistics{}

func NewCompactStatistics() *CompactStatistics {
	return instanceCompactStatistics
}

func (s *CompactStatistics) Init(tags map[string]string) {
	s.tags = make(map[string]string)
	for k, v := range tags {
		s.tags[k] = v
	}
}

func (s *CompactStatistics) Collect(buffer []byte) ([]byte, error) {
	data := map[string]interface{}{
		"Active":             s.itemActive,
		"Errors":             s.itemErrors,
		"MaxMemoryUsed":      s.itemMaxMemoryUsed,
		"RecordPoolGetTotal": s.itemRecordPoolGetTotal,
		"RecordPoolHitTotal": s.itemRecordPoolHitTotal,
	}

	buffer = AddPointToBuffer("compact", s.tags, data, buffer)
	if len(s.buf) > 0 {
		s.mu.Lock()
		buffer = append(buffer, s.buf...)
		s.buf = s.buf[:0]
		s.mu.Unlock()
	}

	return buffer, nil
}

func (s *CompactStatistics) CollectOps() []opsStat.OpsStatistic {
	data := map[string]interface{}{
		"Active":             s.itemActive,
		"Errors":             s.itemErrors,
		"MaxMemoryUsed":      s.itemMaxMemoryUsed,
		"RecordPoolGetTotal": s.itemRecordPoolGetTotal,
		"RecordPoolHitTotal": s.itemRecordPoolHitTotal,
	}

	return []opsStat.OpsStatistic{
		{
			Name:   "compact",
			Tags:   s.tags,
			Values: data,
		},
	}
}

func (s *CompactStatistics) AddActive(i int64) {
	atomic.AddInt64(&s.itemActive, i)
}

func (s *CompactStatistics) AddErrors(i int64) {
	atomic.AddInt64(&s.itemErrors, i)
}

func (s *CompactStatistics) AddMaxMemoryUsed(i int64) {
	atomic.AddInt64(&s.itemMaxMemoryUsed, i)
}

func (s *CompactStatistics) AddRecordPoolGetTotal(i int64) {
	atomic.AddInt64(&s.itemRecordPoolGetTotal, i)
}

func (s *CompactStatistics) AddRecordPoolHitTotal(i int64) {
	atomic.AddInt64(&s.itemRecordPoolHitTotal, i)
}

func (s *CompactStatistics) SetActive(i int64) {
	s.itemActive = i
}
