// Copyright 2024 Huawei Cloud Computing Technologies Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package statistics_test

import (
	"testing"
	"time"

	"github.com/openGemini/openGemini/lib/statisticsPusher/statistics"
)

func Test{{.Name}}(t *testing.T) {
    stat := statistics.New{{$.Name}}Statistics()
	tags := map[string]string{"hostname": "127.0.0.1:8866", "mst": "{{.Measurement}}"}
	stat.Init(tags)

	{{- range .Items}}
    stat.Add{{.}}(2)
    {{- end}}

	fields := map[string]interface{}{
        {{- range .Items}}
        "{{.}}" : int64(2),
        {{- end}}
	}
	statistics.NewTimestamp().Init(time.Second)
	buf, err := stat.Collect(nil)
	if err != nil {
        t.Fatalf("%v", err)
    }

	if err := compareBuffer("{{.Measurement}}", tags, fields, buf); err != nil {
		t.Fatalf("%v", err)
	}
}
