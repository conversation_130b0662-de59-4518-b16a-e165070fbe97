{"Name": "Record", "Measurement": "record", "Items": ["IntervalRecordPoolInUse", "IntervalRecordPoolGet", "IntervalRecordPoolGetReUse", "IntervalRecordPoolAbort", "FileCursorPoolInUse", "FileCursorPoolGet", "FileCursorPoolGetReUse", "FileCursorPoolAbort", "FileLoopCursorPoolInUse", "FileLoopCursorPoolGet", "FileLoopCursorPoolGetReUse", "FileLoopCursorPoolAbort", "FileCursorValidRowPoolInUse", "FileCursorValidRowPoolGet", "FileCursorValidRowPoolGetReUse", "FileCursorValidRowPoolAbort", "FileCursorFilterRecordPoolInUse", "FileCursorFilterRecordPoolGet", "FileCursorFilterRecordPoolGetReUse", "FileCursorFilterRecordPoolAbort", "AggPoolInUse", "AggPoolGet", "AggPoolGetReUse", "AggPoolAbort", "TsmMergePoolInUse", "TsmMergePoolGet", "TsmMergePoolGetReUse", "TsmMergePoolAbort", "TsspSequencePoolInUse", "TsspSequencePoolGet", "TsspSequencePoolGetReUse", "TsspSequencePoolAbort", "SequenceAggPoolInUse", "SequenceAggPoolGet", "SequenceAggPoolGetReUse", "SequenceAggPoolAbort", "CircularRecordPool", "SeriesPoolInUse", "SeriesPoolGet", "SeriesPoolAbort", "SeriesPoolGetReUse", "SeriesLoopPoolInUse", "SeriesLoopPoolGet", "SeriesLoopPoolAbort", "SeriesLoopPoolGetReUse", "LogstoreInUse", "LogstoreGet", "LogstoreAbort", "LogstoreReUse"], "EnablePush": "Y", "PushDuration": "Y"}