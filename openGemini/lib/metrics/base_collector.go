// Copyright 2024 openGemini Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
)

// ModuleIndex stores the indicators of each module
type ModuleIndex struct {
	MetricsMap  map[string]interface{}
	LabelValues map[string]string
	Timestamp   time.Time
}

type Metric struct {
	HelpMap map[string]string
	Labels  []string
}

func NewDesc(subsystem, name, help string, labels []string) *prometheus.Desc {
	return prometheus.NewDesc(
		prometheus.BuildFQName("", subsystem, name),
		help,
		labels,
		nil,
	)
}
