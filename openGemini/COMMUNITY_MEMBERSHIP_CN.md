# openGemini 社区成员资格

**注意:** 此文档会根据 openGemini 社区的状态和反馈不断变化。

| 角色                        | 要求                                                                      | 责任             | 权限                                      |
|---------------------------|-------------------------------------------------------------------------|----------------|-----------------------------------------|
| [Member](#member)         | 至少由1位 Maintainer 提名，通过 1/2 majority vote，积极参与社区活动                       | 欢迎并指导新贡献者      | 成为 openGemini GitHub 组织的成员              |
| [Committer](#committer)   | 至少由1位 Maintainer 提名，通过 1/2 majority vote，具备强大的领域知识，积极为代码和审查做出贡献         | 审查并批准社区成员的贡献   | 对相关仓库中特定包有写入权限                          |
| [Maintainer](#maintainer) | 至少由1位 Maintainer 提名，通过 2/3 majority vote，无现任 Maintainers 反对，展示出优秀的技术判断力 | 参与发布计划和功能开发/维护 | 对相关仓库有最高级别写入权限；在仓库的 Maintainers 文件中记录姓名 |

**注意:** 所有 openGemini 社区成员必须遵守 openGemini [行为准则](./CODE_OF_CONDUCT.md)。

## Member

Members 是社区中活跃的参与者，他们通过创建 PR、审查问题/PR 或参与 Slack/邮件列表上的社区讨论来做出贡献。

### 要求

- 至少由1位 Maintainer 提名
- 需要通过 Maintainers 的 1/2 majority vote，且无现任 Maintainers 反对
- 启用 GitHub 账户的双因子认证
- 积极为社区做出贡献。贡献可能包括但不限于：
    - 创建 PR
    - 审查其他社区成员创建的问题/PR
    - 参与 Slack/邮件列表上的社区讨论
    - 参与 openGemini 社区会议

### 责任和权限

- 成为 openGemini GitHub 组织的成员
- 可以被分配到问题和 PR 上，社区成员也可以请求他们的审查
- 参与分配的问题和 PR
- 欢迎新贡献者
- 指导新贡献者找到相关文档/文件
- 帮助/激励新成员为 openGemini 做贡献

## Committer

Committers 是具有丰富经验和领域知识的活跃成员。他们积极参与问题/PR 的审查，并在审查过程中发现了相关问题。

### 要求

- 至少由1位 Maintainer 提名
- 需要通过 Maintainers 的 1/2 majority vote，且无现任 Maintainers 反对
- 审查了大量 PR
- 具有良好的代码库知识

### 责任和权限

- 审查代码以维护/提高代码质量
- 确认并处理社区成员的审查请求
- 可以批准与相关专业知识相关的代码贡献
- 对仓库中特定包有写入权限
- 继续贡献并指导其他社区成员为 openGemini 项目做贡献

## Maintainer

Maintainers 是在功能设计/开发方面展示出优秀技术判断力的 committers。他们拥有项目和功能的整体知识。

### 要求

- 至少由1位 Maintainer 提名
- 需要通过 Maintainers 的 2/3 majority vote，且无现任 Maintainers 反对
- 在功能设计/开发方面具有良好的技术判断力

### 责任和权限

- 参与发布计划
- 维护项目代码质量
- 确保API在不同版本之间的兼容性，保持前向和后向兼容性，随着功能的演进不破坏现有功能
- 分析并提出 openGemini 项目的新功能/增强功能
- 展示出良好的技术判断力
- 指导贡献者和 Committers
- 对相关仓库有最高级别写入权限（当需要手动合并 PR 时能够点击 Merge PR 按钮）
- 在仓库的 Maintainers 文件中记录姓名
- 参与并推动多个功能的设计/开发

## 原则

- 提名请求：成员可以请求提名担任角色。
- 基于信任的提名：提名基于社区内的信任。
- 私密提名：提名通过邮件列表私下处理，决定后进行公开宣布。
- 角色移除或降级：在以下情况下，角色可能被移除或降级：
    - 自愿退出
    - 违反社区行为准则，导致弹劾
    - 在社区中不活跃，由所有者弹劾

## 修改此章程

对本文件的更改需要由 Maintainers 通过 2/3 majority vote 批准。
