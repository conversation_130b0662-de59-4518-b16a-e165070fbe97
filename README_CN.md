# ThinfluxDB Cluster

ThinfluxDB Cluster - 一个开源分布式时间序列数据库，ThinfluxDB Enterprise 的开源替代方案

## 简介

ThinfluxDB Cluster 是一个开源的 **时间序列数据库**，**没有外部依赖**。它对于记录指标、事件和执行分析很有用。

ThinfluxDB Cluster 启发于 ThinfluxDB Enterprise、 ThinfluxDB v1.8.10 和 ThinfluxDB v0.11.1，旨在替代 ThinfluxDB Enterprise。

ThinfluxDB Cluster 易于维护，可以与上游 ThinfluxDB 1.x 保持实时更新。

## 特性

* 内置 HTTP API，无需编写任何服务器端代码即可启动和运行。
* 数据可以被标记 tag，允许非常灵活的查询。
* 类似 SQL 的查询语言。
* 集群支持开箱即用，因此处理数据可以水平扩展。**集群目前处于生产就绪状态**。
* 易于安装和管理，数据写入查询速度快。
* 旨在实时应答查询。这意味着每个数据点在到来时都会被建立索引，并且在 < 100 毫秒内返回的查询中立即可用。

## 架构

ThinfluxDB Cluster 是一个分布式时间序列数据库系统，专为高写入吞吐量、高查询性能和高可用性而设计。它由两组独立的进程组成：Data 节点和 Meta 节点，这两种节点类型各自承担不同的职责，共同构成完整的集群功能。

**Meta节点**是集群的"大脑"，负责管理集群的元数据和协调集群操作：
- 存储和管理集群元数据（用户、数据库、保留策略、分片映射等）
- 使用Raft共识算法确保元数据一致性
- 监控集群中所有节点的健康状态
- 协调数据分片和复制策略
- 处理节点的加入和离开

**Data节点**是集群的"工作引擎"，负责实际数据的存储和处理：
- 存储实际的时间序列数据
- 处理**写入**请求和**查询**请求
- 执行数据保留策略和连续查询
- 维护索引和缓存以优化性能
- 在节点间复制数据以提供冗余

集群内的通信如下所示：

![ThinfluxDB Cluster架构图，展示了Meta节点和Data节点之间的通信结构](./images/architecture.png)

网络架构图：

![ThinfluxDB Cluster网络架构图，展示了各节点间的网络连接和端口配置](./images/net-architecture.png)

在数据流转过程中，写入请求会到达集群中的任意Data节点，该节点会与Meta节点通信以确定数据应该存储在哪里，然后将数据路由到相应的节点。查询过程也类似，接收查询的Data节点会协调从多个节点收集必要的数据，然后合并结果返回给客户端。

Meta 节点通过 TCP 协议和 Raft 共识协议相互通信，默认都使用端口 `8089`，此端口必须在 Meta 节点之间是可访问的。默认 Meta 节点还将公开绑定到端口 `8091` 的 HTTP API，`thinfluxd-ctl` 命令使用该 API。

Data 节点通过绑定到端口 `8088` 的 TCP 协议相互通信。Data 节点通过绑定到 `8091` 的 HTTP API 与 Meta 节点通信。这些端口必须在 Meta 节点和 Data 节点之间是可访问的。

在集群内，所有 Meta 节点都必须与所有其它 Meta 节点通信。所有 Data 节点必须与所有其它 Data 节点和所有 Meta 节点通信。

Meta节点使用Raft共识协议来保持一致性。由于Raft需要多数节点同意才能推进状态，所以：
- 最小配置：1个Meta节点（但不推荐用于生产环境，因为没有容错能力）
- 推荐配置：3个Meta节点（可以容忍1个节点故障）
- 扩展配置：5个Meta节点（可以容忍2个节点故障）

奇数数量的Meta节点是推荐的，因为偶数数量不会提高容错能力，但会增加资源消耗。例如，4个节点和3个节点都只能容忍1个节点故障。

Data节点的数量更加灵活，取决于数据量、查询负载和高可用性需求：
- 最小配置：1个Data节点（但没有冗余）
- 基本高可用：2个Data节点（可以提供基本的数据冗余）
- 推荐配置：3个或更多Data节点（根据负载和规模可以扩展）

综合考虑， ThinfluxDB 集群的最小可行配置为：
- 开发/测试环境：1个Meta节点 + 1个Data节点（总共2个节点）
- **基本生产环境：3个Meta节点 + 2个Data节点（总共5个节点，如示例中的配置）**
- 高可用生产环境：3个Meta节点 + 3+个Data节点（根据负载和规模可以扩展）

## 从源码构建

### 编译安装

安装依赖：

```bash
sudo yum install -y python3-devel go
```

克隆仓库：

```bash
git clone http://192.168.203.190/work/tdb/tsdb/thinfluxdb-cluster.git
```

构建：

```bash
go clean ./...
go install -ldflags="-X main.version=1.8.10-c1.1.2" ./...
```

二进制文件将位于`$GOPATH/bin`中，包括`thinfluxd`、`thinfluxd-meta`和`thinfluxd-ctl`。

要在构建过程中设置版本和提交标志，请将以下内容传递给install命令：

```bash
-ldflags="-X main.version=$VERSION -X main.branch=$BRANCH -X main.commit=$COMMIT"
```

打包：

```bash
python3 build.py --package --version=1.8.10-c1.1.2 --platform=linux --arch=amd64 --outdir=/home/<USER>/package --clean --release
```

## 从安装包安装

在所有服务器上执行以下操作：

1. 解压安装包：

```bash
tar -zxvf thinfluxdb-cluster-1.8.10-c1.1.2-linux-amd64.tar.gz -C /home/<USER>/thinfluxdb-cluster
```

2. 将二进制文件路径加入环境变量：

```bash
export PATH=$PATH:/home/<USER>/thinfluxdb-cluster/usr/bin
```

## 部署

### 环境配置

以下是集群环境配置，包括各服务器的IP地址、主机名和角色分配：

| IP地址          | 主机名              | 角色     |
|----------------|--------------------|----------|
| ************** | thinfluxdb-meta-01 | Meta节点 |
| ************** | thinfluxdb-meta-02 | Meta节点 |
| ************** | thinfluxdb-meta-03 | Meta节点 |
| ************** | thinfluxdb-data-01 | Data节点 |
| ************** | thinfluxdb-data-02 | Data节点 |

> 注：在实际部署中，可根据业务需求和硬件资源调整节点数量和分布。Meta节点负责集群元数据管理，Data节点负责数据存储和查询处理。

> 重要：上表中的IP地址和主机名仅为示例值，请根据实际环境进行替换。

> 重要：集群节点间需要特定端口进行通信，请确保在所有服务器上配置防火墙规则，允许相关端口（8088、8089、8091等）之间的通信。

### 基本目录创建

在所有服务器上创建以下目录：

```bash
sudo mkdir -p /etc/thinfluxdb
sudo mkdir -p /var/lib/thinfluxdb

sudo chown -R tdtgi:tdtgi /etc/thinfluxdb
sudo chown -R tdtgi:tdtgi /var/lib/thinfluxdb
```

> 注意：请将上述命令中的"tdtgi"替换为环境中实际的用户名和用户组。

### Meta 节点设置

0. 设置说明和要求

生产环境安装过程设置三个 Meta 节点，每个 Meta 节点在自己的服务器上运行。ThinfluxDB Cluster 需要**至少三个 Meta 节点且推荐奇数个 Meta 节点** 以实现高可用和冗余。

> 注 1：ThinfluxDB Cluster 不建议超过三个 Meta 节点，除非您的服务器之间的通信存在长期可靠性问题。

> 注 2：强烈建议不要在同一服务器上部署多个 Meta 节点，因为如果该特定服务器无响应，它会产生更大的潜在故障。ThinfluxDB Cluster 建议在占用空间相对较小的服务器上部署 Meta 节点。

> 注 3：要使用单个 Meta 节点启动集群，请在启动单个 Meta 节点时传递 -single-server 标志。

假设有三台服务器： thinfluxdb-meta-01, thinfluxdb-meta-02 和 thinfluxdb-meta-03，通过端口 8089（Meta节点间Raft通信）和 8091（HTTP API）进行通信。

1. 为每个服务器添加适当的 DNS 条目

> 注: 如果您只想使用 IP 地址而不是主机名，请跳过当前步骤并转到步骤 2。

确保将服务器的主机名和 IP 地址添加到hosts文件中：

```bash
************** thinfluxdb-meta-01
************** thinfluxdb-meta-02
************** thinfluxdb-meta-03
```

2. 编辑每个 Meta 节点的配置文件`/etc/thinfluxdb/thinfluxdb-meta.conf`

```toml
hostname = "thinfluxdb-meta-0x"  # 请将 0x 替换为实际的节点编号，如 01、02 或 03

[meta]
  dir = "/var/lib/thinfluxdb/meta"
```

注：如果只想使用 IP 地址而不是主机名，**必须**将 hostname 设置为 IP 地址。

3. 启动 Meta 服务

分别在服务器 thinfluxdb-meta-01、 thinfluxdb-meta-02 和 thinfluxdb-meta-03 上启动 Meta 服务：

```bash
thinfluxd-meta -config /etc/thinfluxdb/thinfluxdb-meta.conf
```

4. 将 Meta 节点加入集群

仅在一个 Meta 节点上，加入所有 Meta 节点，包括它自己。比如在 thinfluxdb-meta-01 运行：

```bash
thinfluxd-ctl add-meta thinfluxdb-meta-01:8091
thinfluxd-ctl add-meta thinfluxdb-meta-02:8091
thinfluxd-ctl add-meta thinfluxdb-meta-03:8091
```

如果只想使用 IP 地址而不是主机名或者在 Data 节点上，则应该添加 -bind 选项，指定要连接的 Meta 节点并绑定 HTTP 地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 show
```

注意：请将上述命令中的 IP 地址替换为您实际环境中的 IP 地址。

预期的输出是：

```bash
Added meta node x at thinfluxdb-meta-0x:8091
```

注意：实际输出中的节点 ID 和名称将根据您的具体部署情况而变化。

若执行成功，则在任何 Meta 节点上执行：

```bash
thinfluxd-ctl show
```

预期的输出是：

```bash
Data Nodes
==========
ID  TCP Address  Version

Meta Nodes
==========
ID  TCP Address              Version
1   thinfluxdb-meta-01:8091  1.8.10-c1.1.2
2   thinfluxdb-meta-02:8091  1.8.10-c1.1.2
3   thinfluxdb-meta-03:8091  1.8.10-c1.1.2
```

### Data 节点设置

0. 设置说明和要求

生产环境安装过程设置两个 Data 节点，每个 Data 节点在自己的服务器上运行。 ThinfluxDB Cluster **推荐至少两个 Data 节点** 以实现高可用性和冗余。

> 注 1：没有要求每个 Data 节点都运行在自己的服务器上。但是，最佳实践是将每个 Data 节点部署在专用服务器上。

> 注 2：ThinfluxDB Cluster 不能用作负载均衡器。您需要配置自己的负载均衡器以将客户端流量发送到端口 8086（HTTP API 的默认端口）。

假设有两台服务器： thinfluxdb-data-01 和 thinfluxdb-data-02，通过端口 8088（Data节点间通信）和 8091（与Meta节点通信）进行通信。

1. 为每个服务器添加适当的 DNS 条目

> 注: 如果只想使用 IP 地址而不是主机名，请跳过当前步骤并转到步骤 2。

确保将服务器的主机名和 IP 地址添加到hosts文件中：

```bash
************** thinfluxdb-data-01
************** thinfluxdb-data-02
```

2. 编辑配置文件`/etc/thinfluxdb/thinfluxdb.conf`

```toml
#bind-address = ":8088"
hostname = "thinfluxdb-data-0x"  # 请将 0x 替换为实际的节点编号，如 01、02

[meta]
  dir = "/var/lib/thinfluxdb/meta"

[data]
  dir = "/var/lib/thinfluxdb/data"
  engine = "tsm1"
  wal-dir = "/var/lib/thinfluxdb/wal"

[hinted-handoff]
  dir = "/var/lib/thinfluxdb/hh"
```

注：如果只想使用 IP 地址而不是主机名，必须将 hostname 设置为 IP 地址。

3. 启动 Data 服务

分别在服务器 thinfluxdb-data-01 和 thinfluxdb-data-02 上启动 Data 服务

```bash
thinfluxd -config /etc/thinfluxdb/thinfluxdb.conf
```

> 注: Data 节点在未被加入集群之前，出现 `Failed to create storage`，`failed to store statistics` 或 `meta service unavailable` 日志是正常情况。

4. 将 Data 节点加入集群

无论是在集群的初始创建期间还是在增加 Data 节点数量，只有在添加全新节点时才应将 Data 节点加入集群。如果要使用 thinfluxd-ctl update-data 替换现有 Data 节点，请跳过本步骤的其余部分。

在 **Meta 节点上** 对要加入集群的每个 Data 节点**运行一次且仅一次**的 add-data 命令：

```bash
thinfluxd-ctl add-data thinfluxdb-data-01:8088
thinfluxd-ctl add-data thinfluxdb-data-02:8088
```

若在 **Data 节点上**，则应该添加 -bind 选项，指定要连接的 Meta 节点的绑定地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 add-data thinfluxdb-data-01:8088
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 add-data thinfluxdb-data-02:8088
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 show
```

若只想使用 IP 地址而不是主机名，则应该添加 -bind 选项，指定要连接的 Meta 节点的绑定 HTTP 地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind **************:8091 add-data **************:8088
thinfluxd-ctl -bind **************:8091 add-data **************:8088
thinfluxd-ctl -bind **************:8091 show
```

预期的输出是：

```bash
Added data node y at thinfluxdb-data-0x:8088
```

注意：实际输出中的节点 ID 和名称将根据您的具体部署情况而变化。

验证步骤:

在任何 Meta 节点上发出以下命令：

```bash
thinfluxd-ctl show
```

预期的输出是：

```bash
Data Nodes
==========
ID  TCP Address              Version
4   thinfluxdb-data-01:8088  1.8.10-c1.1.2
5   thinfluxdb-data-02:8088  1.8.10-c1.1.2

Meta Nodes
==========
ID  TCP Address              Version
1   thinfluxdb-meta-01:8091  1.8.10-c1.1.2
2   thinfluxdb-meta-02:8091  1.8.10-c1.1.2
3   thinfluxdb-meta-03:8091  1.8.10-c1.1.2
```

## 通过 HTTP API 使用 ThinfluxDB Cluster

### 创建 database

```bash
curl -XPOST "http://thinfluxdb-data-01:8086/query" --data-urlencode "q=CREATE DATABASE mydb WITH REPLICATION 2"
```

输出：

```bash
{"results":[{"statement_id":0}]}
```

表示创建 mydb 数据库成功。

### 写入一些数据

```bash
curl -XPOST "http://thinfluxdb-data-01:8086/write?db=mydb" \
-d 'cpu,host=server01,region=uswest load=42 1434055562000000000'

curl -XPOST "http://thinfluxdb-data-02:8086/write?db=mydb&consistency=all" \
-d 'cpu,host=server02,region=uswest load=78 1434055562000000000'

curl -XPOST "http://thinfluxdb-data-02:8086/write?db=mydb&consistency=quorum" \
-d 'cpu,host=server03,region=useast load=15.4 1434055562000000000'
```

> **注意**：`consistency=[any,one,quorum,all]` 设置数据点的写入一致性。如果不指定一致性，则 `consistency` 为 `one`。
> 
> any: 一旦任何节点写入成功，或者接收节点已将数据写入其 hinted handoff 队列，就立即向客户端返回成功。
> 
> one: 一旦任何节点写入成功，则立即向客户端返回成功，如果只是写入到 hinted handoff 队列中则不会返回。
> 
> quorum: 当大多数节点（大于等于 (副本因子/2)+1 个节点）返回成功时返回成功。此选项仅在副本因子大于 2 时才有用，否则等效于 all。
> 
> all: 仅当所有节点都返回成功时才返回成功。

### 查询数据

```bash
curl -G "http://thinfluxdb-data-02:8086/query?pretty=true" --data-urlencode "db=mydb" \
--data-urlencode "q=SELECT * FROM cpu WHERE host='server01' AND time < now() - 1d"
```

输出（示例数据）：

```bash
{
    "results": [
        {
            "statement_id": 0,
            "series": [
                {
                    "name": "cpu",
                    "columns": [
                        "time",
                        "host",
                        "load",
                        "region"
                    ],
                    "values": [
                        [
                            "2015-06-11T20:46:02Z",
                            "server01",
                            42,
                            "uswest"
                        ]
                    ]
                }
            ]
        }
    ]
}
```

### 分析数据

```bash
curl -G "http://thinfluxdb-data-02:8086/query?pretty=true" --data-urlencode "db=mydb" \
--data-urlencode "q=SELECT mean(load) FROM cpu WHERE region='uswest'"
```

输出：

```bash
{
    "results": [
        {
            "statement_id": 0,
            "series": [
                {
                    "name": "cpu",
                    "columns": [
                        "time",
                        "mean"
                    ],
                    "values": [
                        [
                            "1970-01-01T00:00:00Z",
                            60
                        ]
                    ]
                }
            ]
        }
    ]
}
```

## 通过 CLI 使用 ThinfluxDB Cluster

### 连接到集群

```bash
# 基本连接
[tdtgi@thinfluxdb-meta-01 ~]$ thinflux -host thinfluxdb-data-01
Connected to http://thinfluxdb-data-01:8086 version 1.8.10-c1.1.2
ThinfluxDB shell version: 1.8.10-c1.1.2
>
```

如果已配置身份验证，可以使用用户名和密码连接：

```bash
[tdtgi@thinfluxdb-meta-01 ~]$ thinflux -host thinfluxdb-data-01 -username admin -password password
```

### 创建 DATABASE

```bash
> CREATE DATABASE thinfluxdb
> SHOW DATABASES
name: databases
name
----
_internal
thinfluxdb
```

### 写入数据

使用INSERT语句直接写入数据：

```bash
> USE thinfluxdb
Using database thinfluxdb
> INSERT cpu,host=server01,region=uswest load=42
> INSERT cpu,host=server02,region=uswest load=78
> INSERT cpu,host=server03,region=useast load=15.4
```

### 查询数据

查询所有数据：

```bash
> SELECT * FROM cpu
name: cpu
time                host     load region
----                ----     ---- ------
1748576058976695255 server01 42   uswest
1748576065561447367 server02 78   uswest
1748576070257167140 server03 15.4 useast
```

设置时间精度：

```bash
> PRECISION rfc3339
> SELECT * FROM cpu
name: cpu
time                           host     load region
----                           ----     ---- ------
2025-05-30T03:34:18.976695255Z server01 42   uswest
2025-05-30T03:34:25.561447367Z server02 78   uswest
2025-05-30T03:34:30.25716714Z  server03 15.4 useast
```

使用条件过滤数据：

```bash
> SELECT * FROM cpu WHERE region='uswest'
name: cpu
time                           host     load region
----                           ----     ---- ------
2025-05-30T03:34:18.976695255Z server01 42   uswest
2025-05-30T03:34:25.561447367Z server02 78   uswest
```

### 分析数据

计算平均负载：

```bash
> SELECT MEAN(load) FROM cpu
name: cpu
time                 mean
----                 ----
1970-01-01T00:00:00Z 45.13333333333333
```

按区域分组计算平均负载：

```bash
> SELECT MEAN(load) FROM cpu GROUP BY region
name: cpu
tags: region=useast
time                 mean
----                 ----
1970-01-01T00:00:00Z 15.4

name: cpu
tags: region=uswest
time                 mean
----                 ----
1970-01-01T00:00:00Z 60
```

### 退出CLI

```bash
> exit
```

## 附：配置

### 配置 Meta 节点

以下是 Meta 节点的主要配置项，按照全局设置和功能模块分组：

**全局设置**：

- `reporting-disabled = false`：每 24 小时将数据报告打印输出到日志中。每个报告包括一个随机生成的标识符、操作系统、架构、ThinfluxDB Cluster 版本等。要禁用报告，请将此选项设置为 `true`。环境变量: `THINFLUXDB_REPORTING_DISABLED`

- `bind-address = ""`：早期版本中 Meta 节点 Raft 通信的绑定地址。此设置已不计划使用，将在未来版本中删除。环境变量: `THINFLUXDB_BIND_ADDRESS`

- `hostname = ""`：Meta 节点的主机名。这必须可被集群的所有其它成员解析和访问。环境变量: `THINFLUXDB_HOSTNAME`

**[meta] 设置**：

- `dir = "/var/lib/thinfluxdb/meta"`：集群元数据的存储目录。Data 节点也需要本地 meta 目录。环境变量: `THINFLUXDB_META_DIR`

- `bind-address = ":8089"`：Meta 节点 Raft 通信的绑定地址。为简单起见，ThinfluxDB Cluster 建议在所有 Meta 节点上使用相同的端口，但这不是必需的。环境变量: `THINFLUXDB_META_BIND_ADDRESS`

- `http-bind-address = ":8091"`：HTTP API 绑定的默认地址。环境变量: `THINFLUXDB_META_HTTP_BIND_ADDRESS`

- `https-enabled = false`：确定 Meta 节点是否使用 HTTPS 相互通信。默认情况下，HTTPS 被禁用。我们强烈建议启用 HTTPS。要启用 HTTPS，请将此选项设置为 `true`，并指定 SSL 证书和私钥的路径。环境变量: `THINFLUXDB_META_HTTPS_ENABLED`

- `https-certificate = ""`：如果启用了 HTTPS，请指定 SSL 证书的路径。可使用带有证书和密钥的 PEM 编码包或仅证书文件。环境变量: `THINFLUXDB_META_HTTPS_CERTIFICATE`

- `https-private-key = ""`：如果启用了 HTTPS，请指定 SSL 私钥的路径。可使用带有证书和密钥的 PEM 编码包或仅证书文件。环境变量: `THINFLUXDB_META_HTTPS_PRIVATE_KEY`

- `https-insecure-tls = false`：Meta 节点是否会跳过通过 HTTPS 相互通信的证书验证。这在使用自签名证书进行测试时很有用。环境变量: `THINFLUXDB_META_HTTPS_INSECURE_TLS`

### 配置 Data 节点

以下是 Data 节点的主要配置项：

**全局设置**：

- `bind-address = ":8088"`：RPC 服务用于节点间通信、备份和恢复的 TCP 绑定地址。环境变量: `THINFLUXDB_BIND_ADDRESS`

- `hostname = "localhost"`：Data 节点的主机名。这必须可被集群的所有其它成员解析和访问。环境变量: `THINFLUXDB_HOSTNAME`

- `gossip-frequency = "3s"`：将此节点的内部状态更新至集群的频率。环境变量: `THINFLUXDB_GOSSIP_FREQUENCY`

**[meta] 设置**：

- `dir = "/var/lib/thinfluxdb/meta"`：集群元数据的存储目录。Data 节点也需要本地 meta 目录。环境变量: `THINFLUXDB_META_DIR`

- `meta-tls-enabled = false`：连接到 Meta 节点时是否使用 TLS。如果 Meta 节点的 https-enabled 设置为 true，则将此选项设置为 true。环境变量: `THINFLUXDB_META_META_TLS_ENABLED`

**[data] 设置**：

- `dir = "/var/lib/thinfluxdb/data"`：TSM 存储引擎存储 TSM（读优化）文件的目录。环境变量: `THINFLUXDB_DATA_DIR`

- `wal-dir = "/var/lib/thinfluxdb/wal"`：TSM 存储引擎存储 WAL（写优化）文件的目录。环境变量: `THINFLUXDB_DATA_WAL_DIR`

**[http] 设置**：

- `bind-address = ":8086"`：HTTP API 绑定的默认地址。这是客户端连接进行查询和写入的端口。环境变量: `THINFLUXDB_HTTP_BIND_ADDRESS`

**[hinted-handoff] 设置**：

- `dir = "/var/lib/thinfluxdb/hh"`：存储 hinted handoff 数据的目录。环境变量: `THINFLUXDB_HINTED_HANDOFF_DIR`

### 使用配置文件

显示默认配置：

```bash
# Meta 节点
thinfluxd-meta config

# Data 节点
thinfluxd config
```

### 创建默认配置文件

```bash
# Meta 节点
thinfluxd-meta config > /etc/thinfluxdb/thinfluxdb-meta-generated.conf

# Data 节点
thinfluxd config > /etc/thinfluxdb/thinfluxdb-generated.conf
```

### 使用配置文件启动进程

```bash
# Meta 节点
thinfluxd-meta -config /etc/thinfluxdb/thinfluxdb-meta-generated.conf

# Data 节点
thinfluxd -config /etc/thinfluxdb/thinfluxdb-generated.conf
```

或通过环境变量：

```bash
# Meta 节点
export THINFLUXDB_META_CONFIG_PATH=/etc/thinfluxdb/thinfluxdb-meta-generated.conf
thinfluxd-meta

# Data 节点
export THINFLUXDB_CONFIG_PATH=/etc/thinfluxdb/thinfluxdb-generated.conf
thinfluxd
```

## 使用 Chronograf 可视化管理 ThinfluxDB Cluster

### 环境介绍

部署以下 1 Meta 节点和 1 Data 节点来演示使用 Chronograf 可视化管理 ThinfluxDB Cluster：

| IP地址          | 主机名              | 角色     |
|----------------|--------------------|----------|
| ************** | thinfluxdb-meta-01 | Meta节点 |
| ************** | thinfluxdb-data-01 | Data节点 |

> 注意：此处使用简化的环境（1 Meta 节点和 1 Data 节点）进行 Chronograf 演示，与前面的完整集群部署示例（3 Meta 节点和 2 Data 节点）不同。在实际生产环境中，您可以使用完整集群配置并在任一节点上部署 Chronograf。

### 下载安装 Chronograf 并启动

```bash
wget https://dl.influxdata.com/chronograf/releases/chronograf-1.10.7_linux_amd64.tar.gz
tar -xzf chronograf-1.10.7_linux_amd64.tar.gz
cd chronograf-1.10.7-1/usr/bin/
./chronograf
```

### 访问 Web 界面

```bash
# 本地访问
http://localhost:8888/

# 远程访问（假设 Chronograf 运行在 ************** 上）
http://**************:8888/
```

> 注意：如果需要从其他机器访问 Chronograf，请将 localhost 替换为运行 Chronograf 的服务器 IP 地址，并确保防火墙允许 8888 端口的访问。

### 添加 ThinfluxDB Cluster 配置

1. 点击 `Get Started`

![Get Started](./images/Getstart.png)

2. 添加 ThinfluxDB Cluster 配置

![Add ThinfluxDB Cluster](./images/ThinfluxDB-Cluster-configuration.png)

3. 选择 Dashboard

![Select Dashboard](./images/Dashboards.png)

4. 配置 Kapacitor (可选)

![Configure Kapacitor](./images/Kapacitor.png)

5. 完成配置

![Complete Configuration](./images/Setup-Complete.png)

### 简单操作演示

1. 创建数据库

![Create Database](./images/Create-Database.png)

2. 显示数据库

![Show Databases](./images/show-databases.png)

3. 查看 Admin 界面

![Admin](./images/admin.png)

> 注意：上述图片路径为相对路径，请确保images目录存在且包含相应的图片文件。
