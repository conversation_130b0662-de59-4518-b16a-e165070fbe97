package stressClient

import (
	"testing"
)

func TestNewResponse(t *testing.T) {
	pt := NewBlankTestPoint()
	tr := NewTracer(map[string]string{})
	r := NewResponse(pt, tr)
	expected := "another_tag_value"
	test, err := r.Add<PERSON>(map[string]string{"another_tag": "another_tag_value"})
	if err != nil {
		t.Fat<PERSON>(err)
	}
	got := test.Tags()["another_tag"]
	if expected != got {
		t.<PERSON>rrorf("expected: %v\ngot: %v\n", expected, got)
	}
}
