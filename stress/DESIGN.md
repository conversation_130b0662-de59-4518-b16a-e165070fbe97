## Stress Test
The logic for `StressTest` can be found in `stress/run.go`.

A new `StressTest` type was added and is composed four different parts. The `StressTest` type has one method `Start(wHandle responseHandler, rHandle responseHandler)`. This method starts the stress test.

A `responseHandler` is a function with type signature `func(r <-chan response, t *Timer)`. Response Handlers handle the read and write responses respectively.

### Provisioner
Provisions the InfluxDB instance where the stress test is going to be ran against.

Think things like, creating the database, setting up retention policies, continuous queries, etc.

### Writer
The `Writer` is responsible for Writing data into an InfluxDB instance. It has two components: `PointGenerator` and `InfluxClient`.

##### PointGenerator
The `PointGenerator` is responsible for generating points that will be written into InfluxDB. Additionally, it is reponsible for keeping track of the latest timestamp of the points it is writing (Just incase the its needed by the `Reader`).

Any type that implements the methods `Generate()` and `Time()` is a `PointGenerator`.

##### InfluxClient
The `InfluxClient` is responsible for writing the data that is generated by the `PointGenerator`.

Any type that implements `Batch(ps <-chan Point, r chan<- response)`, and `send(b []byte) response` is an `InfluxClient`.

### Reader
The `Reader` is responsible for querying the database. It has two components: `QueryGenerator` and `QueryClient`.

##### QueryGenerator
The `QueryGenerator` is responsible for generating queries.

##### QueryClient
The `QueryClient` is responsible for executing queries against an InfluxDB instance.

## Basic
`basic.go` implements an each of the components of a stress test.

## Util
`util.go` contains utility methods used throughout the package.

## Config
`config.go` contains the logic for managing the configuration of the stress test.

A sample configuration file can be found in `stress/stress.toml`. This still needs work, but whats there now is good enough IMO.

## Template
`template.go` contains the logic for a basic stress test.
