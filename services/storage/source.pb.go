// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: source.proto

package storage

import (
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ReadSource struct {
	// Database identifies which database to query.
	Database string `protobuf:"bytes,1,opt,name=database,proto3" json:"database,omitempty"`
	// RetentionPolicy identifies which retention policy to query.
	RetentionPolicy string `protobuf:"bytes,2,opt,name=retention_policy,json=retentionPolicy,proto3" json:"retention_policy,omitempty"`
}

func (m *ReadSource) Reset()         { *m = ReadSource{} }
func (m *ReadSource) String() string { return proto.CompactTextString(m) }
func (*ReadSource) ProtoMessage()    {}
func (*ReadSource) Descriptor() ([]byte, []int) {
	return fileDescriptor_9229c9b6eb8e6b85, []int{0}
}
func (m *ReadSource) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadSource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadSource.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadSource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadSource.Merge(m, src)
}
func (m *ReadSource) XXX_Size() int {
	return m.Size()
}
func (m *ReadSource) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadSource.DiscardUnknown(m)
}

var xxx_messageInfo_ReadSource proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ReadSource)(nil), "com.github.influxdata.influxdb.services.storage.ReadSource")
}

func init() { proto.RegisterFile("source.proto", fileDescriptor_9229c9b6eb8e6b85) }

var fileDescriptor_9229c9b6eb8e6b85 = []byte{
	// 206 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x29, 0xce, 0x2f, 0x2d,
	0x4a, 0x4e, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xd2, 0x4f, 0xce, 0xcf, 0xd5, 0x4b, 0xcf,
	0x2c, 0xc9, 0x28, 0x4d, 0xd2, 0xcb, 0xcc, 0x4b, 0xcb, 0x29, 0xad, 0x48, 0x49, 0x2c, 0x49, 0x84,
	0x31, 0x93, 0xf4, 0x8a, 0x53, 0x8b, 0xca, 0x32, 0x93, 0x53, 0x8b, 0xf5, 0x8a, 0x4b, 0xf2, 0x8b,
	0x12, 0xd3, 0x53, 0xa5, 0x44, 0xd2, 0xf3, 0xd3, 0xf3, 0xc1, 0x7a, 0xf5, 0x41, 0x2c, 0x88, 0x31,
	0x4a, 0x19, 0x5c, 0x5c, 0x41, 0xa9, 0x89, 0x29, 0xc1, 0x60, 0xa3, 0x85, 0xa4, 0xb8, 0x38, 0x40,
	0xa6, 0x24, 0x25, 0x16, 0xa7, 0x4a, 0x30, 0x2a, 0x30, 0x6a, 0x70, 0x06, 0xc1, 0xf9, 0x42, 0x76,
	0x5c, 0x02, 0x45, 0xa9, 0x25, 0xa9, 0x79, 0x25, 0x99, 0xf9, 0x79, 0xf1, 0x05, 0xf9, 0x39, 0x99,
	0xc9, 0x95, 0x12, 0x4c, 0x20, 0x35, 0x4e, 0xc2, 0x8f, 0xee, 0xc9, 0xf3, 0x07, 0xc1, 0xe4, 0x02,
	0xc0, 0x52, 0x41, 0xfc, 0x45, 0xa8, 0x02, 0x4e, 0xaa, 0x27, 0x1e, 0xca, 0x31, 0x9c, 0x78, 0x24,
	0xc7, 0x78, 0xe1, 0x91, 0x1c, 0xe3, 0x83, 0x47, 0x72, 0x8c, 0x13, 0x1e, 0xcb, 0x31, 0x5c, 0x78,
	0x2c, 0xc7, 0x70, 0xe3, 0xb1, 0x1c, 0x43, 0x14, 0x3b, 0xd4, 0x99, 0x49, 0x6c, 0x60, 0x77, 0x19,
	0x03, 0x02, 0x00, 0x00, 0xff, 0xff, 0xb8, 0xba, 0x4e, 0xe6, 0xee, 0x00, 0x00, 0x00,
}

func (m *ReadSource) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadSource) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadSource) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.RetentionPolicy) > 0 {
		i -= len(m.RetentionPolicy)
		copy(dAtA[i:], m.RetentionPolicy)
		i = encodeVarintSource(dAtA, i, uint64(len(m.RetentionPolicy)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Database) > 0 {
		i -= len(m.Database)
		copy(dAtA[i:], m.Database)
		i = encodeVarintSource(dAtA, i, uint64(len(m.Database)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintSource(dAtA []byte, offset int, v uint64) int {
	offset -= sovSource(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReadSource) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Database)
	if l > 0 {
		n += 1 + l + sovSource(uint64(l))
	}
	l = len(m.RetentionPolicy)
	if l > 0 {
		n += 1 + l + sovSource(uint64(l))
	}
	return n
}

func sovSource(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozSource(x uint64) (n int) {
	return sovSource(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReadSource) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSource
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadSource: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadSource: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Database", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSource
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSource
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Database = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RetentionPolicy", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSource
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSource
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSource
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RetentionPolicy = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSource(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthSource
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSource(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSource
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSource
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSource
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthSource
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupSource
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthSource
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthSource        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSource          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupSource = fmt.Errorf("proto: unexpected end of group")
)
