syntax = "proto3";
package com.github.influxdata.influxdb.services.storage;
option go_package = "storage";

import "gogoproto/gogo.proto";

option (gogoproto.marshaler_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
option (gogoproto.goproto_getters_all) = false;

message ReadSource {
  // Database identifies which database to query.
  string database = 1;

  // RetentionPolicy identifies which retention policy to query.
  string retention_policy = 2 [(gogoproto.customname) = "RetentionPolicy"];
}
