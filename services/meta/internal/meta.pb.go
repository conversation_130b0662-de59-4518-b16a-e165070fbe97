// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: internal/meta.proto

package meta

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Command_Type int32

const (
	Command_CreateNodeCommand                Command_Type = 1
	Command_DeleteNodeCommand                Command_Type = 2
	Command_CreateDatabaseCommand            Command_Type = 3
	Command_DropDatabaseCommand              Command_Type = 4
	Command_CreateRetentionPolicyCommand     Command_Type = 5
	Command_DropRetentionPolicyCommand       Command_Type = 6
	Command_SetDefaultRetentionPolicyCommand Command_Type = 7
	Command_UpdateRetentionPolicyCommand     Command_Type = 8
	Command_CreateShardGroupCommand          Command_Type = 9
	Command_DeleteShardGroupCommand          Command_Type = 10
	Command_CreateContinuousQueryCommand     Command_Type = 11
	Command_DropContinuousQueryCommand       Command_Type = 12
	Command_CreateUserCommand                Command_Type = 13
	Command_DropUserCommand                  Command_Type = 14
	Command_UpdateUserCommand                Command_Type = 15
	Command_SetPrivilegeCommand              Command_Type = 16
	Command_SetDataCommand                   Command_Type = 17
	Command_SetAdminPrivilegeCommand         Command_Type = 18
	Command_UpdateNodeCommand                Command_Type = 19
	Command_CreateSubscriptionCommand        Command_Type = 21
	Command_DropSubscriptionCommand          Command_Type = 22
	Command_RemovePeerCommand                Command_Type = 23
	Command_CreateMetaNodeCommand            Command_Type = 24
	Command_CreateDataNodeCommand            Command_Type = 25
	Command_UpdateDataNodeCommand            Command_Type = 26
	Command_DeleteMetaNodeCommand            Command_Type = 27
	Command_DeleteDataNodeCommand            Command_Type = 28
	Command_SetMetaNodeCommand               Command_Type = 29
	Command_DropShardCommand                 Command_Type = 30
	Command_TruncateShardGroupsCommand       Command_Type = 31
	Command_PruneShardGroupsCommand          Command_Type = 32
	Command_CopyShardOwnerCommand            Command_Type = 33
	Command_RemoveShardOwnerCommand          Command_Type = 34
)

var Command_Type_name = map[int32]string{
	1:  "CreateNodeCommand",
	2:  "DeleteNodeCommand",
	3:  "CreateDatabaseCommand",
	4:  "DropDatabaseCommand",
	5:  "CreateRetentionPolicyCommand",
	6:  "DropRetentionPolicyCommand",
	7:  "SetDefaultRetentionPolicyCommand",
	8:  "UpdateRetentionPolicyCommand",
	9:  "CreateShardGroupCommand",
	10: "DeleteShardGroupCommand",
	11: "CreateContinuousQueryCommand",
	12: "DropContinuousQueryCommand",
	13: "CreateUserCommand",
	14: "DropUserCommand",
	15: "UpdateUserCommand",
	16: "SetPrivilegeCommand",
	17: "SetDataCommand",
	18: "SetAdminPrivilegeCommand",
	19: "UpdateNodeCommand",
	21: "CreateSubscriptionCommand",
	22: "DropSubscriptionCommand",
	23: "RemovePeerCommand",
	24: "CreateMetaNodeCommand",
	25: "CreateDataNodeCommand",
	26: "UpdateDataNodeCommand",
	27: "DeleteMetaNodeCommand",
	28: "DeleteDataNodeCommand",
	29: "SetMetaNodeCommand",
	30: "DropShardCommand",
	31: "TruncateShardGroupsCommand",
	32: "PruneShardGroupsCommand",
	33: "CopyShardOwnerCommand",
	34: "RemoveShardOwnerCommand",
}

var Command_Type_value = map[string]int32{
	"CreateNodeCommand":                1,
	"DeleteNodeCommand":                2,
	"CreateDatabaseCommand":            3,
	"DropDatabaseCommand":              4,
	"CreateRetentionPolicyCommand":     5,
	"DropRetentionPolicyCommand":       6,
	"SetDefaultRetentionPolicyCommand": 7,
	"UpdateRetentionPolicyCommand":     8,
	"CreateShardGroupCommand":          9,
	"DeleteShardGroupCommand":          10,
	"CreateContinuousQueryCommand":     11,
	"DropContinuousQueryCommand":       12,
	"CreateUserCommand":                13,
	"DropUserCommand":                  14,
	"UpdateUserCommand":                15,
	"SetPrivilegeCommand":              16,
	"SetDataCommand":                   17,
	"SetAdminPrivilegeCommand":         18,
	"UpdateNodeCommand":                19,
	"CreateSubscriptionCommand":        21,
	"DropSubscriptionCommand":          22,
	"RemovePeerCommand":                23,
	"CreateMetaNodeCommand":            24,
	"CreateDataNodeCommand":            25,
	"UpdateDataNodeCommand":            26,
	"DeleteMetaNodeCommand":            27,
	"DeleteDataNodeCommand":            28,
	"SetMetaNodeCommand":               29,
	"DropShardCommand":                 30,
	"TruncateShardGroupsCommand":       31,
	"PruneShardGroupsCommand":          32,
	"CopyShardOwnerCommand":            33,
	"RemoveShardOwnerCommand":          34,
}

func (x Command_Type) Enum() *Command_Type {
	p := new(Command_Type)
	*p = x
	return p
}

func (x Command_Type) String() string {
	return proto.EnumName(Command_Type_name, int32(x))
}

func (x *Command_Type) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Command_Type_value, data, "Command_Type")
	if err != nil {
		return err
	}
	*x = Command_Type(value)
	return nil
}

func (Command_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{12, 0}
}

type Data struct {
	Term            *uint64         `protobuf:"varint,1,req,name=Term" json:"Term,omitempty"`
	Index           *uint64         `protobuf:"varint,2,req,name=Index" json:"Index,omitempty"`
	ClusterID       *uint64         `protobuf:"varint,3,req,name=ClusterID" json:"ClusterID,omitempty"`
	Nodes           []*NodeInfo     `protobuf:"bytes,4,rep,name=Nodes" json:"Nodes,omitempty"`
	Databases       []*DatabaseInfo `protobuf:"bytes,5,rep,name=Databases" json:"Databases,omitempty"`
	Users           []*UserInfo     `protobuf:"bytes,6,rep,name=Users" json:"Users,omitempty"`
	MaxNodeID       *uint64         `protobuf:"varint,7,req,name=MaxNodeID" json:"MaxNodeID,omitempty"`
	MaxShardGroupID *uint64         `protobuf:"varint,8,req,name=MaxShardGroupID" json:"MaxShardGroupID,omitempty"`
	MaxShardID      *uint64         `protobuf:"varint,9,req,name=MaxShardID" json:"MaxShardID,omitempty"`
	// added for 0.10.0
	DataNodes            []*NodeInfo `protobuf:"bytes,10,rep,name=DataNodes" json:"DataNodes,omitempty"`
	MetaNodes            []*NodeInfo `protobuf:"bytes,11,rep,name=MetaNodes" json:"MetaNodes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Data) Reset()         { *m = Data{} }
func (m *Data) String() string { return proto.CompactTextString(m) }
func (*Data) ProtoMessage()    {}
func (*Data) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{0}
}
func (m *Data) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Data.Unmarshal(m, b)
}
func (m *Data) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Data.Marshal(b, m, deterministic)
}
func (m *Data) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Data.Merge(m, src)
}
func (m *Data) XXX_Size() int {
	return xxx_messageInfo_Data.Size(m)
}
func (m *Data) XXX_DiscardUnknown() {
	xxx_messageInfo_Data.DiscardUnknown(m)
}

var xxx_messageInfo_Data proto.InternalMessageInfo

func (m *Data) GetTerm() uint64 {
	if m != nil && m.Term != nil {
		return *m.Term
	}
	return 0
}

func (m *Data) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

func (m *Data) GetClusterID() uint64 {
	if m != nil && m.ClusterID != nil {
		return *m.ClusterID
	}
	return 0
}

func (m *Data) GetNodes() []*NodeInfo {
	if m != nil {
		return m.Nodes
	}
	return nil
}

func (m *Data) GetDatabases() []*DatabaseInfo {
	if m != nil {
		return m.Databases
	}
	return nil
}

func (m *Data) GetUsers() []*UserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *Data) GetMaxNodeID() uint64 {
	if m != nil && m.MaxNodeID != nil {
		return *m.MaxNodeID
	}
	return 0
}

func (m *Data) GetMaxShardGroupID() uint64 {
	if m != nil && m.MaxShardGroupID != nil {
		return *m.MaxShardGroupID
	}
	return 0
}

func (m *Data) GetMaxShardID() uint64 {
	if m != nil && m.MaxShardID != nil {
		return *m.MaxShardID
	}
	return 0
}

func (m *Data) GetDataNodes() []*NodeInfo {
	if m != nil {
		return m.DataNodes
	}
	return nil
}

func (m *Data) GetMetaNodes() []*NodeInfo {
	if m != nil {
		return m.MetaNodes
	}
	return nil
}

type NodeInfo struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Addr                 *string  `protobuf:"bytes,2,opt,name=Addr" json:"Addr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,3,opt,name=TCPAddr" json:"TCPAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NodeInfo) Reset()         { *m = NodeInfo{} }
func (m *NodeInfo) String() string { return proto.CompactTextString(m) }
func (*NodeInfo) ProtoMessage()    {}
func (*NodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{1}
}
func (m *NodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NodeInfo.Unmarshal(m, b)
}
func (m *NodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NodeInfo.Marshal(b, m, deterministic)
}
func (m *NodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NodeInfo.Merge(m, src)
}
func (m *NodeInfo) XXX_Size() int {
	return xxx_messageInfo_NodeInfo.Size(m)
}
func (m *NodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NodeInfo proto.InternalMessageInfo

func (m *NodeInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *NodeInfo) GetAddr() string {
	if m != nil && m.Addr != nil {
		return *m.Addr
	}
	return ""
}

func (m *NodeInfo) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

type DatabaseInfo struct {
	Name                   *string                `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	DefaultRetentionPolicy *string                `protobuf:"bytes,2,req,name=DefaultRetentionPolicy" json:"DefaultRetentionPolicy,omitempty"`
	RetentionPolicies      []*RetentionPolicyInfo `protobuf:"bytes,3,rep,name=RetentionPolicies" json:"RetentionPolicies,omitempty"`
	ContinuousQueries      []*ContinuousQueryInfo `protobuf:"bytes,4,rep,name=ContinuousQueries" json:"ContinuousQueries,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}               `json:"-"`
	XXX_unrecognized       []byte                 `json:"-"`
	XXX_sizecache          int32                  `json:"-"`
}

func (m *DatabaseInfo) Reset()         { *m = DatabaseInfo{} }
func (m *DatabaseInfo) String() string { return proto.CompactTextString(m) }
func (*DatabaseInfo) ProtoMessage()    {}
func (*DatabaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{2}
}
func (m *DatabaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DatabaseInfo.Unmarshal(m, b)
}
func (m *DatabaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DatabaseInfo.Marshal(b, m, deterministic)
}
func (m *DatabaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DatabaseInfo.Merge(m, src)
}
func (m *DatabaseInfo) XXX_Size() int {
	return xxx_messageInfo_DatabaseInfo.Size(m)
}
func (m *DatabaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DatabaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DatabaseInfo proto.InternalMessageInfo

func (m *DatabaseInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DatabaseInfo) GetDefaultRetentionPolicy() string {
	if m != nil && m.DefaultRetentionPolicy != nil {
		return *m.DefaultRetentionPolicy
	}
	return ""
}

func (m *DatabaseInfo) GetRetentionPolicies() []*RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicies
	}
	return nil
}

func (m *DatabaseInfo) GetContinuousQueries() []*ContinuousQueryInfo {
	if m != nil {
		return m.ContinuousQueries
	}
	return nil
}

type RetentionPolicySpec struct {
	Name                 *string  `protobuf:"bytes,1,opt,name=Name" json:"Name,omitempty"`
	Duration             *int64   `protobuf:"varint,2,opt,name=Duration" json:"Duration,omitempty"`
	ShardGroupDuration   *int64   `protobuf:"varint,3,opt,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	ReplicaN             *uint32  `protobuf:"varint,4,opt,name=ReplicaN" json:"ReplicaN,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RetentionPolicySpec) Reset()         { *m = RetentionPolicySpec{} }
func (m *RetentionPolicySpec) String() string { return proto.CompactTextString(m) }
func (*RetentionPolicySpec) ProtoMessage()    {}
func (*RetentionPolicySpec) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{3}
}
func (m *RetentionPolicySpec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetentionPolicySpec.Unmarshal(m, b)
}
func (m *RetentionPolicySpec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetentionPolicySpec.Marshal(b, m, deterministic)
}
func (m *RetentionPolicySpec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetentionPolicySpec.Merge(m, src)
}
func (m *RetentionPolicySpec) XXX_Size() int {
	return xxx_messageInfo_RetentionPolicySpec.Size(m)
}
func (m *RetentionPolicySpec) XXX_DiscardUnknown() {
	xxx_messageInfo_RetentionPolicySpec.DiscardUnknown(m)
}

var xxx_messageInfo_RetentionPolicySpec proto.InternalMessageInfo

func (m *RetentionPolicySpec) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *RetentionPolicySpec) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *RetentionPolicySpec) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *RetentionPolicySpec) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

type RetentionPolicyInfo struct {
	Name                 *string             `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Duration             *int64              `protobuf:"varint,2,req,name=Duration" json:"Duration,omitempty"`
	ShardGroupDuration   *int64              `protobuf:"varint,3,req,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	ReplicaN             *uint32             `protobuf:"varint,4,req,name=ReplicaN" json:"ReplicaN,omitempty"`
	ShardGroups          []*ShardGroupInfo   `protobuf:"bytes,5,rep,name=ShardGroups" json:"ShardGroups,omitempty"`
	Subscriptions        []*SubscriptionInfo `protobuf:"bytes,6,rep,name=Subscriptions" json:"Subscriptions,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RetentionPolicyInfo) Reset()         { *m = RetentionPolicyInfo{} }
func (m *RetentionPolicyInfo) String() string { return proto.CompactTextString(m) }
func (*RetentionPolicyInfo) ProtoMessage()    {}
func (*RetentionPolicyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{4}
}
func (m *RetentionPolicyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RetentionPolicyInfo.Unmarshal(m, b)
}
func (m *RetentionPolicyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RetentionPolicyInfo.Marshal(b, m, deterministic)
}
func (m *RetentionPolicyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RetentionPolicyInfo.Merge(m, src)
}
func (m *RetentionPolicyInfo) XXX_Size() int {
	return xxx_messageInfo_RetentionPolicyInfo.Size(m)
}
func (m *RetentionPolicyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RetentionPolicyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RetentionPolicyInfo proto.InternalMessageInfo

func (m *RetentionPolicyInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *RetentionPolicyInfo) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *RetentionPolicyInfo) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *RetentionPolicyInfo) GetShardGroups() []*ShardGroupInfo {
	if m != nil {
		return m.ShardGroups
	}
	return nil
}

func (m *RetentionPolicyInfo) GetSubscriptions() []*SubscriptionInfo {
	if m != nil {
		return m.Subscriptions
	}
	return nil
}

type ShardGroupInfo struct {
	ID                   *uint64      `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	StartTime            *int64       `protobuf:"varint,2,req,name=StartTime" json:"StartTime,omitempty"`
	EndTime              *int64       `protobuf:"varint,3,req,name=EndTime" json:"EndTime,omitempty"`
	DeletedAt            *int64       `protobuf:"varint,4,req,name=DeletedAt" json:"DeletedAt,omitempty"`
	Shards               []*ShardInfo `protobuf:"bytes,5,rep,name=Shards" json:"Shards,omitempty"`
	TruncatedAt          *int64       `protobuf:"varint,6,opt,name=TruncatedAt" json:"TruncatedAt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ShardGroupInfo) Reset()         { *m = ShardGroupInfo{} }
func (m *ShardGroupInfo) String() string { return proto.CompactTextString(m) }
func (*ShardGroupInfo) ProtoMessage()    {}
func (*ShardGroupInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{5}
}
func (m *ShardGroupInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardGroupInfo.Unmarshal(m, b)
}
func (m *ShardGroupInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardGroupInfo.Marshal(b, m, deterministic)
}
func (m *ShardGroupInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardGroupInfo.Merge(m, src)
}
func (m *ShardGroupInfo) XXX_Size() int {
	return xxx_messageInfo_ShardGroupInfo.Size(m)
}
func (m *ShardGroupInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardGroupInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardGroupInfo proto.InternalMessageInfo

func (m *ShardGroupInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *ShardGroupInfo) GetStartTime() int64 {
	if m != nil && m.StartTime != nil {
		return *m.StartTime
	}
	return 0
}

func (m *ShardGroupInfo) GetEndTime() int64 {
	if m != nil && m.EndTime != nil {
		return *m.EndTime
	}
	return 0
}

func (m *ShardGroupInfo) GetDeletedAt() int64 {
	if m != nil && m.DeletedAt != nil {
		return *m.DeletedAt
	}
	return 0
}

func (m *ShardGroupInfo) GetShards() []*ShardInfo {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *ShardGroupInfo) GetTruncatedAt() int64 {
	if m != nil && m.TruncatedAt != nil {
		return *m.TruncatedAt
	}
	return 0
}

type ShardInfo struct {
	ID                   *uint64       `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	OwnerIDs             []uint64      `protobuf:"varint,2,rep,name=OwnerIDs" json:"OwnerIDs,omitempty"` // Deprecated: Do not use.
	Owners               []*ShardOwner `protobuf:"bytes,3,rep,name=Owners" json:"Owners,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ShardInfo) Reset()         { *m = ShardInfo{} }
func (m *ShardInfo) String() string { return proto.CompactTextString(m) }
func (*ShardInfo) ProtoMessage()    {}
func (*ShardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{6}
}
func (m *ShardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardInfo.Unmarshal(m, b)
}
func (m *ShardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardInfo.Marshal(b, m, deterministic)
}
func (m *ShardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardInfo.Merge(m, src)
}
func (m *ShardInfo) XXX_Size() int {
	return xxx_messageInfo_ShardInfo.Size(m)
}
func (m *ShardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ShardInfo proto.InternalMessageInfo

func (m *ShardInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

// Deprecated: Do not use.
func (m *ShardInfo) GetOwnerIDs() []uint64 {
	if m != nil {
		return m.OwnerIDs
	}
	return nil
}

func (m *ShardInfo) GetOwners() []*ShardOwner {
	if m != nil {
		return m.Owners
	}
	return nil
}

type SubscriptionInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Mode                 *string  `protobuf:"bytes,2,req,name=Mode" json:"Mode,omitempty"`
	Destinations         []string `protobuf:"bytes,3,rep,name=Destinations" json:"Destinations,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubscriptionInfo) Reset()         { *m = SubscriptionInfo{} }
func (m *SubscriptionInfo) String() string { return proto.CompactTextString(m) }
func (*SubscriptionInfo) ProtoMessage()    {}
func (*SubscriptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{7}
}
func (m *SubscriptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubscriptionInfo.Unmarshal(m, b)
}
func (m *SubscriptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubscriptionInfo.Marshal(b, m, deterministic)
}
func (m *SubscriptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubscriptionInfo.Merge(m, src)
}
func (m *SubscriptionInfo) XXX_Size() int {
	return xxx_messageInfo_SubscriptionInfo.Size(m)
}
func (m *SubscriptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SubscriptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SubscriptionInfo proto.InternalMessageInfo

func (m *SubscriptionInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *SubscriptionInfo) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *SubscriptionInfo) GetDestinations() []string {
	if m != nil {
		return m.Destinations
	}
	return nil
}

type ShardOwner struct {
	NodeID               *uint64  `protobuf:"varint,1,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShardOwner) Reset()         { *m = ShardOwner{} }
func (m *ShardOwner) String() string { return proto.CompactTextString(m) }
func (*ShardOwner) ProtoMessage()    {}
func (*ShardOwner) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{8}
}
func (m *ShardOwner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShardOwner.Unmarshal(m, b)
}
func (m *ShardOwner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShardOwner.Marshal(b, m, deterministic)
}
func (m *ShardOwner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShardOwner.Merge(m, src)
}
func (m *ShardOwner) XXX_Size() int {
	return xxx_messageInfo_ShardOwner.Size(m)
}
func (m *ShardOwner) XXX_DiscardUnknown() {
	xxx_messageInfo_ShardOwner.DiscardUnknown(m)
}

var xxx_messageInfo_ShardOwner proto.InternalMessageInfo

func (m *ShardOwner) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

type ContinuousQueryInfo struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Query                *string  `protobuf:"bytes,2,req,name=Query" json:"Query,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContinuousQueryInfo) Reset()         { *m = ContinuousQueryInfo{} }
func (m *ContinuousQueryInfo) String() string { return proto.CompactTextString(m) }
func (*ContinuousQueryInfo) ProtoMessage()    {}
func (*ContinuousQueryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{9}
}
func (m *ContinuousQueryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinuousQueryInfo.Unmarshal(m, b)
}
func (m *ContinuousQueryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinuousQueryInfo.Marshal(b, m, deterministic)
}
func (m *ContinuousQueryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinuousQueryInfo.Merge(m, src)
}
func (m *ContinuousQueryInfo) XXX_Size() int {
	return xxx_messageInfo_ContinuousQueryInfo.Size(m)
}
func (m *ContinuousQueryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinuousQueryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ContinuousQueryInfo proto.InternalMessageInfo

func (m *ContinuousQueryInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *ContinuousQueryInfo) GetQuery() string {
	if m != nil && m.Query != nil {
		return *m.Query
	}
	return ""
}

type UserInfo struct {
	Name                 *string          `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string          `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	Admin                *bool            `protobuf:"varint,3,req,name=Admin" json:"Admin,omitempty"`
	Privileges           []*UserPrivilege `protobuf:"bytes,4,rep,name=Privileges" json:"Privileges,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{10}
}
func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (m *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(m, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UserInfo) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

func (m *UserInfo) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

func (m *UserInfo) GetPrivileges() []*UserPrivilege {
	if m != nil {
		return m.Privileges
	}
	return nil
}

type UserPrivilege struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Privilege            *int32   `protobuf:"varint,2,req,name=Privilege" json:"Privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{11}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (m *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(m, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UserPrivilege) GetPrivilege() int32 {
	if m != nil && m.Privilege != nil {
		return *m.Privilege
	}
	return 0
}

type Command struct {
	Type                         *Command_Type `protobuf:"varint,1,req,name=type,enum=meta.Command_Type" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral         struct{}      `json:"-"`
	proto.XXX_InternalExtensions `json:"-"`
	XXX_unrecognized             []byte `json:"-"`
	XXX_sizecache                int32  `json:"-"`
}

func (m *Command) Reset()         { *m = Command{} }
func (m *Command) String() string { return proto.CompactTextString(m) }
func (*Command) ProtoMessage()    {}
func (*Command) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{12}
}

var extRange_Command = []proto.ExtensionRange{
	{Start: 100, End: 536870911},
}

func (*Command) ExtensionRangeArray() []proto.ExtensionRange {
	return extRange_Command
}

func (m *Command) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Command.Unmarshal(m, b)
}
func (m *Command) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Command.Marshal(b, m, deterministic)
}
func (m *Command) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Command.Merge(m, src)
}
func (m *Command) XXX_Size() int {
	return xxx_messageInfo_Command.Size(m)
}
func (m *Command) XXX_DiscardUnknown() {
	xxx_messageInfo_Command.DiscardUnknown(m)
}

var xxx_messageInfo_Command proto.InternalMessageInfo

func (m *Command) GetType() Command_Type {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return Command_CreateNodeCommand
}

// This isn't used in >= 0.10.0. Kept around for upgrade purposes. Instead
// look at CreateDataNodeCommand and CreateMetaNodeCommand
type CreateNodeCommand struct {
	Host                 *string  `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	Rand                 *uint64  `protobuf:"varint,2,req,name=Rand" json:"Rand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateNodeCommand) Reset()         { *m = CreateNodeCommand{} }
func (m *CreateNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateNodeCommand) ProtoMessage()    {}
func (*CreateNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{13}
}
func (m *CreateNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateNodeCommand.Unmarshal(m, b)
}
func (m *CreateNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateNodeCommand.Merge(m, src)
}
func (m *CreateNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateNodeCommand.Size(m)
}
func (m *CreateNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateNodeCommand proto.InternalMessageInfo

func (m *CreateNodeCommand) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

func (m *CreateNodeCommand) GetRand() uint64 {
	if m != nil && m.Rand != nil {
		return *m.Rand
	}
	return 0
}

var E_CreateNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateNodeCommand)(nil),
	Field:         101,
	Name:          "meta.CreateNodeCommand.command",
	Tag:           "bytes,101,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DeleteNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Force                *bool    `protobuf:"varint,2,req,name=Force" json:"Force,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteNodeCommand) Reset()         { *m = DeleteNodeCommand{} }
func (m *DeleteNodeCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteNodeCommand) ProtoMessage()    {}
func (*DeleteNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{14}
}
func (m *DeleteNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteNodeCommand.Unmarshal(m, b)
}
func (m *DeleteNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteNodeCommand.Marshal(b, m, deterministic)
}
func (m *DeleteNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteNodeCommand.Merge(m, src)
}
func (m *DeleteNodeCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteNodeCommand.Size(m)
}
func (m *DeleteNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteNodeCommand proto.InternalMessageInfo

func (m *DeleteNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *DeleteNodeCommand) GetForce() bool {
	if m != nil && m.Force != nil {
		return *m.Force
	}
	return false
}

var E_DeleteNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteNodeCommand)(nil),
	Field:         102,
	Name:          "meta.DeleteNodeCommand.command",
	Tag:           "bytes,102,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateDatabaseCommand struct {
	Name                 *string              `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	RetentionPolicy      *RetentionPolicyInfo `protobuf:"bytes,2,opt,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CreateDatabaseCommand) Reset()         { *m = CreateDatabaseCommand{} }
func (m *CreateDatabaseCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDatabaseCommand) ProtoMessage()    {}
func (*CreateDatabaseCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{15}
}
func (m *CreateDatabaseCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDatabaseCommand.Unmarshal(m, b)
}
func (m *CreateDatabaseCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDatabaseCommand.Marshal(b, m, deterministic)
}
func (m *CreateDatabaseCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDatabaseCommand.Merge(m, src)
}
func (m *CreateDatabaseCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDatabaseCommand.Size(m)
}
func (m *CreateDatabaseCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDatabaseCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDatabaseCommand proto.InternalMessageInfo

func (m *CreateDatabaseCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateDatabaseCommand) GetRetentionPolicy() *RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicy
	}
	return nil
}

var E_CreateDatabaseCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDatabaseCommand)(nil),
	Field:         103,
	Name:          "meta.CreateDatabaseCommand.command",
	Tag:           "bytes,103,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropDatabaseCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropDatabaseCommand) Reset()         { *m = DropDatabaseCommand{} }
func (m *DropDatabaseCommand) String() string { return proto.CompactTextString(m) }
func (*DropDatabaseCommand) ProtoMessage()    {}
func (*DropDatabaseCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{16}
}
func (m *DropDatabaseCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropDatabaseCommand.Unmarshal(m, b)
}
func (m *DropDatabaseCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropDatabaseCommand.Marshal(b, m, deterministic)
}
func (m *DropDatabaseCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropDatabaseCommand.Merge(m, src)
}
func (m *DropDatabaseCommand) XXX_Size() int {
	return xxx_messageInfo_DropDatabaseCommand.Size(m)
}
func (m *DropDatabaseCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropDatabaseCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropDatabaseCommand proto.InternalMessageInfo

func (m *DropDatabaseCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropDatabaseCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropDatabaseCommand)(nil),
	Field:         104,
	Name:          "meta.DropDatabaseCommand.command",
	Tag:           "bytes,104,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateRetentionPolicyCommand struct {
	Database             *string              `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *RetentionPolicyInfo `protobuf:"bytes,2,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	Default              *bool                `protobuf:"varint,3,opt,name=Default" json:"Default,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CreateRetentionPolicyCommand) Reset()         { *m = CreateRetentionPolicyCommand{} }
func (m *CreateRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*CreateRetentionPolicyCommand) ProtoMessage()    {}
func (*CreateRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{17}
}
func (m *CreateRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *CreateRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *CreateRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRetentionPolicyCommand.Merge(m, src)
}
func (m *CreateRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_CreateRetentionPolicyCommand.Size(m)
}
func (m *CreateRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRetentionPolicyCommand proto.InternalMessageInfo

func (m *CreateRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateRetentionPolicyCommand) GetRetentionPolicy() *RetentionPolicyInfo {
	if m != nil {
		return m.RetentionPolicy
	}
	return nil
}

func (m *CreateRetentionPolicyCommand) GetDefault() bool {
	if m != nil && m.Default != nil {
		return *m.Default
	}
	return false
}

var E_CreateRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateRetentionPolicyCommand)(nil),
	Field:         105,
	Name:          "meta.CreateRetentionPolicyCommand.command",
	Tag:           "bytes,105,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropRetentionPolicyCommand) Reset()         { *m = DropRetentionPolicyCommand{} }
func (m *DropRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*DropRetentionPolicyCommand) ProtoMessage()    {}
func (*DropRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{18}
}
func (m *DropRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *DropRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *DropRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropRetentionPolicyCommand.Merge(m, src)
}
func (m *DropRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_DropRetentionPolicyCommand.Size(m)
}
func (m *DropRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropRetentionPolicyCommand proto.InternalMessageInfo

func (m *DropRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropRetentionPolicyCommand)(nil),
	Field:         106,
	Name:          "meta.DropRetentionPolicyCommand.command",
	Tag:           "bytes,106,opt,name=command",
	Filename:      "internal/meta.proto",
}

type SetDefaultRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultRetentionPolicyCommand) Reset()         { *m = SetDefaultRetentionPolicyCommand{} }
func (m *SetDefaultRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*SetDefaultRetentionPolicyCommand) ProtoMessage()    {}
func (*SetDefaultRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{19}
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultRetentionPolicyCommand.Merge(m, src)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_SetDefaultRetentionPolicyCommand.Size(m)
}
func (m *SetDefaultRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultRetentionPolicyCommand proto.InternalMessageInfo

func (m *SetDefaultRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *SetDefaultRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_SetDefaultRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetDefaultRetentionPolicyCommand)(nil),
	Field:         107,
	Name:          "meta.SetDefaultRetentionPolicyCommand.command",
	Tag:           "bytes,107,opt,name=command",
	Filename:      "internal/meta.proto",
}

type UpdateRetentionPolicyCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	NewName              *string  `protobuf:"bytes,3,opt,name=NewName" json:"NewName,omitempty"`
	Duration             *int64   `protobuf:"varint,4,opt,name=Duration" json:"Duration,omitempty"`
	ReplicaN             *uint32  `protobuf:"varint,5,opt,name=ReplicaN" json:"ReplicaN,omitempty"`
	ShardGroupDuration   *int64   `protobuf:"varint,6,opt,name=ShardGroupDuration" json:"ShardGroupDuration,omitempty"`
	Default              *bool    `protobuf:"varint,7,opt,name=Default" json:"Default,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRetentionPolicyCommand) Reset()         { *m = UpdateRetentionPolicyCommand{} }
func (m *UpdateRetentionPolicyCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateRetentionPolicyCommand) ProtoMessage()    {}
func (*UpdateRetentionPolicyCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{20}
}
func (m *UpdateRetentionPolicyCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Unmarshal(m, b)
}
func (m *UpdateRetentionPolicyCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Marshal(b, m, deterministic)
}
func (m *UpdateRetentionPolicyCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRetentionPolicyCommand.Merge(m, src)
}
func (m *UpdateRetentionPolicyCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateRetentionPolicyCommand.Size(m)
}
func (m *UpdateRetentionPolicyCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRetentionPolicyCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRetentionPolicyCommand proto.InternalMessageInfo

func (m *UpdateRetentionPolicyCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetNewName() string {
	if m != nil && m.NewName != nil {
		return *m.NewName
	}
	return ""
}

func (m *UpdateRetentionPolicyCommand) GetDuration() int64 {
	if m != nil && m.Duration != nil {
		return *m.Duration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetReplicaN() uint32 {
	if m != nil && m.ReplicaN != nil {
		return *m.ReplicaN
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetShardGroupDuration() int64 {
	if m != nil && m.ShardGroupDuration != nil {
		return *m.ShardGroupDuration
	}
	return 0
}

func (m *UpdateRetentionPolicyCommand) GetDefault() bool {
	if m != nil && m.Default != nil {
		return *m.Default
	}
	return false
}

var E_UpdateRetentionPolicyCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateRetentionPolicyCommand)(nil),
	Field:         108,
	Name:          "meta.UpdateRetentionPolicyCommand.command",
	Tag:           "bytes,108,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateShardGroupCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	Timestamp            *int64   `protobuf:"varint,3,req,name=Timestamp" json:"Timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateShardGroupCommand) Reset()         { *m = CreateShardGroupCommand{} }
func (m *CreateShardGroupCommand) String() string { return proto.CompactTextString(m) }
func (*CreateShardGroupCommand) ProtoMessage()    {}
func (*CreateShardGroupCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{21}
}
func (m *CreateShardGroupCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateShardGroupCommand.Unmarshal(m, b)
}
func (m *CreateShardGroupCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateShardGroupCommand.Marshal(b, m, deterministic)
}
func (m *CreateShardGroupCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateShardGroupCommand.Merge(m, src)
}
func (m *CreateShardGroupCommand) XXX_Size() int {
	return xxx_messageInfo_CreateShardGroupCommand.Size(m)
}
func (m *CreateShardGroupCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateShardGroupCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateShardGroupCommand proto.InternalMessageInfo

func (m *CreateShardGroupCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateShardGroupCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *CreateShardGroupCommand) GetTimestamp() int64 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

var E_CreateShardGroupCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateShardGroupCommand)(nil),
	Field:         109,
	Name:          "meta.CreateShardGroupCommand.command",
	Tag:           "bytes,109,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DeleteShardGroupCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,2,req,name=Policy" json:"Policy,omitempty"`
	ShardGroupID         *uint64  `protobuf:"varint,3,req,name=ShardGroupID" json:"ShardGroupID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteShardGroupCommand) Reset()         { *m = DeleteShardGroupCommand{} }
func (m *DeleteShardGroupCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteShardGroupCommand) ProtoMessage()    {}
func (*DeleteShardGroupCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{22}
}
func (m *DeleteShardGroupCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteShardGroupCommand.Unmarshal(m, b)
}
func (m *DeleteShardGroupCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteShardGroupCommand.Marshal(b, m, deterministic)
}
func (m *DeleteShardGroupCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteShardGroupCommand.Merge(m, src)
}
func (m *DeleteShardGroupCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteShardGroupCommand.Size(m)
}
func (m *DeleteShardGroupCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteShardGroupCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteShardGroupCommand proto.InternalMessageInfo

func (m *DeleteShardGroupCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DeleteShardGroupCommand) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *DeleteShardGroupCommand) GetShardGroupID() uint64 {
	if m != nil && m.ShardGroupID != nil {
		return *m.ShardGroupID
	}
	return 0
}

var E_DeleteShardGroupCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteShardGroupCommand)(nil),
	Field:         110,
	Name:          "meta.DeleteShardGroupCommand.command",
	Tag:           "bytes,110,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateContinuousQueryCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	Query                *string  `protobuf:"bytes,3,req,name=Query" json:"Query,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateContinuousQueryCommand) Reset()         { *m = CreateContinuousQueryCommand{} }
func (m *CreateContinuousQueryCommand) String() string { return proto.CompactTextString(m) }
func (*CreateContinuousQueryCommand) ProtoMessage()    {}
func (*CreateContinuousQueryCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{23}
}
func (m *CreateContinuousQueryCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateContinuousQueryCommand.Unmarshal(m, b)
}
func (m *CreateContinuousQueryCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateContinuousQueryCommand.Marshal(b, m, deterministic)
}
func (m *CreateContinuousQueryCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateContinuousQueryCommand.Merge(m, src)
}
func (m *CreateContinuousQueryCommand) XXX_Size() int {
	return xxx_messageInfo_CreateContinuousQueryCommand.Size(m)
}
func (m *CreateContinuousQueryCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateContinuousQueryCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateContinuousQueryCommand proto.InternalMessageInfo

func (m *CreateContinuousQueryCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateContinuousQueryCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateContinuousQueryCommand) GetQuery() string {
	if m != nil && m.Query != nil {
		return *m.Query
	}
	return ""
}

var E_CreateContinuousQueryCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateContinuousQueryCommand)(nil),
	Field:         111,
	Name:          "meta.CreateContinuousQueryCommand.command",
	Tag:           "bytes,111,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropContinuousQueryCommand struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Name                 *string  `protobuf:"bytes,2,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropContinuousQueryCommand) Reset()         { *m = DropContinuousQueryCommand{} }
func (m *DropContinuousQueryCommand) String() string { return proto.CompactTextString(m) }
func (*DropContinuousQueryCommand) ProtoMessage()    {}
func (*DropContinuousQueryCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{24}
}
func (m *DropContinuousQueryCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropContinuousQueryCommand.Unmarshal(m, b)
}
func (m *DropContinuousQueryCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropContinuousQueryCommand.Marshal(b, m, deterministic)
}
func (m *DropContinuousQueryCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropContinuousQueryCommand.Merge(m, src)
}
func (m *DropContinuousQueryCommand) XXX_Size() int {
	return xxx_messageInfo_DropContinuousQueryCommand.Size(m)
}
func (m *DropContinuousQueryCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropContinuousQueryCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropContinuousQueryCommand proto.InternalMessageInfo

func (m *DropContinuousQueryCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropContinuousQueryCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropContinuousQueryCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropContinuousQueryCommand)(nil),
	Field:         112,
	Name:          "meta.DropContinuousQueryCommand.command",
	Tag:           "bytes,112,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string  `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	Admin                *bool    `protobuf:"varint,3,req,name=Admin" json:"Admin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateUserCommand) Reset()         { *m = CreateUserCommand{} }
func (m *CreateUserCommand) String() string { return proto.CompactTextString(m) }
func (*CreateUserCommand) ProtoMessage()    {}
func (*CreateUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{25}
}
func (m *CreateUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateUserCommand.Unmarshal(m, b)
}
func (m *CreateUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateUserCommand.Marshal(b, m, deterministic)
}
func (m *CreateUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateUserCommand.Merge(m, src)
}
func (m *CreateUserCommand) XXX_Size() int {
	return xxx_messageInfo_CreateUserCommand.Size(m)
}
func (m *CreateUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateUserCommand proto.InternalMessageInfo

func (m *CreateUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateUserCommand) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

func (m *CreateUserCommand) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

var E_CreateUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateUserCommand)(nil),
	Field:         113,
	Name:          "meta.CreateUserCommand.command",
	Tag:           "bytes,113,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropUserCommand) Reset()         { *m = DropUserCommand{} }
func (m *DropUserCommand) String() string { return proto.CompactTextString(m) }
func (*DropUserCommand) ProtoMessage()    {}
func (*DropUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{26}
}
func (m *DropUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropUserCommand.Unmarshal(m, b)
}
func (m *DropUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropUserCommand.Marshal(b, m, deterministic)
}
func (m *DropUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropUserCommand.Merge(m, src)
}
func (m *DropUserCommand) XXX_Size() int {
	return xxx_messageInfo_DropUserCommand.Size(m)
}
func (m *DropUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropUserCommand proto.InternalMessageInfo

func (m *DropUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

var E_DropUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropUserCommand)(nil),
	Field:         114,
	Name:          "meta.DropUserCommand.command",
	Tag:           "bytes,114,opt,name=command",
	Filename:      "internal/meta.proto",
}

type UpdateUserCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Hash                 *string  `protobuf:"bytes,2,req,name=Hash" json:"Hash,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserCommand) Reset()         { *m = UpdateUserCommand{} }
func (m *UpdateUserCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateUserCommand) ProtoMessage()    {}
func (*UpdateUserCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{27}
}
func (m *UpdateUserCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserCommand.Unmarshal(m, b)
}
func (m *UpdateUserCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserCommand.Marshal(b, m, deterministic)
}
func (m *UpdateUserCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserCommand.Merge(m, src)
}
func (m *UpdateUserCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateUserCommand.Size(m)
}
func (m *UpdateUserCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserCommand proto.InternalMessageInfo

func (m *UpdateUserCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *UpdateUserCommand) GetHash() string {
	if m != nil && m.Hash != nil {
		return *m.Hash
	}
	return ""
}

var E_UpdateUserCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateUserCommand)(nil),
	Field:         115,
	Name:          "meta.UpdateUserCommand.command",
	Tag:           "bytes,115,opt,name=command",
	Filename:      "internal/meta.proto",
}

type SetPrivilegeCommand struct {
	Username             *string  `protobuf:"bytes,1,req,name=Username" json:"Username,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	Privilege            *int32   `protobuf:"varint,3,req,name=Privilege" json:"Privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPrivilegeCommand) Reset()         { *m = SetPrivilegeCommand{} }
func (m *SetPrivilegeCommand) String() string { return proto.CompactTextString(m) }
func (*SetPrivilegeCommand) ProtoMessage()    {}
func (*SetPrivilegeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{28}
}
func (m *SetPrivilegeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPrivilegeCommand.Unmarshal(m, b)
}
func (m *SetPrivilegeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPrivilegeCommand.Marshal(b, m, deterministic)
}
func (m *SetPrivilegeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPrivilegeCommand.Merge(m, src)
}
func (m *SetPrivilegeCommand) XXX_Size() int {
	return xxx_messageInfo_SetPrivilegeCommand.Size(m)
}
func (m *SetPrivilegeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPrivilegeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetPrivilegeCommand proto.InternalMessageInfo

func (m *SetPrivilegeCommand) GetUsername() string {
	if m != nil && m.Username != nil {
		return *m.Username
	}
	return ""
}

func (m *SetPrivilegeCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *SetPrivilegeCommand) GetPrivilege() int32 {
	if m != nil && m.Privilege != nil {
		return *m.Privilege
	}
	return 0
}

var E_SetPrivilegeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetPrivilegeCommand)(nil),
	Field:         116,
	Name:          "meta.SetPrivilegeCommand.command",
	Tag:           "bytes,116,opt,name=command",
	Filename:      "internal/meta.proto",
}

type SetDataCommand struct {
	Data                 *Data    `protobuf:"bytes,1,req,name=Data" json:"Data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDataCommand) Reset()         { *m = SetDataCommand{} }
func (m *SetDataCommand) String() string { return proto.CompactTextString(m) }
func (*SetDataCommand) ProtoMessage()    {}
func (*SetDataCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{29}
}
func (m *SetDataCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDataCommand.Unmarshal(m, b)
}
func (m *SetDataCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDataCommand.Marshal(b, m, deterministic)
}
func (m *SetDataCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDataCommand.Merge(m, src)
}
func (m *SetDataCommand) XXX_Size() int {
	return xxx_messageInfo_SetDataCommand.Size(m)
}
func (m *SetDataCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDataCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetDataCommand proto.InternalMessageInfo

func (m *SetDataCommand) GetData() *Data {
	if m != nil {
		return m.Data
	}
	return nil
}

var E_SetDataCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetDataCommand)(nil),
	Field:         117,
	Name:          "meta.SetDataCommand.command",
	Tag:           "bytes,117,opt,name=command",
	Filename:      "internal/meta.proto",
}

type SetAdminPrivilegeCommand struct {
	Username             *string  `protobuf:"bytes,1,req,name=Username" json:"Username,omitempty"`
	Admin                *bool    `protobuf:"varint,2,req,name=Admin" json:"Admin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetAdminPrivilegeCommand) Reset()         { *m = SetAdminPrivilegeCommand{} }
func (m *SetAdminPrivilegeCommand) String() string { return proto.CompactTextString(m) }
func (*SetAdminPrivilegeCommand) ProtoMessage()    {}
func (*SetAdminPrivilegeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{30}
}
func (m *SetAdminPrivilegeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Unmarshal(m, b)
}
func (m *SetAdminPrivilegeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Marshal(b, m, deterministic)
}
func (m *SetAdminPrivilegeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetAdminPrivilegeCommand.Merge(m, src)
}
func (m *SetAdminPrivilegeCommand) XXX_Size() int {
	return xxx_messageInfo_SetAdminPrivilegeCommand.Size(m)
}
func (m *SetAdminPrivilegeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetAdminPrivilegeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetAdminPrivilegeCommand proto.InternalMessageInfo

func (m *SetAdminPrivilegeCommand) GetUsername() string {
	if m != nil && m.Username != nil {
		return *m.Username
	}
	return ""
}

func (m *SetAdminPrivilegeCommand) GetAdmin() bool {
	if m != nil && m.Admin != nil {
		return *m.Admin
	}
	return false
}

var E_SetAdminPrivilegeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetAdminPrivilegeCommand)(nil),
	Field:         118,
	Name:          "meta.SetAdminPrivilegeCommand.command",
	Tag:           "bytes,118,opt,name=command",
	Filename:      "internal/meta.proto",
}

type UpdateNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Host                 *string  `protobuf:"bytes,2,req,name=Host" json:"Host,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateNodeCommand) Reset()         { *m = UpdateNodeCommand{} }
func (m *UpdateNodeCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateNodeCommand) ProtoMessage()    {}
func (*UpdateNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{31}
}
func (m *UpdateNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateNodeCommand.Unmarshal(m, b)
}
func (m *UpdateNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateNodeCommand.Marshal(b, m, deterministic)
}
func (m *UpdateNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateNodeCommand.Merge(m, src)
}
func (m *UpdateNodeCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateNodeCommand.Size(m)
}
func (m *UpdateNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateNodeCommand proto.InternalMessageInfo

func (m *UpdateNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *UpdateNodeCommand) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

var E_UpdateNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateNodeCommand)(nil),
	Field:         119,
	Name:          "meta.UpdateNodeCommand.command",
	Tag:           "bytes,119,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateSubscriptionCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,3,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	Mode                 *string  `protobuf:"bytes,4,req,name=Mode" json:"Mode,omitempty"`
	Destinations         []string `protobuf:"bytes,5,rep,name=Destinations" json:"Destinations,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSubscriptionCommand) Reset()         { *m = CreateSubscriptionCommand{} }
func (m *CreateSubscriptionCommand) String() string { return proto.CompactTextString(m) }
func (*CreateSubscriptionCommand) ProtoMessage()    {}
func (*CreateSubscriptionCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{32}
}
func (m *CreateSubscriptionCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSubscriptionCommand.Unmarshal(m, b)
}
func (m *CreateSubscriptionCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSubscriptionCommand.Marshal(b, m, deterministic)
}
func (m *CreateSubscriptionCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSubscriptionCommand.Merge(m, src)
}
func (m *CreateSubscriptionCommand) XXX_Size() int {
	return xxx_messageInfo_CreateSubscriptionCommand.Size(m)
}
func (m *CreateSubscriptionCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSubscriptionCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSubscriptionCommand proto.InternalMessageInfo

func (m *CreateSubscriptionCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetMode() string {
	if m != nil && m.Mode != nil {
		return *m.Mode
	}
	return ""
}

func (m *CreateSubscriptionCommand) GetDestinations() []string {
	if m != nil {
		return m.Destinations
	}
	return nil
}

var E_CreateSubscriptionCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateSubscriptionCommand)(nil),
	Field:         121,
	Name:          "meta.CreateSubscriptionCommand.command",
	Tag:           "bytes,121,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropSubscriptionCommand struct {
	Name                 *string  `protobuf:"bytes,1,req,name=Name" json:"Name,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,3,req,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropSubscriptionCommand) Reset()         { *m = DropSubscriptionCommand{} }
func (m *DropSubscriptionCommand) String() string { return proto.CompactTextString(m) }
func (*DropSubscriptionCommand) ProtoMessage()    {}
func (*DropSubscriptionCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{33}
}
func (m *DropSubscriptionCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropSubscriptionCommand.Unmarshal(m, b)
}
func (m *DropSubscriptionCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropSubscriptionCommand.Marshal(b, m, deterministic)
}
func (m *DropSubscriptionCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropSubscriptionCommand.Merge(m, src)
}
func (m *DropSubscriptionCommand) XXX_Size() int {
	return xxx_messageInfo_DropSubscriptionCommand.Size(m)
}
func (m *DropSubscriptionCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropSubscriptionCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropSubscriptionCommand proto.InternalMessageInfo

func (m *DropSubscriptionCommand) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *DropSubscriptionCommand) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *DropSubscriptionCommand) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

var E_DropSubscriptionCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropSubscriptionCommand)(nil),
	Field:         122,
	Name:          "meta.DropSubscriptionCommand.command",
	Tag:           "bytes,122,opt,name=command",
	Filename:      "internal/meta.proto",
}

type RemovePeerCommand struct {
	ID                   *uint64  `protobuf:"varint,1,opt,name=ID" json:"ID,omitempty"`
	Addr                 *string  `protobuf:"bytes,2,req,name=Addr" json:"Addr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemovePeerCommand) Reset()         { *m = RemovePeerCommand{} }
func (m *RemovePeerCommand) String() string { return proto.CompactTextString(m) }
func (*RemovePeerCommand) ProtoMessage()    {}
func (*RemovePeerCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{34}
}
func (m *RemovePeerCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemovePeerCommand.Unmarshal(m, b)
}
func (m *RemovePeerCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemovePeerCommand.Marshal(b, m, deterministic)
}
func (m *RemovePeerCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemovePeerCommand.Merge(m, src)
}
func (m *RemovePeerCommand) XXX_Size() int {
	return xxx_messageInfo_RemovePeerCommand.Size(m)
}
func (m *RemovePeerCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_RemovePeerCommand.DiscardUnknown(m)
}

var xxx_messageInfo_RemovePeerCommand proto.InternalMessageInfo

func (m *RemovePeerCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *RemovePeerCommand) GetAddr() string {
	if m != nil && m.Addr != nil {
		return *m.Addr
	}
	return ""
}

var E_RemovePeerCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*RemovePeerCommand)(nil),
	Field:         123,
	Name:          "meta.RemovePeerCommand.command",
	Tag:           "bytes,123,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateMetaNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	Rand                 *uint64  `protobuf:"varint,3,req,name=Rand" json:"Rand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateMetaNodeCommand) Reset()         { *m = CreateMetaNodeCommand{} }
func (m *CreateMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateMetaNodeCommand) ProtoMessage()    {}
func (*CreateMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{35}
}
func (m *CreateMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateMetaNodeCommand.Unmarshal(m, b)
}
func (m *CreateMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateMetaNodeCommand.Merge(m, src)
}
func (m *CreateMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateMetaNodeCommand.Size(m)
}
func (m *CreateMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateMetaNodeCommand proto.InternalMessageInfo

func (m *CreateMetaNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *CreateMetaNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

func (m *CreateMetaNodeCommand) GetRand() uint64 {
	if m != nil && m.Rand != nil {
		return *m.Rand
	}
	return 0
}

var E_CreateMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateMetaNodeCommand)(nil),
	Field:         124,
	Name:          "meta.CreateMetaNodeCommand.command",
	Tag:           "bytes,124,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CreateDataNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateDataNodeCommand) Reset()         { *m = CreateDataNodeCommand{} }
func (m *CreateDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*CreateDataNodeCommand) ProtoMessage()    {}
func (*CreateDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{36}
}
func (m *CreateDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateDataNodeCommand.Unmarshal(m, b)
}
func (m *CreateDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *CreateDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateDataNodeCommand.Merge(m, src)
}
func (m *CreateDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_CreateDataNodeCommand.Size(m)
}
func (m *CreateDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CreateDataNodeCommand proto.InternalMessageInfo

func (m *CreateDataNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *CreateDataNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

var E_CreateDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CreateDataNodeCommand)(nil),
	Field:         125,
	Name:          "meta.CreateDataNodeCommand.command",
	Tag:           "bytes,125,opt,name=command",
	Filename:      "internal/meta.proto",
}

type UpdateDataNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	HTTPAddr             *string  `protobuf:"bytes,2,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,3,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateDataNodeCommand) Reset()         { *m = UpdateDataNodeCommand{} }
func (m *UpdateDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*UpdateDataNodeCommand) ProtoMessage()    {}
func (*UpdateDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{37}
}
func (m *UpdateDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDataNodeCommand.Unmarshal(m, b)
}
func (m *UpdateDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *UpdateDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDataNodeCommand.Merge(m, src)
}
func (m *UpdateDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_UpdateDataNodeCommand.Size(m)
}
func (m *UpdateDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDataNodeCommand proto.InternalMessageInfo

func (m *UpdateDataNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *UpdateDataNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *UpdateDataNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

var E_UpdateDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*UpdateDataNodeCommand)(nil),
	Field:         126,
	Name:          "meta.UpdateDataNodeCommand.command",
	Tag:           "bytes,126,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DeleteMetaNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteMetaNodeCommand) Reset()         { *m = DeleteMetaNodeCommand{} }
func (m *DeleteMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteMetaNodeCommand) ProtoMessage()    {}
func (*DeleteMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{38}
}
func (m *DeleteMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteMetaNodeCommand.Unmarshal(m, b)
}
func (m *DeleteMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *DeleteMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteMetaNodeCommand.Merge(m, src)
}
func (m *DeleteMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteMetaNodeCommand.Size(m)
}
func (m *DeleteMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteMetaNodeCommand proto.InternalMessageInfo

func (m *DeleteMetaNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DeleteMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteMetaNodeCommand)(nil),
	Field:         127,
	Name:          "meta.DeleteMetaNodeCommand.command",
	Tag:           "bytes,127,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DeleteDataNodeCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteDataNodeCommand) Reset()         { *m = DeleteDataNodeCommand{} }
func (m *DeleteDataNodeCommand) String() string { return proto.CompactTextString(m) }
func (*DeleteDataNodeCommand) ProtoMessage()    {}
func (*DeleteDataNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{39}
}
func (m *DeleteDataNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteDataNodeCommand.Unmarshal(m, b)
}
func (m *DeleteDataNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteDataNodeCommand.Marshal(b, m, deterministic)
}
func (m *DeleteDataNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteDataNodeCommand.Merge(m, src)
}
func (m *DeleteDataNodeCommand) XXX_Size() int {
	return xxx_messageInfo_DeleteDataNodeCommand.Size(m)
}
func (m *DeleteDataNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteDataNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteDataNodeCommand proto.InternalMessageInfo

func (m *DeleteDataNodeCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DeleteDataNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DeleteDataNodeCommand)(nil),
	Field:         128,
	Name:          "meta.DeleteDataNodeCommand.command",
	Tag:           "bytes,128,opt,name=command",
	Filename:      "internal/meta.proto",
}

type Response struct {
	OK                   *bool    `protobuf:"varint,1,req,name=OK" json:"OK,omitempty"`
	Error                *string  `protobuf:"bytes,2,opt,name=Error" json:"Error,omitempty"`
	Index                *uint64  `protobuf:"varint,3,opt,name=Index" json:"Index,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{40}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetOK() bool {
	if m != nil && m.OK != nil {
		return *m.OK
	}
	return false
}

func (m *Response) GetError() string {
	if m != nil && m.Error != nil {
		return *m.Error
	}
	return ""
}

func (m *Response) GetIndex() uint64 {
	if m != nil && m.Index != nil {
		return *m.Index
	}
	return 0
}

// SetMetaNodeCommand is for the initial metanode in a cluster or
// if the single host restarts and its hostname changes, this will update it
type SetMetaNodeCommand struct {
	HTTPAddr             *string  `protobuf:"bytes,1,req,name=HTTPAddr" json:"HTTPAddr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,2,req,name=TCPAddr" json:"TCPAddr,omitempty"`
	Rand                 *uint64  `protobuf:"varint,3,req,name=Rand" json:"Rand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMetaNodeCommand) Reset()         { *m = SetMetaNodeCommand{} }
func (m *SetMetaNodeCommand) String() string { return proto.CompactTextString(m) }
func (*SetMetaNodeCommand) ProtoMessage()    {}
func (*SetMetaNodeCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{41}
}
func (m *SetMetaNodeCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMetaNodeCommand.Unmarshal(m, b)
}
func (m *SetMetaNodeCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMetaNodeCommand.Marshal(b, m, deterministic)
}
func (m *SetMetaNodeCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMetaNodeCommand.Merge(m, src)
}
func (m *SetMetaNodeCommand) XXX_Size() int {
	return xxx_messageInfo_SetMetaNodeCommand.Size(m)
}
func (m *SetMetaNodeCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMetaNodeCommand.DiscardUnknown(m)
}

var xxx_messageInfo_SetMetaNodeCommand proto.InternalMessageInfo

func (m *SetMetaNodeCommand) GetHTTPAddr() string {
	if m != nil && m.HTTPAddr != nil {
		return *m.HTTPAddr
	}
	return ""
}

func (m *SetMetaNodeCommand) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

func (m *SetMetaNodeCommand) GetRand() uint64 {
	if m != nil && m.Rand != nil {
		return *m.Rand
	}
	return 0
}

var E_SetMetaNodeCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*SetMetaNodeCommand)(nil),
	Field:         129,
	Name:          "meta.SetMetaNodeCommand.command",
	Tag:           "bytes,129,opt,name=command",
	Filename:      "internal/meta.proto",
}

type DropShardCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DropShardCommand) Reset()         { *m = DropShardCommand{} }
func (m *DropShardCommand) String() string { return proto.CompactTextString(m) }
func (*DropShardCommand) ProtoMessage()    {}
func (*DropShardCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{42}
}
func (m *DropShardCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DropShardCommand.Unmarshal(m, b)
}
func (m *DropShardCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DropShardCommand.Marshal(b, m, deterministic)
}
func (m *DropShardCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DropShardCommand.Merge(m, src)
}
func (m *DropShardCommand) XXX_Size() int {
	return xxx_messageInfo_DropShardCommand.Size(m)
}
func (m *DropShardCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_DropShardCommand.DiscardUnknown(m)
}

var xxx_messageInfo_DropShardCommand proto.InternalMessageInfo

func (m *DropShardCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

var E_DropShardCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*DropShardCommand)(nil),
	Field:         130,
	Name:          "meta.DropShardCommand.command",
	Tag:           "bytes,130,opt,name=command",
	Filename:      "internal/meta.proto",
}

type TruncateShardGroupsCommand struct {
	Timestamp            *int64   `protobuf:"varint,1,req,name=Timestamp" json:"Timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TruncateShardGroupsCommand) Reset()         { *m = TruncateShardGroupsCommand{} }
func (m *TruncateShardGroupsCommand) String() string { return proto.CompactTextString(m) }
func (*TruncateShardGroupsCommand) ProtoMessage()    {}
func (*TruncateShardGroupsCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{43}
}
func (m *TruncateShardGroupsCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TruncateShardGroupsCommand.Unmarshal(m, b)
}
func (m *TruncateShardGroupsCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TruncateShardGroupsCommand.Marshal(b, m, deterministic)
}
func (m *TruncateShardGroupsCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TruncateShardGroupsCommand.Merge(m, src)
}
func (m *TruncateShardGroupsCommand) XXX_Size() int {
	return xxx_messageInfo_TruncateShardGroupsCommand.Size(m)
}
func (m *TruncateShardGroupsCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_TruncateShardGroupsCommand.DiscardUnknown(m)
}

var xxx_messageInfo_TruncateShardGroupsCommand proto.InternalMessageInfo

func (m *TruncateShardGroupsCommand) GetTimestamp() int64 {
	if m != nil && m.Timestamp != nil {
		return *m.Timestamp
	}
	return 0
}

var E_TruncateShardGroupsCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*TruncateShardGroupsCommand)(nil),
	Field:         131,
	Name:          "meta.TruncateShardGroupsCommand.command",
	Tag:           "bytes,131,opt,name=command",
	Filename:      "internal/meta.proto",
}

type PruneShardGroupsCommand struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PruneShardGroupsCommand) Reset()         { *m = PruneShardGroupsCommand{} }
func (m *PruneShardGroupsCommand) String() string { return proto.CompactTextString(m) }
func (*PruneShardGroupsCommand) ProtoMessage()    {}
func (*PruneShardGroupsCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{44}
}
func (m *PruneShardGroupsCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PruneShardGroupsCommand.Unmarshal(m, b)
}
func (m *PruneShardGroupsCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PruneShardGroupsCommand.Marshal(b, m, deterministic)
}
func (m *PruneShardGroupsCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PruneShardGroupsCommand.Merge(m, src)
}
func (m *PruneShardGroupsCommand) XXX_Size() int {
	return xxx_messageInfo_PruneShardGroupsCommand.Size(m)
}
func (m *PruneShardGroupsCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_PruneShardGroupsCommand.DiscardUnknown(m)
}

var xxx_messageInfo_PruneShardGroupsCommand proto.InternalMessageInfo

var E_PruneShardGroupsCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*PruneShardGroupsCommand)(nil),
	Field:         132,
	Name:          "meta.PruneShardGroupsCommand.command",
	Tag:           "bytes,132,opt,name=command",
	Filename:      "internal/meta.proto",
}

type CopyShardOwnerCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	NodeID               *uint64  `protobuf:"varint,2,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CopyShardOwnerCommand) Reset()         { *m = CopyShardOwnerCommand{} }
func (m *CopyShardOwnerCommand) String() string { return proto.CompactTextString(m) }
func (*CopyShardOwnerCommand) ProtoMessage()    {}
func (*CopyShardOwnerCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{45}
}
func (m *CopyShardOwnerCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyShardOwnerCommand.Unmarshal(m, b)
}
func (m *CopyShardOwnerCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyShardOwnerCommand.Marshal(b, m, deterministic)
}
func (m *CopyShardOwnerCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyShardOwnerCommand.Merge(m, src)
}
func (m *CopyShardOwnerCommand) XXX_Size() int {
	return xxx_messageInfo_CopyShardOwnerCommand.Size(m)
}
func (m *CopyShardOwnerCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyShardOwnerCommand.DiscardUnknown(m)
}

var xxx_messageInfo_CopyShardOwnerCommand proto.InternalMessageInfo

func (m *CopyShardOwnerCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *CopyShardOwnerCommand) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

var E_CopyShardOwnerCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*CopyShardOwnerCommand)(nil),
	Field:         133,
	Name:          "meta.CopyShardOwnerCommand.command",
	Tag:           "bytes,133,opt,name=command",
	Filename:      "internal/meta.proto",
}

type RemoveShardOwnerCommand struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	NodeID               *uint64  `protobuf:"varint,2,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveShardOwnerCommand) Reset()         { *m = RemoveShardOwnerCommand{} }
func (m *RemoveShardOwnerCommand) String() string { return proto.CompactTextString(m) }
func (*RemoveShardOwnerCommand) ProtoMessage()    {}
func (*RemoveShardOwnerCommand) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{46}
}
func (m *RemoveShardOwnerCommand) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveShardOwnerCommand.Unmarshal(m, b)
}
func (m *RemoveShardOwnerCommand) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveShardOwnerCommand.Marshal(b, m, deterministic)
}
func (m *RemoveShardOwnerCommand) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveShardOwnerCommand.Merge(m, src)
}
func (m *RemoveShardOwnerCommand) XXX_Size() int {
	return xxx_messageInfo_RemoveShardOwnerCommand.Size(m)
}
func (m *RemoveShardOwnerCommand) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveShardOwnerCommand.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveShardOwnerCommand proto.InternalMessageInfo

func (m *RemoveShardOwnerCommand) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *RemoveShardOwnerCommand) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

var E_RemoveShardOwnerCommand_Command = &proto.ExtensionDesc{
	ExtendedType:  (*Command)(nil),
	ExtensionType: (*RemoveShardOwnerCommand)(nil),
	Field:         134,
	Name:          "meta.RemoveShardOwnerCommand.command",
	Tag:           "bytes,134,opt,name=command",
	Filename:      "internal/meta.proto",
}

func init() {
	proto.RegisterEnum("meta.Command_Type", Command_Type_name, Command_Type_value)
	proto.RegisterType((*Data)(nil), "meta.Data")
	proto.RegisterType((*NodeInfo)(nil), "meta.NodeInfo")
	proto.RegisterType((*DatabaseInfo)(nil), "meta.DatabaseInfo")
	proto.RegisterType((*RetentionPolicySpec)(nil), "meta.RetentionPolicySpec")
	proto.RegisterType((*RetentionPolicyInfo)(nil), "meta.RetentionPolicyInfo")
	proto.RegisterType((*ShardGroupInfo)(nil), "meta.ShardGroupInfo")
	proto.RegisterType((*ShardInfo)(nil), "meta.ShardInfo")
	proto.RegisterType((*SubscriptionInfo)(nil), "meta.SubscriptionInfo")
	proto.RegisterType((*ShardOwner)(nil), "meta.ShardOwner")
	proto.RegisterType((*ContinuousQueryInfo)(nil), "meta.ContinuousQueryInfo")
	proto.RegisterType((*UserInfo)(nil), "meta.UserInfo")
	proto.RegisterType((*UserPrivilege)(nil), "meta.UserPrivilege")
	proto.RegisterType((*Command)(nil), "meta.Command")
	proto.RegisterExtension(E_CreateNodeCommand_Command)
	proto.RegisterType((*CreateNodeCommand)(nil), "meta.CreateNodeCommand")
	proto.RegisterExtension(E_DeleteNodeCommand_Command)
	proto.RegisterType((*DeleteNodeCommand)(nil), "meta.DeleteNodeCommand")
	proto.RegisterExtension(E_CreateDatabaseCommand_Command)
	proto.RegisterType((*CreateDatabaseCommand)(nil), "meta.CreateDatabaseCommand")
	proto.RegisterExtension(E_DropDatabaseCommand_Command)
	proto.RegisterType((*DropDatabaseCommand)(nil), "meta.DropDatabaseCommand")
	proto.RegisterExtension(E_CreateRetentionPolicyCommand_Command)
	proto.RegisterType((*CreateRetentionPolicyCommand)(nil), "meta.CreateRetentionPolicyCommand")
	proto.RegisterExtension(E_DropRetentionPolicyCommand_Command)
	proto.RegisterType((*DropRetentionPolicyCommand)(nil), "meta.DropRetentionPolicyCommand")
	proto.RegisterExtension(E_SetDefaultRetentionPolicyCommand_Command)
	proto.RegisterType((*SetDefaultRetentionPolicyCommand)(nil), "meta.SetDefaultRetentionPolicyCommand")
	proto.RegisterExtension(E_UpdateRetentionPolicyCommand_Command)
	proto.RegisterType((*UpdateRetentionPolicyCommand)(nil), "meta.UpdateRetentionPolicyCommand")
	proto.RegisterExtension(E_CreateShardGroupCommand_Command)
	proto.RegisterType((*CreateShardGroupCommand)(nil), "meta.CreateShardGroupCommand")
	proto.RegisterExtension(E_DeleteShardGroupCommand_Command)
	proto.RegisterType((*DeleteShardGroupCommand)(nil), "meta.DeleteShardGroupCommand")
	proto.RegisterExtension(E_CreateContinuousQueryCommand_Command)
	proto.RegisterType((*CreateContinuousQueryCommand)(nil), "meta.CreateContinuousQueryCommand")
	proto.RegisterExtension(E_DropContinuousQueryCommand_Command)
	proto.RegisterType((*DropContinuousQueryCommand)(nil), "meta.DropContinuousQueryCommand")
	proto.RegisterExtension(E_CreateUserCommand_Command)
	proto.RegisterType((*CreateUserCommand)(nil), "meta.CreateUserCommand")
	proto.RegisterExtension(E_DropUserCommand_Command)
	proto.RegisterType((*DropUserCommand)(nil), "meta.DropUserCommand")
	proto.RegisterExtension(E_UpdateUserCommand_Command)
	proto.RegisterType((*UpdateUserCommand)(nil), "meta.UpdateUserCommand")
	proto.RegisterExtension(E_SetPrivilegeCommand_Command)
	proto.RegisterType((*SetPrivilegeCommand)(nil), "meta.SetPrivilegeCommand")
	proto.RegisterExtension(E_SetDataCommand_Command)
	proto.RegisterType((*SetDataCommand)(nil), "meta.SetDataCommand")
	proto.RegisterExtension(E_SetAdminPrivilegeCommand_Command)
	proto.RegisterType((*SetAdminPrivilegeCommand)(nil), "meta.SetAdminPrivilegeCommand")
	proto.RegisterExtension(E_UpdateNodeCommand_Command)
	proto.RegisterType((*UpdateNodeCommand)(nil), "meta.UpdateNodeCommand")
	proto.RegisterExtension(E_CreateSubscriptionCommand_Command)
	proto.RegisterType((*CreateSubscriptionCommand)(nil), "meta.CreateSubscriptionCommand")
	proto.RegisterExtension(E_DropSubscriptionCommand_Command)
	proto.RegisterType((*DropSubscriptionCommand)(nil), "meta.DropSubscriptionCommand")
	proto.RegisterExtension(E_RemovePeerCommand_Command)
	proto.RegisterType((*RemovePeerCommand)(nil), "meta.RemovePeerCommand")
	proto.RegisterExtension(E_CreateMetaNodeCommand_Command)
	proto.RegisterType((*CreateMetaNodeCommand)(nil), "meta.CreateMetaNodeCommand")
	proto.RegisterExtension(E_CreateDataNodeCommand_Command)
	proto.RegisterType((*CreateDataNodeCommand)(nil), "meta.CreateDataNodeCommand")
	proto.RegisterExtension(E_UpdateDataNodeCommand_Command)
	proto.RegisterType((*UpdateDataNodeCommand)(nil), "meta.UpdateDataNodeCommand")
	proto.RegisterExtension(E_DeleteMetaNodeCommand_Command)
	proto.RegisterType((*DeleteMetaNodeCommand)(nil), "meta.DeleteMetaNodeCommand")
	proto.RegisterExtension(E_DeleteDataNodeCommand_Command)
	proto.RegisterType((*DeleteDataNodeCommand)(nil), "meta.DeleteDataNodeCommand")
	proto.RegisterType((*Response)(nil), "meta.Response")
	proto.RegisterExtension(E_SetMetaNodeCommand_Command)
	proto.RegisterType((*SetMetaNodeCommand)(nil), "meta.SetMetaNodeCommand")
	proto.RegisterExtension(E_DropShardCommand_Command)
	proto.RegisterType((*DropShardCommand)(nil), "meta.DropShardCommand")
	proto.RegisterExtension(E_TruncateShardGroupsCommand_Command)
	proto.RegisterType((*TruncateShardGroupsCommand)(nil), "meta.TruncateShardGroupsCommand")
	proto.RegisterExtension(E_PruneShardGroupsCommand_Command)
	proto.RegisterType((*PruneShardGroupsCommand)(nil), "meta.PruneShardGroupsCommand")
	proto.RegisterExtension(E_CopyShardOwnerCommand_Command)
	proto.RegisterType((*CopyShardOwnerCommand)(nil), "meta.CopyShardOwnerCommand")
	proto.RegisterExtension(E_RemoveShardOwnerCommand_Command)
	proto.RegisterType((*RemoveShardOwnerCommand)(nil), "meta.RemoveShardOwnerCommand")
}

func init() { proto.RegisterFile("internal/meta.proto", fileDescriptor_59b0956366e72083) }

var fileDescriptor_59b0956366e72083 = []byte{
	// 1940 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x4b, 0x8f, 0x24, 0x39,
	0x11, 0x96, 0xb3, 0xde, 0xd1, 0xd3, 0x8f, 0x71, 0xbf, 0xb2, 0x67, 0x7a, 0x7a, 0x8b, 0xd4, 0x68,
	0x29, 0x21, 0xd4, 0xa0, 0x42, 0xda, 0x13, 0xaf, 0xd9, 0xae, 0x99, 0xe9, 0x62, 0xd4, 0x0f, 0xb2,
	0x7a, 0xaf, 0x48, 0x39, 0x5d, 0x9e, 0x9d, 0x82, 0xaa, 0xcc, 0x22, 0x33, 0x6b, 0x66, 0x9a, 0xa5,
	0xa1, 0x79, 0x2d, 0x12, 0xa7, 0x45, 0x08, 0x71, 0x67, 0x0f, 0x1c, 0x11, 0x42, 0x02, 0x21, 0x4e,
	0xfc, 0x01, 0x7e, 0x06, 0x27, 0xee, 0x5c, 0x91, 0xed, 0x74, 0xda, 0x99, 0xb6, 0xb3, 0xbb, 0x97,
	0xe1, 0x96, 0x8e, 0x08, 0x3b, 0xbe, 0x08, 0x87, 0xc3, 0x11, 0x4e, 0x58, 0x9f, 0x84, 0x29, 0x89,
	0xc3, 0x60, 0xfa, 0xa5, 0x19, 0x49, 0x83, 0xfd, 0x79, 0x1c, 0xa5, 0x11, 0xae, 0xd3, 0x6f, 0xef,
	0x93, 0x1a, 0xd4, 0x07, 0x41, 0x1a, 0x60, 0x0c, 0xf5, 0x33, 0x12, 0xcf, 0x5c, 0xd4, 0x75, 0x7a,
	0x75, 0x9f, 0x7d, 0xe3, 0x0d, 0x68, 0x0c, 0xc3, 0x31, 0x79, 0xe3, 0x3a, 0x8c, 0xc8, 0x07, 0x78,
	0x17, 0x3a, 0x07, 0xd3, 0x45, 0x92, 0x92, 0x78, 0x38, 0x70, 0x6b, 0x8c, 0x23, 0x09, 0xf8, 0x21,
	0x34, 0x8e, 0xa3, 0x31, 0x49, 0xdc, 0x7a, 0xb7, 0xd6, 0x5b, 0xea, 0xaf, 0xec, 0x33, 0x95, 0x94,
	0x34, 0x0c, 0x5f, 0x44, 0x3e, 0x67, 0xe2, 0x2f, 0x43, 0x87, 0x6a, 0x7d, 0x1e, 0x24, 0x24, 0x71,
	0x1b, 0x4c, 0x12, 0x73, 0x49, 0x41, 0x66, 0xd2, 0x52, 0x88, 0xae, 0xfb, 0x41, 0x42, 0xe2, 0xc4,
	0x6d, 0xaa, 0xeb, 0x52, 0x12, 0x5f, 0x97, 0x31, 0x29, 0xb6, 0xa3, 0xe0, 0x0d, 0xd3, 0x36, 0x70,
	0x5b, 0x1c, 0x5b, 0x4e, 0xc0, 0x3d, 0x58, 0x3d, 0x0a, 0xde, 0x8c, 0x5e, 0x06, 0xf1, 0xf8, 0x69,
	0x1c, 0x2d, 0xe6, 0xc3, 0x81, 0xdb, 0x66, 0x32, 0x65, 0x32, 0xde, 0x03, 0x10, 0xa4, 0xe1, 0xc0,
	0xed, 0x30, 0x21, 0x85, 0x82, 0xbf, 0xc8, 0xf1, 0x73, 0x4b, 0xc1, 0x68, 0xa9, 0x14, 0xa0, 0xd2,
	0x47, 0x44, 0x48, 0x2f, 0x99, 0xa5, 0x73, 0x01, 0xef, 0x10, 0xda, 0x82, 0x8c, 0x57, 0xc0, 0x19,
	0x0e, 0xb2, 0x3d, 0x71, 0x86, 0x03, 0xba, 0x4b, 0x8f, 0xc6, 0xe3, 0xd8, 0x75, 0xba, 0xa8, 0xd7,
	0xf1, 0xd9, 0x37, 0x76, 0xa1, 0x75, 0x76, 0x70, 0xca, 0xc8, 0x35, 0x46, 0x16, 0x43, 0xef, 0xdf,
	0x08, 0xee, 0xa8, 0xfe, 0xa4, 0xd3, 0x8f, 0x83, 0x19, 0x61, 0x0b, 0x76, 0x7c, 0xf6, 0x8d, 0xdf,
	0x83, 0xad, 0x01, 0x79, 0x11, 0x2c, 0xa6, 0xa9, 0x4f, 0x52, 0x12, 0xa6, 0x93, 0x28, 0x3c, 0x8d,
	0xa6, 0x93, 0xf3, 0x0b, 0xb6, 0xeb, 0x1d, 0xdf, 0xc2, 0xc5, 0x4f, 0xe1, 0x6e, 0x91, 0x34, 0x21,
	0x89, 0x5b, 0x63, 0xc6, 0xed, 0x70, 0xe3, 0x4a, 0x33, 0x98, 0x9d, 0xfa, 0x1c, 0xba, 0xd0, 0x41,
	0x14, 0xa6, 0x93, 0x70, 0x11, 0x2d, 0x92, 0x6f, 0x2f, 0x48, 0x3c, 0xc9, 0xa3, 0x27, 0x5b, 0xa8,
	0xc8, 0xce, 0x16, 0xd2, 0xe6, 0x78, 0xbf, 0x46, 0xb0, 0x5e, 0xd2, 0x39, 0x9a, 0x93, 0x73, 0xc5,
	0x6a, 0x94, 0x5b, 0x7d, 0x0f, 0xda, 0x83, 0x45, 0x1c, 0x50, 0x49, 0xe6, 0xcc, 0x9a, 0x9f, 0x8f,
	0xf1, 0x3e, 0x60, 0x19, 0x0c, 0xb9, 0x54, 0x8d, 0x49, 0x19, 0x38, 0x74, 0x2d, 0x9f, 0xcc, 0xa7,
	0x93, 0xf3, 0xe0, 0xd8, 0xad, 0x77, 0x51, 0x6f, 0xd9, 0xcf, 0xc7, 0xde, 0x2f, 0x1d, 0x0d, 0x93,
	0x75, 0x27, 0x8a, 0x98, 0x9c, 0x1b, 0x61, 0x72, 0x6e, 0x84, 0xc9, 0x51, 0x31, 0xe1, 0xf7, 0x60,
	0x49, 0xce, 0x10, 0xc7, 0x6f, 0x83, 0xbb, 0x5a, 0x39, 0x05, 0xd4, 0xcb, 0xaa, 0x20, 0xfe, 0x2a,
	0x2c, 0x8f, 0x16, 0xcf, 0x93, 0xf3, 0x78, 0x32, 0xa7, 0x3a, 0xc4, 0x51, 0xdc, 0xca, 0x66, 0x2a,
	0x2c, 0x36, 0xb7, 0x28, 0xec, 0xfd, 0x03, 0xc1, 0x4a, 0x71, 0x75, 0x2d, 0xba, 0x77, 0xa1, 0x33,
	0x4a, 0x83, 0x38, 0x3d, 0x9b, 0xcc, 0x48, 0xe6, 0x01, 0x49, 0xa0, 0x71, 0xfe, 0x38, 0x1c, 0x33,
	0x1e, 0xb7, 0x5b, 0x0c, 0xe9, 0xbc, 0x01, 0x99, 0x92, 0x94, 0x8c, 0x1f, 0xa5, 0xcc, 0xda, 0x9a,
	0x2f, 0x09, 0xf8, 0xf3, 0xd0, 0x64, 0x7a, 0x85, 0xa5, 0xab, 0x8a, 0xa5, 0x0c, 0x68, 0xc6, 0xc6,
	0x5d, 0x58, 0x3a, 0x8b, 0x17, 0xe1, 0x79, 0xc0, 0x17, 0x6a, 0xb2, 0x0d, 0x57, 0x49, 0x1e, 0x81,
	0x4e, 0x3e, 0x4d, 0x43, 0xbf, 0x07, 0xed, 0x93, 0xd7, 0x21, 0x4d, 0x82, 0x89, 0xeb, 0x74, 0x6b,
	0xbd, 0xfa, 0xfb, 0x8e, 0x8b, 0xfc, 0x9c, 0x86, 0x7b, 0xd0, 0x64, 0xdf, 0xe2, 0x94, 0xac, 0x29,
	0x38, 0x18, 0xc3, 0xcf, 0xf8, 0xde, 0x77, 0x60, 0xad, 0xec, 0x4d, 0x63, 0xc0, 0x60, 0xa8, 0x1f,
	0x45, 0x63, 0x92, 0x1d, 0x54, 0xf6, 0x8d, 0x3d, 0xb8, 0x33, 0x20, 0x49, 0x3a, 0x09, 0x03, 0xbe,
	0x47, 0x54, 0x57, 0xc7, 0x2f, 0xd0, 0xbc, 0x87, 0x00, 0x52, 0x2b, 0xde, 0x82, 0x66, 0x96, 0x30,
	0xb9, 0x2d, 0xd9, 0xc8, 0xfb, 0x06, 0xac, 0x1b, 0x0e, 0x9e, 0x11, 0xc8, 0x06, 0x34, 0x98, 0x40,
	0x86, 0x84, 0x0f, 0xbc, 0x4b, 0x68, 0x8b, 0xfc, 0x6c, 0x83, 0x7f, 0x18, 0x24, 0x2f, 0x05, 0x7c,
	0xfa, 0x4d, 0x57, 0x7a, 0x34, 0x9e, 0x4d, 0x78, 0x68, 0xb7, 0x7d, 0x3e, 0xc0, 0x5f, 0x01, 0x38,
	0x8d, 0x27, 0xaf, 0x26, 0x53, 0xf2, 0x61, 0x9e, 0x1b, 0xd6, 0xe5, 0x0d, 0x90, 0xf3, 0x7c, 0x45,
	0xcc, 0x1b, 0xc2, 0x72, 0x81, 0xc9, 0xce, 0x57, 0x96, 0x0d, 0x33, 0x1c, 0xf9, 0x98, 0x86, 0x50,
	0x2e, 0xc8, 0x00, 0x35, 0x7c, 0x49, 0xf0, 0xfe, 0xd9, 0x82, 0xd6, 0x41, 0x34, 0x9b, 0x05, 0xe1,
	0x18, 0xbf, 0x0b, 0xf5, 0xf4, 0x62, 0xce, 0x57, 0x58, 0x11, 0xb7, 0x56, 0xc6, 0xdc, 0x3f, 0xbb,
	0x98, 0x13, 0x9f, 0xf1, 0xbd, 0x4f, 0x5a, 0x50, 0xa7, 0x43, 0xbc, 0x09, 0x77, 0x0f, 0x62, 0x12,
	0xa4, 0x84, 0xfa, 0x35, 0x13, 0x5c, 0x43, 0x94, 0xcc, 0x63, 0x54, 0x25, 0x3b, 0x78, 0x07, 0x36,
	0xb9, 0xb4, 0x80, 0x26, 0x58, 0x35, 0xbc, 0x0d, 0xeb, 0x83, 0x38, 0x9a, 0x97, 0x19, 0x75, 0xdc,
	0x85, 0x5d, 0x3e, 0xa7, 0x94, 0x69, 0x84, 0x44, 0x03, 0xef, 0xc1, 0x3d, 0x3a, 0xd5, 0xc2, 0x6f,
	0xe2, 0x87, 0xd0, 0x1d, 0x91, 0xd4, 0x9c, 0xe9, 0x85, 0x54, 0x8b, 0xea, 0xf9, 0x60, 0x3e, 0xb6,
	0xeb, 0x69, 0xe3, 0xfb, 0xb0, 0xcd, 0x91, 0xc8, 0x93, 0x2e, 0x98, 0x1d, 0xca, 0xe4, 0x16, 0xeb,
	0x4c, 0x90, 0x36, 0x94, 0x62, 0x4e, 0x48, 0x2c, 0x09, 0x1b, 0x2c, 0xfc, 0x3b, 0xd2, 0xcf, 0x74,
	0xd7, 0x05, 0x79, 0x19, 0xaf, 0xc3, 0x2a, 0x9d, 0xa6, 0x12, 0x57, 0xa8, 0x2c, 0xb7, 0x44, 0x25,
	0xaf, 0x52, 0x0f, 0x8f, 0x48, 0x9a, 0xef, 0xbb, 0x60, 0xac, 0x61, 0x0c, 0x2b, 0xd4, 0x3f, 0x41,
	0x1a, 0x08, 0xda, 0x5d, 0xbc, 0x0b, 0xee, 0x88, 0xa4, 0x2c, 0x40, 0xb5, 0x19, 0x58, 0x6a, 0x50,
	0xb7, 0x77, 0x1d, 0x3f, 0x80, 0x9d, 0xcc, 0x41, 0xca, 0x01, 0x17, 0xec, 0x4d, 0xe6, 0xa2, 0x38,
	0x9a, 0x9b, 0x98, 0x5b, 0x74, 0x49, 0x9f, 0xcc, 0xa2, 0x57, 0xe4, 0x94, 0x48, 0xd0, 0xdb, 0x32,
	0x62, 0x44, 0x09, 0x21, 0x58, 0x6e, 0x31, 0x98, 0x54, 0xd6, 0x0e, 0x65, 0x71, 0x7c, 0x65, 0xd6,
	0x3d, 0xca, 0xe2, 0xfb, 0x54, 0x5e, 0xf0, 0xbe, 0x64, 0x95, 0x67, 0xed, 0xe2, 0x2d, 0xc0, 0x23,
	0x92, 0x96, 0xa7, 0x3c, 0xc0, 0x1b, 0xb0, 0xc6, 0x4c, 0xa2, 0x7b, 0x2e, 0xa8, 0x7b, 0x74, 0x33,
	0x45, 0x62, 0x55, 0xae, 0x18, 0xc1, 0x7f, 0x87, 0x3a, 0xe2, 0x34, 0x5e, 0x84, 0x26, 0x66, 0x97,
	0x99, 0x15, 0xcd, 0x2f, 0x64, 0x0e, 0x13, 0xac, 0xcf, 0xd1, 0x79, 0xdc, 0x47, 0x3a, 0xd3, 0xfb,
	0x42, 0xbb, 0x3d, 0x5e, 0xbb, 0xba, 0xba, 0xba, 0x72, 0xbc, 0x4b, 0xc3, 0x99, 0x64, 0xf9, 0x28,
	0x4a, 0x52, 0x91, 0xa3, 0xe8, 0x37, 0xa5, 0xf9, 0x41, 0x38, 0xce, 0x2a, 0x60, 0xf6, 0xdd, 0xff,
	0x26, 0xb4, 0xce, 0xb3, 0x29, 0xcb, 0x85, 0xe3, 0xef, 0x92, 0x2e, 0xea, 0x2d, 0xf5, 0xb7, 0x33,
	0x62, 0x59, 0x81, 0x2f, 0xa6, 0x79, 0x1f, 0x19, 0xce, 0xbe, 0x76, 0x9f, 0x6c, 0x40, 0xe3, 0x49,
	0x14, 0x9f, 0xf3, 0x74, 0xd4, 0xf6, 0xf9, 0xa0, 0x42, 0xf9, 0x0b, 0x55, 0xb9, 0xb6, 0xbc, 0x54,
	0xfe, 0x17, 0x64, 0x49, 0x31, 0xc6, 0x24, 0x7d, 0x00, 0xab, 0x7a, 0x5d, 0x88, 0xaa, 0x8b, 0xbc,
	0xf2, 0x8c, 0xfe, 0xc0, 0x0a, 0xfa, 0x43, 0xb6, 0xd6, 0x7d, 0xd5, 0x63, 0x25, 0x54, 0x12, 0xf8,
	0xcc, 0x98, 0xff, 0x4c, 0xa8, 0xfb, 0xef, 0x5b, 0x15, 0xbe, 0x54, 0xc1, 0x1b, 0x96, 0x93, 0xea,
	0xfe, 0x85, 0xaa, 0xd3, 0x6a, 0xe5, 0x7d, 0x62, 0x74, 0x9b, 0x73, 0x3b, 0xb7, 0xd1, 0x8a, 0x27,
	0x4b, 0xc9, 0xac, 0xfa, 0x6c, 0xfb, 0x62, 0xd8, 0x7f, 0x66, 0xb5, 0x6f, 0xc2, 0xec, 0xf3, 0x54,
	0x87, 0x9a, 0xe1, 0x4b, 0x43, 0x7f, 0x87, 0xaa, 0x6e, 0x87, 0x4a, 0x33, 0x85, 0xef, 0x1d, 0xc5,
	0xf7, 0x43, 0x2b, 0xb6, 0xef, 0x32, 0x6c, 0x5d, 0xe9, 0xfb, 0xeb, 0x90, 0x7d, 0x8a, 0xae, 0xbf,
	0x97, 0x6e, 0x8d, 0xef, 0xc4, 0x8a, 0xef, 0x7b, 0x0c, 0xdf, 0xbb, 0x59, 0x5d, 0x76, 0x8d, 0x5e,
	0x89, 0xf2, 0xaf, 0x4e, 0xf5, 0xbd, 0x78, 0x5b, 0x84, 0x74, 0xdf, 0x8f, 0xc9, 0x6b, 0x46, 0xce,
	0x3a, 0xba, 0x6c, 0x58, 0x68, 0x11, 0xea, 0xa5, 0xb6, 0x45, 0x2d, 0xf9, 0x1b, 0xc5, 0x36, 0xc4,
	0xd2, 0x3e, 0x34, 0xad, 0x2d, 0x8d, 0x12, 0x79, 0xad, 0x9b, 0x46, 0xde, 0x54, 0x8d, 0xbc, 0x2a,
	0x7f, 0x48, 0xcf, 0xfd, 0x19, 0x59, 0xeb, 0x85, 0x4a, 0xa7, 0x6d, 0x41, 0xb3, 0xd0, 0xa3, 0x66,
	0x23, 0x5a, 0xc5, 0xd1, 0x86, 0x20, 0x49, 0x83, 0xd9, 0x3c, 0x6b, 0x12, 0x24, 0xa1, 0xff, 0xc4,
	0x0a, 0x7d, 0xc6, 0xa0, 0x3f, 0x50, 0x0f, 0x8d, 0x06, 0x48, 0xa2, 0xfe, 0x1b, 0xb2, 0x16, 0x32,
	0x9f, 0x09, 0xb5, 0x07, 0x77, 0x0a, 0x6f, 0x12, 0xfc, 0x4d, 0xa5, 0x40, 0xab, 0xc0, 0x1e, 0xaa,
	0xd8, 0x2d, 0xb0, 0x24, 0xf6, 0x3f, 0xa1, 0xea, 0x3a, 0xeb, 0xd6, 0xb1, 0x9a, 0x97, 0xfe, 0x35,
	0xa5, 0xf4, 0xaf, 0x88, 0x92, 0x48, 0xcf, 0x4f, 0x66, 0x24, 0x7a, 0x7e, 0x7a, 0x3b, 0x88, 0x2b,
	0xf2, 0xd3, 0xbc, 0x9c, 0x9f, 0xae, 0x43, 0xf6, 0x1b, 0x64, 0xa8, 0x39, 0xff, 0xb7, 0x5e, 0xa7,
	0xe2, 0x82, 0xff, 0xbe, 0x5e, 0x5d, 0x28, 0x6a, 0x25, 0x2a, 0xa2, 0x55, 0xbc, 0xc6, 0x3b, 0xf2,
	0xeb, 0x56, 0x45, 0x31, 0x53, 0xb4, 0x29, 0xfd, 0x60, 0x54, 0x73, 0x69, 0xa8, 0xa1, 0x6f, 0x6a,
	0x7b, 0x85, 0x95, 0x89, 0x6a, 0xa5, 0xa6, 0x40, 0xaa, 0xff, 0x23, 0x32, 0x16, 0xeb, 0x34, 0x1c,
	0xa8, 0x7c, 0x28, 0x51, 0xe4, 0xe3, 0x42, 0xa8, 0x38, 0x55, 0x1d, 0x60, 0xad, 0xd4, 0x01, 0x56,
	0x14, 0x14, 0xa9, 0x5a, 0x50, 0x18, 0x00, 0x49, 0xc4, 0x51, 0xb9, 0x89, 0xc0, 0x7b, 0xfc, 0xf1,
	0x95, 0xe1, 0x5c, 0xea, 0x83, 0x7c, 0x01, 0xf5, 0x19, 0xbd, 0xff, 0x35, 0xab, 0xd6, 0x05, 0xd3,
	0xba, 0x21, 0xaf, 0x2a, 0xb9, 0xaa, 0x54, 0xf8, 0x5b, 0x64, 0x6f, 0x51, 0x2a, 0xfd, 0x94, 0x47,
	0xa6, 0xa3, 0x46, 0xe6, 0x53, 0x2b, 0x9a, 0x57, 0x0c, 0xcd, 0x5e, 0x8e, 0xc6, 0xa8, 0x51, 0xe2,
	0xba, 0x30, 0xf4, 0x46, 0xa6, 0xa7, 0x4e, 0x56, 0x8d, 0x3b, 0xb2, 0x1a, 0xaf, 0x88, 0x9a, 0xd7,
	0x7a, 0xd4, 0x18, 0x8b, 0xdf, 0xff, 0xa0, 0x8a, 0x06, 0xcc, 0xfa, 0x2a, 0x67, 0x8b, 0x99, 0x9e,
	0x5e, 0xe5, 0xf1, 0x34, 0xa8, 0x95, 0x72, 0xe2, 0xa9, 0xa6, 0x5e, 0xf1, 0x54, 0xd3, 0xd0, 0x9f,
	0x6a, 0xfa, 0x87, 0x56, 0x8b, 0x2f, 0x98, 0xc5, 0xef, 0x14, 0xee, 0x2c, 0xdd, 0x24, 0x69, 0xf9,
	0xdf, 0x91, 0xb5, 0xb7, 0xfc, 0xff, 0xd9, 0x5d, 0x71, 0x6f, 0xfd, 0xa0, 0x70, 0x6f, 0x99, 0x81,
	0x15, 0x42, 0x46, 0xeb, 0x7d, 0xf3, 0x90, 0x41, 0xda, 0xeb, 0xb8, 0x23, 0x5e, 0xc7, 0x2b, 0x42,
	0xe6, 0x23, 0x35, 0x64, 0xb4, 0xc5, 0xa5, 0xea, 0x3f, 0x20, 0x4b, 0x83, 0x4d, 0x5d, 0x74, 0x78,
	0x76, 0xc6, 0x9f, 0xde, 0xb3, 0x23, 0x24, 0xc6, 0xea, 0xab, 0x3c, 0x87, 0x23, 0x86, 0x79, 0x4b,
	0x59, 0x53, 0x5a, 0x4a, 0x7b, 0x83, 0xf4, 0x43, 0xbd, 0x41, 0x2a, 0xc1, 0x28, 0x5c, 0x47, 0xe6,
	0x7e, 0xff, 0xb3, 0x21, 0xad, 0x40, 0x75, 0x69, 0x6e, 0xdb, 0x8c, 0xa8, 0x3e, 0x45, 0x96, 0xa7,
	0x06, 0xed, 0xc8, 0xab, 0x28, 0x1d, 0x3b, 0xca, 0xda, 0x4d, 0x51, 0xfe, 0x48, 0x45, 0x69, 0x84,
	0xa0, 0x36, 0x97, 0xe6, 0x47, 0x8f, 0x32, 0xc8, 0x0a, 0x75, 0x3f, 0x56, 0xd5, 0x19, 0x17, 0x93,
	0xea, 0x42, 0xcb, 0x43, 0x8a, 0xa6, 0xee, 0xb1, 0x55, 0xdd, 0x15, 0xd2, 0xf5, 0x59, 0xcd, 0x7b,
	0x42, 0x9b, 0x83, 0x64, 0x1e, 0x85, 0x09, 0xa1, 0x2a, 0x4e, 0x9e, 0x31, 0x15, 0x6d, 0xdf, 0x39,
	0x79, 0x46, 0xb3, 0xfd, 0xe3, 0x38, 0x8e, 0xc4, 0x5f, 0x25, 0x3e, 0x90, 0x3f, 0xff, 0x6a, 0xec,
	0x7c, 0xf1, 0x81, 0xf7, 0x7b, 0x64, 0x7a, 0xe6, 0x79, 0x8b, 0x27, 0xc1, 0x7e, 0xd1, 0xfe, 0x84,
	0xdb, 0xeb, 0xe6, 0xb7, 0x8c, 0xd5, 0xb9, 0x63, 0xfd, 0xc9, 0x49, 0xf3, 0xab, 0x3d, 0x2f, 0xfc,
	0x94, 0xeb, 0xd9, 0x52, 0x32, 0x93, 0xb2, 0x90, 0xd4, 0xf2, 0x31, 0xaa, 0x7a, 0xc3, 0x2a, 0xf6,
	0x22, 0xa8, 0xdc, 0x8b, 0x7c, 0xcb, 0xaa, 0xfe, 0x67, 0x48, 0xad, 0x42, 0xed, 0x0a, 0x24, 0x90,
	0xe7, 0xd6, 0xb7, 0xb2, 0x8a, 0x2b, 0xfb, 0xe7, 0x48, 0xcd, 0xbf, 0x96, 0xf9, 0x05, 0x63, 0xcd,
	0x6f, 0x6e, 0xda, 0x21, 0x96, 0xbf, 0x13, 0x1c, 0xf5, 0x77, 0x42, 0x45, 0x20, 0xff, 0xa2, 0x10,
	0xc8, 0x46, 0x2d, 0x12, 0xc8, 0xaf, 0x90, 0xf5, 0x85, 0xef, 0xc6, 0x50, 0xec, 0x5e, 0xf9, 0xb8,
	0xe0, 0x15, 0x8b, 0x9e, 0x1c, 0xcc, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x7d, 0xe7, 0x5c, 0xdb,
	0x59, 0x1f, 0x00, 0x00,
}
