// Code generated by "stringer -type=FieldType"; DO NOT EDIT.

package models

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[Integer-0]
	_ = x[Float-1]
	_ = x[Boolean-2]
	_ = x[String-3]
	_ = x[Empty-4]
	_ = x[Unsigned-5]
}

const _FieldType_name = "IntegerFloatBooleanStringEmptyUnsigned"

var _FieldType_index = [...]uint8{0, 7, 12, 19, 25, 30, 38}

func (i FieldType) String() string {
	if i < 0 || i >= FieldType(len(_FieldType_index)-1) {
		return "FieldType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _FieldType_name[_FieldType_index[i]:_FieldType_index[i+1]]
}
