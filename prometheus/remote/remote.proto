// This file is copied (except for package name) from https://github.com/prometheus/prometheus/blob/master/storage/remote/remote.proto

// Copyright 2016 Prometheus Team
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package remote;

message Sample {
  double value       = 1;
  int64 timestamp_ms = 2;
}

message LabelPair {
  string name  = 1;
  string value = 2;
}

message TimeSeries {
  repeated LabelPair labels = 1;
  // Sorted by time, oldest sample first.
  repeated Sample samples   = 2;
}

message WriteRequest {
  repeated TimeSeries timeseries = 1;
}

message ReadRequest {
  repeated Query queries = 1;
}

message ReadResponse {
  // In same order as the request's queries.
  repeated QueryResult results = 1;
}

message Query {
  int64 start_timestamp_ms = 1;
  int64 end_timestamp_ms = 2;
  repeated LabelMatcher matchers = 3;
}

enum MatchType {
  EQUAL = 0;
  NOT_EQUAL = 1;
  REGEX_MATCH = 2;
  REGEX_NO_MATCH = 3;
}

message LabelMatcher {
  MatchType type = 1;
  string name = 2;
  string value = 3;
}

message QueryResult {
  repeated TimeSeries timeseries = 1;
}