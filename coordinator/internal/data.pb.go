// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: internal/data.proto

package internal

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type WriteShardRequest struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	Points               [][]byte `protobuf:"bytes,2,rep,name=Points" json:"Points,omitempty"`
	Database             *string  `protobuf:"bytes,3,opt,name=Database" json:"Database,omitempty"`
	RetentionPolicy      *string  `protobuf:"bytes,4,opt,name=RetentionPolicy" json:"RetentionPolicy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteShardRequest) Reset()         { *m = WriteShardRequest{} }
func (m *WriteShardRequest) String() string { return proto.CompactTextString(m) }
func (*WriteShardRequest) ProtoMessage()    {}
func (*WriteShardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{0}
}
func (m *WriteShardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteShardRequest.Unmarshal(m, b)
}
func (m *WriteShardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteShardRequest.Marshal(b, m, deterministic)
}
func (m *WriteShardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteShardRequest.Merge(m, src)
}
func (m *WriteShardRequest) XXX_Size() int {
	return xxx_messageInfo_WriteShardRequest.Size(m)
}
func (m *WriteShardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteShardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_WriteShardRequest proto.InternalMessageInfo

func (m *WriteShardRequest) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *WriteShardRequest) GetPoints() [][]byte {
	if m != nil {
		return m.Points
	}
	return nil
}

func (m *WriteShardRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *WriteShardRequest) GetRetentionPolicy() string {
	if m != nil && m.RetentionPolicy != nil {
		return *m.RetentionPolicy
	}
	return ""
}

type WriteShardResponse struct {
	Code                 *int32   `protobuf:"varint,1,req,name=Code" json:"Code,omitempty"`
	Message              *string  `protobuf:"bytes,2,opt,name=Message" json:"Message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteShardResponse) Reset()         { *m = WriteShardResponse{} }
func (m *WriteShardResponse) String() string { return proto.CompactTextString(m) }
func (*WriteShardResponse) ProtoMessage()    {}
func (*WriteShardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{1}
}
func (m *WriteShardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteShardResponse.Unmarshal(m, b)
}
func (m *WriteShardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteShardResponse.Marshal(b, m, deterministic)
}
func (m *WriteShardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteShardResponse.Merge(m, src)
}
func (m *WriteShardResponse) XXX_Size() int {
	return xxx_messageInfo_WriteShardResponse.Size(m)
}
func (m *WriteShardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteShardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_WriteShardResponse proto.InternalMessageInfo

func (m *WriteShardResponse) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *WriteShardResponse) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type ExecuteStatementRequest struct {
	Statement            *string  `protobuf:"bytes,1,req,name=Statement" json:"Statement,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExecuteStatementRequest) Reset()         { *m = ExecuteStatementRequest{} }
func (m *ExecuteStatementRequest) String() string { return proto.CompactTextString(m) }
func (*ExecuteStatementRequest) ProtoMessage()    {}
func (*ExecuteStatementRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{2}
}
func (m *ExecuteStatementRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExecuteStatementRequest.Unmarshal(m, b)
}
func (m *ExecuteStatementRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExecuteStatementRequest.Marshal(b, m, deterministic)
}
func (m *ExecuteStatementRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecuteStatementRequest.Merge(m, src)
}
func (m *ExecuteStatementRequest) XXX_Size() int {
	return xxx_messageInfo_ExecuteStatementRequest.Size(m)
}
func (m *ExecuteStatementRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecuteStatementRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExecuteStatementRequest proto.InternalMessageInfo

func (m *ExecuteStatementRequest) GetStatement() string {
	if m != nil && m.Statement != nil {
		return *m.Statement
	}
	return ""
}

func (m *ExecuteStatementRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

type ExecuteStatementResponse struct {
	Code                 *int32   `protobuf:"varint,1,req,name=Code" json:"Code,omitempty"`
	Message              *string  `protobuf:"bytes,2,opt,name=Message" json:"Message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExecuteStatementResponse) Reset()         { *m = ExecuteStatementResponse{} }
func (m *ExecuteStatementResponse) String() string { return proto.CompactTextString(m) }
func (*ExecuteStatementResponse) ProtoMessage()    {}
func (*ExecuteStatementResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{3}
}
func (m *ExecuteStatementResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExecuteStatementResponse.Unmarshal(m, b)
}
func (m *ExecuteStatementResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExecuteStatementResponse.Marshal(b, m, deterministic)
}
func (m *ExecuteStatementResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExecuteStatementResponse.Merge(m, src)
}
func (m *ExecuteStatementResponse) XXX_Size() int {
	return xxx_messageInfo_ExecuteStatementResponse.Size(m)
}
func (m *ExecuteStatementResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExecuteStatementResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExecuteStatementResponse proto.InternalMessageInfo

func (m *ExecuteStatementResponse) GetCode() int32 {
	if m != nil && m.Code != nil {
		return *m.Code
	}
	return 0
}

func (m *ExecuteStatementResponse) GetMessage() string {
	if m != nil && m.Message != nil {
		return *m.Message
	}
	return ""
}

type TaskManagerStatementRequest struct {
	Statement            *string  `protobuf:"bytes,1,req,name=Statement" json:"Statement,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskManagerStatementRequest) Reset()         { *m = TaskManagerStatementRequest{} }
func (m *TaskManagerStatementRequest) String() string { return proto.CompactTextString(m) }
func (*TaskManagerStatementRequest) ProtoMessage()    {}
func (*TaskManagerStatementRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{4}
}
func (m *TaskManagerStatementRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskManagerStatementRequest.Unmarshal(m, b)
}
func (m *TaskManagerStatementRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskManagerStatementRequest.Marshal(b, m, deterministic)
}
func (m *TaskManagerStatementRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskManagerStatementRequest.Merge(m, src)
}
func (m *TaskManagerStatementRequest) XXX_Size() int {
	return xxx_messageInfo_TaskManagerStatementRequest.Size(m)
}
func (m *TaskManagerStatementRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskManagerStatementRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TaskManagerStatementRequest proto.InternalMessageInfo

func (m *TaskManagerStatementRequest) GetStatement() string {
	if m != nil && m.Statement != nil {
		return *m.Statement
	}
	return ""
}

type TaskManagerStatementResponse struct {
	Result               []byte   `protobuf:"bytes,1,req,name=Result" json:"Result,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaskManagerStatementResponse) Reset()         { *m = TaskManagerStatementResponse{} }
func (m *TaskManagerStatementResponse) String() string { return proto.CompactTextString(m) }
func (*TaskManagerStatementResponse) ProtoMessage()    {}
func (*TaskManagerStatementResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{5}
}
func (m *TaskManagerStatementResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaskManagerStatementResponse.Unmarshal(m, b)
}
func (m *TaskManagerStatementResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaskManagerStatementResponse.Marshal(b, m, deterministic)
}
func (m *TaskManagerStatementResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaskManagerStatementResponse.Merge(m, src)
}
func (m *TaskManagerStatementResponse) XXX_Size() int {
	return xxx_messageInfo_TaskManagerStatementResponse.Size(m)
}
func (m *TaskManagerStatementResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TaskManagerStatementResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TaskManagerStatementResponse proto.InternalMessageInfo

func (m *TaskManagerStatementResponse) GetResult() []byte {
	if m != nil {
		return m.Result
	}
	return nil
}

func (m *TaskManagerStatementResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type MeasurementNamesRequest struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	Condition            *string  `protobuf:"bytes,2,opt,name=Condition" json:"Condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementNamesRequest) Reset()         { *m = MeasurementNamesRequest{} }
func (m *MeasurementNamesRequest) String() string { return proto.CompactTextString(m) }
func (*MeasurementNamesRequest) ProtoMessage()    {}
func (*MeasurementNamesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{6}
}
func (m *MeasurementNamesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementNamesRequest.Unmarshal(m, b)
}
func (m *MeasurementNamesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementNamesRequest.Marshal(b, m, deterministic)
}
func (m *MeasurementNamesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementNamesRequest.Merge(m, src)
}
func (m *MeasurementNamesRequest) XXX_Size() int {
	return xxx_messageInfo_MeasurementNamesRequest.Size(m)
}
func (m *MeasurementNamesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementNamesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementNamesRequest proto.InternalMessageInfo

func (m *MeasurementNamesRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *MeasurementNamesRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

type MeasurementNamesResponse struct {
	Names                [][]byte `protobuf:"bytes,1,rep,name=Names" json:"Names,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementNamesResponse) Reset()         { *m = MeasurementNamesResponse{} }
func (m *MeasurementNamesResponse) String() string { return proto.CompactTextString(m) }
func (*MeasurementNamesResponse) ProtoMessage()    {}
func (*MeasurementNamesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{7}
}
func (m *MeasurementNamesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementNamesResponse.Unmarshal(m, b)
}
func (m *MeasurementNamesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementNamesResponse.Marshal(b, m, deterministic)
}
func (m *MeasurementNamesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementNamesResponse.Merge(m, src)
}
func (m *MeasurementNamesResponse) XXX_Size() int {
	return xxx_messageInfo_MeasurementNamesResponse.Size(m)
}
func (m *MeasurementNamesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementNamesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementNamesResponse proto.InternalMessageInfo

func (m *MeasurementNamesResponse) GetNames() [][]byte {
	if m != nil {
		return m.Names
	}
	return nil
}

func (m *MeasurementNamesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type TagKeysRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Condition            *string  `protobuf:"bytes,2,opt,name=Condition" json:"Condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagKeysRequest) Reset()         { *m = TagKeysRequest{} }
func (m *TagKeysRequest) String() string { return proto.CompactTextString(m) }
func (*TagKeysRequest) ProtoMessage()    {}
func (*TagKeysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{8}
}
func (m *TagKeysRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagKeysRequest.Unmarshal(m, b)
}
func (m *TagKeysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagKeysRequest.Marshal(b, m, deterministic)
}
func (m *TagKeysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagKeysRequest.Merge(m, src)
}
func (m *TagKeysRequest) XXX_Size() int {
	return xxx_messageInfo_TagKeysRequest.Size(m)
}
func (m *TagKeysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagKeysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagKeysRequest proto.InternalMessageInfo

func (m *TagKeysRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *TagKeysRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

type TagKeysResponse struct {
	TagKeys              []byte   `protobuf:"bytes,1,opt,name=TagKeys" json:"TagKeys,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagKeysResponse) Reset()         { *m = TagKeysResponse{} }
func (m *TagKeysResponse) String() string { return proto.CompactTextString(m) }
func (*TagKeysResponse) ProtoMessage()    {}
func (*TagKeysResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{9}
}
func (m *TagKeysResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagKeysResponse.Unmarshal(m, b)
}
func (m *TagKeysResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagKeysResponse.Marshal(b, m, deterministic)
}
func (m *TagKeysResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagKeysResponse.Merge(m, src)
}
func (m *TagKeysResponse) XXX_Size() int {
	return xxx_messageInfo_TagKeysResponse.Size(m)
}
func (m *TagKeysResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TagKeysResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TagKeysResponse proto.InternalMessageInfo

func (m *TagKeysResponse) GetTagKeys() []byte {
	if m != nil {
		return m.TagKeys
	}
	return nil
}

func (m *TagKeysResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type TagValuesRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Condition            *string  `protobuf:"bytes,2,opt,name=Condition" json:"Condition,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagValuesRequest) Reset()         { *m = TagValuesRequest{} }
func (m *TagValuesRequest) String() string { return proto.CompactTextString(m) }
func (*TagValuesRequest) ProtoMessage()    {}
func (*TagValuesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{10}
}
func (m *TagValuesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagValuesRequest.Unmarshal(m, b)
}
func (m *TagValuesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagValuesRequest.Marshal(b, m, deterministic)
}
func (m *TagValuesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagValuesRequest.Merge(m, src)
}
func (m *TagValuesRequest) XXX_Size() int {
	return xxx_messageInfo_TagValuesRequest.Size(m)
}
func (m *TagValuesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagValuesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagValuesRequest proto.InternalMessageInfo

func (m *TagValuesRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *TagValuesRequest) GetCondition() string {
	if m != nil && m.Condition != nil {
		return *m.Condition
	}
	return ""
}

type TagValuesResponse struct {
	TagValues            []byte   `protobuf:"bytes,1,opt,name=TagValues" json:"TagValues,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TagValuesResponse) Reset()         { *m = TagValuesResponse{} }
func (m *TagValuesResponse) String() string { return proto.CompactTextString(m) }
func (*TagValuesResponse) ProtoMessage()    {}
func (*TagValuesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{11}
}
func (m *TagValuesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagValuesResponse.Unmarshal(m, b)
}
func (m *TagValuesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagValuesResponse.Marshal(b, m, deterministic)
}
func (m *TagValuesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagValuesResponse.Merge(m, src)
}
func (m *TagValuesResponse) XXX_Size() int {
	return xxx_messageInfo_TagValuesResponse.Size(m)
}
func (m *TagValuesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TagValuesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TagValuesResponse proto.InternalMessageInfo

func (m *TagValuesResponse) GetTagValues() []byte {
	if m != nil {
		return m.TagValues
	}
	return nil
}

func (m *TagValuesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type SeriesSketchesRequest struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeriesSketchesRequest) Reset()         { *m = SeriesSketchesRequest{} }
func (m *SeriesSketchesRequest) String() string { return proto.CompactTextString(m) }
func (*SeriesSketchesRequest) ProtoMessage()    {}
func (*SeriesSketchesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{12}
}
func (m *SeriesSketchesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeriesSketchesRequest.Unmarshal(m, b)
}
func (m *SeriesSketchesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeriesSketchesRequest.Marshal(b, m, deterministic)
}
func (m *SeriesSketchesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesSketchesRequest.Merge(m, src)
}
func (m *SeriesSketchesRequest) XXX_Size() int {
	return xxx_messageInfo_SeriesSketchesRequest.Size(m)
}
func (m *SeriesSketchesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesSketchesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesSketchesRequest proto.InternalMessageInfo

func (m *SeriesSketchesRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

type SeriesSketchesResponse struct {
	Sketch               []byte   `protobuf:"bytes,1,req,name=Sketch" json:"Sketch,omitempty"`
	TSSketch             []byte   `protobuf:"bytes,2,req,name=TSSketch" json:"TSSketch,omitempty"`
	Err                  *string  `protobuf:"bytes,3,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeriesSketchesResponse) Reset()         { *m = SeriesSketchesResponse{} }
func (m *SeriesSketchesResponse) String() string { return proto.CompactTextString(m) }
func (*SeriesSketchesResponse) ProtoMessage()    {}
func (*SeriesSketchesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{13}
}
func (m *SeriesSketchesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeriesSketchesResponse.Unmarshal(m, b)
}
func (m *SeriesSketchesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeriesSketchesResponse.Marshal(b, m, deterministic)
}
func (m *SeriesSketchesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeriesSketchesResponse.Merge(m, src)
}
func (m *SeriesSketchesResponse) XXX_Size() int {
	return xxx_messageInfo_SeriesSketchesResponse.Size(m)
}
func (m *SeriesSketchesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SeriesSketchesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SeriesSketchesResponse proto.InternalMessageInfo

func (m *SeriesSketchesResponse) GetSketch() []byte {
	if m != nil {
		return m.Sketch
	}
	return nil
}

func (m *SeriesSketchesResponse) GetTSSketch() []byte {
	if m != nil {
		return m.TSSketch
	}
	return nil
}

func (m *SeriesSketchesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type MeasurementsSketchesRequest struct {
	Database             *string  `protobuf:"bytes,1,req,name=Database" json:"Database,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementsSketchesRequest) Reset()         { *m = MeasurementsSketchesRequest{} }
func (m *MeasurementsSketchesRequest) String() string { return proto.CompactTextString(m) }
func (*MeasurementsSketchesRequest) ProtoMessage()    {}
func (*MeasurementsSketchesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{14}
}
func (m *MeasurementsSketchesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementsSketchesRequest.Unmarshal(m, b)
}
func (m *MeasurementsSketchesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementsSketchesRequest.Marshal(b, m, deterministic)
}
func (m *MeasurementsSketchesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementsSketchesRequest.Merge(m, src)
}
func (m *MeasurementsSketchesRequest) XXX_Size() int {
	return xxx_messageInfo_MeasurementsSketchesRequest.Size(m)
}
func (m *MeasurementsSketchesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementsSketchesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementsSketchesRequest proto.InternalMessageInfo

func (m *MeasurementsSketchesRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

type MeasurementsSketchesResponse struct {
	Sketch               []byte   `protobuf:"bytes,1,req,name=Sketch" json:"Sketch,omitempty"`
	TSSketch             []byte   `protobuf:"bytes,2,req,name=TSSketch" json:"TSSketch,omitempty"`
	Err                  *string  `protobuf:"bytes,3,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementsSketchesResponse) Reset()         { *m = MeasurementsSketchesResponse{} }
func (m *MeasurementsSketchesResponse) String() string { return proto.CompactTextString(m) }
func (*MeasurementsSketchesResponse) ProtoMessage()    {}
func (*MeasurementsSketchesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{15}
}
func (m *MeasurementsSketchesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementsSketchesResponse.Unmarshal(m, b)
}
func (m *MeasurementsSketchesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementsSketchesResponse.Marshal(b, m, deterministic)
}
func (m *MeasurementsSketchesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementsSketchesResponse.Merge(m, src)
}
func (m *MeasurementsSketchesResponse) XXX_Size() int {
	return xxx_messageInfo_MeasurementsSketchesResponse.Size(m)
}
func (m *MeasurementsSketchesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementsSketchesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementsSketchesResponse proto.InternalMessageInfo

func (m *MeasurementsSketchesResponse) GetSketch() []byte {
	if m != nil {
		return m.Sketch
	}
	return nil
}

func (m *MeasurementsSketchesResponse) GetTSSketch() []byte {
	if m != nil {
		return m.TSSketch
	}
	return nil
}

func (m *MeasurementsSketchesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type StoreReadFilterRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Request              []byte   `protobuf:"bytes,2,req,name=Request" json:"Request,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreReadFilterRequest) Reset()         { *m = StoreReadFilterRequest{} }
func (m *StoreReadFilterRequest) String() string { return proto.CompactTextString(m) }
func (*StoreReadFilterRequest) ProtoMessage()    {}
func (*StoreReadFilterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{16}
}
func (m *StoreReadFilterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreReadFilterRequest.Unmarshal(m, b)
}
func (m *StoreReadFilterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreReadFilterRequest.Marshal(b, m, deterministic)
}
func (m *StoreReadFilterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreReadFilterRequest.Merge(m, src)
}
func (m *StoreReadFilterRequest) XXX_Size() int {
	return xxx_messageInfo_StoreReadFilterRequest.Size(m)
}
func (m *StoreReadFilterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreReadFilterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreReadFilterRequest proto.InternalMessageInfo

func (m *StoreReadFilterRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *StoreReadFilterRequest) GetRequest() []byte {
	if m != nil {
		return m.Request
	}
	return nil
}

type StoreReadFilterResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreReadFilterResponse) Reset()         { *m = StoreReadFilterResponse{} }
func (m *StoreReadFilterResponse) String() string { return proto.CompactTextString(m) }
func (*StoreReadFilterResponse) ProtoMessage()    {}
func (*StoreReadFilterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{17}
}
func (m *StoreReadFilterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreReadFilterResponse.Unmarshal(m, b)
}
func (m *StoreReadFilterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreReadFilterResponse.Marshal(b, m, deterministic)
}
func (m *StoreReadFilterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreReadFilterResponse.Merge(m, src)
}
func (m *StoreReadFilterResponse) XXX_Size() int {
	return xxx_messageInfo_StoreReadFilterResponse.Size(m)
}
func (m *StoreReadFilterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreReadFilterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreReadFilterResponse proto.InternalMessageInfo

func (m *StoreReadFilterResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type StoreReadGroupRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Request              []byte   `protobuf:"bytes,2,req,name=Request" json:"Request,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreReadGroupRequest) Reset()         { *m = StoreReadGroupRequest{} }
func (m *StoreReadGroupRequest) String() string { return proto.CompactTextString(m) }
func (*StoreReadGroupRequest) ProtoMessage()    {}
func (*StoreReadGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{18}
}
func (m *StoreReadGroupRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreReadGroupRequest.Unmarshal(m, b)
}
func (m *StoreReadGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreReadGroupRequest.Marshal(b, m, deterministic)
}
func (m *StoreReadGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreReadGroupRequest.Merge(m, src)
}
func (m *StoreReadGroupRequest) XXX_Size() int {
	return xxx_messageInfo_StoreReadGroupRequest.Size(m)
}
func (m *StoreReadGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreReadGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_StoreReadGroupRequest proto.InternalMessageInfo

func (m *StoreReadGroupRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *StoreReadGroupRequest) GetRequest() []byte {
	if m != nil {
		return m.Request
	}
	return nil
}

type StoreReadGroupResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StoreReadGroupResponse) Reset()         { *m = StoreReadGroupResponse{} }
func (m *StoreReadGroupResponse) String() string { return proto.CompactTextString(m) }
func (*StoreReadGroupResponse) ProtoMessage()    {}
func (*StoreReadGroupResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{19}
}
func (m *StoreReadGroupResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StoreReadGroupResponse.Unmarshal(m, b)
}
func (m *StoreReadGroupResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StoreReadGroupResponse.Marshal(b, m, deterministic)
}
func (m *StoreReadGroupResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StoreReadGroupResponse.Merge(m, src)
}
func (m *StoreReadGroupResponse) XXX_Size() int {
	return xxx_messageInfo_StoreReadGroupResponse.Size(m)
}
func (m *StoreReadGroupResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StoreReadGroupResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StoreReadGroupResponse proto.InternalMessageInfo

func (m *StoreReadGroupResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type CreateIteratorRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Measurement          []byte   `protobuf:"bytes,2,req,name=Measurement" json:"Measurement,omitempty"`
	Opt                  []byte   `protobuf:"bytes,3,req,name=Opt" json:"Opt,omitempty"`
	SpanContext          []byte   `protobuf:"bytes,4,opt,name=SpanContext" json:"SpanContext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateIteratorRequest) Reset()         { *m = CreateIteratorRequest{} }
func (m *CreateIteratorRequest) String() string { return proto.CompactTextString(m) }
func (*CreateIteratorRequest) ProtoMessage()    {}
func (*CreateIteratorRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{20}
}
func (m *CreateIteratorRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIteratorRequest.Unmarshal(m, b)
}
func (m *CreateIteratorRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIteratorRequest.Marshal(b, m, deterministic)
}
func (m *CreateIteratorRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIteratorRequest.Merge(m, src)
}
func (m *CreateIteratorRequest) XXX_Size() int {
	return xxx_messageInfo_CreateIteratorRequest.Size(m)
}
func (m *CreateIteratorRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIteratorRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIteratorRequest proto.InternalMessageInfo

func (m *CreateIteratorRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *CreateIteratorRequest) GetMeasurement() []byte {
	if m != nil {
		return m.Measurement
	}
	return nil
}

func (m *CreateIteratorRequest) GetOpt() []byte {
	if m != nil {
		return m.Opt
	}
	return nil
}

func (m *CreateIteratorRequest) GetSpanContext() []byte {
	if m != nil {
		return m.SpanContext
	}
	return nil
}

type CreateIteratorResponse struct {
	Err                  *string        `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	Type                 *int32         `protobuf:"varint,2,req,name=Type" json:"Type,omitempty"`
	Stats                *IteratorStats `protobuf:"bytes,3,opt,name=Stats" json:"Stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *CreateIteratorResponse) Reset()         { *m = CreateIteratorResponse{} }
func (m *CreateIteratorResponse) String() string { return proto.CompactTextString(m) }
func (*CreateIteratorResponse) ProtoMessage()    {}
func (*CreateIteratorResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{21}
}
func (m *CreateIteratorResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateIteratorResponse.Unmarshal(m, b)
}
func (m *CreateIteratorResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateIteratorResponse.Marshal(b, m, deterministic)
}
func (m *CreateIteratorResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateIteratorResponse.Merge(m, src)
}
func (m *CreateIteratorResponse) XXX_Size() int {
	return xxx_messageInfo_CreateIteratorResponse.Size(m)
}
func (m *CreateIteratorResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateIteratorResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateIteratorResponse proto.InternalMessageInfo

func (m *CreateIteratorResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func (m *CreateIteratorResponse) GetType() int32 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

func (m *CreateIteratorResponse) GetStats() *IteratorStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type IteratorStats struct {
	SeriesN              *int64   `protobuf:"varint,1,opt,name=SeriesN" json:"SeriesN,omitempty"`
	PointN               *int64   `protobuf:"varint,2,opt,name=PointN" json:"PointN,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IteratorStats) Reset()         { *m = IteratorStats{} }
func (m *IteratorStats) String() string { return proto.CompactTextString(m) }
func (*IteratorStats) ProtoMessage()    {}
func (*IteratorStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{22}
}
func (m *IteratorStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IteratorStats.Unmarshal(m, b)
}
func (m *IteratorStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IteratorStats.Marshal(b, m, deterministic)
}
func (m *IteratorStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IteratorStats.Merge(m, src)
}
func (m *IteratorStats) XXX_Size() int {
	return xxx_messageInfo_IteratorStats.Size(m)
}
func (m *IteratorStats) XXX_DiscardUnknown() {
	xxx_messageInfo_IteratorStats.DiscardUnknown(m)
}

var xxx_messageInfo_IteratorStats proto.InternalMessageInfo

func (m *IteratorStats) GetSeriesN() int64 {
	if m != nil && m.SeriesN != nil {
		return *m.SeriesN
	}
	return 0
}

func (m *IteratorStats) GetPointN() int64 {
	if m != nil && m.PointN != nil {
		return *m.PointN
	}
	return 0
}

type IteratorCostRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Measurement          []byte   `protobuf:"bytes,2,req,name=Measurement" json:"Measurement,omitempty"`
	Opt                  []byte   `protobuf:"bytes,3,req,name=Opt" json:"Opt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IteratorCostRequest) Reset()         { *m = IteratorCostRequest{} }
func (m *IteratorCostRequest) String() string { return proto.CompactTextString(m) }
func (*IteratorCostRequest) ProtoMessage()    {}
func (*IteratorCostRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{23}
}
func (m *IteratorCostRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IteratorCostRequest.Unmarshal(m, b)
}
func (m *IteratorCostRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IteratorCostRequest.Marshal(b, m, deterministic)
}
func (m *IteratorCostRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IteratorCostRequest.Merge(m, src)
}
func (m *IteratorCostRequest) XXX_Size() int {
	return xxx_messageInfo_IteratorCostRequest.Size(m)
}
func (m *IteratorCostRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_IteratorCostRequest.DiscardUnknown(m)
}

var xxx_messageInfo_IteratorCostRequest proto.InternalMessageInfo

func (m *IteratorCostRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *IteratorCostRequest) GetMeasurement() []byte {
	if m != nil {
		return m.Measurement
	}
	return nil
}

func (m *IteratorCostRequest) GetOpt() []byte {
	if m != nil {
		return m.Opt
	}
	return nil
}

type IteratorCostResponse struct {
	Err                  *string       `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	Cost                 *IteratorCost `protobuf:"bytes,2,opt,name=Cost" json:"Cost,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *IteratorCostResponse) Reset()         { *m = IteratorCostResponse{} }
func (m *IteratorCostResponse) String() string { return proto.CompactTextString(m) }
func (*IteratorCostResponse) ProtoMessage()    {}
func (*IteratorCostResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{24}
}
func (m *IteratorCostResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IteratorCostResponse.Unmarshal(m, b)
}
func (m *IteratorCostResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IteratorCostResponse.Marshal(b, m, deterministic)
}
func (m *IteratorCostResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IteratorCostResponse.Merge(m, src)
}
func (m *IteratorCostResponse) XXX_Size() int {
	return xxx_messageInfo_IteratorCostResponse.Size(m)
}
func (m *IteratorCostResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_IteratorCostResponse.DiscardUnknown(m)
}

var xxx_messageInfo_IteratorCostResponse proto.InternalMessageInfo

func (m *IteratorCostResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func (m *IteratorCostResponse) GetCost() *IteratorCost {
	if m != nil {
		return m.Cost
	}
	return nil
}

type IteratorCost struct {
	NumShards            *int64   `protobuf:"varint,1,opt,name=NumShards" json:"NumShards,omitempty"`
	NumSeries            *int64   `protobuf:"varint,2,opt,name=NumSeries" json:"NumSeries,omitempty"`
	CachedValues         *int64   `protobuf:"varint,3,opt,name=CachedValues" json:"CachedValues,omitempty"`
	NumFiles             *int64   `protobuf:"varint,4,opt,name=NumFiles" json:"NumFiles,omitempty"`
	BlocksRead           *int64   `protobuf:"varint,5,opt,name=BlocksRead" json:"BlocksRead,omitempty"`
	BlockSize            *int64   `protobuf:"varint,6,opt,name=BlockSize" json:"BlockSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IteratorCost) Reset()         { *m = IteratorCost{} }
func (m *IteratorCost) String() string { return proto.CompactTextString(m) }
func (*IteratorCost) ProtoMessage()    {}
func (*IteratorCost) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{25}
}
func (m *IteratorCost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IteratorCost.Unmarshal(m, b)
}
func (m *IteratorCost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IteratorCost.Marshal(b, m, deterministic)
}
func (m *IteratorCost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IteratorCost.Merge(m, src)
}
func (m *IteratorCost) XXX_Size() int {
	return xxx_messageInfo_IteratorCost.Size(m)
}
func (m *IteratorCost) XXX_DiscardUnknown() {
	xxx_messageInfo_IteratorCost.DiscardUnknown(m)
}

var xxx_messageInfo_IteratorCost proto.InternalMessageInfo

func (m *IteratorCost) GetNumShards() int64 {
	if m != nil && m.NumShards != nil {
		return *m.NumShards
	}
	return 0
}

func (m *IteratorCost) GetNumSeries() int64 {
	if m != nil && m.NumSeries != nil {
		return *m.NumSeries
	}
	return 0
}

func (m *IteratorCost) GetCachedValues() int64 {
	if m != nil && m.CachedValues != nil {
		return *m.CachedValues
	}
	return 0
}

func (m *IteratorCost) GetNumFiles() int64 {
	if m != nil && m.NumFiles != nil {
		return *m.NumFiles
	}
	return 0
}

func (m *IteratorCost) GetBlocksRead() int64 {
	if m != nil && m.BlocksRead != nil {
		return *m.BlocksRead
	}
	return 0
}

func (m *IteratorCost) GetBlockSize() int64 {
	if m != nil && m.BlockSize != nil {
		return *m.BlockSize
	}
	return 0
}

type FieldDimensionsRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Measurement          []byte   `protobuf:"bytes,2,req,name=Measurement" json:"Measurement,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FieldDimensionsRequest) Reset()         { *m = FieldDimensionsRequest{} }
func (m *FieldDimensionsRequest) String() string { return proto.CompactTextString(m) }
func (*FieldDimensionsRequest) ProtoMessage()    {}
func (*FieldDimensionsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{26}
}
func (m *FieldDimensionsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldDimensionsRequest.Unmarshal(m, b)
}
func (m *FieldDimensionsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldDimensionsRequest.Marshal(b, m, deterministic)
}
func (m *FieldDimensionsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldDimensionsRequest.Merge(m, src)
}
func (m *FieldDimensionsRequest) XXX_Size() int {
	return xxx_messageInfo_FieldDimensionsRequest.Size(m)
}
func (m *FieldDimensionsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldDimensionsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_FieldDimensionsRequest proto.InternalMessageInfo

func (m *FieldDimensionsRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *FieldDimensionsRequest) GetMeasurement() []byte {
	if m != nil {
		return m.Measurement
	}
	return nil
}

type FieldDimensionsResponse struct {
	Fields               []byte   `protobuf:"bytes,1,req,name=Fields" json:"Fields,omitempty"`
	Dimensions           []string `protobuf:"bytes,2,rep,name=Dimensions" json:"Dimensions,omitempty"`
	Err                  *string  `protobuf:"bytes,3,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FieldDimensionsResponse) Reset()         { *m = FieldDimensionsResponse{} }
func (m *FieldDimensionsResponse) String() string { return proto.CompactTextString(m) }
func (*FieldDimensionsResponse) ProtoMessage()    {}
func (*FieldDimensionsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{27}
}
func (m *FieldDimensionsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldDimensionsResponse.Unmarshal(m, b)
}
func (m *FieldDimensionsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldDimensionsResponse.Marshal(b, m, deterministic)
}
func (m *FieldDimensionsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldDimensionsResponse.Merge(m, src)
}
func (m *FieldDimensionsResponse) XXX_Size() int {
	return xxx_messageInfo_FieldDimensionsResponse.Size(m)
}
func (m *FieldDimensionsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldDimensionsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_FieldDimensionsResponse proto.InternalMessageInfo

func (m *FieldDimensionsResponse) GetFields() []byte {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *FieldDimensionsResponse) GetDimensions() []string {
	if m != nil {
		return m.Dimensions
	}
	return nil
}

func (m *FieldDimensionsResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type MapTypeRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Measurement          []byte   `protobuf:"bytes,2,req,name=Measurement" json:"Measurement,omitempty"`
	Field                *string  `protobuf:"bytes,3,req,name=Field" json:"Field,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MapTypeRequest) Reset()         { *m = MapTypeRequest{} }
func (m *MapTypeRequest) String() string { return proto.CompactTextString(m) }
func (*MapTypeRequest) ProtoMessage()    {}
func (*MapTypeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{28}
}
func (m *MapTypeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MapTypeRequest.Unmarshal(m, b)
}
func (m *MapTypeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MapTypeRequest.Marshal(b, m, deterministic)
}
func (m *MapTypeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MapTypeRequest.Merge(m, src)
}
func (m *MapTypeRequest) XXX_Size() int {
	return xxx_messageInfo_MapTypeRequest.Size(m)
}
func (m *MapTypeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_MapTypeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_MapTypeRequest proto.InternalMessageInfo

func (m *MapTypeRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *MapTypeRequest) GetMeasurement() []byte {
	if m != nil {
		return m.Measurement
	}
	return nil
}

func (m *MapTypeRequest) GetField() string {
	if m != nil && m.Field != nil {
		return *m.Field
	}
	return ""
}

type MapTypeResponse struct {
	Type                 *int32   `protobuf:"varint,1,req,name=Type" json:"Type,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MapTypeResponse) Reset()         { *m = MapTypeResponse{} }
func (m *MapTypeResponse) String() string { return proto.CompactTextString(m) }
func (*MapTypeResponse) ProtoMessage()    {}
func (*MapTypeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{29}
}
func (m *MapTypeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MapTypeResponse.Unmarshal(m, b)
}
func (m *MapTypeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MapTypeResponse.Marshal(b, m, deterministic)
}
func (m *MapTypeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MapTypeResponse.Merge(m, src)
}
func (m *MapTypeResponse) XXX_Size() int {
	return xxx_messageInfo_MapTypeResponse.Size(m)
}
func (m *MapTypeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_MapTypeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_MapTypeResponse proto.InternalMessageInfo

func (m *MapTypeResponse) GetType() int32 {
	if m != nil && m.Type != nil {
		return *m.Type
	}
	return 0
}

func (m *MapTypeResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type ExpandSourcesRequest struct {
	ShardIDs             []uint64 `protobuf:"varint,1,rep,name=ShardIDs" json:"ShardIDs,omitempty"`
	Sources              []byte   `protobuf:"bytes,2,req,name=Sources" json:"Sources,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpandSourcesRequest) Reset()         { *m = ExpandSourcesRequest{} }
func (m *ExpandSourcesRequest) String() string { return proto.CompactTextString(m) }
func (*ExpandSourcesRequest) ProtoMessage()    {}
func (*ExpandSourcesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{30}
}
func (m *ExpandSourcesRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandSourcesRequest.Unmarshal(m, b)
}
func (m *ExpandSourcesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandSourcesRequest.Marshal(b, m, deterministic)
}
func (m *ExpandSourcesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandSourcesRequest.Merge(m, src)
}
func (m *ExpandSourcesRequest) XXX_Size() int {
	return xxx_messageInfo_ExpandSourcesRequest.Size(m)
}
func (m *ExpandSourcesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandSourcesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandSourcesRequest proto.InternalMessageInfo

func (m *ExpandSourcesRequest) GetShardIDs() []uint64 {
	if m != nil {
		return m.ShardIDs
	}
	return nil
}

func (m *ExpandSourcesRequest) GetSources() []byte {
	if m != nil {
		return m.Sources
	}
	return nil
}

type ExpandSourcesResponse struct {
	Sources              []byte   `protobuf:"bytes,1,req,name=Sources" json:"Sources,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpandSourcesResponse) Reset()         { *m = ExpandSourcesResponse{} }
func (m *ExpandSourcesResponse) String() string { return proto.CompactTextString(m) }
func (*ExpandSourcesResponse) ProtoMessage()    {}
func (*ExpandSourcesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{31}
}
func (m *ExpandSourcesResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpandSourcesResponse.Unmarshal(m, b)
}
func (m *ExpandSourcesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpandSourcesResponse.Marshal(b, m, deterministic)
}
func (m *ExpandSourcesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpandSourcesResponse.Merge(m, src)
}
func (m *ExpandSourcesResponse) XXX_Size() int {
	return xxx_messageInfo_ExpandSourcesResponse.Size(m)
}
func (m *ExpandSourcesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpandSourcesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ExpandSourcesResponse proto.InternalMessageInfo

func (m *ExpandSourcesResponse) GetSources() []byte {
	if m != nil {
		return m.Sources
	}
	return nil
}

func (m *ExpandSourcesResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type BackupShardRequest struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	Since                *int64   `protobuf:"varint,2,opt,name=Since" json:"Since,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BackupShardRequest) Reset()         { *m = BackupShardRequest{} }
func (m *BackupShardRequest) String() string { return proto.CompactTextString(m) }
func (*BackupShardRequest) ProtoMessage()    {}
func (*BackupShardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{32}
}
func (m *BackupShardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackupShardRequest.Unmarshal(m, b)
}
func (m *BackupShardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackupShardRequest.Marshal(b, m, deterministic)
}
func (m *BackupShardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackupShardRequest.Merge(m, src)
}
func (m *BackupShardRequest) XXX_Size() int {
	return xxx_messageInfo_BackupShardRequest.Size(m)
}
func (m *BackupShardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BackupShardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BackupShardRequest proto.InternalMessageInfo

func (m *BackupShardRequest) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *BackupShardRequest) GetSince() int64 {
	if m != nil && m.Since != nil {
		return *m.Since
	}
	return 0
}

type BackupShardResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BackupShardResponse) Reset()         { *m = BackupShardResponse{} }
func (m *BackupShardResponse) String() string { return proto.CompactTextString(m) }
func (*BackupShardResponse) ProtoMessage()    {}
func (*BackupShardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{33}
}
func (m *BackupShardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BackupShardResponse.Unmarshal(m, b)
}
func (m *BackupShardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BackupShardResponse.Marshal(b, m, deterministic)
}
func (m *BackupShardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BackupShardResponse.Merge(m, src)
}
func (m *BackupShardResponse) XXX_Size() int {
	return xxx_messageInfo_BackupShardResponse.Size(m)
}
func (m *BackupShardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BackupShardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BackupShardResponse proto.InternalMessageInfo

func (m *BackupShardResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type CopyShardRequest struct {
	Host                 *string  `protobuf:"bytes,1,req,name=Host" json:"Host,omitempty"`
	Database             *string  `protobuf:"bytes,2,req,name=Database" json:"Database,omitempty"`
	Policy               *string  `protobuf:"bytes,3,req,name=Policy" json:"Policy,omitempty"`
	ShardID              *uint64  `protobuf:"varint,4,req,name=ShardID" json:"ShardID,omitempty"`
	Since                *int64   `protobuf:"varint,5,opt,name=Since" json:"Since,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CopyShardRequest) Reset()         { *m = CopyShardRequest{} }
func (m *CopyShardRequest) String() string { return proto.CompactTextString(m) }
func (*CopyShardRequest) ProtoMessage()    {}
func (*CopyShardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{34}
}
func (m *CopyShardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyShardRequest.Unmarshal(m, b)
}
func (m *CopyShardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyShardRequest.Marshal(b, m, deterministic)
}
func (m *CopyShardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyShardRequest.Merge(m, src)
}
func (m *CopyShardRequest) XXX_Size() int {
	return xxx_messageInfo_CopyShardRequest.Size(m)
}
func (m *CopyShardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyShardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CopyShardRequest proto.InternalMessageInfo

func (m *CopyShardRequest) GetHost() string {
	if m != nil && m.Host != nil {
		return *m.Host
	}
	return ""
}

func (m *CopyShardRequest) GetDatabase() string {
	if m != nil && m.Database != nil {
		return *m.Database
	}
	return ""
}

func (m *CopyShardRequest) GetPolicy() string {
	if m != nil && m.Policy != nil {
		return *m.Policy
	}
	return ""
}

func (m *CopyShardRequest) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

func (m *CopyShardRequest) GetSince() int64 {
	if m != nil && m.Since != nil {
		return *m.Since
	}
	return 0
}

type CopyShardResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CopyShardResponse) Reset()         { *m = CopyShardResponse{} }
func (m *CopyShardResponse) String() string { return proto.CompactTextString(m) }
func (*CopyShardResponse) ProtoMessage()    {}
func (*CopyShardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{35}
}
func (m *CopyShardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CopyShardResponse.Unmarshal(m, b)
}
func (m *CopyShardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CopyShardResponse.Marshal(b, m, deterministic)
}
func (m *CopyShardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CopyShardResponse.Merge(m, src)
}
func (m *CopyShardResponse) XXX_Size() int {
	return xxx_messageInfo_CopyShardResponse.Size(m)
}
func (m *CopyShardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CopyShardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CopyShardResponse proto.InternalMessageInfo

func (m *CopyShardResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type RemoveShardRequest struct {
	ShardID              *uint64  `protobuf:"varint,1,req,name=ShardID" json:"ShardID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveShardRequest) Reset()         { *m = RemoveShardRequest{} }
func (m *RemoveShardRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveShardRequest) ProtoMessage()    {}
func (*RemoveShardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{36}
}
func (m *RemoveShardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveShardRequest.Unmarshal(m, b)
}
func (m *RemoveShardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveShardRequest.Marshal(b, m, deterministic)
}
func (m *RemoveShardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveShardRequest.Merge(m, src)
}
func (m *RemoveShardRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveShardRequest.Size(m)
}
func (m *RemoveShardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveShardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveShardRequest proto.InternalMessageInfo

func (m *RemoveShardRequest) GetShardID() uint64 {
	if m != nil && m.ShardID != nil {
		return *m.ShardID
	}
	return 0
}

type RemoveShardResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveShardResponse) Reset()         { *m = RemoveShardResponse{} }
func (m *RemoveShardResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveShardResponse) ProtoMessage()    {}
func (*RemoveShardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{37}
}
func (m *RemoveShardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveShardResponse.Unmarshal(m, b)
}
func (m *RemoveShardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveShardResponse.Marshal(b, m, deterministic)
}
func (m *RemoveShardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveShardResponse.Merge(m, src)
}
func (m *RemoveShardResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveShardResponse.Size(m)
}
func (m *RemoveShardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveShardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveShardResponse proto.InternalMessageInfo

func (m *RemoveShardResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type ShowShardsResponse struct {
	Shards               []byte   `protobuf:"bytes,1,req,name=Shards" json:"Shards,omitempty"`
	Err                  *string  `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ShowShardsResponse) Reset()         { *m = ShowShardsResponse{} }
func (m *ShowShardsResponse) String() string { return proto.CompactTextString(m) }
func (*ShowShardsResponse) ProtoMessage()    {}
func (*ShowShardsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{38}
}
func (m *ShowShardsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ShowShardsResponse.Unmarshal(m, b)
}
func (m *ShowShardsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ShowShardsResponse.Marshal(b, m, deterministic)
}
func (m *ShowShardsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ShowShardsResponse.Merge(m, src)
}
func (m *ShowShardsResponse) XXX_Size() int {
	return xxx_messageInfo_ShowShardsResponse.Size(m)
}
func (m *ShowShardsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ShowShardsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ShowShardsResponse proto.InternalMessageInfo

func (m *ShowShardsResponse) GetShards() []byte {
	if m != nil {
		return m.Shards
	}
	return nil
}

func (m *ShowShardsResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type JoinClusterRequest struct {
	MetaServers          []string `protobuf:"bytes,1,rep,name=MetaServers" json:"MetaServers,omitempty"`
	Update               *bool    `protobuf:"varint,2,req,name=Update" json:"Update,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinClusterRequest) Reset()         { *m = JoinClusterRequest{} }
func (m *JoinClusterRequest) String() string { return proto.CompactTextString(m) }
func (*JoinClusterRequest) ProtoMessage()    {}
func (*JoinClusterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{39}
}
func (m *JoinClusterRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinClusterRequest.Unmarshal(m, b)
}
func (m *JoinClusterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinClusterRequest.Marshal(b, m, deterministic)
}
func (m *JoinClusterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinClusterRequest.Merge(m, src)
}
func (m *JoinClusterRequest) XXX_Size() int {
	return xxx_messageInfo_JoinClusterRequest.Size(m)
}
func (m *JoinClusterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinClusterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_JoinClusterRequest proto.InternalMessageInfo

func (m *JoinClusterRequest) GetMetaServers() []string {
	if m != nil {
		return m.MetaServers
	}
	return nil
}

func (m *JoinClusterRequest) GetUpdate() bool {
	if m != nil && m.Update != nil {
		return *m.Update
	}
	return false
}

type JoinClusterResponse struct {
	Node                 *NodeInfo `protobuf:"bytes,1,opt,name=Node" json:"Node,omitempty"`
	Err                  *string   `protobuf:"bytes,2,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *JoinClusterResponse) Reset()         { *m = JoinClusterResponse{} }
func (m *JoinClusterResponse) String() string { return proto.CompactTextString(m) }
func (*JoinClusterResponse) ProtoMessage()    {}
func (*JoinClusterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{40}
}
func (m *JoinClusterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinClusterResponse.Unmarshal(m, b)
}
func (m *JoinClusterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinClusterResponse.Marshal(b, m, deterministic)
}
func (m *JoinClusterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinClusterResponse.Merge(m, src)
}
func (m *JoinClusterResponse) XXX_Size() int {
	return xxx_messageInfo_JoinClusterResponse.Size(m)
}
func (m *JoinClusterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinClusterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_JoinClusterResponse proto.InternalMessageInfo

func (m *JoinClusterResponse) GetNode() *NodeInfo {
	if m != nil {
		return m.Node
	}
	return nil
}

func (m *JoinClusterResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type NodeInfo struct {
	ID                   *uint64  `protobuf:"varint,1,req,name=ID" json:"ID,omitempty"`
	Addr                 *string  `protobuf:"bytes,2,opt,name=Addr" json:"Addr,omitempty"`
	TCPAddr              *string  `protobuf:"bytes,3,opt,name=TCPAddr" json:"TCPAddr,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NodeInfo) Reset()         { *m = NodeInfo{} }
func (m *NodeInfo) String() string { return proto.CompactTextString(m) }
func (*NodeInfo) ProtoMessage()    {}
func (*NodeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{41}
}
func (m *NodeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NodeInfo.Unmarshal(m, b)
}
func (m *NodeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NodeInfo.Marshal(b, m, deterministic)
}
func (m *NodeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NodeInfo.Merge(m, src)
}
func (m *NodeInfo) XXX_Size() int {
	return xxx_messageInfo_NodeInfo.Size(m)
}
func (m *NodeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NodeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NodeInfo proto.InternalMessageInfo

func (m *NodeInfo) GetID() uint64 {
	if m != nil && m.ID != nil {
		return *m.ID
	}
	return 0
}

func (m *NodeInfo) GetAddr() string {
	if m != nil && m.Addr != nil {
		return *m.Addr
	}
	return ""
}

func (m *NodeInfo) GetTCPAddr() string {
	if m != nil && m.TCPAddr != nil {
		return *m.TCPAddr
	}
	return ""
}

type LeaveClusterResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LeaveClusterResponse) Reset()         { *m = LeaveClusterResponse{} }
func (m *LeaveClusterResponse) String() string { return proto.CompactTextString(m) }
func (*LeaveClusterResponse) ProtoMessage()    {}
func (*LeaveClusterResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{42}
}
func (m *LeaveClusterResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LeaveClusterResponse.Unmarshal(m, b)
}
func (m *LeaveClusterResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LeaveClusterResponse.Marshal(b, m, deterministic)
}
func (m *LeaveClusterResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LeaveClusterResponse.Merge(m, src)
}
func (m *LeaveClusterResponse) XXX_Size() int {
	return xxx_messageInfo_LeaveClusterResponse.Size(m)
}
func (m *LeaveClusterResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_LeaveClusterResponse.DiscardUnknown(m)
}

var xxx_messageInfo_LeaveClusterResponse proto.InternalMessageInfo

func (m *LeaveClusterResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

type RemoveHintedHandoffRequest struct {
	NodeID               *uint64  `protobuf:"varint,1,req,name=NodeID" json:"NodeID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveHintedHandoffRequest) Reset()         { *m = RemoveHintedHandoffRequest{} }
func (m *RemoveHintedHandoffRequest) String() string { return proto.CompactTextString(m) }
func (*RemoveHintedHandoffRequest) ProtoMessage()    {}
func (*RemoveHintedHandoffRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{43}
}
func (m *RemoveHintedHandoffRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveHintedHandoffRequest.Unmarshal(m, b)
}
func (m *RemoveHintedHandoffRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveHintedHandoffRequest.Marshal(b, m, deterministic)
}
func (m *RemoveHintedHandoffRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveHintedHandoffRequest.Merge(m, src)
}
func (m *RemoveHintedHandoffRequest) XXX_Size() int {
	return xxx_messageInfo_RemoveHintedHandoffRequest.Size(m)
}
func (m *RemoveHintedHandoffRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveHintedHandoffRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveHintedHandoffRequest proto.InternalMessageInfo

func (m *RemoveHintedHandoffRequest) GetNodeID() uint64 {
	if m != nil && m.NodeID != nil {
		return *m.NodeID
	}
	return 0
}

type RemoveHintedHandoffResponse struct {
	Err                  *string  `protobuf:"bytes,1,opt,name=Err" json:"Err,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveHintedHandoffResponse) Reset()         { *m = RemoveHintedHandoffResponse{} }
func (m *RemoveHintedHandoffResponse) String() string { return proto.CompactTextString(m) }
func (*RemoveHintedHandoffResponse) ProtoMessage()    {}
func (*RemoveHintedHandoffResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_7438786364df21e1, []int{44}
}
func (m *RemoveHintedHandoffResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveHintedHandoffResponse.Unmarshal(m, b)
}
func (m *RemoveHintedHandoffResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveHintedHandoffResponse.Marshal(b, m, deterministic)
}
func (m *RemoveHintedHandoffResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveHintedHandoffResponse.Merge(m, src)
}
func (m *RemoveHintedHandoffResponse) XXX_Size() int {
	return xxx_messageInfo_RemoveHintedHandoffResponse.Size(m)
}
func (m *RemoveHintedHandoffResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveHintedHandoffResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveHintedHandoffResponse proto.InternalMessageInfo

func (m *RemoveHintedHandoffResponse) GetErr() string {
	if m != nil && m.Err != nil {
		return *m.Err
	}
	return ""
}

func init() {
	proto.RegisterType((*WriteShardRequest)(nil), "internal.WriteShardRequest")
	proto.RegisterType((*WriteShardResponse)(nil), "internal.WriteShardResponse")
	proto.RegisterType((*ExecuteStatementRequest)(nil), "internal.ExecuteStatementRequest")
	proto.RegisterType((*ExecuteStatementResponse)(nil), "internal.ExecuteStatementResponse")
	proto.RegisterType((*TaskManagerStatementRequest)(nil), "internal.TaskManagerStatementRequest")
	proto.RegisterType((*TaskManagerStatementResponse)(nil), "internal.TaskManagerStatementResponse")
	proto.RegisterType((*MeasurementNamesRequest)(nil), "internal.MeasurementNamesRequest")
	proto.RegisterType((*MeasurementNamesResponse)(nil), "internal.MeasurementNamesResponse")
	proto.RegisterType((*TagKeysRequest)(nil), "internal.TagKeysRequest")
	proto.RegisterType((*TagKeysResponse)(nil), "internal.TagKeysResponse")
	proto.RegisterType((*TagValuesRequest)(nil), "internal.TagValuesRequest")
	proto.RegisterType((*TagValuesResponse)(nil), "internal.TagValuesResponse")
	proto.RegisterType((*SeriesSketchesRequest)(nil), "internal.SeriesSketchesRequest")
	proto.RegisterType((*SeriesSketchesResponse)(nil), "internal.SeriesSketchesResponse")
	proto.RegisterType((*MeasurementsSketchesRequest)(nil), "internal.MeasurementsSketchesRequest")
	proto.RegisterType((*MeasurementsSketchesResponse)(nil), "internal.MeasurementsSketchesResponse")
	proto.RegisterType((*StoreReadFilterRequest)(nil), "internal.StoreReadFilterRequest")
	proto.RegisterType((*StoreReadFilterResponse)(nil), "internal.StoreReadFilterResponse")
	proto.RegisterType((*StoreReadGroupRequest)(nil), "internal.StoreReadGroupRequest")
	proto.RegisterType((*StoreReadGroupResponse)(nil), "internal.StoreReadGroupResponse")
	proto.RegisterType((*CreateIteratorRequest)(nil), "internal.CreateIteratorRequest")
	proto.RegisterType((*CreateIteratorResponse)(nil), "internal.CreateIteratorResponse")
	proto.RegisterType((*IteratorStats)(nil), "internal.IteratorStats")
	proto.RegisterType((*IteratorCostRequest)(nil), "internal.IteratorCostRequest")
	proto.RegisterType((*IteratorCostResponse)(nil), "internal.IteratorCostResponse")
	proto.RegisterType((*IteratorCost)(nil), "internal.IteratorCost")
	proto.RegisterType((*FieldDimensionsRequest)(nil), "internal.FieldDimensionsRequest")
	proto.RegisterType((*FieldDimensionsResponse)(nil), "internal.FieldDimensionsResponse")
	proto.RegisterType((*MapTypeRequest)(nil), "internal.MapTypeRequest")
	proto.RegisterType((*MapTypeResponse)(nil), "internal.MapTypeResponse")
	proto.RegisterType((*ExpandSourcesRequest)(nil), "internal.ExpandSourcesRequest")
	proto.RegisterType((*ExpandSourcesResponse)(nil), "internal.ExpandSourcesResponse")
	proto.RegisterType((*BackupShardRequest)(nil), "internal.BackupShardRequest")
	proto.RegisterType((*BackupShardResponse)(nil), "internal.BackupShardResponse")
	proto.RegisterType((*CopyShardRequest)(nil), "internal.CopyShardRequest")
	proto.RegisterType((*CopyShardResponse)(nil), "internal.CopyShardResponse")
	proto.RegisterType((*RemoveShardRequest)(nil), "internal.RemoveShardRequest")
	proto.RegisterType((*RemoveShardResponse)(nil), "internal.RemoveShardResponse")
	proto.RegisterType((*ShowShardsResponse)(nil), "internal.ShowShardsResponse")
	proto.RegisterType((*JoinClusterRequest)(nil), "internal.JoinClusterRequest")
	proto.RegisterType((*JoinClusterResponse)(nil), "internal.JoinClusterResponse")
	proto.RegisterType((*NodeInfo)(nil), "internal.NodeInfo")
	proto.RegisterType((*LeaveClusterResponse)(nil), "internal.LeaveClusterResponse")
	proto.RegisterType((*RemoveHintedHandoffRequest)(nil), "internal.RemoveHintedHandoffRequest")
	proto.RegisterType((*RemoveHintedHandoffResponse)(nil), "internal.RemoveHintedHandoffResponse")
}

func init() { proto.RegisterFile("internal/data.proto", fileDescriptor_7438786364df21e1) }

var fileDescriptor_7438786364df21e1 = []byte{
	// 1101 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x56, 0x6d, 0x6f, 0xe3, 0x44,
	0x10, 0x96, 0xf3, 0xd2, 0x6b, 0xe6, 0xc2, 0x5d, 0xeb, 0xa6, 0xa9, 0x75, 0xad, 0x50, 0xb5, 0x12,
	0x10, 0x1d, 0xa2, 0x27, 0x01, 0x12, 0x42, 0x08, 0xa4, 0xab, 0xdb, 0x92, 0x1e, 0xd7, 0x5c, 0xb5,
	0x0e, 0xc7, 0x37, 0xa4, 0x25, 0x9e, 0xb6, 0x56, 0x13, 0xaf, 0xb1, 0xd7, 0xa5, 0xe5, 0x1f, 0x00,
	0x7f, 0x8c, 0x9f, 0x85, 0xf6, 0xcd, 0x2f, 0x4d, 0x02, 0x39, 0x28, 0xdf, 0xfc, 0x3c, 0xbb, 0x3b,
	0xf3, 0x78, 0x66, 0x76, 0x66, 0x61, 0x2b, 0x8a, 0x05, 0xa6, 0x31, 0x9b, 0xbe, 0x08, 0x99, 0x60,
	0x07, 0x49, 0xca, 0x05, 0x77, 0xd7, 0x2d, 0x49, 0xfe, 0x70, 0x60, 0xf3, 0x87, 0x34, 0x12, 0x18,
	0x5c, 0xb1, 0x34, 0xa4, 0xf8, 0x73, 0x8e, 0x99, 0x70, 0x3d, 0x78, 0xa4, 0xf0, 0xe9, 0x91, 0xe7,
	0xec, 0x37, 0x06, 0x2d, 0x6a, 0xa1, 0xdb, 0x87, 0xb5, 0x73, 0x1e, 0xc5, 0x22, 0xf3, 0x1a, 0xfb,
	0xcd, 0x41, 0x97, 0x1a, 0xe4, 0x3e, 0x83, 0xf5, 0x23, 0x26, 0xd8, 0x4f, 0x2c, 0x43, 0xaf, 0xb9,
	0xef, 0x0c, 0x3a, 0xb4, 0xc0, 0xee, 0x00, 0x9e, 0x52, 0x14, 0x18, 0x8b, 0x88, 0xc7, 0xe7, 0x7c,
	0x1a, 0x4d, 0xee, 0xbc, 0x96, 0xda, 0x72, 0x9f, 0x26, 0x87, 0xe0, 0x56, 0xc5, 0x64, 0x09, 0x8f,
	0x33, 0x74, 0x5d, 0x68, 0xf9, 0x3c, 0x44, 0x25, 0xa5, 0x4d, 0xd5, 0xb7, 0x54, 0x78, 0x86, 0x59,
	0xc6, 0x2e, 0xd1, 0x6b, 0x28, 0x5b, 0x16, 0x92, 0x00, 0x76, 0x8e, 0x6f, 0x71, 0x92, 0x0b, 0x0c,
	0x04, 0x13, 0x38, 0xc3, 0x58, 0xd8, 0xdf, 0xda, 0x83, 0x4e, 0xc1, 0x29, 0x6b, 0x1d, 0x5a, 0x12,
	0xb5, 0x5f, 0x68, 0xa8, 0xc5, 0x02, 0x93, 0x21, 0x78, 0xf3, 0x46, 0xff, 0x95, 0xbc, 0xaf, 0x60,
	0x77, 0xcc, 0xb2, 0xeb, 0x33, 0x16, 0xb3, 0x4b, 0x4c, 0xdf, 0x4d, 0x22, 0x19, 0xc2, 0xde, 0xe2,
	0xc3, 0x46, 0x4a, 0x1f, 0xd6, 0x28, 0x66, 0xf9, 0x54, 0x1f, 0xed, 0x52, 0x83, 0xdc, 0x0d, 0x68,
	0x1e, 0xa7, 0xa9, 0x91, 0x22, 0x3f, 0x65, 0x94, 0xce, 0x90, 0x65, 0x79, 0xaa, 0x0c, 0x8c, 0xd8,
	0x0c, 0x33, 0x2b, 0xa1, 0x1a, 0x07, 0xa7, 0x1e, 0x07, 0x29, 0xcf, 0xe7, 0x71, 0x18, 0xc9, 0x9c,
	0x19, 0x73, 0x25, 0x41, 0x0e, 0xc1, 0x9b, 0x37, 0x6a, 0xa4, 0xf5, 0xa0, 0xad, 0x08, 0xcf, 0x51,
	0x75, 0xa3, 0xc1, 0x02, 0x61, 0xaf, 0xe0, 0xc9, 0x98, 0x5d, 0x7e, 0x87, 0x77, 0x55, 0x3d, 0xa6,
	0xfa, 0xf4, 0xe1, 0x16, 0x2d, 0xf0, 0x3f, 0xe8, 0xf9, 0x1a, 0x9e, 0x16, 0xb6, 0x8c, 0x0c, 0x0f,
	0x1e, 0x19, 0xca, 0x73, 0xf6, 0x9d, 0x41, 0x97, 0x5a, 0xb8, 0x40, 0xca, 0x6b, 0xd8, 0x18, 0xb3,
	0xcb, 0xb7, 0x6c, 0x9a, 0xe3, 0x03, 0x88, 0xf1, 0x61, 0xb3, 0x62, 0xcd, 0xc8, 0xd9, 0x83, 0x4e,
	0x41, 0x1a, 0x41, 0x25, 0xb1, 0x40, 0xd2, 0x67, 0xb0, 0x1d, 0x60, 0x1a, 0x61, 0x16, 0x5c, 0xa3,
	0x98, 0x5c, 0xad, 0x94, 0x34, 0xf2, 0x23, 0xf4, 0xef, 0x1f, 0x2a, 0xeb, 0x45, 0x73, 0xb6, 0x5e,
	0x34, 0x92, 0xd6, 0xc6, 0x81, 0x59, 0x69, 0xa8, 0x95, 0x02, 0x5b, 0x51, 0xcd, 0x52, 0xd4, 0x97,
	0xb0, 0x5b, 0x49, 0xfb, 0x3b, 0x49, 0x0b, 0x61, 0x6f, 0xf1, 0xd1, 0x07, 0x15, 0x38, 0x82, 0x7e,
	0x20, 0x78, 0x8a, 0x14, 0x59, 0x78, 0x12, 0x4d, 0x05, 0xa6, 0xab, 0xa4, 0xd3, 0x83, 0x47, 0x66,
	0x9b, 0x71, 0x61, 0x21, 0xf9, 0x18, 0x76, 0xe6, 0xec, 0x19, 0xc1, 0xc6, 0xb9, 0x53, 0x3a, 0x3f,
	0x83, 0xed, 0x62, 0xf3, 0xb7, 0x29, 0xcf, 0x93, 0xff, 0xe6, 0xfb, 0x79, 0xe5, 0x5f, 0x8c, 0xb9,
	0xa5, 0xae, 0x7f, 0x73, 0x60, 0xdb, 0x4f, 0x91, 0x09, 0x3c, 0x15, 0x98, 0x32, 0xc1, 0x57, 0xfa,
	0xef, 0x7d, 0x78, 0x5c, 0xc9, 0x89, 0xf1, 0x5f, 0xa5, 0xa4, 0xa7, 0x37, 0x89, 0xf0, 0x9a, 0x6a,
	0x45, 0x7e, 0xca, 0x33, 0x41, 0xc2, 0x62, 0x9f, 0xc7, 0x02, 0x6f, 0x85, 0x6a, 0xef, 0x5d, 0x5a,
	0xa5, 0xc8, 0x0c, 0xfa, 0xf7, 0xa5, 0x2c, 0xd3, 0x2d, 0x3b, 0xea, 0xf8, 0x2e, 0xd1, 0x5d, 0xb8,
	0x4d, 0xd5, 0xb7, 0xfb, 0x09, 0xb4, 0x65, 0xbf, 0xcb, 0x54, 0x5e, 0x1f, 0x7f, 0xba, 0x73, 0x60,
	0x47, 0xd8, 0x81, 0x35, 0xa8, 0x96, 0xa9, 0xde, 0x45, 0x5e, 0xc2, 0x7b, 0x35, 0x5e, 0x8d, 0x34,
	0x75, 0x09, 0x46, 0xca, 0x53, 0x93, 0x5a, 0x58, 0x8c, 0xb4, 0x91, 0xba, 0x68, 0x4d, 0x33, 0xd2,
	0x46, 0x04, 0x61, 0xcb, 0x9a, 0xf0, 0x79, 0x26, 0xfe, 0xa7, 0xd0, 0x91, 0x31, 0xf4, 0xea, 0x6e,
	0x96, 0x86, 0xe5, 0xb9, 0x1c, 0x34, 0xaa, 0x22, 0x64, 0x04, 0xfa, 0xf3, 0x11, 0x50, 0xe7, 0xd5,
	0x1e, 0xf2, 0xa7, 0x03, 0xdd, 0x2a, 0x2d, 0x3b, 0xcd, 0x28, 0x9f, 0x29, 0xa5, 0x99, 0x89, 0x40,
	0x49, 0xd8, 0x55, 0x15, 0x11, 0x13, 0x86, 0x92, 0x70, 0x09, 0x74, 0x7d, 0x36, 0xb9, 0xc2, 0xd0,
	0x34, 0xaa, 0xa6, 0xda, 0x50, 0xe3, 0x64, 0x58, 0x46, 0xf9, 0xec, 0x24, 0x9a, 0x62, 0xa6, 0xd2,
	0xdf, 0xa4, 0x05, 0x76, 0xdf, 0x07, 0x38, 0x9c, 0xf2, 0xc9, 0x75, 0x26, 0x8b, 0xd6, 0x6b, 0xab,
	0xd5, 0x0a, 0x23, 0xbd, 0x2b, 0x14, 0x44, 0xbf, 0xa2, 0xb7, 0xa6, 0xbd, 0x17, 0x04, 0x79, 0x0b,
	0xfd, 0x93, 0x08, 0xa7, 0xe1, 0x51, 0x34, 0xc3, 0x38, 0x8b, 0x78, 0x9c, 0x3d, 0x48, 0x2a, 0xc8,
	0x04, 0x76, 0xe6, 0xec, 0x96, 0x6d, 0x47, 0x2d, 0x65, 0xb6, 0xed, 0x68, 0x24, 0x7f, 0xa4, 0xdc,
	0xad, 0x5e, 0x40, 0x1d, 0x5a, 0x61, 0x16, 0xb4, 0x9e, 0x10, 0x9e, 0x9c, 0xb1, 0x44, 0x56, 0xf0,
	0xc3, 0xd4, 0x4f, 0x0f, 0xda, 0x4a, 0x8b, 0xaa, 0xa0, 0x0e, 0xd5, 0x80, 0x7c, 0x01, 0x4f, 0x0b,
	0x2f, 0xe5, 0xab, 0x44, 0xdd, 0x21, 0xa7, 0x72, 0x87, 0x16, 0x8d, 0xb8, 0xde, 0xf1, 0x6d, 0xc2,
	0xe2, 0x30, 0xe0, 0x79, 0x3a, 0x59, 0x6d, 0xcc, 0xc9, 0x9b, 0xa4, 0x77, 0xdb, 0xde, 0x64, 0x20,
	0xf1, 0x61, 0xfb, 0x9e, 0xb5, 0x72, 0xea, 0xda, 0x23, 0x4e, 0xed, 0xc8, 0x02, 0x49, 0x47, 0xe0,
	0x1e, 0xb2, 0xc9, 0x75, 0x9e, 0xac, 0xf8, 0x22, 0xed, 0x41, 0x3b, 0x88, 0xe2, 0x09, 0x9a, 0xb2,
	0xd5, 0x80, 0x7c, 0x04, 0x5b, 0x35, 0x2b, 0x4b, 0x7b, 0xe4, 0xef, 0x0e, 0x6c, 0xf8, 0x3c, 0xb9,
	0xab, 0x79, 0x73, 0xa1, 0x35, 0x94, 0x37, 0x4d, 0x8f, 0x2b, 0xf5, 0xfd, 0x77, 0xcf, 0x43, 0xdd,
	0x42, 0xd4, 0xc3, 0x56, 0xa7, 0xc5, 0xa0, 0xaa, 0xea, 0xd6, 0x12, 0xd5, 0xed, 0xaa, 0xea, 0x0f,
	0x60, 0xb3, 0xa2, 0x65, 0xa9, 0xe6, 0x03, 0x70, 0x29, 0xce, 0xf8, 0xcd, 0x8a, 0x8f, 0x76, 0x19,
	0x8c, 0xda, 0xfe, 0xa5, 0x86, 0xbf, 0x01, 0x37, 0xb8, 0xe2, 0xbf, 0xe8, 0xa6, 0x50, 0x1b, 0xc2,
	0xb6, 0x6f, 0xe8, 0x21, 0xac, 0x9b, 0xc6, 0x7c, 0xee, 0x46, 0xe0, 0xbe, 0xe2, 0x51, 0xec, 0x4f,
	0xf3, 0xac, 0x32, 0x64, 0x55, 0x55, 0x0b, 0x16, 0x60, 0x7a, 0x83, 0xa9, 0xae, 0xa7, 0x0e, 0xad,
	0x52, 0xd2, 0xc3, 0xf7, 0x49, 0xc8, 0x84, 0x8e, 0xec, 0x3a, 0x35, 0x88, 0xbc, 0x81, 0xad, 0x9a,
	0x3d, 0x23, 0xe8, 0x43, 0x68, 0x8d, 0xf4, 0x8b, 0x5b, 0x36, 0x42, 0xb7, 0x6c, 0x84, 0x92, 0x3d,
	0x8d, 0x2f, 0x38, 0x55, 0xeb, 0x0b, 0x04, 0x0e, 0x61, 0xdd, 0xee, 0x71, 0x9f, 0x40, 0xa3, 0x08,
	0x55, 0xe3, 0xf4, 0x48, 0x26, 0xfd, 0x65, 0x18, 0xda, 0xed, 0xea, 0x5b, 0x3d, 0x17, 0xfd, 0x73,
	0x45, 0xeb, 0x4b, 0x6d, 0x21, 0x19, 0x40, 0xef, 0x35, 0xb2, 0x1b, 0xbc, 0xaf, 0x6d, 0x3e, 0xa8,
	0x9f, 0xc3, 0x33, 0x1d, 0xfd, 0xa1, 0xd4, 0x19, 0x0e, 0x59, 0x1c, 0xf2, 0x8b, 0x0b, 0x1b, 0x9c,
	0x3e, 0xac, 0x29, 0x45, 0x56, 0x89, 0x41, 0xe4, 0x05, 0xec, 0x2e, 0x3c, 0xb5, 0xcc, 0xcd, 0x5f,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xea, 0x96, 0x17, 0x43, 0xe9, 0x0d, 0x00, 0x00,
}
