### Welcome to the ThinfluxDB Cluster configuration file.

# The values in this file override the default values used by the system if
# a config option is not specified. The commented out lines are the configuration
# field and the default value used. Uncommenting a line and changing the value
# will change the value used at runtime when the process is restarted.

# Once every 24 hours ThinfluxDB Cluster will report usage data in log output
# The data includes a random ID, os, arch, version, the number of series and other
# usage data. No data from user databases is ever transmitted.
# Change this option to true to disable reporting.
# reporting-disabled = false

# The TCP bind address to use for cluster-internal services.
# bind-address = ":8091"

# Hostname advertised by this host for remote addresses. This must be resolvable by all
# other nodes in the cluster.
# hostname = ""

###
### [meta]
###
### Settings specific to meta node operation.
###

[meta]
  # Directory where cluster meta data is stored.
  dir = "/var/lib/thinfluxdb/meta"

  # The default address for meta node communication
  # bind-address = ":8089"

  # The default address to bind the API to.
  # http-bind-address = ":8091"

  # Determines whether meta nodes use HTTPS to communicate with each other.
  # https-enabled = false

  # The SSL certificate to use when HTTPS is enabled.  The certificate should be a PEM encoded
  # bundle of the certificate and key.  If it is just the certificate, a key must be
  # specified in https-private-key.
  # https-certificate = ""

  # Use a separate private key location.
  # https-private-key = ""

  # Whether meta nodes will skip certificate validation communicating with each other over HTTPS.
  # This is useful when testing with self-signed certificates.
  # https-insecure-tls = false

  # Whether to use TLS to communicate with data nodes.
  # data-use-tls = false

  # Whether meta nodes will skip certificate validation communicating with data nodes over TLS.
  # This is useful when testing with self-signed certificates.
  # data-insecure-tls = false

  # The default frequency with which the node will gossip its known announcements.
  # gossip-frequency = "5s"

  # The default length of time an announcement is kept before it is considered too old.
  # announcement-expiration = "30s"

  # Automatically create a default retention policy when creating a database.
  # retention-autocreate = true

  # The amount of time in candidate state without a leader before we attempt an election.
  # election-timeout = "1s"

  # The amount of time in follower state without a leader before we attempt an election.
  # heartbeat-timeout = "1s"

  # Control how long the "lease" lasts for being the leader without being able to contact a quorum
  # of nodes. If we reach this interval without contact, we will step down as leader.
  # leader-lease-timeout = "500ms"

  # The amount of time without an Apply() operation before we heartbeat to ensure a timely
  # commit. Due to random staggering, may be delayed as much as 2x this value.
  # commit-timeout = "50ms"

  # Timeout waiting for consensus before getting the latest Raft snapshot.
  # consensus-timeout = "30s"

  # Enables cluster level trace logging.
  # cluster-tracing = false

  # Enables cluster API level trace logging.
  # logging-enabled = true

  # Determines whether the pprof endpoint is enabled.  This endpoint is used for
  # troubleshooting and monitoring.
  # pprof-enabled = true

  # The default duration of leases.
  # lease-duration = "1m0s"

  # If true, HTTP endpoints require authentication.
  # This setting must have the same value as the data nodes' meta.meta-auth-enabled 
  # configuration.
  # auth-enabled = false

  # Whether LDAP is allowed to be set.
  # If true, you will need to use `thinfluxd ldap set-config` and set enabled=true to use LDAP authentication.
  # ldap-allowed = false

  # The shared secret used by the API for JWT authentication.
  # shared-secret = ""

  # The shared secret used by the internal API for JWT authentication.
  # This setting must have the same value as the data nodes' 
  # meta.meta-internal-shared-secret configuration.
  # internal-shared-secret = ""

###
### [logging]
###
### Controls how the logger emits logs to the output.
###

[logging]
# Determines which log encoder to use for logs. Available options
# are auto, logfmt, and json. auto will use a more a more user-friendly
# output format if the output terminal is a TTY, but the format is not as
# easily machine-readable. When the output is a non-TTY, auto will use
# logfmt.
# format = "auto"

# Determines which level of logs will be emitted. The available levels
# are error, warn, info, and debug. Logs that are equal to or above the
# specified level will be emitted.
# level = "info"

# Suppresses the logo output that is printed when the program is started.
# The logo is always suppressed if STDOUT is not a TTY.
# suppress-logo = false

[tls]
# Determines the available set of cipher suites. See https://golang.org/pkg/crypto/tls/#pkg-constants
# for a list of available ciphers, which depends on the version of Go (use the query
# SHOW DIAGNOSTICS to see the version of Go used to build ThinfluxDB). If not specified, uses
# the default settings from Go's crypto/tls package.
# ciphers = [
#   "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305",
#   "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305",
# ]

# Minimum version of the tls protocol that will be negotiated. If not specified, uses the
# default settings from Go's crypto/tls package.
# min-version = "tls1.2"

# Maximum version of the tls protocol that will be negotiated. If not specified, uses the
# default settings from Go's crypto/tls package.
# max-version = "tls1.3"
