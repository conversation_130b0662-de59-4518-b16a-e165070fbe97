influx_stress(1)
================

NAME
----
influx_stress - Runs a stress test against one or multiple InfluxDB servers

SYNOPSIS
--------
[verse]
'influx_stress' [options]

DESCRIPTION
-----------
Runs write and query stress tests against one or multiple InfluxDB servers to
create reproducible performance benchmarks against InfluxDB.

OPTIONS
-------
-addr <addr>::
  IP address and port of the database where response times will persist. This
  is not for specifying which database to test against. That option is located
  inside of the configuration file. The default is 'http://localhost:8086'.

-database <name>::
  The database where response times will persist. This is not for specifying
  which database to test against. See '-db' or the configuration file for that
  option. The default is 'stress'.

-retention-policy <name>::
  The retention policy where response times will persist. This is not for
  specifying which retention policy to test against. See the configuration file
  for that option. The default is an empty string which will use the default
  retention policy.

-config <path>::
  The stress configuration file.

-cpuprofile <path>::
  Write the cpu profile to the path. No cpu profile is written unless this is
  used. This profiles 'influx_stress', not the InfluxDB server.

-db <name>::
  The target database within the test system for write and query load.

-tags <values>::
  A comma separated list of tags.

-v2::
  Use version 2 of the stress tool. The default is to use version 1.

include::footer.txt[]
