influxd-restore(1)
==================

NAME
----
influxd-restore - Restores databases or specific shards to an InfluxDB OSS instance
  from the specified PATH. Complete documentation
  for the '-portable' restore method described here, and the deprecated legacy restore format,
  is located here:
    https://docs.influxdata.com/influxdb/latest/administration/backup_and_restore/


SYNOPSIS
--------
'influxd restore' -portable [options] PATH

DESCRIPTION
-----------
Uses backup copies from the specified PATH to restore databases or specific shards from InfluxDB OSS
  or InfluxDB Enterprise to an InfluxDB OSS instance.

OPTIONS
-------

Note: Restore using the '-portable' option consumes files in an improved Enterprise-compatible
  format that includes a file manifest.

-portable::
  Required to activate the portable restore mode. If not specified, the legacy restore mode is used.

-host <host:port>::
  InfluxDB OSS host to connect to where the data will be restored. Defaults to '127.0.0.1:8088'.

-db <db_name>::
  Name of database to be restored from the backup (InfluxDB OSS or InfluxDB Enterprise)

-newdb <newdb_name>::
  Name of the InfluxDB OSS database into which the archived data will be imported on the target system. Optional.
   If not given, then the value of '-db <db_name>' is used.  The new database name must be unique to the target system.

-rp  <rp_name>::
  Name of retention policy from the backup that will be restored. Optional. Requires that '-db <db_name>' is specified.

-newrp <newrp_name>::
  Name of the retention policy to be created on the target system. Optional. Requires that '-rp <rp_name>' is set.
  If not given, the '-rp <rp_name>' value is used.

-shard <shard_id>::
  Identifier of the shard to be restored. Optional. If specified, then '-db <db_name>' and '-rp <rp_name>' are required.

PATH
  Path to directory containing the backup files.

SEE ALSO
--------
*influxd-backup*(1)

include::footer.txt[]
