influxd(1)
==========

NAME
----
influxd - InfluxDB server daemon

SYNOPSIS
--------
[verse]
'influxd' [command] [options]

DESCRIPTION
-----------
'influxd' is the server daemon for InfluxDB.

COMMANDS
--------
These commands can be invoked using the 'influxd' program. The default is 'run' if the command parameter is skipped.

backup::
  Downloads a snapshot of a data node and saves it to disk.

config::
  Displays the default configuration. This can also read an existing configuration file and output the default values for any missing fields. Default values and existing entries in a configuration file can be customized through environment variables.

restore::
  Uses backups to restore the metastore, databases, retention policies, or specific shards. The InfluxDB process must not be running during a restore.

run::
  Runs the InfluxDB server. This is the default command if none is specified.

version::
  Displays the InfluxDB version, build branch, and git commit hash.

SEE ALSO
--------
*influxd-backup*(1), *influxd-config*(1), *influxd-restore*(1), *influxd-run*(1), *influxd-version*(1)

include::footer.txt[]
