influx_inspect(1)
=================

NAME
----
influx_inspect - Displays detailed information about InfluxDB data files

SYNPOSIS
--------
[verse]
'influx_inspect dumptsm' [options]
'influx_inspect export' [options]
'influx_inspect report' [options]
'influx_inspect verify' [options]
'influx_inspect verify-seriesfile' [options]

DESCRIPTION
-----------
Displays detailed information about InfluxDB data files through one of the
following commands.

*dumptsm*::
  Dumps low-level details about tsm1 files.

*export*::
  Exports TSM files into InfluxDB line protocol format.

*report*::
  Displays shard level report.

*verify*::
  Verifies integrity of TSM files.

DUMPTSM OPTIONS
---------------
-all::
  Dump all data. Caution: This may print a lot of information.

-blocks::
  Dump raw block data.

-filter-key <string>::
  Only display index and block data that match this key substring.

-index::
  Dump raw index data.

EXPORT OPTIONS
--------------
-compress::
  Compress the output.

-db <name>::
  The database to export. Optional.

-rp <name>::
  The retention policy to export. Optional. Requires the '-db <name>' option to be specified.

-data-dir <path>::
  Data storage path. Defaults to '~/.influxdb/data'.

-wal-dir <path>::
  Wal storage path. Defaults to '~/.influxdb/wal'.

-start <timestamp>::
  The start time of the export. The timestamp is in RFC3339 format. Optional.

-end <timestamp>::
  The end time of the export. The timestamp is in RFC3339 format. Optional.

-out <path>::
  Destination file to write exported data to. Defaults to '~/.influxdb/export'.

REPORT OPTIONS
--------------
-detailed::
  Report detailed cardinality estimates.

-pattern <string>::
  Include only files matching a pattern.

VERIFY OPTIONS
--------------
-dir <path>::
  Root storage path. Defaults to '~/.influxdb'.

VERIFY-SERIESFILE OPTIONS
-------------------------
-dir <path>::
  Root data storage path. Defaults to '~/.influxdb/data'.

-db <name>::
  Specific db to check. Optional.

-file <path>::
  Path to a specific series file to check. Overrides '-dir' and '-db'. Optional.

-v::
  Verbose output. Optional.

-c <amount>::
  Number of concurrent workers to run. Defaults to the number of cores on the machine. Optional.

include:footer.txt[]
