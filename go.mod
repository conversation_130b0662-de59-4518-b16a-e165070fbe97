module github.com/influxdata/influxdb

go 1.16

require (
	cloud.google.com/go/bigtable v1.2.0 // indirect
	collectd.org v0.3.0
	github.com/BurntSushi/toml v0.3.1
	github.com/apache/arrow/go/arrow v0.0.0-20191024131854-af6fa24be0db
	github.com/benb<PERSON><PERSON>son/tmpl v1.1.0
	github.com/bmizerany/pat v0.0.0-20170815010413-6226ea591a40
	github.com/cespare/xxhash v1.1.0
	github.com/davecgh/go-spew v1.1.1
	github.com/dgrijalva/jwt-go/v4 v4.0.0-preview1
	github.com/dgryski/go-bitstream v0.0.0-20180413035011-3522498ce2c8
	github.com/glycerine/go-unsnap-stream v0.0.0-20180323001048-9f0cb55181dd // indirect
	github.com/glycerine/goconvey v0.0.0-20190410193231-58a59202ab31 // indirect
	github.com/gogo/protobuf v1.3.2
	github.com/golang/snappy v0.0.0-20180518054509-2e65f85255db
	github.com/google/go-cmp v0.6.0
	github.com/hashicorp/raft v1.3.11
	github.com/hashicorp/raft-boltdb/v2 v2.2.2
	github.com/influxdata/flux v0.65.1
	github.com/influxdata/influxql v1.1.1-0.20200828144457-65d3ef77d385
	github.com/influxdata/pkg-config v0.2.8
	github.com/influxdata/roaring v0.4.13-0.20180809181101-fc520f41fab6
	github.com/influxdata/usage-client v0.0.0-20160829180054-6d3895376368
	github.com/jsternberg/zap-logfmt v1.0.0
	github.com/jwilder/encoding v0.0.0-20170811194829-b4e1701a28ef
	github.com/klauspost/compress v1.4.0 // indirect
	github.com/klauspost/cpuid v0.0.0-20170728055534-ae7887de9fa5 // indirect
	github.com/klauspost/crc32 v0.0.0-20161016154125-cb6bfca970f6 // indirect
	github.com/klauspost/pgzip v1.0.2-0.20170402124221-0bf5dcad4ada
	github.com/mattn/go-isatty v0.0.4
	github.com/mschoch/smat v0.0.0-20160514031455-90eadee771ae // indirect
	github.com/opentracing/opentracing-go v1.0.3-0.20180606204148-bd9c31933947
	github.com/paulbellamy/ratecounter v0.2.0
	github.com/peterh/liner v1.0.1-0.20180619022028-8c1271fcf47f
	github.com/philhofer/fwd v1.0.0 // indirect
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.0.0
	github.com/retailnext/hllpp v1.0.1-0.20180308014038-101a6d2f8b52
	github.com/segmentio/kafka-go v0.2.0 // indirect
	github.com/shirou/gopsutil/v3 v3.24.5
	github.com/smartystreets/goconvey v1.6.4 // indirect
	github.com/spf13/cast v1.3.0
	github.com/stretchr/testify v1.9.0
	github.com/tinylib/msgp v1.0.2
	github.com/willf/bitset v1.1.3 // indirect
	github.com/xlab/treeprint v0.0.0-20180616005107-d6fb6747feb6
	go.uber.org/zap v1.9.1
	golang.org/x/crypto v0.0.0-20210322153248-0c34fe9e7dc2
	golang.org/x/sync v0.0.0-20201020160332-67f06af15bc9
	golang.org/x/sys v0.20.0
	golang.org/x/text v0.3.3
	golang.org/x/time v0.0.0-20190308202827-9d24e82272b4
	golang.org/x/tools v0.0.0-20210106214847-113979e3529a
	gonum.org/v1/gonum v0.6.0 // indirect
	google.golang.org/grpc v1.26.0
	gopkg.in/fatih/pool.v2 v2.0.0
)

replace github.com/influxdata/influxql => github.com/influxtsdb/influxql v1.1.1-0.20220918111639-aa1802d18d78
