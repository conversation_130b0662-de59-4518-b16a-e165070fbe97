package gen

{{range .}}
type {{.name}}ConstantValuesSequence struct {
	v {{.Type}}
}

func New{{.Name}}ConstantValuesSequence(v {{.Type}}) {{.Name}}ValuesSequence {
	return &{{.name}}ConstantValuesSequence{
		v: v,
	}
}

func (g *{{.name}}ConstantValuesSequence) Reset() {
}

func (g *{{.name}}ConstantValuesSequence) Write(vs []{{.Type}}) {
	for i := 0; i < len(vs); i++ {
		vs[i] = g.v
	}
}
{{end}}

{{range .}}
type {{.name}}ArrayValuesSequence struct {
	v  []{{.Type}}
	vi int
}

func New{{.Name}}ArrayValuesSequence(v []{{.Type}}) {{.Name}}ValuesSequence {
	return &{{.name}}ArrayValuesSequence{
		v: v,
	}
}

func (g *{{.name}}ArrayValuesSequence) Reset() {
	g.vi = 0
}

func (g *{{.name}}ArrayValuesSequence) Write(vs []{{.Type}}) {
	var (
		v  = g.v
		vi = g.vi
	)
	for i := 0; i < len(vs); i++ {
		if vi >= len(v) {
			vi = 0
		}
		vs[i] = v[vi]
		vi += 1
	}
	g.vi = vi
}
{{end}}
