// Code generated by tmpl; DO NOT EDIT.
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// Source: arrays.gen.go.tmpl

package gen

import (
	"github.com/influxdata/influxdb/tsdb"
	"github.com/influxdata/influxdb/tsdb/engine/tsm1"
)

type floatArray struct {
	tsdb.FloatArray
}

func newFloatArrayLen(sz int) *floatArray {
	return &floatArray{
		FloatArray: tsdb.FloatArray{
			Timestamps: make([]int64, sz),
			Values:     make([]float64, sz),
		},
	}
}

func (a *floatArray) Encode(b []byte) ([]byte, error) {
	return tsm1.EncodeFloatArrayBlock(&a.Float<PERSON>rray, b)
}

type integerArray struct {
	tsdb.IntegerArray
}

func newIntegerArrayLen(sz int) *integerArray {
	return &integerArray{
		IntegerArray: tsdb.IntegerArray{
			Timestamps: make([]int64, sz),
			Values:     make([]int64, sz),
		},
	}
}

func (a *integerArray) Encode(b []byte) ([]byte, error) {
	return tsm1.EncodeIntegerArrayBlock(&a.IntegerArray, b)
}

type unsignedArray struct {
	tsdb.UnsignedArray
}

func newUnsignedArrayLen(sz int) *unsignedArray {
	return &unsignedArray{
		UnsignedArray: tsdb.UnsignedArray{
			Timestamps: make([]int64, sz),
			Values:     make([]uint64, sz),
		},
	}
}

func (a *unsignedArray) Encode(b []byte) ([]byte, error) {
	return tsm1.EncodeUnsignedArrayBlock(&a.UnsignedArray, b)
}

type stringArray struct {
	tsdb.StringArray
}

func newStringArrayLen(sz int) *stringArray {
	return &stringArray{
		StringArray: tsdb.StringArray{
			Timestamps: make([]int64, sz),
			Values:     make([]string, sz),
		},
	}
}

func (a *stringArray) Encode(b []byte) ([]byte, error) {
	return tsm1.EncodeStringArrayBlock(&a.StringArray, b)
}

type booleanArray struct {
	tsdb.BooleanArray
}

func newBooleanArrayLen(sz int) *booleanArray {
	return &booleanArray{
		BooleanArray: tsdb.BooleanArray{
			Timestamps: make([]int64, sz),
			Values:     make([]bool, sz),
		},
	}
}

func (a *booleanArray) Encode(b []byte) ([]byte, error) {
	return tsm1.EncodeBooleanArrayBlock(&a.BooleanArray, b)
}
