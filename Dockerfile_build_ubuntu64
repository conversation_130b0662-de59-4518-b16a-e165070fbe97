FROM ubuntu:focal

RUN apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    asciidoc \
    autoconf \
    build-essential \
    docbook-xsl \
    git \
    libtool \
    make \
    mercurial \
    protobuf-compiler \
    python \
    python3-pip \
    python3 \
    python3-boto \
    python3-software-properties \
    rpm \
    ruby \
    ruby-dev \
    software-properties-common \
    wget \
    xmlto \
    zip

RUN gem install fpm

# setup environment
ENV GO_VERSION  1.16.15
ENV GOARCH      amd64
ENV GOROOT      /usr/local/go
ENV GOPATH      /root/go
ENV PATH        $GOPATH/bin:$GOROOT/bin:$PATH
ENV PROJECT_DIR /root/influxdb

# install go
RUN wget --no-verbose https://storage.googleapis.com/golang/go${GO_VERSION}.linux-${GOARCH}.tar.gz -O- | tar -C /usr/local/ -zxf-

RUN mkdir -p $PROJECT_DIR

WORKDIR $PROJECT_DIR
VOLUME  $PROJECT_DIR

ENTRYPOINT [ "/root/influxdb/build.py" ]
