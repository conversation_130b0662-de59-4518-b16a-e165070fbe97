// Generated by tmpl
// https://github.com/benb<PERSON><PERSON>son/tmpl
//
// DO NOT EDIT!
// Source: stream_reader_gen_test.go.tmpl

package reads_test

import (
	"sort"

	"github.com/influxdata/influxdb/storage/reads/datatypes"
)

type FloatPoints datatypes.ReadResponse_FloatPointsFrame

func (a FloatPoints) Len() int { return len(a.Timestamps) }

func (a FloatPoints) Less(i, j int) bool { return a.Timestamps[i] < a.Timestamps[j] }

func (a FloatPoints) Swap(i, j int) {
	a.Timestamps[i], a.Timestamps[j] = a.Timestamps[j], a.Timestamps[i]
	a.Values[i], a.Values[j] = a.Values[j], a.Values[i]
}

type floatS map[int64]float64

func floatF(points floatS) datatypes.ReadResponse_Frame {
	var block FloatPoints
	for t, v := range points {
		block.Timestamps = append(block.Timestamps, t)
		block.Values = append(block.Values, v)
	}

	sort.Sort(block)
	pointsFrame := datatypes.ReadResponse_FloatPointsFrame(block)

	return datatypes.ReadResponse_Frame{
		Data: &datatypes.ReadResponse_Frame_FloatPoints{
			FloatPoints: &pointsFrame,
		},
	}
}

type IntegerPoints datatypes.ReadResponse_IntegerPointsFrame

func (a IntegerPoints) Len() int { return len(a.Timestamps) }

func (a IntegerPoints) Less(i, j int) bool { return a.Timestamps[i] < a.Timestamps[j] }

func (a IntegerPoints) Swap(i, j int) {
	a.Timestamps[i], a.Timestamps[j] = a.Timestamps[j], a.Timestamps[i]
	a.Values[i], a.Values[j] = a.Values[j], a.Values[i]
}

type integerS map[int64]int64

func integerF(points integerS) datatypes.ReadResponse_Frame {
	var block IntegerPoints
	for t, v := range points {
		block.Timestamps = append(block.Timestamps, t)
		block.Values = append(block.Values, v)
	}

	sort.Sort(block)
	pointsFrame := datatypes.ReadResponse_IntegerPointsFrame(block)

	return datatypes.ReadResponse_Frame{
		Data: &datatypes.ReadResponse_Frame_IntegerPoints{
			IntegerPoints: &pointsFrame,
		},
	}
}

type UnsignedPoints datatypes.ReadResponse_UnsignedPointsFrame

func (a UnsignedPoints) Len() int { return len(a.Timestamps) }

func (a UnsignedPoints) Less(i, j int) bool { return a.Timestamps[i] < a.Timestamps[j] }

func (a UnsignedPoints) Swap(i, j int) {
	a.Timestamps[i], a.Timestamps[j] = a.Timestamps[j], a.Timestamps[i]
	a.Values[i], a.Values[j] = a.Values[j], a.Values[i]
}

type unsignedS map[int64]uint64

func unsignedF(points unsignedS) datatypes.ReadResponse_Frame {
	var block UnsignedPoints
	for t, v := range points {
		block.Timestamps = append(block.Timestamps, t)
		block.Values = append(block.Values, v)
	}

	sort.Sort(block)
	pointsFrame := datatypes.ReadResponse_UnsignedPointsFrame(block)

	return datatypes.ReadResponse_Frame{
		Data: &datatypes.ReadResponse_Frame_UnsignedPoints{
			UnsignedPoints: &pointsFrame,
		},
	}
}

type StringPoints datatypes.ReadResponse_StringPointsFrame

func (a StringPoints) Len() int { return len(a.Timestamps) }

func (a StringPoints) Less(i, j int) bool { return a.Timestamps[i] < a.Timestamps[j] }

func (a StringPoints) Swap(i, j int) {
	a.Timestamps[i], a.Timestamps[j] = a.Timestamps[j], a.Timestamps[i]
	a.Values[i], a.Values[j] = a.Values[j], a.Values[i]
}

type stringS map[int64]string

func stringF(points stringS) datatypes.ReadResponse_Frame {
	var block StringPoints
	for t, v := range points {
		block.Timestamps = append(block.Timestamps, t)
		block.Values = append(block.Values, v)
	}

	sort.Sort(block)
	pointsFrame := datatypes.ReadResponse_StringPointsFrame(block)

	return datatypes.ReadResponse_Frame{
		Data: &datatypes.ReadResponse_Frame_StringPoints{
			StringPoints: &pointsFrame,
		},
	}
}

type BooleanPoints datatypes.ReadResponse_BooleanPointsFrame

func (a BooleanPoints) Len() int { return len(a.Timestamps) }

func (a BooleanPoints) Less(i, j int) bool { return a.Timestamps[i] < a.Timestamps[j] }

func (a BooleanPoints) Swap(i, j int) {
	a.Timestamps[i], a.Timestamps[j] = a.Timestamps[j], a.Timestamps[i]
	a.Values[i], a.Values[j] = a.Values[j], a.Values[i]
}

type booleanS map[int64]bool

func booleanF(points booleanS) datatypes.ReadResponse_Frame {
	var block BooleanPoints
	for t, v := range points {
		block.Timestamps = append(block.Timestamps, t)
		block.Values = append(block.Values, v)
	}

	sort.Sort(block)
	pointsFrame := datatypes.ReadResponse_BooleanPointsFrame(block)

	return datatypes.ReadResponse_Frame{
		Data: &datatypes.ReadResponse_Frame_BooleanPoints{
			BooleanPoints: &pointsFrame,
		},
	}
}
