// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: storage_common.proto

package datatypes

import (
	context "context"
	encoding_binary "encoding/binary"
	fmt "fmt"
	_ "github.com/gogo/protobuf/gogoproto"
	proto "github.com/gogo/protobuf/proto"
	types "github.com/gogo/protobuf/types"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type ReadGroupRequest_Group int32

const (
	// GroupNone returns all series as a single group.
	// The single GroupFrame.TagKeys will be the union of all tag keys.
	GroupNone ReadGroupRequest_Group = 0
	// GroupBy returns a group for each unique value of the specified GroupKeys.
	GroupBy ReadGroupRequest_Group = 2
)

var ReadGroupRequest_Group_name = map[int32]string{
	0: "GROUP_NONE",
	2: "GROUP_BY",
}

var ReadGroupRequest_Group_value = map[string]int32{
	"GROUP_NONE": 0,
	"GROUP_BY":   2,
}

func (x ReadGroupRequest_Group) String() string {
	return proto.EnumName(ReadGroupRequest_Group_name, int32(x))
}

func (ReadGroupRequest_Group) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{1, 0}
}

// TODO(jlapacik): This field is only used in unit tests.
// Specifically the two tests in group_resultset_test.go.
// This field should be removed and the tests that depend
// on it refactored.
type ReadGroupRequest_HintFlags int32

const (
	HintNone     ReadGroupRequest_HintFlags = 0
	HintNoPoints ReadGroupRequest_HintFlags = 1
	HintNoSeries ReadGroupRequest_HintFlags = 2
	// HintSchemaAllTime performs schema queries without using time ranges
	HintSchemaAllTime ReadGroupRequest_HintFlags = 4
)

var ReadGroupRequest_HintFlags_name = map[int32]string{
	0: "HINT_NONE",
	1: "HINT_NO_POINTS",
	2: "HINT_NO_SERIES",
	4: "HINT_SCHEMA_ALL_TIME",
}

var ReadGroupRequest_HintFlags_value = map[string]int32{
	"HINT_NONE":            0,
	"HINT_NO_POINTS":       1,
	"HINT_NO_SERIES":       2,
	"HINT_SCHEMA_ALL_TIME": 4,
}

func (x ReadGroupRequest_HintFlags) String() string {
	return proto.EnumName(ReadGroupRequest_HintFlags_name, int32(x))
}

func (ReadGroupRequest_HintFlags) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{1, 1}
}

type Aggregate_AggregateType int32

const (
	AggregateTypeNone  Aggregate_AggregateType = 0
	AggregateTypeSum   Aggregate_AggregateType = 1
	AggregateTypeCount Aggregate_AggregateType = 2
)

var Aggregate_AggregateType_name = map[int32]string{
	0: "NONE",
	1: "SUM",
	2: "COUNT",
}

var Aggregate_AggregateType_value = map[string]int32{
	"NONE":  0,
	"SUM":   1,
	"COUNT": 2,
}

func (x Aggregate_AggregateType) String() string {
	return proto.EnumName(Aggregate_AggregateType_name, int32(x))
}

func (Aggregate_AggregateType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{2, 0}
}

type ReadResponse_FrameType int32

const (
	FrameTypeSeries ReadResponse_FrameType = 0
	FrameTypePoints ReadResponse_FrameType = 1
)

var ReadResponse_FrameType_name = map[int32]string{
	0: "SERIES",
	1: "POINTS",
}

var ReadResponse_FrameType_value = map[string]int32{
	"SERIES": 0,
	"POINTS": 1,
}

func (x ReadResponse_FrameType) String() string {
	return proto.EnumName(ReadResponse_FrameType_name, int32(x))
}

func (ReadResponse_FrameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 0}
}

type ReadResponse_DataType int32

const (
	DataTypeFloat    ReadResponse_DataType = 0
	DataTypeInteger  ReadResponse_DataType = 1
	DataTypeUnsigned ReadResponse_DataType = 2
	DataTypeBoolean  ReadResponse_DataType = 3
	DataTypeString   ReadResponse_DataType = 4
)

var ReadResponse_DataType_name = map[int32]string{
	0: "FLOAT",
	1: "INTEGER",
	2: "UNSIGNED",
	3: "BOOLEAN",
	4: "STRING",
}

var ReadResponse_DataType_value = map[string]int32{
	"FLOAT":    0,
	"INTEGER":  1,
	"UNSIGNED": 2,
	"BOOLEAN":  3,
	"STRING":   4,
}

func (x ReadResponse_DataType) String() string {
	return proto.EnumName(ReadResponse_DataType_name, int32(x))
}

func (ReadResponse_DataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 1}
}

type ReadFilterRequest struct {
	ReadSource *types.Any     `protobuf:"bytes,1,opt,name=read_source,json=readSource,proto3" json:"read_source,omitempty"`
	Range      TimestampRange `protobuf:"bytes,2,opt,name=range,proto3" json:"range"`
	Predicate  *Predicate     `protobuf:"bytes,3,opt,name=predicate,proto3" json:"predicate,omitempty"`
}

func (m *ReadFilterRequest) Reset()         { *m = ReadFilterRequest{} }
func (m *ReadFilterRequest) String() string { return proto.CompactTextString(m) }
func (*ReadFilterRequest) ProtoMessage()    {}
func (*ReadFilterRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{0}
}
func (m *ReadFilterRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadFilterRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadFilterRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadFilterRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadFilterRequest.Merge(m, src)
}
func (m *ReadFilterRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReadFilterRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadFilterRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadFilterRequest proto.InternalMessageInfo

type ReadGroupRequest struct {
	ReadSource *types.Any     `protobuf:"bytes,1,opt,name=read_source,json=readSource,proto3" json:"read_source,omitempty"`
	Range      TimestampRange `protobuf:"bytes,2,opt,name=range,proto3" json:"range"`
	Predicate  *Predicate     `protobuf:"bytes,3,opt,name=predicate,proto3" json:"predicate,omitempty"`
	// GroupKeys specifies a list of tag keys used to order the data.
	// It is dependent on the Group property to determine its behavior.
	GroupKeys []string               `protobuf:"bytes,4,rep,name=group_keys,json=groupKeys,proto3" json:"group_keys,omitempty"`
	Group     ReadGroupRequest_Group `protobuf:"varint,5,opt,name=group,proto3,enum=influxdata.platform.storage.ReadGroupRequest_Group" json:"group,omitempty"`
	Aggregate *Aggregate             `protobuf:"bytes,6,opt,name=aggregate,proto3" json:"aggregate,omitempty"`
	Hints     HintFlags              `protobuf:"fixed32,7,opt,name=hints,proto3,casttype=HintFlags" json:"hints,omitempty"`
}

func (m *ReadGroupRequest) Reset()         { *m = ReadGroupRequest{} }
func (m *ReadGroupRequest) String() string { return proto.CompactTextString(m) }
func (*ReadGroupRequest) ProtoMessage()    {}
func (*ReadGroupRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{1}
}
func (m *ReadGroupRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadGroupRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadGroupRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadGroupRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadGroupRequest.Merge(m, src)
}
func (m *ReadGroupRequest) XXX_Size() int {
	return m.Size()
}
func (m *ReadGroupRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadGroupRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReadGroupRequest proto.InternalMessageInfo

type Aggregate struct {
	Type Aggregate_AggregateType `protobuf:"varint,1,opt,name=type,proto3,enum=influxdata.platform.storage.Aggregate_AggregateType" json:"type,omitempty"`
}

func (m *Aggregate) Reset()         { *m = Aggregate{} }
func (m *Aggregate) String() string { return proto.CompactTextString(m) }
func (*Aggregate) ProtoMessage()    {}
func (*Aggregate) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{2}
}
func (m *Aggregate) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Aggregate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Aggregate.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Aggregate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Aggregate.Merge(m, src)
}
func (m *Aggregate) XXX_Size() int {
	return m.Size()
}
func (m *Aggregate) XXX_DiscardUnknown() {
	xxx_messageInfo_Aggregate.DiscardUnknown(m)
}

var xxx_messageInfo_Aggregate proto.InternalMessageInfo

type Tag struct {
	Key   []byte `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
}

func (m *Tag) Reset()         { *m = Tag{} }
func (m *Tag) String() string { return proto.CompactTextString(m) }
func (*Tag) ProtoMessage()    {}
func (*Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{3}
}
func (m *Tag) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Tag.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tag.Merge(m, src)
}
func (m *Tag) XXX_Size() int {
	return m.Size()
}
func (m *Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_Tag proto.InternalMessageInfo

// Response message for ReadFilter and ReadGroup
type ReadResponse struct {
	Frames []ReadResponse_Frame `protobuf:"bytes,1,rep,name=frames,proto3" json:"frames"`
}

func (m *ReadResponse) Reset()         { *m = ReadResponse{} }
func (m *ReadResponse) String() string { return proto.CompactTextString(m) }
func (*ReadResponse) ProtoMessage()    {}
func (*ReadResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4}
}
func (m *ReadResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse.Merge(m, src)
}
func (m *ReadResponse) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse proto.InternalMessageInfo

type ReadResponse_Frame struct {
	// Types that are valid to be assigned to Data:
	//	*ReadResponse_Frame_Group
	//	*ReadResponse_Frame_Series
	//	*ReadResponse_Frame_FloatPoints
	//	*ReadResponse_Frame_IntegerPoints
	//	*ReadResponse_Frame_UnsignedPoints
	//	*ReadResponse_Frame_BooleanPoints
	//	*ReadResponse_Frame_StringPoints
	Data isReadResponse_Frame_Data `protobuf_oneof:"data"`
}

func (m *ReadResponse_Frame) Reset()         { *m = ReadResponse_Frame{} }
func (m *ReadResponse_Frame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_Frame) ProtoMessage()    {}
func (*ReadResponse_Frame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 0}
}
func (m *ReadResponse_Frame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_Frame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_Frame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_Frame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_Frame.Merge(m, src)
}
func (m *ReadResponse_Frame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_Frame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_Frame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_Frame proto.InternalMessageInfo

type isReadResponse_Frame_Data interface {
	isReadResponse_Frame_Data()
	MarshalTo([]byte) (int, error)
	Size() int
}

type ReadResponse_Frame_Group struct {
	Group *ReadResponse_GroupFrame `protobuf:"bytes,7,opt,name=group,proto3,oneof" json:"group,omitempty"`
}
type ReadResponse_Frame_Series struct {
	Series *ReadResponse_SeriesFrame `protobuf:"bytes,1,opt,name=series,proto3,oneof" json:"series,omitempty"`
}
type ReadResponse_Frame_FloatPoints struct {
	FloatPoints *ReadResponse_FloatPointsFrame `protobuf:"bytes,2,opt,name=float_points,json=floatPoints,proto3,oneof" json:"float_points,omitempty"`
}
type ReadResponse_Frame_IntegerPoints struct {
	IntegerPoints *ReadResponse_IntegerPointsFrame `protobuf:"bytes,3,opt,name=integer_points,json=integerPoints,proto3,oneof" json:"integer_points,omitempty"`
}
type ReadResponse_Frame_UnsignedPoints struct {
	UnsignedPoints *ReadResponse_UnsignedPointsFrame `protobuf:"bytes,4,opt,name=unsigned_points,json=unsignedPoints,proto3,oneof" json:"unsigned_points,omitempty"`
}
type ReadResponse_Frame_BooleanPoints struct {
	BooleanPoints *ReadResponse_BooleanPointsFrame `protobuf:"bytes,5,opt,name=boolean_points,json=booleanPoints,proto3,oneof" json:"boolean_points,omitempty"`
}
type ReadResponse_Frame_StringPoints struct {
	StringPoints *ReadResponse_StringPointsFrame `protobuf:"bytes,6,opt,name=string_points,json=stringPoints,proto3,oneof" json:"string_points,omitempty"`
}

func (*ReadResponse_Frame_Group) isReadResponse_Frame_Data()          {}
func (*ReadResponse_Frame_Series) isReadResponse_Frame_Data()         {}
func (*ReadResponse_Frame_FloatPoints) isReadResponse_Frame_Data()    {}
func (*ReadResponse_Frame_IntegerPoints) isReadResponse_Frame_Data()  {}
func (*ReadResponse_Frame_UnsignedPoints) isReadResponse_Frame_Data() {}
func (*ReadResponse_Frame_BooleanPoints) isReadResponse_Frame_Data()  {}
func (*ReadResponse_Frame_StringPoints) isReadResponse_Frame_Data()   {}

func (m *ReadResponse_Frame) GetData() isReadResponse_Frame_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ReadResponse_Frame) GetGroup() *ReadResponse_GroupFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_Group); ok {
		return x.Group
	}
	return nil
}

func (m *ReadResponse_Frame) GetSeries() *ReadResponse_SeriesFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_Series); ok {
		return x.Series
	}
	return nil
}

func (m *ReadResponse_Frame) GetFloatPoints() *ReadResponse_FloatPointsFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_FloatPoints); ok {
		return x.FloatPoints
	}
	return nil
}

func (m *ReadResponse_Frame) GetIntegerPoints() *ReadResponse_IntegerPointsFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_IntegerPoints); ok {
		return x.IntegerPoints
	}
	return nil
}

func (m *ReadResponse_Frame) GetUnsignedPoints() *ReadResponse_UnsignedPointsFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_UnsignedPoints); ok {
		return x.UnsignedPoints
	}
	return nil
}

func (m *ReadResponse_Frame) GetBooleanPoints() *ReadResponse_BooleanPointsFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_BooleanPoints); ok {
		return x.BooleanPoints
	}
	return nil
}

func (m *ReadResponse_Frame) GetStringPoints() *ReadResponse_StringPointsFrame {
	if x, ok := m.GetData().(*ReadResponse_Frame_StringPoints); ok {
		return x.StringPoints
	}
	return nil
}

// XXX_OneofWrappers is for the internal use of the proto package.
func (*ReadResponse_Frame) XXX_OneofWrappers() []interface{} {
	return []interface{}{
		(*ReadResponse_Frame_Group)(nil),
		(*ReadResponse_Frame_Series)(nil),
		(*ReadResponse_Frame_FloatPoints)(nil),
		(*ReadResponse_Frame_IntegerPoints)(nil),
		(*ReadResponse_Frame_UnsignedPoints)(nil),
		(*ReadResponse_Frame_BooleanPoints)(nil),
		(*ReadResponse_Frame_StringPoints)(nil),
	}
}

type ReadResponse_GroupFrame struct {
	// TagKeys
	TagKeys [][]byte `protobuf:"bytes,1,rep,name=tag_keys,json=tagKeys,proto3" json:"tag_keys,omitempty"`
	// PartitionKeyVals is the values of the partition key for this group, order matching ReadGroupRequest.GroupKeys
	PartitionKeyVals [][]byte `protobuf:"bytes,2,rep,name=partition_key_vals,json=partitionKeyVals,proto3" json:"partition_key_vals,omitempty"`
}

func (m *ReadResponse_GroupFrame) Reset()         { *m = ReadResponse_GroupFrame{} }
func (m *ReadResponse_GroupFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_GroupFrame) ProtoMessage()    {}
func (*ReadResponse_GroupFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 1}
}
func (m *ReadResponse_GroupFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_GroupFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_GroupFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_GroupFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_GroupFrame.Merge(m, src)
}
func (m *ReadResponse_GroupFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_GroupFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_GroupFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_GroupFrame proto.InternalMessageInfo

type ReadResponse_SeriesFrame struct {
	Tags     []Tag                 `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	DataType ReadResponse_DataType `protobuf:"varint,2,opt,name=data_type,json=dataType,proto3,enum=influxdata.platform.storage.ReadResponse_DataType" json:"data_type,omitempty"`
}

func (m *ReadResponse_SeriesFrame) Reset()         { *m = ReadResponse_SeriesFrame{} }
func (m *ReadResponse_SeriesFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_SeriesFrame) ProtoMessage()    {}
func (*ReadResponse_SeriesFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 2}
}
func (m *ReadResponse_SeriesFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_SeriesFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_SeriesFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_SeriesFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_SeriesFrame.Merge(m, src)
}
func (m *ReadResponse_SeriesFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_SeriesFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_SeriesFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_SeriesFrame proto.InternalMessageInfo

type ReadResponse_FloatPointsFrame struct {
	Timestamps []int64   `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []float64 `protobuf:"fixed64,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *ReadResponse_FloatPointsFrame) Reset()         { *m = ReadResponse_FloatPointsFrame{} }
func (m *ReadResponse_FloatPointsFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_FloatPointsFrame) ProtoMessage()    {}
func (*ReadResponse_FloatPointsFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 3}
}
func (m *ReadResponse_FloatPointsFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_FloatPointsFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_FloatPointsFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_FloatPointsFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_FloatPointsFrame.Merge(m, src)
}
func (m *ReadResponse_FloatPointsFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_FloatPointsFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_FloatPointsFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_FloatPointsFrame proto.InternalMessageInfo

type ReadResponse_IntegerPointsFrame struct {
	Timestamps []int64 `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []int64 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *ReadResponse_IntegerPointsFrame) Reset()         { *m = ReadResponse_IntegerPointsFrame{} }
func (m *ReadResponse_IntegerPointsFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_IntegerPointsFrame) ProtoMessage()    {}
func (*ReadResponse_IntegerPointsFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 4}
}
func (m *ReadResponse_IntegerPointsFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_IntegerPointsFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_IntegerPointsFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_IntegerPointsFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_IntegerPointsFrame.Merge(m, src)
}
func (m *ReadResponse_IntegerPointsFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_IntegerPointsFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_IntegerPointsFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_IntegerPointsFrame proto.InternalMessageInfo

type ReadResponse_UnsignedPointsFrame struct {
	Timestamps []int64  `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []uint64 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *ReadResponse_UnsignedPointsFrame) Reset()         { *m = ReadResponse_UnsignedPointsFrame{} }
func (m *ReadResponse_UnsignedPointsFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_UnsignedPointsFrame) ProtoMessage()    {}
func (*ReadResponse_UnsignedPointsFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 5}
}
func (m *ReadResponse_UnsignedPointsFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_UnsignedPointsFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_UnsignedPointsFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_UnsignedPointsFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_UnsignedPointsFrame.Merge(m, src)
}
func (m *ReadResponse_UnsignedPointsFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_UnsignedPointsFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_UnsignedPointsFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_UnsignedPointsFrame proto.InternalMessageInfo

type ReadResponse_BooleanPointsFrame struct {
	Timestamps []int64 `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []bool  `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
}

func (m *ReadResponse_BooleanPointsFrame) Reset()         { *m = ReadResponse_BooleanPointsFrame{} }
func (m *ReadResponse_BooleanPointsFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_BooleanPointsFrame) ProtoMessage()    {}
func (*ReadResponse_BooleanPointsFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 6}
}
func (m *ReadResponse_BooleanPointsFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_BooleanPointsFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_BooleanPointsFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_BooleanPointsFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_BooleanPointsFrame.Merge(m, src)
}
func (m *ReadResponse_BooleanPointsFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_BooleanPointsFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_BooleanPointsFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_BooleanPointsFrame proto.InternalMessageInfo

type ReadResponse_StringPointsFrame struct {
	Timestamps []int64  `protobuf:"fixed64,1,rep,packed,name=timestamps,proto3" json:"timestamps,omitempty"`
	Values     []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
}

func (m *ReadResponse_StringPointsFrame) Reset()         { *m = ReadResponse_StringPointsFrame{} }
func (m *ReadResponse_StringPointsFrame) String() string { return proto.CompactTextString(m) }
func (*ReadResponse_StringPointsFrame) ProtoMessage()    {}
func (*ReadResponse_StringPointsFrame) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{4, 7}
}
func (m *ReadResponse_StringPointsFrame) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ReadResponse_StringPointsFrame) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ReadResponse_StringPointsFrame.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ReadResponse_StringPointsFrame) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReadResponse_StringPointsFrame.Merge(m, src)
}
func (m *ReadResponse_StringPointsFrame) XXX_Size() int {
	return m.Size()
}
func (m *ReadResponse_StringPointsFrame) XXX_DiscardUnknown() {
	xxx_messageInfo_ReadResponse_StringPointsFrame.DiscardUnknown(m)
}

var xxx_messageInfo_ReadResponse_StringPointsFrame proto.InternalMessageInfo

type CapabilitiesResponse struct {
	Caps map[string]string `protobuf:"bytes,1,rep,name=caps,proto3" json:"caps,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (m *CapabilitiesResponse) Reset()         { *m = CapabilitiesResponse{} }
func (m *CapabilitiesResponse) String() string { return proto.CompactTextString(m) }
func (*CapabilitiesResponse) ProtoMessage()    {}
func (*CapabilitiesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{5}
}
func (m *CapabilitiesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *CapabilitiesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_CapabilitiesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *CapabilitiesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CapabilitiesResponse.Merge(m, src)
}
func (m *CapabilitiesResponse) XXX_Size() int {
	return m.Size()
}
func (m *CapabilitiesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CapabilitiesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CapabilitiesResponse proto.InternalMessageInfo

// Specifies a continuous range of nanosecond timestamps.
type TimestampRange struct {
	// Start defines the inclusive lower bound.
	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	// End defines the exclusive upper bound.
	End int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (m *TimestampRange) Reset()         { *m = TimestampRange{} }
func (m *TimestampRange) String() string { return proto.CompactTextString(m) }
func (*TimestampRange) ProtoMessage()    {}
func (*TimestampRange) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{6}
}
func (m *TimestampRange) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TimestampRange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TimestampRange.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TimestampRange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimestampRange.Merge(m, src)
}
func (m *TimestampRange) XXX_Size() int {
	return m.Size()
}
func (m *TimestampRange) XXX_DiscardUnknown() {
	xxx_messageInfo_TimestampRange.DiscardUnknown(m)
}

var xxx_messageInfo_TimestampRange proto.InternalMessageInfo

// TagKeysRequest is the request message for Storage.TagKeys.
type TagKeysRequest struct {
	TagsSource *types.Any     `protobuf:"bytes,1,opt,name=tags_source,json=tagsSource,proto3" json:"tags_source,omitempty"`
	Range      TimestampRange `protobuf:"bytes,2,opt,name=range,proto3" json:"range"`
	Predicate  *Predicate     `protobuf:"bytes,3,opt,name=predicate,proto3" json:"predicate,omitempty"`
}

func (m *TagKeysRequest) Reset()         { *m = TagKeysRequest{} }
func (m *TagKeysRequest) String() string { return proto.CompactTextString(m) }
func (*TagKeysRequest) ProtoMessage()    {}
func (*TagKeysRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{7}
}
func (m *TagKeysRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TagKeysRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TagKeysRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TagKeysRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagKeysRequest.Merge(m, src)
}
func (m *TagKeysRequest) XXX_Size() int {
	return m.Size()
}
func (m *TagKeysRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagKeysRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagKeysRequest proto.InternalMessageInfo

// TagValuesRequest is the request message for Storage.TagValues.
type TagValuesRequest struct {
	TagsSource *types.Any     `protobuf:"bytes,1,opt,name=tags_source,json=tagsSource,proto3" json:"tags_source,omitempty"`
	Range      TimestampRange `protobuf:"bytes,2,opt,name=range,proto3" json:"range"`
	Predicate  *Predicate     `protobuf:"bytes,3,opt,name=predicate,proto3" json:"predicate,omitempty"`
	TagKey     string         `protobuf:"bytes,4,opt,name=tag_key,json=tagKey,proto3" json:"tag_key,omitempty"`
}

func (m *TagValuesRequest) Reset()         { *m = TagValuesRequest{} }
func (m *TagValuesRequest) String() string { return proto.CompactTextString(m) }
func (*TagValuesRequest) ProtoMessage()    {}
func (*TagValuesRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{8}
}
func (m *TagValuesRequest) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TagValuesRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TagValuesRequest.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TagValuesRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagValuesRequest.Merge(m, src)
}
func (m *TagValuesRequest) XXX_Size() int {
	return m.Size()
}
func (m *TagValuesRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_TagValuesRequest.DiscardUnknown(m)
}

var xxx_messageInfo_TagValuesRequest proto.InternalMessageInfo

// Response message for Storage.TagKeys and Storage.TagValues.
type StringValuesResponse struct {
	Values [][]byte `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (m *StringValuesResponse) Reset()         { *m = StringValuesResponse{} }
func (m *StringValuesResponse) String() string { return proto.CompactTextString(m) }
func (*StringValuesResponse) ProtoMessage()    {}
func (*StringValuesResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_715e4bf4cdf1f73d, []int{9}
}
func (m *StringValuesResponse) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *StringValuesResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_StringValuesResponse.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *StringValuesResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StringValuesResponse.Merge(m, src)
}
func (m *StringValuesResponse) XXX_Size() int {
	return m.Size()
}
func (m *StringValuesResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_StringValuesResponse.DiscardUnknown(m)
}

var xxx_messageInfo_StringValuesResponse proto.InternalMessageInfo

func init() {
	proto.RegisterEnum("influxdata.platform.storage.ReadGroupRequest_Group", ReadGroupRequest_Group_name, ReadGroupRequest_Group_value)
	proto.RegisterEnum("influxdata.platform.storage.ReadGroupRequest_HintFlags", ReadGroupRequest_HintFlags_name, ReadGroupRequest_HintFlags_value)
	proto.RegisterEnum("influxdata.platform.storage.Aggregate_AggregateType", Aggregate_AggregateType_name, Aggregate_AggregateType_value)
	proto.RegisterEnum("influxdata.platform.storage.ReadResponse_FrameType", ReadResponse_FrameType_name, ReadResponse_FrameType_value)
	proto.RegisterEnum("influxdata.platform.storage.ReadResponse_DataType", ReadResponse_DataType_name, ReadResponse_DataType_value)
	proto.RegisterType((*ReadFilterRequest)(nil), "influxdata.platform.storage.ReadFilterRequest")
	proto.RegisterType((*ReadGroupRequest)(nil), "influxdata.platform.storage.ReadGroupRequest")
	proto.RegisterType((*Aggregate)(nil), "influxdata.platform.storage.Aggregate")
	proto.RegisterType((*Tag)(nil), "influxdata.platform.storage.Tag")
	proto.RegisterType((*ReadResponse)(nil), "influxdata.platform.storage.ReadResponse")
	proto.RegisterType((*ReadResponse_Frame)(nil), "influxdata.platform.storage.ReadResponse.Frame")
	proto.RegisterType((*ReadResponse_GroupFrame)(nil), "influxdata.platform.storage.ReadResponse.GroupFrame")
	proto.RegisterType((*ReadResponse_SeriesFrame)(nil), "influxdata.platform.storage.ReadResponse.SeriesFrame")
	proto.RegisterType((*ReadResponse_FloatPointsFrame)(nil), "influxdata.platform.storage.ReadResponse.FloatPointsFrame")
	proto.RegisterType((*ReadResponse_IntegerPointsFrame)(nil), "influxdata.platform.storage.ReadResponse.IntegerPointsFrame")
	proto.RegisterType((*ReadResponse_UnsignedPointsFrame)(nil), "influxdata.platform.storage.ReadResponse.UnsignedPointsFrame")
	proto.RegisterType((*ReadResponse_BooleanPointsFrame)(nil), "influxdata.platform.storage.ReadResponse.BooleanPointsFrame")
	proto.RegisterType((*ReadResponse_StringPointsFrame)(nil), "influxdata.platform.storage.ReadResponse.StringPointsFrame")
	proto.RegisterType((*CapabilitiesResponse)(nil), "influxdata.platform.storage.CapabilitiesResponse")
	proto.RegisterMapType((map[string]string)(nil), "influxdata.platform.storage.CapabilitiesResponse.CapsEntry")
	proto.RegisterType((*TimestampRange)(nil), "influxdata.platform.storage.TimestampRange")
	proto.RegisterType((*TagKeysRequest)(nil), "influxdata.platform.storage.TagKeysRequest")
	proto.RegisterType((*TagValuesRequest)(nil), "influxdata.platform.storage.TagValuesRequest")
	proto.RegisterType((*StringValuesResponse)(nil), "influxdata.platform.storage.StringValuesResponse")
}

func init() { proto.RegisterFile("storage_common.proto", fileDescriptor_715e4bf4cdf1f73d) }

var fileDescriptor_715e4bf4cdf1f73d = []byte{
	// 1498 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x57, 0xcb, 0x6f, 0xdb, 0xc8,
	0x19, 0x17, 0xf5, 0x34, 0x3f, 0xc9, 0x32, 0x3d, 0x51, 0x5d, 0x87, 0x69, 0x24, 0x56, 0x28, 0x52,
	0x17, 0x49, 0xe4, 0xd4, 0x49, 0xd1, 0x20, 0x6d, 0x0f, 0x96, 0x23, 0x5b, 0x6a, 0x6c, 0xc9, 0xa0,
	0xe4, 0x00, 0xe9, 0x45, 0x18, 0xdb, 0x63, 0x86, 0x88, 0x44, 0xb2, 0x24, 0x15, 0x58, 0x40, 0x2f,
	0xbd, 0x05, 0x3a, 0xb5, 0x97, 0x1e, 0x0a, 0x08, 0x58, 0x60, 0x8f, 0x7b, 0xdf, 0xbf, 0x21, 0x87,
	0x05, 0x36, 0xc7, 0x3d, 0x09, 0xbb, 0x0a, 0xb0, 0xc0, 0xfe, 0x0b, 0x7b, 0x5a, 0xcc, 0x0c, 0x47,
	0xa2, 0x6c, 0xc3, 0x96, 0xf6, 0xb4, 0xc8, 0x6d, 0xe6, 0x7b, 0xfc, 0xbe, 0x07, 0xbf, 0xc7, 0x10,
	0x72, 0x9e, 0x6f, 0xbb, 0xd8, 0x20, 0xed, 0x13, 0xbb, 0xdb, 0xb5, 0xad, 0x92, 0xe3, 0xda, 0xbe,
	0x8d, 0xee, 0x98, 0xd6, 0x59, 0xa7, 0x77, 0x7e, 0x8a, 0x7d, 0x5c, 0x72, 0x3a, 0xd8, 0x3f, 0xb3,
	0xdd, 0x6e, 0x29, 0x90, 0x54, 0x73, 0x86, 0x6d, 0xd8, 0x4c, 0x6e, 0x93, 0x9e, 0xb8, 0x8a, 0x7a,
	0xc7, 0xb0, 0x6d, 0xa3, 0x43, 0x36, 0xd9, 0xed, 0xb8, 0x77, 0xb6, 0x49, 0xba, 0x8e, 0xdf, 0x0f,
	0x98, 0xb7, 0x2f, 0x32, 0xb1, 0x25, 0x58, 0x2b, 0x8e, 0x4b, 0x4e, 0xcd, 0x13, 0xec, 0x13, 0x4e,
	0x28, 0xfe, 0x20, 0xc1, 0xaa, 0x4e, 0xf0, 0xe9, 0xae, 0xd9, 0xf1, 0x89, 0xab, 0x93, 0x7f, 0xf6,
	0x88, 0xe7, 0xa3, 0x0a, 0xa4, 0x5d, 0x82, 0x4f, 0xdb, 0x9e, 0xdd, 0x73, 0x4f, 0xc8, 0xba, 0xa4,
	0x49, 0x1b, 0xe9, 0xad, 0x5c, 0x89, 0xe3, 0x96, 0x04, 0x6e, 0x69, 0xdb, 0xea, 0x97, 0xb3, 0xe3,
	0x51, 0x01, 0x28, 0x42, 0x93, 0xc9, 0xea, 0xe0, 0x4e, 0xce, 0x68, 0x0f, 0x12, 0x2e, 0xb6, 0x0c,
	0xb2, 0x1e, 0x65, 0x00, 0xf7, 0x4b, 0xd7, 0x04, 0x5a, 0x6a, 0x99, 0x5d, 0xe2, 0xf9, 0xb8, 0xeb,
	0xe8, 0x54, 0xa5, 0x1c, 0x7f, 0x3f, 0x2a, 0x44, 0x74, 0xae, 0x8f, 0x9e, 0x83, 0x3c, 0x71, 0x7c,
	0x3d, 0xc6, 0xc0, 0xee, 0x5d, 0x0b, 0x76, 0x28, 0xa4, 0xf5, 0xa9, 0x62, 0xf1, 0xab, 0x04, 0x28,
	0xd4, 0xd3, 0x3d, 0xd7, 0xee, 0x39, 0x9f, 0x74, 0xa8, 0xe8, 0x01, 0x80, 0x41, 0xa3, 0x6c, 0xbf,
	0x21, 0x7d, 0x6f, 0x3d, 0xae, 0xc5, 0x36, 0xe4, 0xf2, 0xf2, 0x78, 0x54, 0x90, 0x59, 0xec, 0x2f,
	0x48, 0xdf, 0xd3, 0x65, 0x43, 0x1c, 0x51, 0x0d, 0x12, 0xec, 0xb2, 0x9e, 0xd0, 0xa4, 0x8d, 0xec,
	0xd6, 0xe3, 0x6b, 0xed, 0x5d, 0xcc, 0x60, 0x89, 0x5f, 0x38, 0x02, 0x75, 0x1f, 0x1b, 0x86, 0x4b,
	0x0c, 0xea, 0x7e, 0x72, 0x0e, 0xf7, 0xb7, 0x85, 0xb4, 0x3e, 0x55, 0x44, 0x0f, 0x20, 0xf1, 0xda,
	0xb4, 0x7c, 0x6f, 0x3d, 0xa5, 0x49, 0x1b, 0xa9, 0xf2, 0xda, 0x78, 0x54, 0x48, 0x54, 0x29, 0xe1,
	0xc7, 0x51, 0x41, 0xa6, 0x87, 0xdd, 0x0e, 0x36, 0x3c, 0x9d, 0x0b, 0x15, 0xf7, 0x20, 0xc1, 0x7c,
	0x40, 0x77, 0x01, 0xf6, 0xf4, 0xc6, 0xd1, 0x61, 0xbb, 0xde, 0xa8, 0x57, 0x94, 0x88, 0xba, 0x3c,
	0x18, 0x6a, 0x3c, 0xe2, 0xba, 0x6d, 0x11, 0x74, 0x1b, 0x96, 0x38, 0xbb, 0xfc, 0x4a, 0x89, 0xaa,
	0xe9, 0xc1, 0x50, 0x4b, 0x31, 0x66, 0xb9, 0xaf, 0xc6, 0xdf, 0x7d, 0x9e, 0x8f, 0x14, 0xbf, 0x90,
	0x60, 0x8a, 0x8e, 0xee, 0x80, 0x5c, 0xad, 0xd5, 0x5b, 0x02, 0x2c, 0x33, 0x18, 0x6a, 0x4b, 0x94,
	0xcb, 0xb0, 0x7e, 0x07, 0xd9, 0x80, 0xd9, 0x3e, 0x6c, 0xd4, 0xea, 0xad, 0xa6, 0x22, 0xa9, 0xca,
	0x60, 0xa8, 0x65, 0xb8, 0xc4, 0xa1, 0x4d, 0x3d, 0x0b, 0x4b, 0x35, 0x2b, 0x7a, 0xad, 0xd2, 0x54,
	0xa2, 0x61, 0xa9, 0x26, 0x71, 0x4d, 0xe2, 0xa1, 0x4d, 0xc8, 0x31, 0xa9, 0xe6, 0x4e, 0xb5, 0x72,
	0xb0, 0xdd, 0xde, 0xde, 0xdf, 0x6f, 0xb7, 0x6a, 0x07, 0x15, 0x25, 0xae, 0xfe, 0x6a, 0x30, 0xd4,
	0x56, 0xa9, 0x6c, 0xf3, 0xe4, 0x35, 0xe9, 0xe2, 0xed, 0x4e, 0x87, 0x96, 0x4e, 0xe0, 0xed, 0xd7,
	0x12, 0xc8, 0x93, 0xec, 0xa1, 0x2a, 0xc4, 0xfd, 0xbe, 0xc3, 0x0b, 0x38, 0xbb, 0xf5, 0x64, 0xbe,
	0x9c, 0x4f, 0x4f, 0xad, 0xbe, 0x43, 0x74, 0x86, 0x50, 0x3c, 0x87, 0xe5, 0x19, 0x32, 0x2a, 0x40,
	0x3c, 0xc8, 0x01, 0xf3, 0x67, 0x86, 0xc9, 0x92, 0x71, 0x17, 0x62, 0xcd, 0xa3, 0x03, 0x45, 0x52,
	0x73, 0x83, 0xa1, 0xa6, 0xcc, 0xf0, 0x9b, 0xbd, 0x2e, 0xfa, 0x2d, 0x24, 0x76, 0x1a, 0x47, 0xf5,
	0x96, 0x12, 0x55, 0xd7, 0x06, 0x43, 0x0d, 0xcd, 0x08, 0xec, 0xd8, 0x3d, 0xcb, 0x0f, 0x22, 0x7a,
	0x08, 0xb1, 0x16, 0x36, 0x90, 0x02, 0xb1, 0x37, 0xa4, 0xcf, 0x22, 0xc9, 0xe8, 0xf4, 0x88, 0x72,
	0x90, 0x78, 0x8b, 0x3b, 0x3d, 0xde, 0x5d, 0x19, 0x9d, 0x5f, 0x8a, 0xff, 0xcd, 0x42, 0x86, 0x56,
	0xa3, 0x4e, 0x3c, 0xc7, 0xb6, 0x3c, 0x82, 0x0e, 0x20, 0x79, 0xe6, 0xe2, 0x2e, 0xf1, 0xd6, 0x25,
	0x2d, 0xb6, 0x91, 0xde, 0xda, 0xbc, 0xb1, 0x90, 0x85, 0x6a, 0x69, 0x97, 0xea, 0x05, 0x9d, 0x18,
	0x80, 0xa8, 0xef, 0x92, 0x90, 0x60, 0x74, 0xb4, 0x2f, 0x1a, 0x24, 0xc5, 0x2a, 0xfa, 0xc9, 0xfc,
	0xb8, 0xac, 0xc0, 0x18, 0x48, 0x35, 0x22, 0x7a, 0xa4, 0x01, 0x49, 0x8f, 0x7d, 0xf9, 0x60, 0xda,
	0xfc, 0x69, 0x7e, 0x38, 0x5e, 0x31, 0x02, 0x2f, 0x80, 0x41, 0x0e, 0x64, 0xce, 0x3a, 0x36, 0xf6,
	0xdb, 0x0e, 0x2b, 0xbb, 0x60, 0x06, 0x3d, 0x5b, 0x20, 0x7a, 0xaa, 0xcd, 0x6b, 0x96, 0x27, 0x62,
	0x65, 0x3c, 0x2a, 0xa4, 0x43, 0xd4, 0x6a, 0x44, 0x4f, 0x9f, 0x4d, 0xaf, 0xe8, 0x1c, 0xb2, 0xa6,
	0xe5, 0x13, 0x83, 0xb8, 0xc2, 0x26, 0x1f, 0x55, 0x7f, 0x9d, 0xdf, 0x66, 0x8d, 0xeb, 0x87, 0xad,
	0xae, 0x8e, 0x47, 0x85, 0xe5, 0x19, 0x7a, 0x35, 0xa2, 0x2f, 0x9b, 0x61, 0x02, 0xfa, 0x17, 0xac,
	0xf4, 0x2c, 0xcf, 0x34, 0x2c, 0x72, 0x2a, 0x4c, 0xc7, 0x99, 0xe9, 0xbf, 0xcd, 0x6f, 0xfa, 0x28,
	0x00, 0x08, 0xdb, 0x46, 0xe3, 0x51, 0x21, 0x3b, 0xcb, 0xa8, 0x46, 0xf4, 0x6c, 0x6f, 0x86, 0x42,
	0xe3, 0x3e, 0xb6, 0xed, 0x0e, 0xc1, 0x96, 0x30, 0x9e, 0x58, 0x34, 0xee, 0x32, 0xd7, 0xbf, 0x14,
	0xf7, 0x0c, 0x9d, 0xc6, 0x7d, 0x1c, 0x26, 0x20, 0x1f, 0x96, 0x3d, 0xdf, 0x35, 0x2d, 0x43, 0x18,
	0xe6, 0xc3, 0xf5, 0x2f, 0x0b, 0xd4, 0x0e, 0x53, 0x0f, 0xdb, 0x55, 0xc6, 0xa3, 0x42, 0x26, 0x4c,
	0xae, 0x46, 0xf4, 0x8c, 0x17, 0xba, 0x97, 0x93, 0x10, 0xa7, 0xc8, 0xea, 0x39, 0xc0, 0xb4, 0x92,
	0xd1, 0x3d, 0x58, 0xf2, 0xb1, 0xc1, 0x77, 0x0b, 0xed, 0xb4, 0x4c, 0x39, 0x3d, 0x1e, 0x15, 0x52,
	0x2d, 0x6c, 0xb0, 0xcd, 0x92, 0xf2, 0xf9, 0x01, 0x95, 0x01, 0x39, 0xd8, 0xf5, 0x4d, 0xdf, 0xb4,
	0x2d, 0x2a, 0xdd, 0x7e, 0x8b, 0x3b, 0xb4, 0x3a, 0xa9, 0x46, 0x6e, 0x3c, 0x2a, 0x28, 0x87, 0x82,
	0xfb, 0x82, 0xf4, 0x5f, 0xe2, 0x8e, 0xa7, 0x2b, 0xce, 0x05, 0x8a, 0xfa, 0x7f, 0x09, 0xd2, 0xa1,
	0xaa, 0x47, 0xcf, 0x20, 0xee, 0x63, 0x43, 0x74, 0xb8, 0x76, 0xfd, 0x9e, 0xc5, 0x46, 0xd0, 0xd2,
	0x4c, 0x07, 0x35, 0x40, 0xa6, 0x82, 0x6d, 0x36, 0x28, 0xa3, 0x6c, 0x50, 0x6e, 0xcd, 0x9f, 0xbf,
	0xe7, 0xd8, 0xc7, 0x6c, 0x4c, 0x2e, 0x9d, 0x06, 0x27, 0xf5, 0xef, 0xa0, 0x5c, 0x6c, 0x1d, 0x94,
	0x07, 0xf0, 0xc5, 0x7e, 0xe7, 0x6e, 0x2a, 0x7a, 0x88, 0x82, 0xd6, 0x20, 0xc9, 0xc6, 0x17, 0x4f,
	0x84, 0xa4, 0x07, 0x37, 0x75, 0x1f, 0xd0, 0xe5, 0x96, 0x58, 0x10, 0x2d, 0x36, 0x41, 0x3b, 0x80,
	0x5b, 0x57, 0x54, 0xf9, 0x82, 0x70, 0xf1, 0xb0, 0x73, 0x97, 0xeb, 0x76, 0x41, 0xb4, 0xa5, 0x09,
	0xda, 0x0b, 0x58, 0xbd, 0x54, 0x8c, 0x0b, 0x82, 0xc9, 0x02, 0xac, 0xd8, 0x04, 0x99, 0x01, 0x04,
	0xab, 0x2a, 0x19, 0x2c, 0xda, 0x88, 0x7a, 0x6b, 0x30, 0xd4, 0x56, 0x26, 0xac, 0x60, 0xd7, 0x16,
	0x20, 0x39, 0xd9, 0xd7, 0xb3, 0x02, 0xdc, 0x97, 0x60, 0x13, 0x7d, 0x29, 0xc1, 0x92, 0xf8, 0xde,
	0xe8, 0x37, 0x90, 0xd8, 0xdd, 0x6f, 0x6c, 0xb7, 0x94, 0x88, 0xba, 0x3a, 0x18, 0x6a, 0xcb, 0x82,
	0xc1, 0x3e, 0x3d, 0xd2, 0x20, 0x55, 0xab, 0xb7, 0x2a, 0x7b, 0x15, 0x5d, 0x40, 0x0a, 0x7e, 0xf0,
	0x39, 0x51, 0x11, 0x96, 0x8e, 0xea, 0xcd, 0xda, 0x5e, 0xbd, 0xf2, 0x5c, 0x89, 0xf2, 0x1d, 0x29,
	0x44, 0xc4, 0x37, 0xa2, 0x28, 0xe5, 0x46, 0x63, 0xbf, 0xb2, 0x5d, 0x57, 0x62, 0xb3, 0x28, 0x41,
	0xde, 0x51, 0x1e, 0x92, 0xcd, 0x96, 0x5e, 0xab, 0xef, 0x29, 0x71, 0x15, 0x0d, 0x86, 0x5a, 0x56,
	0x08, 0xf0, 0x54, 0x06, 0x8e, 0x7f, 0x26, 0x41, 0x6e, 0x07, 0x3b, 0xf8, 0xd8, 0xec, 0x98, 0xbe,
	0x49, 0xbc, 0xc9, 0x6e, 0x6c, 0x40, 0xfc, 0x04, 0x3b, 0xa2, 0x6f, 0xae, 0x1f, 0x1b, 0x57, 0x01,
	0x50, 0xa2, 0x57, 0xb1, 0x7c, 0xb7, 0xaf, 0x33, 0x20, 0xf5, 0xcf, 0x20, 0x4f, 0x48, 0xe1, 0x95,
	0x2d, 0x5f, 0xb1, 0xb2, 0xe5, 0x60, 0x65, 0x3f, 0x8b, 0x3e, 0x95, 0x8a, 0x4f, 0x21, 0x3b, 0xfb,
	0x00, 0xa6, 0xb2, 0x9e, 0x8f, 0x5d, 0x9f, 0xe9, 0xc7, 0x74, 0x7e, 0xa1, 0x98, 0xc4, 0x3a, 0x65,
	0xfa, 0x31, 0x9d, 0x1e, 0x8b, 0xdf, 0x4b, 0x90, 0x15, 0x43, 0x66, 0xfa, 0x7c, 0xa7, 0xad, 0x3d,
	0xf7, 0xf3, 0xbd, 0x85, 0x0d, 0x4f, 0x3c, 0xdf, 0xfd, 0xc9, 0xf9, 0x97, 0xf6, 0xa7, 0xf2, 0xef,
	0x28, 0x28, 0x2d, 0x6c, 0xbc, 0x64, 0x15, 0xfe, 0x49, 0x87, 0x8a, 0x7e, 0x0d, 0xa9, 0x60, 0x97,
	0xb0, 0x3d, 0x2e, 0xeb, 0x49, 0xbe, 0x3d, 0x8a, 0x25, 0xc8, 0xf1, 0xca, 0x16, 0x59, 0x08, 0x0a,
	0x79, 0x3a, 0x07, 0xd8, 0xea, 0x11, 0x73, 0x60, 0xeb, 0x7f, 0x71, 0x48, 0x35, 0xb9, 0x25, 0x64,
	0x02, 0x4c, 0x7f, 0x6a, 0x51, 0xe9, 0xc6, 0x19, 0x3f, 0xf3, 0xf7, 0xab, 0xfe, 0x61, 0xee, 0x9d,
	0xf0, 0x48, 0x42, 0x06, 0xc8, 0x93, 0x3f, 0x22, 0xf4, 0x70, 0xa1, 0x3f, 0xa7, 0xc5, 0x0c, 0xbd,
	0x01, 0xb1, 0x60, 0xd1, 0xfd, 0x9b, 0xb6, 0x5e, 0xa8, 0x43, 0xd4, 0x3f, 0x5e, 0x2b, 0x7c, 0x55,
	0x8a, 0x1f, 0x49, 0xc8, 0x06, 0x79, 0x52, 0x7f, 0x37, 0x44, 0x75, 0xb1, 0x4e, 0x7f, 0x9e, 0xc1,
	0x57, 0x90, 0x09, 0x4f, 0x1d, 0xb4, 0x76, 0xa9, 0xae, 0x2b, 0x5d, 0xc7, 0xef, 0xdf, 0x00, 0x7e,
	0xd5, 0xe0, 0x2a, 0xff, 0xfe, 0xfd, 0x77, 0xf9, 0xc8, 0xfb, 0x71, 0x5e, 0xfa, 0x30, 0xce, 0x4b,
	0xdf, 0x8e, 0xf3, 0xd2, 0x7f, 0x3e, 0xe6, 0x23, 0x1f, 0x3e, 0xe6, 0x23, 0xdf, 0x7c, 0xcc, 0x47,
	0xfe, 0xc1, 0x5e, 0x04, 0xf4, 0x41, 0xe0, 0x1d, 0x27, 0x99, 0xad, 0xc7, 0x3f, 0x05, 0x00, 0x00,
	0xff, 0xff, 0x7f, 0x0e, 0xbb, 0xbb, 0xa6, 0x11, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StorageClient is the client API for Storage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StorageClient interface {
	// ReadFilter performs a filter operation at storage
	ReadFilter(ctx context.Context, in *ReadFilterRequest, opts ...grpc.CallOption) (Storage_ReadFilterClient, error)
	// ReadGroup performs a group operation at storage
	ReadGroup(ctx context.Context, in *ReadGroupRequest, opts ...grpc.CallOption) (Storage_ReadGroupClient, error)
	// TagKeys performs a read operation for tag keys
	TagKeys(ctx context.Context, in *TagKeysRequest, opts ...grpc.CallOption) (Storage_TagKeysClient, error)
	// TagValues performs a read operation for tag values
	TagValues(ctx context.Context, in *TagValuesRequest, opts ...grpc.CallOption) (Storage_TagValuesClient, error)
	// Capabilities returns a map of keys and values identifying the capabilities supported by the storage engine
	Capabilities(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*CapabilitiesResponse, error)
}

type storageClient struct {
	cc *grpc.ClientConn
}

func NewStorageClient(cc *grpc.ClientConn) StorageClient {
	return &storageClient{cc}
}

func (c *storageClient) ReadFilter(ctx context.Context, in *ReadFilterRequest, opts ...grpc.CallOption) (Storage_ReadFilterClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Storage_serviceDesc.Streams[0], "/influxdata.platform.storage.Storage/ReadFilter", opts...)
	if err != nil {
		return nil, err
	}
	x := &storageReadFilterClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Storage_ReadFilterClient interface {
	Recv() (*ReadResponse, error)
	grpc.ClientStream
}

type storageReadFilterClient struct {
	grpc.ClientStream
}

func (x *storageReadFilterClient) Recv() (*ReadResponse, error) {
	m := new(ReadResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *storageClient) ReadGroup(ctx context.Context, in *ReadGroupRequest, opts ...grpc.CallOption) (Storage_ReadGroupClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Storage_serviceDesc.Streams[1], "/influxdata.platform.storage.Storage/ReadGroup", opts...)
	if err != nil {
		return nil, err
	}
	x := &storageReadGroupClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Storage_ReadGroupClient interface {
	Recv() (*ReadResponse, error)
	grpc.ClientStream
}

type storageReadGroupClient struct {
	grpc.ClientStream
}

func (x *storageReadGroupClient) Recv() (*ReadResponse, error) {
	m := new(ReadResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *storageClient) TagKeys(ctx context.Context, in *TagKeysRequest, opts ...grpc.CallOption) (Storage_TagKeysClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Storage_serviceDesc.Streams[2], "/influxdata.platform.storage.Storage/TagKeys", opts...)
	if err != nil {
		return nil, err
	}
	x := &storageTagKeysClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Storage_TagKeysClient interface {
	Recv() (*StringValuesResponse, error)
	grpc.ClientStream
}

type storageTagKeysClient struct {
	grpc.ClientStream
}

func (x *storageTagKeysClient) Recv() (*StringValuesResponse, error) {
	m := new(StringValuesResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *storageClient) TagValues(ctx context.Context, in *TagValuesRequest, opts ...grpc.CallOption) (Storage_TagValuesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_Storage_serviceDesc.Streams[3], "/influxdata.platform.storage.Storage/TagValues", opts...)
	if err != nil {
		return nil, err
	}
	x := &storageTagValuesClient{stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

type Storage_TagValuesClient interface {
	Recv() (*StringValuesResponse, error)
	grpc.ClientStream
}

type storageTagValuesClient struct {
	grpc.ClientStream
}

func (x *storageTagValuesClient) Recv() (*StringValuesResponse, error) {
	m := new(StringValuesResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

func (c *storageClient) Capabilities(ctx context.Context, in *types.Empty, opts ...grpc.CallOption) (*CapabilitiesResponse, error) {
	out := new(CapabilitiesResponse)
	err := c.cc.Invoke(ctx, "/influxdata.platform.storage.Storage/Capabilities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StorageServer is the server API for Storage service.
type StorageServer interface {
	// ReadFilter performs a filter operation at storage
	ReadFilter(*ReadFilterRequest, Storage_ReadFilterServer) error
	// ReadGroup performs a group operation at storage
	ReadGroup(*ReadGroupRequest, Storage_ReadGroupServer) error
	// TagKeys performs a read operation for tag keys
	TagKeys(*TagKeysRequest, Storage_TagKeysServer) error
	// TagValues performs a read operation for tag values
	TagValues(*TagValuesRequest, Storage_TagValuesServer) error
	// Capabilities returns a map of keys and values identifying the capabilities supported by the storage engine
	Capabilities(context.Context, *types.Empty) (*CapabilitiesResponse, error)
}

// UnimplementedStorageServer can be embedded to have forward compatible implementations.
type UnimplementedStorageServer struct {
}

func (*UnimplementedStorageServer) ReadFilter(req *ReadFilterRequest, srv Storage_ReadFilterServer) error {
	return status.Errorf(codes.Unimplemented, "method ReadFilter not implemented")
}
func (*UnimplementedStorageServer) ReadGroup(req *ReadGroupRequest, srv Storage_ReadGroupServer) error {
	return status.Errorf(codes.Unimplemented, "method ReadGroup not implemented")
}
func (*UnimplementedStorageServer) TagKeys(req *TagKeysRequest, srv Storage_TagKeysServer) error {
	return status.Errorf(codes.Unimplemented, "method TagKeys not implemented")
}
func (*UnimplementedStorageServer) TagValues(req *TagValuesRequest, srv Storage_TagValuesServer) error {
	return status.Errorf(codes.Unimplemented, "method TagValues not implemented")
}
func (*UnimplementedStorageServer) Capabilities(ctx context.Context, req *types.Empty) (*CapabilitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Capabilities not implemented")
}

func RegisterStorageServer(s *grpc.Server, srv StorageServer) {
	s.RegisterService(&_Storage_serviceDesc, srv)
}

func _Storage_ReadFilter_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ReadFilterRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(StorageServer).ReadFilter(m, &storageReadFilterServer{stream})
}

type Storage_ReadFilterServer interface {
	Send(*ReadResponse) error
	grpc.ServerStream
}

type storageReadFilterServer struct {
	grpc.ServerStream
}

func (x *storageReadFilterServer) Send(m *ReadResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Storage_ReadGroup_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ReadGroupRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(StorageServer).ReadGroup(m, &storageReadGroupServer{stream})
}

type Storage_ReadGroupServer interface {
	Send(*ReadResponse) error
	grpc.ServerStream
}

type storageReadGroupServer struct {
	grpc.ServerStream
}

func (x *storageReadGroupServer) Send(m *ReadResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Storage_TagKeys_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TagKeysRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(StorageServer).TagKeys(m, &storageTagKeysServer{stream})
}

type Storage_TagKeysServer interface {
	Send(*StringValuesResponse) error
	grpc.ServerStream
}

type storageTagKeysServer struct {
	grpc.ServerStream
}

func (x *storageTagKeysServer) Send(m *StringValuesResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Storage_TagValues_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(TagValuesRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(StorageServer).TagValues(m, &storageTagValuesServer{stream})
}

type Storage_TagValuesServer interface {
	Send(*StringValuesResponse) error
	grpc.ServerStream
}

type storageTagValuesServer struct {
	grpc.ServerStream
}

func (x *storageTagValuesServer) Send(m *StringValuesResponse) error {
	return x.ServerStream.SendMsg(m)
}

func _Storage_Capabilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(types.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StorageServer).Capabilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/influxdata.platform.storage.Storage/Capabilities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StorageServer).Capabilities(ctx, req.(*types.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

var _Storage_serviceDesc = grpc.ServiceDesc{
	ServiceName: "influxdata.platform.storage.Storage",
	HandlerType: (*StorageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Capabilities",
			Handler:    _Storage_Capabilities_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ReadFilter",
			Handler:       _Storage_ReadFilter_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ReadGroup",
			Handler:       _Storage_ReadGroup_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "TagKeys",
			Handler:       _Storage_TagKeys_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "TagValues",
			Handler:       _Storage_TagValues_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "storage_common.proto",
}

func (m *ReadFilterRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadFilterRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadFilterRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Predicate != nil {
		{
			size, err := m.Predicate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStorageCommon(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.ReadSource != nil {
		{
			size, err := m.ReadSource.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadGroupRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadGroupRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadGroupRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Hints != 0 {
		i -= 4
		encoding_binary.LittleEndian.PutUint32(dAtA[i:], uint32(m.Hints))
		i--
		dAtA[i] = 0x3d
	}
	if m.Aggregate != nil {
		{
			size, err := m.Aggregate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	if m.Group != 0 {
		i = encodeVarintStorageCommon(dAtA, i, uint64(m.Group))
		i--
		dAtA[i] = 0x28
	}
	if len(m.GroupKeys) > 0 {
		for iNdEx := len(m.GroupKeys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.GroupKeys[iNdEx])
			copy(dAtA[i:], m.GroupKeys[iNdEx])
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.GroupKeys[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if m.Predicate != nil {
		{
			size, err := m.Predicate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStorageCommon(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.ReadSource != nil {
		{
			size, err := m.ReadSource.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Aggregate) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Aggregate) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Aggregate) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		i = encodeVarintStorageCommon(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *Tag) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Tag) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Tag) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Value) > 0 {
		i -= len(m.Value)
		copy(dAtA[i:], m.Value)
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Value)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Key) > 0 {
		i -= len(m.Key)
		copy(dAtA[i:], m.Key)
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Key)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Frames) > 0 {
		for iNdEx := len(m.Frames) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Frames[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintStorageCommon(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_Frame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_Frame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		{
			size := m.Data.Size()
			i -= size
			if _, err := m.Data.MarshalTo(dAtA[i:]); err != nil {
				return 0, err
			}
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_Frame_Series) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_Series) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Series != nil {
		{
			size, err := m.Series.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_FloatPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_FloatPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.FloatPoints != nil {
		{
			size, err := m.FloatPoints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x12
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_IntegerPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_IntegerPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.IntegerPoints != nil {
		{
			size, err := m.IntegerPoints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_UnsignedPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_UnsignedPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.UnsignedPoints != nil {
		{
			size, err := m.UnsignedPoints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x22
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_BooleanPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_BooleanPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.BooleanPoints != nil {
		{
			size, err := m.BooleanPoints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x2a
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_StringPoints) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_StringPoints) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.StringPoints != nil {
		{
			size, err := m.StringPoints.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x32
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_Frame_Group) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_Frame_Group) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	if m.Group != nil {
		{
			size, err := m.Group.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x3a
	}
	return len(dAtA) - i, nil
}
func (m *ReadResponse_GroupFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_GroupFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_GroupFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.PartitionKeyVals) > 0 {
		for iNdEx := len(m.PartitionKeyVals) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.PartitionKeyVals[iNdEx])
			copy(dAtA[i:], m.PartitionKeyVals[iNdEx])
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.PartitionKeyVals[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.TagKeys) > 0 {
		for iNdEx := len(m.TagKeys) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.TagKeys[iNdEx])
			copy(dAtA[i:], m.TagKeys[iNdEx])
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.TagKeys[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_SeriesFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_SeriesFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_SeriesFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.DataType != 0 {
		i = encodeVarintStorageCommon(dAtA, i, uint64(m.DataType))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Tags) > 0 {
		for iNdEx := len(m.Tags) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Tags[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintStorageCommon(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_FloatPointsFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_FloatPointsFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_FloatPointsFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			f15 := math.Float64bits(float64(m.Values[iNdEx]))
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(f15))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Values)*8))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_IntegerPointsFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_IntegerPointsFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_IntegerPointsFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		dAtA17 := make([]byte, len(m.Values)*10)
		var j16 int
		for _, num1 := range m.Values {
			num := uint64(num1)
			for num >= 1<<7 {
				dAtA17[j16] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j16++
			}
			dAtA17[j16] = uint8(num)
			j16++
		}
		i -= j16
		copy(dAtA[i:], dAtA17[:j16])
		i = encodeVarintStorageCommon(dAtA, i, uint64(j16))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_UnsignedPointsFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_UnsignedPointsFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_UnsignedPointsFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		dAtA19 := make([]byte, len(m.Values)*10)
		var j18 int
		for _, num := range m.Values {
			for num >= 1<<7 {
				dAtA19[j18] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j18++
			}
			dAtA19[j18] = uint8(num)
			j18++
		}
		i -= j18
		copy(dAtA[i:], dAtA19[:j18])
		i = encodeVarintStorageCommon(dAtA, i, uint64(j18))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_BooleanPointsFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_BooleanPointsFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_BooleanPointsFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i--
			if m.Values[iNdEx] {
				dAtA[i] = 1
			} else {
				dAtA[i] = 0
			}
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Values)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ReadResponse_StringPointsFrame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadResponse_StringPointsFrame) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ReadResponse_StringPointsFrame) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0x12
		}
	}
	if len(m.Timestamps) > 0 {
		for iNdEx := len(m.Timestamps) - 1; iNdEx >= 0; iNdEx-- {
			i -= 8
			encoding_binary.LittleEndian.PutUint64(dAtA[i:], uint64(m.Timestamps[iNdEx]))
		}
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Timestamps)*8))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *CapabilitiesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CapabilitiesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *CapabilitiesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Caps) > 0 {
		for k := range m.Caps {
			v := m.Caps[k]
			baseI := i
			i -= len(v)
			copy(dAtA[i:], v)
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(v)))
			i--
			dAtA[i] = 0x12
			i -= len(k)
			copy(dAtA[i:], k)
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(k)))
			i--
			dAtA[i] = 0xa
			i = encodeVarintStorageCommon(dAtA, i, uint64(baseI-i))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func (m *TimestampRange) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimestampRange) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TimestampRange) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.End != 0 {
		i = encodeVarintStorageCommon(dAtA, i, uint64(m.End))
		i--
		dAtA[i] = 0x10
	}
	if m.Start != 0 {
		i = encodeVarintStorageCommon(dAtA, i, uint64(m.Start))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TagKeysRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagKeysRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TagKeysRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.Predicate != nil {
		{
			size, err := m.Predicate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStorageCommon(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.TagsSource != nil {
		{
			size, err := m.TagsSource.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *TagValuesRequest) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagValuesRequest) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TagValuesRequest) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.TagKey) > 0 {
		i -= len(m.TagKey)
		copy(dAtA[i:], m.TagKey)
		i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.TagKey)))
		i--
		dAtA[i] = 0x22
	}
	if m.Predicate != nil {
		{
			size, err := m.Predicate.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0x1a
	}
	{
		size, err := m.Range.MarshalToSizedBuffer(dAtA[:i])
		if err != nil {
			return 0, err
		}
		i -= size
		i = encodeVarintStorageCommon(dAtA, i, uint64(size))
	}
	i--
	dAtA[i] = 0x12
	if m.TagsSource != nil {
		{
			size, err := m.TagsSource.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintStorageCommon(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *StringValuesResponse) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StringValuesResponse) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *StringValuesResponse) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if len(m.Values) > 0 {
		for iNdEx := len(m.Values) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.Values[iNdEx])
			copy(dAtA[i:], m.Values[iNdEx])
			i = encodeVarintStorageCommon(dAtA, i, uint64(len(m.Values[iNdEx])))
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintStorageCommon(dAtA []byte, offset int, v uint64) int {
	offset -= sovStorageCommon(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ReadFilterRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ReadSource != nil {
		l = m.ReadSource.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = m.Range.Size()
	n += 1 + l + sovStorageCommon(uint64(l))
	if m.Predicate != nil {
		l = m.Predicate.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}

func (m *ReadGroupRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.ReadSource != nil {
		l = m.ReadSource.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = m.Range.Size()
	n += 1 + l + sovStorageCommon(uint64(l))
	if m.Predicate != nil {
		l = m.Predicate.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	if len(m.GroupKeys) > 0 {
		for _, s := range m.GroupKeys {
			l = len(s)
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	if m.Group != 0 {
		n += 1 + sovStorageCommon(uint64(m.Group))
	}
	if m.Aggregate != nil {
		l = m.Aggregate.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	if m.Hints != 0 {
		n += 5
	}
	return n
}

func (m *Aggregate) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovStorageCommon(uint64(m.Type))
	}
	return n
}

func (m *Tag) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Key)
	if l > 0 {
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = len(m.Value)
	if l > 0 {
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}

func (m *ReadResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Frames) > 0 {
		for _, e := range m.Frames {
			l = e.Size()
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	return n
}

func (m *ReadResponse_Frame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Data != nil {
		n += m.Data.Size()
	}
	return n
}

func (m *ReadResponse_Frame_Series) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Series != nil {
		l = m.Series.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_FloatPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.FloatPoints != nil {
		l = m.FloatPoints.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_IntegerPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.IntegerPoints != nil {
		l = m.IntegerPoints.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_UnsignedPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.UnsignedPoints != nil {
		l = m.UnsignedPoints.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_BooleanPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.BooleanPoints != nil {
		l = m.BooleanPoints.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_StringPoints) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.StringPoints != nil {
		l = m.StringPoints.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_Frame_Group) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Group != nil {
		l = m.Group.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}
func (m *ReadResponse_GroupFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.TagKeys) > 0 {
		for _, b := range m.TagKeys {
			l = len(b)
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	if len(m.PartitionKeyVals) > 0 {
		for _, b := range m.PartitionKeyVals {
			l = len(b)
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	return n
}

func (m *ReadResponse_SeriesFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Tags) > 0 {
		for _, e := range m.Tags {
			l = e.Size()
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	if m.DataType != 0 {
		n += 1 + sovStorageCommon(uint64(m.DataType))
	}
	return n
}

func (m *ReadResponse_FloatPointsFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Values)*8)) + len(m.Values)*8
	}
	return n
}

func (m *ReadResponse_IntegerPointsFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		l = 0
		for _, e := range m.Values {
			l += sovStorageCommon(uint64(e))
		}
		n += 1 + sovStorageCommon(uint64(l)) + l
	}
	return n
}

func (m *ReadResponse_UnsignedPointsFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		l = 0
		for _, e := range m.Values {
			l += sovStorageCommon(uint64(e))
		}
		n += 1 + sovStorageCommon(uint64(l)) + l
	}
	return n
}

func (m *ReadResponse_BooleanPointsFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Values))) + len(m.Values)*1
	}
	return n
}

func (m *ReadResponse_StringPointsFrame) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Timestamps) > 0 {
		n += 1 + sovStorageCommon(uint64(len(m.Timestamps)*8)) + len(m.Timestamps)*8
	}
	if len(m.Values) > 0 {
		for _, s := range m.Values {
			l = len(s)
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	return n
}

func (m *CapabilitiesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Caps) > 0 {
		for k, v := range m.Caps {
			_ = k
			_ = v
			mapEntrySize := 1 + len(k) + sovStorageCommon(uint64(len(k))) + 1 + len(v) + sovStorageCommon(uint64(len(v)))
			n += mapEntrySize + 1 + sovStorageCommon(uint64(mapEntrySize))
		}
	}
	return n
}

func (m *TimestampRange) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Start != 0 {
		n += 1 + sovStorageCommon(uint64(m.Start))
	}
	if m.End != 0 {
		n += 1 + sovStorageCommon(uint64(m.End))
	}
	return n
}

func (m *TagKeysRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagsSource != nil {
		l = m.TagsSource.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = m.Range.Size()
	n += 1 + l + sovStorageCommon(uint64(l))
	if m.Predicate != nil {
		l = m.Predicate.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}

func (m *TagValuesRequest) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.TagsSource != nil {
		l = m.TagsSource.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = m.Range.Size()
	n += 1 + l + sovStorageCommon(uint64(l))
	if m.Predicate != nil {
		l = m.Predicate.Size()
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	l = len(m.TagKey)
	if l > 0 {
		n += 1 + l + sovStorageCommon(uint64(l))
	}
	return n
}

func (m *StringValuesResponse) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Values) > 0 {
		for _, b := range m.Values {
			l = len(b)
			n += 1 + l + sovStorageCommon(uint64(l))
		}
	}
	return n
}

func sovStorageCommon(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozStorageCommon(x uint64) (n int) {
	return sovStorageCommon(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ReadFilterRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadFilterRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadFilterRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReadSource == nil {
				m.ReadSource = &types.Any{}
			}
			if err := m.ReadSource.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Predicate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Predicate == nil {
				m.Predicate = &Predicate{}
			}
			if err := m.Predicate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadGroupRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadGroupRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadGroupRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ReadSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.ReadSource == nil {
				m.ReadSource = &types.Any{}
			}
			if err := m.ReadSource.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Predicate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Predicate == nil {
				m.Predicate = &Predicate{}
			}
			if err := m.Predicate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field GroupKeys", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.GroupKeys = append(m.GroupKeys, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			m.Group = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Group |= ReadGroupRequest_Group(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Aggregate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Aggregate == nil {
				m.Aggregate = &Aggregate{}
			}
			if err := m.Aggregate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 5 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hints", wireType)
			}
			m.Hints = 0
			if (iNdEx + 4) > l {
				return io.ErrUnexpectedEOF
			}
			m.Hints = HintFlags(encoding_binary.LittleEndian.Uint32(dAtA[iNdEx:]))
			iNdEx += 4
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Aggregate) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Aggregate: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Aggregate: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= Aggregate_AggregateType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Tag) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Tag: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Tag: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Key = append(m.Key[:0], dAtA[iNdEx:postIndex]...)
			if m.Key == nil {
				m.Key = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ReadResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ReadResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Frames", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Frames = append(m.Frames, ReadResponse_Frame{})
			if err := m.Frames[len(m.Frames)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_Frame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Frame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Frame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Series", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_SeriesFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_Series{v}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FloatPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_FloatPointsFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_FloatPoints{v}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field IntegerPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_IntegerPointsFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_IntegerPoints{v}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UnsignedPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_UnsignedPointsFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_UnsignedPoints{v}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field BooleanPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_BooleanPointsFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_BooleanPoints{v}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field StringPoints", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_StringPointsFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_StringPoints{v}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			v := &ReadResponse_GroupFrame{}
			if err := v.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			m.Data = &ReadResponse_Frame_Group{v}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_GroupFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: GroupFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: GroupFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagKeys", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TagKeys = append(m.TagKeys, make([]byte, postIndex-iNdEx))
			copy(m.TagKeys[len(m.TagKeys)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field PartitionKeyVals", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.PartitionKeyVals = append(m.PartitionKeyVals, make([]byte, postIndex-iNdEx))
			copy(m.PartitionKeyVals[len(m.PartitionKeyVals)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_SeriesFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: SeriesFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: SeriesFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Tags", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Tags = append(m.Tags, Tag{})
			if err := m.Tags[len(m.Tags)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field DataType", wireType)
			}
			m.DataType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DataType |= ReadResponse_DataType(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_FloatPointsFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FloatPointsFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FloatPointsFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 1 {
				var v uint64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				v2 := float64(math.Float64frombits(v))
				m.Values = append(m.Values, v2)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]float64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = uint64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					v2 := float64(math.Float64frombits(v))
					m.Values = append(m.Values, v2)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_IntegerPointsFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: IntegerPointsFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: IntegerPointsFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorageCommon
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_UnsignedPointsFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: UnsignedPointsFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: UnsignedPointsFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				var count int
				for _, integer := range dAtA[iNdEx:postIndex] {
					if integer < 128 {
						count++
					}
				}
				elementCount = count
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]uint64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorageCommon
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_BooleanPointsFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: BooleanPointsFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: BooleanPointsFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType == 0 {
				var v int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Values = append(m.Values, bool(v != 0))
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen
				if elementCount != 0 && len(m.Values) == 0 {
					m.Values = make([]bool, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorageCommon
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= int(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Values = append(m.Values, bool(v != 0))
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadResponse_StringPointsFrame) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StringPointsFrame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StringPointsFrame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 1 {
				var v int64
				if (iNdEx + 8) > l {
					return io.ErrUnexpectedEOF
				}
				v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
				iNdEx += 8
				m.Timestamps = append(m.Timestamps, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= int(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthStorageCommon
				}
				postIndex := iNdEx + packedLen
				if postIndex < 0 {
					return ErrInvalidLengthStorageCommon
				}
				if postIndex > l {
					return io.ErrUnexpectedEOF
				}
				var elementCount int
				elementCount = packedLen / 8
				if elementCount != 0 && len(m.Timestamps) == 0 {
					m.Timestamps = make([]int64, 0, elementCount)
				}
				for iNdEx < postIndex {
					var v int64
					if (iNdEx + 8) > l {
						return io.ErrUnexpectedEOF
					}
					v = int64(encoding_binary.LittleEndian.Uint64(dAtA[iNdEx:]))
					iNdEx += 8
					m.Timestamps = append(m.Timestamps, v)
				}
			} else {
				return fmt.Errorf("proto: wrong wireType = %d for field Timestamps", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *CapabilitiesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: CapabilitiesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: CapabilitiesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Caps", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Caps == nil {
				m.Caps = make(map[string]string)
			}
			var mapkey string
			var mapvalue string
			for iNdEx < postIndex {
				entryPreIndex := iNdEx
				var wire uint64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowStorageCommon
					}
					if iNdEx >= l {
						return io.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					wire |= uint64(b&0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				fieldNum := int32(wire >> 3)
				if fieldNum == 1 {
					var stringLenmapkey uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorageCommon
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapkey |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapkey := int(stringLenmapkey)
					if intStringLenmapkey < 0 {
						return ErrInvalidLengthStorageCommon
					}
					postStringIndexmapkey := iNdEx + intStringLenmapkey
					if postStringIndexmapkey < 0 {
						return ErrInvalidLengthStorageCommon
					}
					if postStringIndexmapkey > l {
						return io.ErrUnexpectedEOF
					}
					mapkey = string(dAtA[iNdEx:postStringIndexmapkey])
					iNdEx = postStringIndexmapkey
				} else if fieldNum == 2 {
					var stringLenmapvalue uint64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowStorageCommon
						}
						if iNdEx >= l {
							return io.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						stringLenmapvalue |= uint64(b&0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					intStringLenmapvalue := int(stringLenmapvalue)
					if intStringLenmapvalue < 0 {
						return ErrInvalidLengthStorageCommon
					}
					postStringIndexmapvalue := iNdEx + intStringLenmapvalue
					if postStringIndexmapvalue < 0 {
						return ErrInvalidLengthStorageCommon
					}
					if postStringIndexmapvalue > l {
						return io.ErrUnexpectedEOF
					}
					mapvalue = string(dAtA[iNdEx:postStringIndexmapvalue])
					iNdEx = postStringIndexmapvalue
				} else {
					iNdEx = entryPreIndex
					skippy, err := skipStorageCommon(dAtA[iNdEx:])
					if err != nil {
						return err
					}
					if (skippy < 0) || (iNdEx+skippy) < 0 {
						return ErrInvalidLengthStorageCommon
					}
					if (iNdEx + skippy) > postIndex {
						return io.ErrUnexpectedEOF
					}
					iNdEx += skippy
				}
			}
			m.Caps[mapkey] = mapvalue
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimestampRange) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TimestampRange: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TimestampRange: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field End", wireType)
			}
			m.End = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.End |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagKeysRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TagKeysRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TagKeysRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagsSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TagsSource == nil {
				m.TagsSource = &types.Any{}
			}
			if err := m.TagsSource.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Predicate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Predicate == nil {
				m.Predicate = &Predicate{}
			}
			if err := m.Predicate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagValuesRequest) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TagValuesRequest: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TagValuesRequest: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagsSource", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.TagsSource == nil {
				m.TagsSource = &types.Any{}
			}
			if err := m.TagsSource.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Range", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := m.Range.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Predicate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Predicate == nil {
				m.Predicate = &Predicate{}
			}
			if err := m.Predicate.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TagKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.TagKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *StringValuesResponse) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: StringValuesResponse: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: StringValuesResponse: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Values", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthStorageCommon
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Values = append(m.Values, make([]byte, postIndex-iNdEx))
			copy(m.Values[len(m.Values)-1], dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipStorageCommon(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthStorageCommon
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipStorageCommon(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowStorageCommon
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowStorageCommon
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthStorageCommon
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupStorageCommon
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthStorageCommon
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthStorageCommon        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowStorageCommon          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupStorageCommon = fmt.Errorf("proto: unexpected end of group")
)
