syntax = "proto3";
package influxdata.platform.storage;
option go_package = "datatypes";

import "gogoproto/gogo.proto";

message Node {
  enum Type {
    option (gogoproto.goproto_enum_prefix) = false;

    LOGICAL_EXPRESSION = 0 [(gogoproto.enumvalue_customname) = "NodeTypeLogicalExpression"];
    COMPARISON_EXPRESSION = 1 [(gogoproto.enumvalue_customname) = "NodeTypeComparisonExpression"];
    PAREN_EXPRESSION = 2 [(gogoproto.enumvalue_customname) = "NodeTypeParenExpression"];
    TAG_REF = 3 [(gogoproto.enumvalue_customname) = "NodeTypeTagRef"];
    LITERAL = 4 [(gogoproto.enumvalue_customname) = "NodeTypeLiteral"];
    FIELD_REF = 5 [(gogoproto.enumvalue_customname) = "NodeTypeFieldRef"];
  }

  enum Comparison {
    option (gogoproto.goproto_enum_prefix) = false;
    EQUAL = 0 [(gogoproto.enumvalue_customname) = "ComparisonEqual"];
    NOT_EQUAL = 1 [(gogoproto.enumvalue_customname) = "ComparisonNotEqual"];
    STARTS_WITH = 2 [(gogoproto.enumvalue_customname) = "ComparisonStartsWith"];
    REGEX = 3 [(gogoproto.enumvalue_customname) = "ComparisonRegex"];
    NOT_REGEX = 4 [(gogoproto.enumvalue_customname) = "ComparisonNotRegex"];
    LT = 5 [(gogoproto.enumvalue_customname) = "ComparisonLess"];
    LTE = 6 [(gogoproto.enumvalue_customname) = "ComparisonLessEqual"];
    GT = 7 [(gogoproto.enumvalue_customname) = "ComparisonGreater"];
    GTE = 8 [(gogoproto.enumvalue_customname) = "ComparisonGreaterEqual"];
  }

  // Logical operators apply to boolean values and combine to produce a single boolean result.
  enum Logical {
    option (gogoproto.goproto_enum_prefix) = false;

    AND = 0 [(gogoproto.enumvalue_customname) = "LogicalAnd"];
    OR = 1 [(gogoproto.enumvalue_customname) = "LogicalOr"];
  }


  Type node_type = 1 [(gogoproto.customname) = "NodeType", (gogoproto.jsontag) = "nodeType"];
  repeated Node children = 2;

  oneof value {
    string string_value = 3 [(gogoproto.customname) = "StringValue"];
    bool bool_value = 4 [(gogoproto.customname) = "BooleanValue"];
    int64 int_value = 5 [(gogoproto.customname) = "IntegerValue"];
    uint64 uint_value = 6 [(gogoproto.customname) = "UnsignedValue"];
    double float_value = 7 [(gogoproto.customname) = "FloatValue"];
    string regex_value = 8 [(gogoproto.customname) = "RegexValue"];
    string tag_ref_value = 9 [(gogoproto.customname) = "TagRefValue"];
    string field_ref_value = 10 [(gogoproto.customname) = "FieldRefValue"];
    Logical logical = 11;
    Comparison comparison = 12;
  }
}

message Predicate {
  Node root = 1;
}
