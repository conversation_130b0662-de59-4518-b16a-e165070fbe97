# ThinfluxDB Cluster

ThinfluxDB Cluster - 一个开源分布式时间序列数据库，ThinfluxDB Enterprise 的开源替代方案

## 从安装包安装

在所有服务器上执行以下操作：

1. 解压安装包：

```bash
tar -zxvf thinfluxdb-cluster-1.8.10-c1.1.2-linux-amd64.tar.gz -C /home/<USER>/thinfluxdb-cluster
```

2. 将二进制文件路径加入环境变量：

```bash
export PATH=$PATH:/home/<USER>/thinfluxdb-cluster/usr/bin
```

## 部署

### 环境配置

以下是集群环境配置，包括各服务器的IP地址、主机名和角色分配：

| IP地址          | 主机名              | 角色     |
|----------------|--------------------|----------|
| ************** | thinfluxdb-meta-01 | Meta节点 |
| ************** | thinfluxdb-meta-02 | Meta节点 |
| ************** | thinfluxdb-meta-03 | Meta节点 |
| ************** | thinfluxdb-data-01 | Data节点 |
| ************** | thinfluxdb-data-02 | Data节点 |

> 注：在实际部署中，可根据业务需求和硬件资源调整节点数量和分布。Meta节点负责集群元数据管理，Data节点负责数据存储和查询处理。

> 重要：上表中的IP地址和主机名仅为示例值，请根据实际环境进行替换。

> 重要：集群节点间需要特定端口进行通信，请确保在所有服务器上配置防火墙规则，允许相关端口（8088、8089、8091等）之间的通信。

### 基本目录创建

在所有服务器上创建以下目录：

```bash
sudo mkdir -p /etc/thinfluxdb
sudo mkdir -p /var/lib/thinfluxdb

sudo chown -R tdtgi:tdtgi /etc/thinfluxdb
sudo chown -R tdtgi:tdtgi /var/lib/thinfluxdb
```

> 注意：请将上述命令中的"tdtgi"替换为环境中实际的用户名和用户组。

### Meta 节点设置

0. 设置说明和要求

生产环境安装过程设置三个 Meta 节点，每个 Meta 节点在自己的服务器上运行。ThinfluxDB Cluster 需要**至少三个 Meta 节点且推荐奇数个 Meta 节点** 以实现高可用和冗余。

> 注 1：ThinfluxDB Cluster 不建议超过三个 Meta 节点，除非您的服务器之间的通信存在长期可靠性问题。

> 注 2：强烈建议不要在同一服务器上部署多个 Meta 节点，因为如果该特定服务器无响应，它会产生更大的潜在故障。ThinfluxDB Cluster 建议在占用空间相对较小的服务器上部署 Meta 节点。

> 注 3：要使用单个 Meta 节点启动集群，请在启动单个 Meta 节点时传递 -single-server 标志。

假设有三台服务器： thinfluxdb-meta-01, thinfluxdb-meta-02 和 thinfluxdb-meta-03，通过端口 8089（Meta节点间Raft通信）和 8091（HTTP API）进行通信。

1. 为每个服务器添加适当的 DNS 条目

> 注: 如果您只想使用 IP 地址而不是主机名，请跳过当前步骤并转到步骤 2。

确保将服务器的主机名和 IP 地址添加到hosts文件中：

```bash
************** thinfluxdb-meta-01
************** thinfluxdb-meta-02
************** thinfluxdb-meta-03
```

2. 编辑每个 Meta 节点的配置文件`/etc/thinfluxdb/thinfluxdb-meta.conf`

```toml
hostname = "thinfluxdb-meta-0x"  # 请将 0x 替换为实际的节点编号，如 01、02 或 03

[meta]
  dir = "/var/lib/thinfluxdb/meta"
```

注：如果只想使用 IP 地址而不是主机名，**必须**将 hostname 设置为 IP 地址。

3. 启动 Meta 服务

分别在服务器 thinfluxdb-meta-01、 thinfluxdb-meta-02 和 thinfluxdb-meta-03 上启动 Meta 服务：

```bash
thinfluxd-meta -config /etc/thinfluxdb/thinfluxdb-meta.conf
```

4. 将 Meta 节点加入集群

仅在一个 Meta 节点上，加入所有 Meta 节点，包括它自己。比如在 thinfluxdb-meta-01 运行：

```bash
thinfluxd-ctl add-meta thinfluxdb-meta-01:8091
thinfluxd-ctl add-meta thinfluxdb-meta-02:8091
thinfluxd-ctl add-meta thinfluxdb-meta-03:8091
```

如果只想使用 IP 地址而不是主机名或者在 Data 节点上，则应该添加 -bind 选项，指定要连接的 Meta 节点并绑定 HTTP 地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 add-meta **************:8091
thinfluxd-ctl -bind **************:8091 show
```

注意：请将上述命令中的 IP 地址替换为您实际环境中的 IP 地址。

预期的输出是：

```bash
Added meta node x at thinfluxdb-meta-0x:8091
```

注意：实际输出中的节点 ID 和名称将根据您的具体部署情况而变化。

若执行成功，则在任何 Meta 节点上执行：

```bash
thinfluxd-ctl show
```

预期的输出是：

```bash
Data Nodes
==========
ID  TCP Address  Version

Meta Nodes
==========
ID  TCP Address              Version
1   thinfluxdb-meta-01:8091  1.8.10-c1.1.2
2   thinfluxdb-meta-02:8091  1.8.10-c1.1.2
3   thinfluxdb-meta-03:8091  1.8.10-c1.1.2
```

### Data 节点设置

0. 设置说明和要求

生产环境安装过程设置两个 Data 节点，每个 Data 节点在自己的服务器上运行。 ThinfluxDB Cluster **推荐至少两个 Data 节点** 以实现高可用性和冗余。

> 注 1：没有要求每个 Data 节点都运行在自己的服务器上。但是，最佳实践是将每个 Data 节点部署在专用服务器上。

> 注 2：ThinfluxDB Cluster 不能用作负载均衡器。您需要配置自己的负载均衡器以将客户端流量发送到端口 8086（HTTP API 的默认端口）。

假设有两台服务器： thinfluxdb-data-01 和 thinfluxdb-data-02，通过端口 8088（Data节点间通信）和 8091（与Meta节点通信）进行通信。

1. 为每个服务器添加适当的 DNS 条目

> 注: 如果只想使用 IP 地址而不是主机名，请跳过当前步骤并转到步骤 2。

确保将服务器的主机名和 IP 地址添加到hosts文件中：

```bash
************** thinfluxdb-data-01
************** thinfluxdb-data-02
```

2. 编辑配置文件`/etc/thinfluxdb/thinfluxdb.conf`

```toml
#bind-address = ":8088"
hostname = "thinfluxdb-data-0x"  # 请将 0x 替换为实际的节点编号，如 01、02

[meta]
  dir = "/var/lib/thinfluxdb/meta"

[data]
  dir = "/var/lib/thinfluxdb/data"
  engine = "tsm1"
  wal-dir = "/var/lib/thinfluxdb/wal"

[hinted-handoff]
  dir = "/var/lib/thinfluxdb/hh"
```

注：如果只想使用 IP 地址而不是主机名，必须将 hostname 设置为 IP 地址。

3. 启动 Data 服务

分别在服务器 thinfluxdb-data-01 和 thinfluxdb-data-02 上启动 Data 服务

```bash
thinfluxd -config /etc/thinfluxdb/thinfluxdb.conf
```

> 注: Data 节点在未被加入集群之前，出现 `Failed to create storage`，`failed to store statistics` 或 `meta service unavailable` 日志是正常情况。

4. 将 Data 节点加入集群

无论是在集群的初始创建期间还是在增加 Data 节点数量，只有在添加全新节点时才应将 Data 节点加入集群。如果要使用 thinfluxd-ctl update-data 替换现有 Data 节点，请跳过本步骤的其余部分。

在 **Meta 节点上** 对要加入集群的每个 Data 节点**运行一次且仅一次**的 add-data 命令：

```bash
thinfluxd-ctl add-data thinfluxdb-data-01:8088
thinfluxd-ctl add-data thinfluxdb-data-02:8088
```

若在 **Data 节点上**，则应该添加 -bind 选项，指定要连接的 Meta 节点的绑定地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 add-data thinfluxdb-data-01:8088
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 add-data thinfluxdb-data-02:8088
thinfluxd-ctl -bind thinfluxdb-meta-01:8091 show
```

若只想使用 IP 地址而不是主机名，则应该添加 -bind 选项，指定要连接的 Meta 节点的绑定 HTTP 地址（默认值为 localhost:8091），执行：

```bash
thinfluxd-ctl -bind **************:8091 add-data **************:8088
thinfluxd-ctl -bind **************:8091 add-data **************:8088
thinfluxd-ctl -bind **************:8091 show
```

预期的输出是：

```bash
Added data node y at thinfluxdb-data-0x:8088
```

注意：实际输出中的节点 ID 和名称将根据您的具体部署情况而变化。

验证步骤:

在任何 Meta 节点上发出以下命令：

```bash
thinfluxd-ctl show
```

预期的输出是：

```bash
Data Nodes
==========
ID  TCP Address              Version
4   thinfluxdb-data-01:8088  1.8.10-c1.1.2
5   thinfluxdb-data-02:8088  1.8.10-c1.1.2

Meta Nodes
==========
ID  TCP Address              Version
1   thinfluxdb-meta-01:8091  1.8.10-c1.1.2
2   thinfluxdb-meta-02:8091  1.8.10-c1.1.2
3   thinfluxdb-meta-03:8091  1.8.10-c1.1.2
```

## 通过 HTTP API 使用 ThinfluxDB Cluster

### 创建 database

```bash
curl -XPOST "http://thinfluxdb-data-01:8086/query" --data-urlencode "q=CREATE DATABASE mydb WITH REPLICATION 2"
```

输出：

```bash
{"results":[{"statement_id":0}]}
```

表示创建 mydb 数据库成功。

### 写入一些数据

```bash
curl -XPOST "http://thinfluxdb-data-01:8086/write?db=mydb" \
-d 'cpu,host=server01,region=uswest load=42 1434055562000000000'

curl -XPOST "http://thinfluxdb-data-02:8086/write?db=mydb&consistency=all" \
-d 'cpu,host=server02,region=uswest load=78 1434055562000000000'

curl -XPOST "http://thinfluxdb-data-02:8086/write?db=mydb&consistency=quorum" \
-d 'cpu,host=server03,region=useast load=15.4 1434055562000000000'
```

> **注意**：`consistency=[any,one,quorum,all]` 设置数据点的写入一致性。如果不指定一致性，则 `consistency` 为 `one`。
> 
> any: 一旦任何节点写入成功，或者接收节点已将数据写入其 hinted handoff 队列，就立即向客户端返回成功。
> 
> one: 一旦任何节点写入成功，则立即向客户端返回成功，如果只是写入到 hinted handoff 队列中则不会返回。
> 
> quorum: 当大多数节点（大于等于 (副本因子/2)+1 个节点）返回成功时返回成功。此选项仅在副本因子大于 2 时才有用，否则等效于 all。
> 
> all: 仅当所有节点都返回成功时才返回成功。

### 查询数据

```bash
curl -G "http://thinfluxdb-data-02:8086/query?pretty=true" --data-urlencode "db=mydb" \
--data-urlencode "q=SELECT * FROM cpu WHERE host='server01' AND time < now() - 1d"
```

输出（示例数据）：

```bash
{
    "results": [
        {
            "statement_id": 0,
            "series": [
                {
                    "name": "cpu",
                    "columns": [
                        "time",
                        "host",
                        "load",
                        "region"
                    ],
                    "values": [
                        [
                            "2015-06-11T20:46:02Z",
                            "server01",
                            42,
                            "uswest"
                        ]
                    ]
                }
            ]
        }
    ]
}
```

### 分析数据

```bash
curl -G "http://thinfluxdb-data-02:8086/query?pretty=true" --data-urlencode "db=mydb" \
--data-urlencode "q=SELECT mean(load) FROM cpu WHERE region='uswest'"
```

输出：

```bash
{
    "results": [
        {
            "statement_id": 0,
            "series": [
                {
                    "name": "cpu",
                    "columns": [
                        "time",
                        "mean"
                    ],
                    "values": [
                        [
                            "1970-01-01T00:00:00Z",
                            60
                        ]
                    ]
                }
            ]
        }
    ]
}
```

## 通过 CLI 使用 ThinfluxDB Cluster

### 连接到集群

```bash
# 基本连接
[tdtgi@thinfluxdb-meta-01 ~]$ thinflux -host thinfluxdb-data-01
Connected to http://thinfluxdb-data-01:8086 version 1.8.10-c1.1.2
ThinfluxDB shell version: 1.8.10-c1.1.2
>
```

如果已配置身份验证，可以使用用户名和密码连接：

```bash
[tdtgi@thinfluxdb-meta-01 ~]$ thinflux -host thinfluxdb-data-01 -username admin -password password
```

### 创建 DATABASE

```bash
> CREATE DATABASE thinfluxdb
> SHOW DATABASES
name: databases
name
----
_internal
thinfluxdb
```

### 写入数据

使用INSERT语句直接写入数据：

```bash
> USE thinfluxdb
Using database thinfluxdb
> INSERT cpu,host=server01,region=uswest load=42
> INSERT cpu,host=server02,region=uswest load=78
> INSERT cpu,host=server03,region=useast load=15.4
```

### 查询数据

查询所有数据：

```bash
> SELECT * FROM cpu
name: cpu
time                host     load region
----                ----     ---- ------
1748576058976695255 server01 42   uswest
1748576065561447367 server02 78   uswest
1748576070257167140 server03 15.4 useast
```

设置时间精度：

```bash
> PRECISION rfc3339
> SELECT * FROM cpu
name: cpu
time                           host     load region
----                           ----     ---- ------
2025-05-30T03:34:18.976695255Z server01 42   uswest
2025-05-30T03:34:25.561447367Z server02 78   uswest
2025-05-30T03:34:30.25716714Z  server03 15.4 useast
```

使用条件过滤数据：

```bash
> SELECT * FROM cpu WHERE region='uswest'
name: cpu
time                           host     load region
----                           ----     ---- ------
2025-05-30T03:34:18.976695255Z server01 42   uswest
2025-05-30T03:34:25.561447367Z server02 78   uswest
```

### 分析数据

计算平均负载：

```bash
> SELECT MEAN(load) FROM cpu
name: cpu
time                 mean
----                 ----
1970-01-01T00:00:00Z 45.13333333333333
```

按区域分组计算平均负载：

```bash
> SELECT MEAN(load) FROM cpu GROUP BY region
name: cpu
tags: region=useast
time                 mean
----                 ----
1970-01-01T00:00:00Z 15.4

name: cpu
tags: region=uswest
time                 mean
----                 ----
1970-01-01T00:00:00Z 60
```

### 退出CLI

```bash
> exit
```

## 附：配置

### 配置 Meta 节点

以下是 Meta 节点的主要配置项，按照全局设置和功能模块分组：

**全局设置**：

- `reporting-disabled = false`：每 24 小时将数据报告打印输出到日志中。每个报告包括一个随机生成的标识符、操作系统、架构、ThinfluxDB Cluster 版本等。要禁用报告，请将此选项设置为 `true`。环境变量: `THINFLUXDB_REPORTING_DISABLED`

- `bind-address = ""`：早期版本中 Meta 节点 Raft 通信的绑定地址。此设置已不计划使用，将在未来版本中删除。环境变量: `THINFLUXDB_BIND_ADDRESS`

- `hostname = ""`：Meta 节点的主机名。这必须可被集群的所有其它成员解析和访问。环境变量: `THINFLUXDB_HOSTNAME`

**[meta] 设置**：

- `dir = "/var/lib/thinfluxdb/meta"`：集群元数据的存储目录。Data 节点也需要本地 meta 目录。环境变量: `THINFLUXDB_META_DIR`

- `bind-address = ":8089"`：Meta 节点 Raft 通信的绑定地址。为简单起见，ThinfluxDB Cluster 建议在所有 Meta 节点上使用相同的端口，但这不是必需的。环境变量: `THINFLUXDB_META_BIND_ADDRESS`

- `http-bind-address = ":8091"`：HTTP API 绑定的默认地址。环境变量: `THINFLUXDB_META_HTTP_BIND_ADDRESS`

- `https-enabled = false`：确定 Meta 节点是否使用 HTTPS 相互通信。默认情况下，HTTPS 被禁用。我们强烈建议启用 HTTPS。要启用 HTTPS，请将此选项设置为 `true`，并指定 SSL 证书和私钥的路径。环境变量: `THINFLUXDB_META_HTTPS_ENABLED`

- `https-certificate = ""`：如果启用了 HTTPS，请指定 SSL 证书的路径。可使用带有证书和密钥的 PEM 编码包或仅证书文件。环境变量: `THINFLUXDB_META_HTTPS_CERTIFICATE`

- `https-private-key = ""`：如果启用了 HTTPS，请指定 SSL 私钥的路径。可使用带有证书和密钥的 PEM 编码包或仅证书文件。环境变量: `THINFLUXDB_META_HTTPS_PRIVATE_KEY`

- `https-insecure-tls = false`：Meta 节点是否会跳过通过 HTTPS 相互通信的证书验证。这在使用自签名证书进行测试时很有用。环境变量: `THINFLUXDB_META_HTTPS_INSECURE_TLS`

### 配置 Data 节点

以下是 Data 节点的主要配置项：

**全局设置**：

- `bind-address = ":8088"`：RPC 服务用于节点间通信、备份和恢复的 TCP 绑定地址。环境变量: `THINFLUXDB_BIND_ADDRESS`

- `hostname = "localhost"`：Data 节点的主机名。这必须可被集群的所有其它成员解析和访问。环境变量: `THINFLUXDB_HOSTNAME`

- `gossip-frequency = "3s"`：将此节点的内部状态更新至集群的频率。环境变量: `THINFLUXDB_GOSSIP_FREQUENCY`

**[meta] 设置**：

- `dir = "/var/lib/thinfluxdb/meta"`：集群元数据的存储目录。Data 节点也需要本地 meta 目录。环境变量: `THINFLUXDB_META_DIR`

- `meta-tls-enabled = false`：连接到 Meta 节点时是否使用 TLS。如果 Meta 节点的 https-enabled 设置为 true，则将此选项设置为 true。环境变量: `THINFLUXDB_META_META_TLS_ENABLED`

**[data] 设置**：

- `dir = "/var/lib/thinfluxdb/data"`：TSM 存储引擎存储 TSM（读优化）文件的目录。环境变量: `THINFLUXDB_DATA_DIR`

- `wal-dir = "/var/lib/thinfluxdb/wal"`：TSM 存储引擎存储 WAL（写优化）文件的目录。环境变量: `THINFLUXDB_DATA_WAL_DIR`

**[http] 设置**：

- `bind-address = ":8086"`：HTTP API 绑定的默认地址。这是客户端连接进行查询和写入的端口。环境变量: `THINFLUXDB_HTTP_BIND_ADDRESS`

**[hinted-handoff] 设置**：

- `dir = "/var/lib/thinfluxdb/hh"`：存储 hinted handoff 数据的目录。环境变量: `THINFLUXDB_HINTED_HANDOFF_DIR`

### 使用配置文件

显示默认配置：

```bash
# Meta 节点
thinfluxd-meta config

# Data 节点
thinfluxd config
```

### 创建默认配置文件

```bash
# Meta 节点
thinfluxd-meta config > /etc/thinfluxdb/thinfluxdb-meta-generated.conf

# Data 节点
thinfluxd config > /etc/thinfluxdb/thinfluxdb-generated.conf
```

### 使用配置文件启动进程

```bash
# Meta 节点
thinfluxd-meta -config /etc/thinfluxdb/thinfluxdb-meta-generated.conf

# Data 节点
thinfluxd -config /etc/thinfluxdb/thinfluxdb-generated.conf
```

或通过环境变量：

```bash
# Meta 节点
export THINFLUXDB_META_CONFIG_PATH=/etc/thinfluxdb/thinfluxdb-meta-generated.conf
thinfluxd-meta

# Data 节点
export THINFLUXDB_CONFIG_PATH=/etc/thinfluxdb/thinfluxdb-generated.conf
thinfluxd
```

## 使用 Chronograf 可视化管理 ThinfluxDB Cluster

### 环境介绍

部署以下 1 Meta 节点和 1 Data 节点来演示使用 Chronograf 可视化管理 ThinfluxDB Cluster：

| IP地址          | 主机名              | 角色     |
|----------------|--------------------|----------|
| ************** | thinfluxdb-meta-01 | Meta节点 |
| ************** | thinfluxdb-data-01 | Data节点 |

> 注意：此处使用简化的环境（1 Meta 节点和 1 Data 节点）进行 Chronograf 演示，与前面的完整集群部署示例（3 Meta 节点和 2 Data 节点）不同。在实际生产环境中，您可以使用完整集群配置并在任一节点上部署 Chronograf。

### 下载安装 Chronograf 并启动

```bash
wget https://dl.influxdata.com/chronograf/releases/chronograf-1.10.7_linux_amd64.tar.gz
tar -xzf chronograf-1.10.7_linux_amd64.tar.gz
cd chronograf-1.10.7-1/usr/bin/
./chronograf
```

### 访问 Web 界面

```bash
# 本地访问
http://localhost:8888/

# 远程访问（假设 Chronograf 运行在 ************** 上）
http://**************:8888/
```

> 注意：如果需要从其他机器访问 Chronograf，请将 localhost 替换为运行 Chronograf 的服务器 IP 地址，并确保防火墙允许 8888 端口的访问。

### 添加 ThinfluxDB Cluster 配置

1. 点击 `Get Started`

![Get Started](./images/Getstart.png)

2. 添加 ThinfluxDB Cluster 配置

![Add ThinfluxDB Cluster](./images/ThinfluxDB-Cluster-configuration.png)

3. 选择 Dashboard

![Select Dashboard](./images/Dashboards.png)

4. 配置 Kapacitor (可选)

![Configure Kapacitor](./images/Kapacitor.png)

5. 完成配置

![Complete Configuration](./images/Setup-Complete.png)

### 简单操作演示

1. 创建数据库

![Create Database](./images/Create-Database.png)

2. 显示数据库

![Show Databases](./images/show-databases.png)

3. 查看 Admin 界面

![Admin](./images/admin.png)

> 注意：上述图片路径为相对路径，请确保images目录存在且包含相应的图片文件。
