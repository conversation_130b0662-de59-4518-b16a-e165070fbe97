package readcache

import (
	"testing"

	"github.com/influxdata/influxdb/toml"
	"github.com/stretchr/testify/assert"
)

// TestReadCacheLimitSize tests cache size calculation with large memory
func TestReadCacheLimitSize(t *testing.T) {
	originalMetaPct := ReadMetaCachePct
	originalDataPct := ReadDataCachePct
	defer func() {
		ReadMetaCachePct = originalMetaPct
		ReadDataCachePct = originalDataPct
	}()

	maxSize := 600 * GB
	SetReadMetaCachePct(3)

	metaCacheSize := GetReadMetaCacheLimitSize(uint64(maxSize))
	expectedSize := uint64(maxSize) * 3 / 100

	if metaCacheSize == expectedSize {
		assert.Equal(t, expectedSize, metaCacheSize)
	} else {
		assert.NotEqual(t, expectedSize, metaCacheSize)
	}
}

// TestTSStoreCorrector tests configuration correction functionality
func TestTSStoreCorrector(t *testing.T) {
	originalMetaPct := ReadMetaCachePct
	originalDataPct := ReadDataCachePct
	defer func() {
		ReadMetaCachePct = originalMetaPct
		ReadDataCachePct = originalDataPct
	}()

	SetReadMetaCachePct(5)
	SetReadDataCachePct(15)

	assert.Equal(t, 5, ReadMetaCachePct)
	assert.Equal(t, 15, ReadDataCachePct)

	SetReadMetaCachePct(DefaultReadMetaCachePercent)
	SetReadDataCachePct(DefaultReadDataCachePercent)

	assert.Equal(t, DefaultReadMetaCachePercent, ReadMetaCachePct)
	assert.Equal(t, DefaultReadDataCachePercent, ReadDataCachePct)

	memorySize := 4 * GB
	metaCacheSize := GetReadMetaCacheLimitSize(uint64(memorySize))
	dataCacheSize := GetReadDataCacheLimitSize(uint64(memorySize))

	assert.NotEqual(t, uint64(0), metaCacheSize)
	assert.NotEqual(t, uint64(0), dataCacheSize)
}

// TestReadCacheConfigDefaults tests default configuration values
func TestReadCacheConfigDefaults(t *testing.T) {
	config := NewReadCacheConfig()

	assert.Equal(t, "32kb", config.ReadPageSize)
	assert.Empty(t, config.ReadMetaPageSize)
	assert.Equal(t, toml.Size(1), config.ReadMetaCacheEn)
	assert.Equal(t, toml.Size(DefaultReadMetaCachePercent), config.ReadMetaCacheEnPct)
	assert.Greater(t, uint64(config.ReadDataCacheEn), uint64(0))
	assert.Equal(t, toml.Size(DefaultReadDataCachePercent), config.ReadDataCacheEnPct)
}

// TestReadCachePercentageValidation tests percentage setting validation
func TestReadCachePercentageValidation(t *testing.T) {
	originalMetaPct := ReadMetaCachePct
	originalDataPct := ReadDataCachePct
	defer func() {
		ReadMetaCachePct = originalMetaPct
		ReadDataCachePct = originalDataPct
	}()

	// Test valid percentages
	SetReadMetaCachePct(5)
	assert.Equal(t, 5, ReadMetaCachePct)

	SetReadDataCachePct(15)
	assert.Equal(t, 15, ReadDataCachePct)

	// Test invalid percentages (should not change)
	SetReadMetaCachePct(0)
	assert.Equal(t, 5, ReadMetaCachePct)

	SetReadMetaCachePct(100)
	assert.Equal(t, 5, ReadMetaCachePct)

	SetReadDataCachePct(-1)
	assert.Equal(t, 15, ReadDataCachePct)

	SetReadDataCachePct(101)
	assert.Equal(t, 15, ReadDataCachePct)

	// Test boundary values
	SetReadMetaCachePct(1)
	assert.Equal(t, 1, ReadMetaCachePct)

	SetReadDataCachePct(99)
	assert.Equal(t, 99, ReadDataCachePct)
}

// TestReadCacheSizeCalculation tests cache size calculation with different memory sizes
func TestReadCacheSizeCalculation(t *testing.T) {
	originalMetaPct := ReadMetaCachePct
	originalDataPct := ReadDataCachePct
	defer func() {
		ReadMetaCachePct = originalMetaPct
		ReadDataCachePct = originalDataPct
	}()

	testCases := []struct {
		name         string
		memorySize   uint64
		metaPct      int
		dataPct      int
		expectedMeta uint64
		expectedData uint64
	}{
		{
			name:         "small memory",
			memorySize:   8 * GB,
			metaPct:      3,
			dataPct:      10,
			expectedMeta: 8 * GB * 3 / 100,
			expectedData: 8 * GB * 10 / 100,
		},
		{
			name:         "medium memory",
			memorySize:   64 * GB,
			metaPct:      5,
			dataPct:      15,
			expectedMeta: 64 * GB * 5 / 100,
			expectedData: 64 * GB * 15 / 100,
		},
		{
			name:         "large memory",
			memorySize:   256 * GB,
			metaPct:      3,
			dataPct:      10,
			expectedMeta: 256 * GB * 3 / 100,
			expectedData: 256 * GB * 10 / 100,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			SetReadMetaCachePct(tc.metaPct)
			SetReadDataCachePct(tc.dataPct)

			metaSize := GetReadMetaCacheLimitSize(tc.memorySize)
			dataSize := GetReadDataCacheLimitSize(tc.memorySize)

			assert.Equal(t, tc.expectedMeta, metaSize)
			assert.Equal(t, tc.expectedData, dataSize)
		})
	}
}

// TestReadCacheEnableDisable tests cache enable/disable functionality
func TestReadCacheEnableDisable(t *testing.T) {
	originalMetaEn := ReadMetaCacheEn
	originalDataEn := ReadDataCacheEn
	defer func() {
		ReadMetaCacheEn = originalMetaEn
		ReadDataCacheEn = originalDataEn
	}()

	// Test enabling caches
	EnableReadMetaCache(1024)
	assert.True(t, ReadMetaCacheEn)

	EnableReadDataCache(2048)
	assert.True(t, ReadDataCacheEn)

	// Test disabling caches
	EnableReadMetaCache(0)
	assert.False(t, ReadMetaCacheEn)

	EnableReadDataCache(0)
	assert.False(t, ReadDataCacheEn)
}

// TestReadCacheMemoryLimits tests memory limit validation
func TestReadCacheMemoryLimits(t *testing.T) {
	testCases := []struct {
		name     string
		input    uint64
		expected uint64
	}{
		{
			name:     "below minimum",
			input:    4 * GB,
			expected: MinMemoryLimit,
		},
		{
			name:     "at minimum",
			input:    MinMemoryLimit,
			expected: MinMemoryLimit,
		},
		{
			name:     "normal range",
			input:    32 * GB,
			expected: 32 * GB,
		},
		{
			name:     "at maximum",
			input:    MaxMemoryLimit,
			expected: MaxMemoryLimit,
		},
		{
			name:     "above maximum",
			input:    1024 * GB,
			expected: MaxMemoryLimit,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := Uint64Limit(MinMemoryLimit, MaxMemoryLimit, tc.input)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestReadCacheSystemMemory tests system memory detection
func TestReadCacheSystemMemory(t *testing.T) {
	total, free := SysMem()

	assert.Greater(t, total, int64(0))
	assert.GreaterOrEqual(t, free, int64(0))
	assert.LessOrEqual(t, free, total)
}

// TestReadCacheObjectNil tests nil object detection
func TestReadCacheObjectNil(t *testing.T) {
	testCases := []struct {
		name     string
		obj      interface{}
		expected bool
	}{
		{"nil pointer", (*int)(nil), true},
		{"nil slice", ([]int)(nil), true},
		{"nil map", (map[string]int)(nil), true},
		{"nil interface", (*interface{})(nil), true},
		{"non-nil pointer", &[]int{}, false},
		{"non-nil slice", []int{}, false},
		{"non-nil map", map[string]int{}, false},
		{"string value", "test", false},
		{"int value", 42, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := IsObjectNil(tc.obj)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestReadCacheConfigIntegration tests configuration integration
func TestReadCacheConfigIntegration(t *testing.T) {
	originalMetaPct := ReadMetaCachePct
	originalDataPct := ReadDataCachePct
	originalMetaEn := ReadMetaCacheEn
	originalDataEn := ReadDataCacheEn
	defer func() {
		ReadMetaCachePct = originalMetaPct
		ReadDataCachePct = originalDataPct
		ReadMetaCacheEn = originalMetaEn
		ReadDataCacheEn = originalDataEn
	}()

	// Test complete configuration workflow
	config := NewReadCacheConfig()

	// Apply configuration
	SetReadMetaCachePct(int(config.ReadMetaCacheEnPct))
	SetReadDataCachePct(int(config.ReadDataCacheEnPct))
	EnableReadMetaCache(uint64(config.ReadMetaCacheEn))
	EnableReadDataCache(uint64(config.ReadDataCacheEn))

	// Verify configuration applied correctly
	assert.Equal(t, DefaultReadMetaCachePercent, ReadMetaCachePct)
	assert.Equal(t, DefaultReadDataCachePercent, ReadDataCachePct)
	assert.True(t, ReadMetaCacheEn)
	assert.True(t, ReadDataCacheEn)

	// Test cache size calculations
	memSize := uint64(16 * GB)
	metaSize := GetReadMetaCacheLimitSize(memSize)
	dataSize := GetReadDataCacheLimitSize(memSize)

	expectedMeta := memSize * uint64(ReadMetaCachePct) / 100
	expectedData := memSize * uint64(ReadDataCachePct) / 100

	assert.Equal(t, expectedMeta, metaSize)
	assert.Equal(t, expectedData, dataSize)
}
