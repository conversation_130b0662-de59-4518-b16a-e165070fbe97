package tsm1

import (
	"encoding/binary"
	"fmt"

	"github.com/influxdata/influxdb/tsdb/readcache"
)

// TSMPageCacheReader provides page-based caching for TSM files
// Adapted from openGemini's PageCacheReader for TSM format
type TSMPageCacheReader struct {
	accessor      *mmapAccessor
	init          bool
	startOffset   int64 // Data area start offset
	endOffset     int64 // Data area end offset
	maxPageId     int64 // Maximum page ID
	maxPageOffset int64 // Maximum page offset
	read          func(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error)
}

// NewTSMPageCacheReader creates a new TSM page cache reader
func NewTSMPageCacheReader(accessor *mmapAccessor) *TSMPageCacheReader {
	pcr := &TSMPageCacheReader{
		accessor: accessor,
		init:     false,
	}
	// Strategy selection (similar to openGemini)
	if readcache.IsPageSizeVariable {
		pcr.read = pcr.ReadVariablePageSize
	} else {
		pcr.read = pcr.ReadFixPageSize
	}
	return pcr
}

// Init initializes the page cache reader (similar to openGemini)
func (pcr *TSMPageCacheReader) Init() {
	// TSM file structure: Header(5) + Blocks(N) + Index(N) + Footer(8)
	// Data area: from header end to index start
	// Index area: from index start to footer start

	pcr.accessor.mu.RLock()
	defer pcr.accessor.mu.RUnlock()

	// Header is 5 bytes (4 bytes magic + 1 byte version)
	pcr.startOffset = 5

	// Footer is last 8 bytes containing index offset
	footerPos := len(pcr.accessor.b) - 8
	indexStart := binary.BigEndian.Uint64(pcr.accessor.b[footerPos : footerPos+8])

	// Data area ends where index starts
	pcr.endOffset = int64(indexStart)

	pcr.maxPageId, pcr.maxPageOffset = pcr.GetMaxPageIdAndOffset()
}

// GetMaxPageIdAndOffset calculates maximum page ID and offset (from openGemini)
func (pcr *TSMPageCacheReader) GetMaxPageIdAndOffset() (int64, int64) {
	over := (pcr.endOffset - pcr.startOffset) % readcache.PageSize
	if over > 0 {
		over = 1
	}
	endPageId := (pcr.endOffset-pcr.startOffset)/readcache.PageSize + over
	endPageOffset := (endPageId-1)*readcache.PageSize + pcr.startOffset
	return endPageId, endPageOffset
}

// GetCachePageIdsAndOffsets gets all cache pageIds containing bytes from start to start + size (from openGemini)
func (pcr *TSMPageCacheReader) GetCachePageIdsAndOffsets(start int64, size uint32) ([]int64, []int64, error) {
	end := start + int64(size)
	if start < pcr.startOffset || start >= pcr.endOffset || end < pcr.startOffset || end > pcr.endOffset {
		return nil, nil, fmt.Errorf("invalid read offset of GetCachePageIdsAndOffsets() start:%v end:%v startOffset:%v endOffset:%v", start, end, pcr.startOffset, pcr.endOffset)
	}

	pageIds := make([]int64, 0, int64(size)/readcache.PageSize+1)
	pageOffsets := make([]int64, 0, int64(size)/readcache.PageSize+1)
	startPageId := ((start - pcr.startOffset) / readcache.PageSize) + 1
	startPageOffset := (startPageId-1)*readcache.PageSize + pcr.startOffset

	for ; startPageOffset < end; startPageOffset += readcache.PageSize {
		pageIds = append(pageIds, startPageId)
		pageOffsets = append(pageOffsets, startPageOffset)
		startPageId++
	}
	return pageIds, pageOffsets, nil
}

// ReadSinglePage reads a single page (from openGemini)
func (pcr *TSMPageCacheReader) ReadSinglePage(cacheKey string, pageOffset int64, pageSize int64, buf *[]byte) (*readcache.CachePage, []byte, error) {
	cacheIns := readcache.GetReadDataCacheIns()
	var pageCache *readcache.CachePage
	var ok bool

	if value, isGet := cacheIns.GetPageCache(cacheKey); isGet {
		pageCache, ok = value.(*readcache.CachePage)
		if !ok {
			return nil, nil, fmt.Errorf("cacheValue is not a page")
		}
		return pageCache, pageCache.Value, nil
	} else {
		pageCache := readcache.CachePagePool.Get()
		pageCache.Ref()

		// Read from mmap
		pcr.accessor.mu.RLock()
		if int64(len(pcr.accessor.b)) < pageOffset+pageSize {
			pcr.accessor.mu.RUnlock()
			pageCache.Unref(readcache.CachePagePool)
			return nil, nil, ErrTSMClosed
		}

		tempPage := make([]byte, pageSize)
		copy(tempPage, pcr.accessor.b[pageOffset:pageOffset+pageSize])
		pcr.accessor.mu.RUnlock()

		pageCache.Value = tempPage
		pageCache.Size = int64(len(tempPage))
		cacheIns.AddPageCache(cacheKey, pageCache, int64(len(tempPage)), readcache.CachePagePool)
		return pageCache, tempPage, nil
	}
}

// ReadDataBlock reads data block with caching (similar to openGemini ReadDataBlock)
func (pcr *TSMPageCacheReader) ReadDataBlock(entry *IndexEntry, values []Value) ([]Value, *readcache.CachePage, error) {
	if !readcache.ReadDataCacheEn || !pcr.accessor.cacheEnable() {
		values, err := pcr.accessor.readBlockDirect(entry, values)
		return values, nil, err
	}

	var cachePage *readcache.CachePage
	values, err := pcr.accessor.readBlockWithCacheAndRef(entry, values, &cachePage)
	return values, cachePage, err
}

// ReadMetaBytes reads metadata bytes with caching (similar to openGemini GetTSSPFileBytes)
func (pcr *TSMPageCacheReader) ReadMetaBytes(entry *IndexEntry, b []byte) (uint32, []byte, *readcache.CachePage, error) {
	if !readcache.ReadMetaCacheEn || !pcr.accessor.cacheEnable() {
		crc, block, err := pcr.accessor.readBytesDirect(entry, b)
		return crc, block, nil, err
	}

	var cachePage *readcache.CachePage
	crc, block, err := pcr.accessor.readBytesWithCacheAndRef(entry, b, &cachePage)
	return crc, block, cachePage, err
}

// UnrefCachePage releases data cache page reference
func (pcr *TSMPageCacheReader) UnrefCachePage(cachePage *readcache.CachePage) {
	pcr.accessor.unrefCachePage(cachePage)
}

// UnrefMetaCachePage releases metadata cache page reference
func (pcr *TSMPageCacheReader) UnrefMetaCachePage(cachePage *readcache.CachePage) {
	pcr.accessor.unrefMetaCachePage(cachePage)
}

// Read unified read interface (from openGemini)
func (pcr *TSMPageCacheReader) Read(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error) {
	return pcr.read(offset, size, buf)
}

// ReadVariablePageSize variable page size strategy (from openGemini)
func (pcr *TSMPageCacheReader) ReadVariablePageSize(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error) {
	cacheIns := readcache.GetReadDataCacheIns()
	cacheKey := cacheIns.CreateCacheKey(pcr.accessor.f.Name(), offset)
	var b []byte
	var page *readcache.CachePage
	var ok bool

	if value, isGet := cacheIns.Get(cacheKey); isGet {
		page, ok = value.(*readcache.CachePage)
		if !ok {
			return nil, nil, fmt.Errorf("cacheValue is not a page")
		}
		if page.Size >= int64(size) {
			b = page.Value[:size]
			return b, nil, nil
		}
	}

	// Read from mmap
	pcr.accessor.mu.RLock()
	if int64(len(pcr.accessor.b)) < offset+int64(size) {
		pcr.accessor.mu.RUnlock()
		return nil, nil, ErrTSMClosed
	}

	b = make([]byte, size)
	copy(b, pcr.accessor.b[offset:offset+int64(size)])
	pcr.accessor.mu.RUnlock()

	cacheIns.AddPage(cacheKey, b, int64(size), readcache.CachePagePool)
	return b, nil, nil
}

// ReadFixPageSize fixed page size strategy (simplified from openGemini)
func (pcr *TSMPageCacheReader) ReadFixPageSize(offset int64, size uint32, buf *[]byte) ([]byte, *readcache.CachePage, error) {
	if !pcr.init {
		pcr.Init()
		pcr.init = true
	}

	// For TSM files, we use simplified logic compared to openGemini's complex page management
	// This is because TSM blocks are typically smaller and don't need complex page splitting
	return pcr.ReadVariablePageSize(offset, size, buf)
}
