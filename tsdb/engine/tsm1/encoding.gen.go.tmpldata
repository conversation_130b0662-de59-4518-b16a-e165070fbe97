[{"Name": "", "name": "", "Type": "", "CastType": ""}, {"Name": "Float", "name": "float", "Type": "BlockFloat64", "CastType": ""}, {"Name": "Integer", "name": "integer", "Type": "BlockInteger", "CastType": ""}, {"Name": "Unsigned", "name": "unsigned", "Type": "BlockUnsigned", "CastType": "int64"}, {"Name": "String", "name": "string", "Type": "BlockString", "CastType": ""}, {"Name": "Boolean", "name": "boolean", "Type": "BlockBoolean", "CastType": ""}]