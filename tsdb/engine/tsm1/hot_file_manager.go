package tsm1

import (
	"sync"
	"sync/atomic"
	"time"
)

// HotFileConfig holds configuration for hot file management
type HotFileConfig struct {
	Enabled              bool          // Enable hot file detection
	MemoryAllowedPercent int           // Percent of system memory allowed for hot files
	Duration             time.Duration // Time window for considering files as hot
	MaxFileSize          int64         // Maximum file size to be considered hot
}

// DefaultHotFileConfig returns default configuration
func DefaultHotFileConfig() *HotFileConfig {
	return &HotFileConfig{
		Enabled:              false,     // Disabled by default
		MemoryAllowedPercent: 5,         // 5% of system memory
		Duration:             time.Hour, // 1 hour
		MaxFileSize:          2 << 30,   // 2GB
	}
}

// HotFileManager manages hot files in memory
type HotFileManager struct {
	mu     sync.RWMutex
	config *HotFileConfig

	hotFiles        map[string]*HotFileInfo // path -> hot file info
	totalMemorySize int64                   // Total memory used by hot files
	maxMemorySize   int64                   // Maximum allowed memory

	running bool
	stopCh  chan struct{}
	wg      sync.WaitGroup
}

// HotFileInfo holds information about a hot file
type HotFileInfo struct {
	Path         string
	Size         int64
	MinTime      int64
	MaxTime      int64
	LastAccessed int64
	AccessCount  int64
}

var globalHotFileManager *HotFileManager
var hotFileManagerOnce sync.Once

// GetHotFileManager returns the global hot file manager instance
func GetHotFileManager() *HotFileManager {
	if globalHotFileManager != nil {
		return globalHotFileManager
	}
	hotFileManagerOnce.Do(func() {
		globalHotFileManager = NewHotFileManager(DefaultHotFileConfig())
	})
	return globalHotFileManager
}

// NewHotFileManager creates a new hot file manager
func NewHotFileManager(config *HotFileConfig) *HotFileManager {
	return &HotFileManager{
		config:   config,
		hotFiles: make(map[string]*HotFileInfo),
		stopCh:   make(chan struct{}),
	}
}

// Start starts the hot file manager
func (hfm *HotFileManager) Start() {
	if !hfm.config.Enabled {
		return
	}

	hfm.mu.Lock()
	defer hfm.mu.Unlock()

	if hfm.running {
		return
	}

	// Calculate max memory size (simplified, should use actual system memory)
	hfm.maxMemorySize = 1024 * 1024 * 1024 * int64(hfm.config.MemoryAllowedPercent) / 100 // Simplified

	hfm.running = true
	hfm.wg.Add(1)
	go hfm.backgroundCleanup()
}

// Stop stops the hot file manager
func (hfm *HotFileManager) Stop() {
	hfm.mu.Lock()
	defer hfm.mu.Unlock()

	if !hfm.running {
		return
	}

	close(hfm.stopCh)
	hfm.running = false
	hfm.mu.Unlock()
	hfm.wg.Wait()
	hfm.mu.Lock()
}

// IsHotFile checks if a file should be considered hot
func (hfm *HotFileManager) IsHotFile(path string, minTime, maxTime int64, fileSize int64) bool {
	if !hfm.config.Enabled {
		return false
	}

	// Check file size limit
	if fileSize > hfm.config.MaxFileSize {
		return false
	}

	// Check time range (file contains recent data)
	now := time.Now().UnixNano()
	hotThreshold := now - hfm.config.Duration.Nanoseconds()

	// File is hot if it contains data within the hot duration
	return maxTime >= hotThreshold
}

// MarkFileAccessed marks a file as accessed
func (hfm *HotFileManager) MarkFileAccessed(path string, minTime, maxTime int64, fileSize int64) {
	if !hfm.config.Enabled {
		return
	}

	hfm.mu.Lock()
	defer hfm.mu.Unlock()

	now := time.Now().UnixNano()

	if info, exists := hfm.hotFiles[path]; exists {
		info.LastAccessed = now
		atomic.AddInt64(&info.AccessCount, 1)
	} else if hfm.IsHotFile(path, minTime, maxTime, fileSize) {
		// Add new hot file
		hfm.hotFiles[path] = &HotFileInfo{
			Path:         path,
			Size:         fileSize,
			MinTime:      minTime,
			MaxTime:      maxTime,
			LastAccessed: now,
			AccessCount:  1,
		}
		atomic.AddInt64(&hfm.totalMemorySize, fileSize)
	}
}

// IsFileHot checks if a specific file is currently marked as hot
func (hfm *HotFileManager) IsFileHot(path string) bool {
	if !hfm.config.Enabled {
		return false
	}

	hfm.mu.RLock()
	defer hfm.mu.RUnlock()

	_, exists := hfm.hotFiles[path]
	return exists
}

// GetHotFileStats returns statistics about hot files
func (hfm *HotFileManager) GetHotFileStats() (count int, totalSize int64) {
	hfm.mu.RLock()
	defer hfm.mu.RUnlock()

	return len(hfm.hotFiles), atomic.LoadInt64(&hfm.totalMemorySize)
}

// backgroundCleanup runs periodic cleanup of expired hot files
func (hfm *HotFileManager) backgroundCleanup() {
	defer hfm.wg.Done()

	ticker := time.NewTicker(time.Minute) // Cleanup every minute
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hfm.cleanup()
		case <-hfm.stopCh:
			return
		}
	}
}

// cleanup removes expired hot files
func (hfm *HotFileManager) cleanup() {
	hfm.mu.Lock()
	defer hfm.mu.Unlock()

	now := time.Now().UnixNano()
	expireThreshold := now - hfm.config.Duration.Nanoseconds()

	for path, info := range hfm.hotFiles {
		// Remove files that are no longer in the hot time range
		if info.MaxTime < expireThreshold {
			delete(hfm.hotFiles, path)
			atomic.AddInt64(&hfm.totalMemorySize, -info.Size)
		}
	}

	// If still over memory limit, remove least recently accessed files
	for atomic.LoadInt64(&hfm.totalMemorySize) > hfm.maxMemorySize && len(hfm.hotFiles) > 0 {
		var oldestPath string
		var oldestTime int64 = now

		for path, info := range hfm.hotFiles {
			if info.LastAccessed < oldestTime {
				oldestTime = info.LastAccessed
				oldestPath = path
			}
		}

		if oldestPath != "" {
			info := hfm.hotFiles[oldestPath]
			delete(hfm.hotFiles, oldestPath)
			atomic.AddInt64(&hfm.totalMemorySize, -info.Size)
		}
	}
}
