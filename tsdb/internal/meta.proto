syntax = "proto3";

package tsdb;

//========================================================================
//
// Metadata
//
//========================================================================

message Series {
  string Key = 1;
  repeated Tag Tags = 2;
}

message Tag {
  string Key = 1;
  string Value = 2;
}

message MeasurementFields {
  bytes Name = 1;
  repeated Field Fields = 2;
}

message Field {
  bytes Name = 1;
  int32 Type = 2;
}

message MeasurementFieldSet {
	repeated MeasurementFields Measurements = 1;
}
