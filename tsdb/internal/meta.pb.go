// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: internal/meta.proto

package tsdb

import (
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Series struct {
	Key                  string   `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Tags                 []*Tag   `protobuf:"bytes,2,rep,name=Tags,proto3" json:"Tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Series) Reset()         { *m = Series{} }
func (m *Series) String() string { return proto.CompactTextString(m) }
func (*Series) ProtoMessage()    {}
func (*Series) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{0}
}
func (m *Series) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Series.Unmarshal(m, b)
}
func (m *Series) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Series.Marshal(b, m, deterministic)
}
func (m *Series) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Series.Merge(m, src)
}
func (m *Series) XXX_Size() int {
	return xxx_messageInfo_Series.Size(m)
}
func (m *Series) XXX_DiscardUnknown() {
	xxx_messageInfo_Series.DiscardUnknown(m)
}

var xxx_messageInfo_Series proto.InternalMessageInfo

func (m *Series) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Series) GetTags() []*Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type Tag struct {
	Key                  string   `protobuf:"bytes,1,opt,name=Key,proto3" json:"Key,omitempty"`
	Value                string   `protobuf:"bytes,2,opt,name=Value,proto3" json:"Value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Tag) Reset()         { *m = Tag{} }
func (m *Tag) String() string { return proto.CompactTextString(m) }
func (*Tag) ProtoMessage()    {}
func (*Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{1}
}
func (m *Tag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tag.Unmarshal(m, b)
}
func (m *Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tag.Marshal(b, m, deterministic)
}
func (m *Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tag.Merge(m, src)
}
func (m *Tag) XXX_Size() int {
	return xxx_messageInfo_Tag.Size(m)
}
func (m *Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_Tag proto.InternalMessageInfo

func (m *Tag) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *Tag) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type MeasurementFields struct {
	Name                 []byte   `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Fields               []*Field `protobuf:"bytes,2,rep,name=Fields,proto3" json:"Fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MeasurementFields) Reset()         { *m = MeasurementFields{} }
func (m *MeasurementFields) String() string { return proto.CompactTextString(m) }
func (*MeasurementFields) ProtoMessage()    {}
func (*MeasurementFields) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{2}
}
func (m *MeasurementFields) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementFields.Unmarshal(m, b)
}
func (m *MeasurementFields) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementFields.Marshal(b, m, deterministic)
}
func (m *MeasurementFields) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementFields.Merge(m, src)
}
func (m *MeasurementFields) XXX_Size() int {
	return xxx_messageInfo_MeasurementFields.Size(m)
}
func (m *MeasurementFields) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementFields.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementFields proto.InternalMessageInfo

func (m *MeasurementFields) GetName() []byte {
	if m != nil {
		return m.Name
	}
	return nil
}

func (m *MeasurementFields) GetFields() []*Field {
	if m != nil {
		return m.Fields
	}
	return nil
}

type Field struct {
	Name                 []byte   `protobuf:"bytes,1,opt,name=Name,proto3" json:"Name,omitempty"`
	Type                 int32    `protobuf:"varint,2,opt,name=Type,proto3" json:"Type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Field) Reset()         { *m = Field{} }
func (m *Field) String() string { return proto.CompactTextString(m) }
func (*Field) ProtoMessage()    {}
func (*Field) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{3}
}
func (m *Field) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Field.Unmarshal(m, b)
}
func (m *Field) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Field.Marshal(b, m, deterministic)
}
func (m *Field) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Field.Merge(m, src)
}
func (m *Field) XXX_Size() int {
	return xxx_messageInfo_Field.Size(m)
}
func (m *Field) XXX_DiscardUnknown() {
	xxx_messageInfo_Field.DiscardUnknown(m)
}

var xxx_messageInfo_Field proto.InternalMessageInfo

func (m *Field) GetName() []byte {
	if m != nil {
		return m.Name
	}
	return nil
}

func (m *Field) GetType() int32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type MeasurementFieldSet struct {
	Measurements         []*MeasurementFields `protobuf:"bytes,1,rep,name=Measurements,proto3" json:"Measurements,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MeasurementFieldSet) Reset()         { *m = MeasurementFieldSet{} }
func (m *MeasurementFieldSet) String() string { return proto.CompactTextString(m) }
func (*MeasurementFieldSet) ProtoMessage()    {}
func (*MeasurementFieldSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_59b0956366e72083, []int{4}
}
func (m *MeasurementFieldSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MeasurementFieldSet.Unmarshal(m, b)
}
func (m *MeasurementFieldSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MeasurementFieldSet.Marshal(b, m, deterministic)
}
func (m *MeasurementFieldSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MeasurementFieldSet.Merge(m, src)
}
func (m *MeasurementFieldSet) XXX_Size() int {
	return xxx_messageInfo_MeasurementFieldSet.Size(m)
}
func (m *MeasurementFieldSet) XXX_DiscardUnknown() {
	xxx_messageInfo_MeasurementFieldSet.DiscardUnknown(m)
}

var xxx_messageInfo_MeasurementFieldSet proto.InternalMessageInfo

func (m *MeasurementFieldSet) GetMeasurements() []*MeasurementFields {
	if m != nil {
		return m.Measurements
	}
	return nil
}

func init() {
	proto.RegisterType((*Series)(nil), "tsdb.Series")
	proto.RegisterType((*Tag)(nil), "tsdb.Tag")
	proto.RegisterType((*MeasurementFields)(nil), "tsdb.MeasurementFields")
	proto.RegisterType((*Field)(nil), "tsdb.Field")
	proto.RegisterType((*MeasurementFieldSet)(nil), "tsdb.MeasurementFieldSet")
}

func init() { proto.RegisterFile("internal/meta.proto", fileDescriptor_59b0956366e72083) }

var fileDescriptor_59b0956366e72083 = []byte{
	// 226 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x90, 0x3d, 0x6b, 0xc3, 0x30,
	0x10, 0x86, 0x71, 0x2c, 0x1b, 0x72, 0xc9, 0xd0, 0x5e, 0x0a, 0xd5, 0x52, 0x08, 0xea, 0x92, 0xa5,
	0x0e, 0xb4, 0x53, 0xe9, 0xde, 0xa5, 0x1f, 0x83, 0x22, 0xba, 0x5f, 0xc8, 0x61, 0x0c, 0xb6, 0x13,
	0x24, 0x65, 0xc8, 0xbf, 0x2f, 0x3e, 0x79, 0xe8, 0x87, 0xb7, 0x57, 0xcf, 0xe9, 0xd5, 0x23, 0x0e,
	0x56, 0x4d, 0x1f, 0xd9, 0xf7, 0xd4, 0x6e, 0x3b, 0x8e, 0x54, 0x9d, 0xfc, 0x31, 0x1e, 0x51, 0xc5,
	0x70, 0xd8, 0x9b, 0x67, 0x28, 0x77, 0xec, 0x1b, 0x0e, 0x78, 0x05, 0xf9, 0x1b, 0x5f, 0x74, 0xb6,
	0xce, 0x36, 0x73, 0x3b, 0x44, 0xbc, 0x03, 0xe5, 0xa8, 0x0e, 0x7a, 0xb6, 0xce, 0x37, 0x8b, 0xc7,
	0x79, 0x35, 0x14, 0x2a, 0x47, 0xb5, 0x15, 0x6c, 0x1e, 0x20, 0x77, 0x54, 0x4f, 0xf4, 0x6e, 0xa0,
	0xf8, 0xa2, 0xf6, 0xcc, 0x7a, 0x26, 0x2c, 0x1d, 0xcc, 0x3b, 0x5c, 0x7f, 0x30, 0x85, 0xb3, 0xe7,
	0x8e, 0xfb, 0xf8, 0xda, 0x70, 0x7b, 0x08, 0x88, 0xa0, 0x3e, 0xa9, 0x63, 0x69, 0x2f, 0xad, 0x64,
	0xbc, 0x87, 0x32, 0x4d, 0x47, 0xf1, 0x22, 0x89, 0x85, 0xd9, 0x71, 0x64, 0xb6, 0x50, 0x48, 0x9a,
	0x7c, 0x01, 0x41, 0xb9, 0xcb, 0x29, 0xf9, 0x0b, 0x2b, 0xd9, 0x58, 0x58, 0xfd, 0xd5, 0xef, 0x38,
	0xe2, 0x0b, 0x2c, 0x7f, 0xe0, 0xa0, 0x33, 0x51, 0xde, 0x26, 0xe5, 0xbf, 0xff, 0xda, 0x5f, 0x97,
	0xf7, 0xa5, 0x6c, 0xf2, 0xe9, 0x3b, 0x00, 0x00, 0xff, 0xff, 0xa3, 0xed, 0xcf, 0x26, 0x60, 0x01,
	0x00, 0x00,
}
