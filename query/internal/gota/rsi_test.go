package gota

import "testing"

func TestRSI(t *testing.T) {
	list := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1}

	// expList is generated by the following code:
	// expList, _ := talib.Rsi(list, 10, nil)
	expList := []float64{100, 100, 100, 100, 100, 90, 81, 72.89999999999999, 65.61, 59.04899999999999, 53.144099999999995, 47.82969, 43.04672099999999, 38.74204889999999, 34.86784400999999, 31.381059608999994, 28.242953648099995, 25.418658283289997, 22.876792454961}

	rsi := NewRSI(10, WarmSMA)
	var actList []float64
	for _, v := range list {
		if vOut := rsi.Add(v); rsi.Warmed() {
			actList = append(actList, vOut)
		}
	}

	if diff := diffFloats(expList, actList, 0.0000001); diff != "" {
		t.Errorf("unexpected floats:\n%s", diff)
	}
}
