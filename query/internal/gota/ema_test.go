package gota

import "testing"

func TestEMA(t *testing.T) {
	list := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1}

	// expList is generated by the following code:
	// expList, _ := talib.Ema(list, 10, nil)
	expList := []float64{5.5, 6.5, 7.5, 8.5, 9.5, 10.5, 11.136363636363637, 11.475206611570249, 11.570623591284749, 11.466873847414794, 11.200169511521196, 10.800138691244614, 10.291022565563775, 9.692654826370362, 9.021263039757569, 8.290124305256192, 7.510101704300521, 6.690083212609517, 5.837340810316878, 4.957824299350173}

	ema := NewEMA(10, WarmSMA)
	var actList []float64
	for _, v := range list {
		if vOut := ema.Add(v); ema.Warmed() {
			actList = append(actList, vOut)
		}
	}

	if diff := diffFloats(expList, actList, 0.0000001); diff != "" {
		t.Errorf("unexpected floats:\n%s", diff)
	}
}

func TestDEMA(t *testing.T) {
	list := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1}

	// expList is generated by the following code:
	// expList, _ := talib.Dema(list, 10, nil)
	expList := []float64{13.568840926166246, 12.701748119313985, 11.701405062848783, 10.611872766773773, 9.465595022565749, 8.28616628396151, 7.090477085921927, 5.8903718513360275, 4.693925476073202, 3.5064225149113692, 2.331104912318361}

	dema := NewDEMA(10, WarmSMA)
	var actList []float64
	for _, v := range list {
		if vOut := dema.Add(v); dema.Warmed() {
			actList = append(actList, vOut)
		}
	}

	if diff := diffFloats(expList, actList, 0.0000001); diff != "" {
		t.Errorf("unexpected floats:\n%s", diff)
	}
}

func TestTEMA(t *testing.T) {
	list := []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1}

	// expList is generated by the following code:
	// expList, _ := talib.Tema(list, 4, nil)
	expList := []float64{10, 11, 12, 13, 14, 15, 14.431999999999995, 13.345600000000001, 12.155520000000001, 11, 9.906687999999997, 8.86563072, 7.8589122560000035, 6.871005491200005, 5.891160883200005, 4.912928706560004, 3.932955104051203, 2.9498469349785603, 1.9633255712030717, 0.9736696408637435}

	tema := NewTEMA(4, WarmSMA)
	var actList []float64
	for _, v := range list {
		if vOut := tema.Add(v); tema.Warmed() {
			actList = append(actList, vOut)
		}
	}

	if diff := diffFloats(expList, actList, 0.0000001); diff != "" {
		t.Errorf("unexpected floats:\n%s", diff)
	}
}

func TestEmaWarmCount(t *testing.T) {
	period := 9
	ema := NewEMA(period, WarmSMA)

	var i int
	for i = 0; i < period*10; i++ {
		ema.Add(float64(i))
		if ema.Warmed() {
			break
		}
	}

	if got, want := i, ema.WarmCount(); got != want {
		t.Errorf("unexpected warm count: got=%d want=%d", got, want)
	}
}

func TestDemaWarmCount(t *testing.T) {
	period := 9
	dema := NewDEMA(period, WarmSMA)

	var i int
	for i = 0; i < period*10; i++ {
		dema.Add(float64(i))
		if dema.Warmed() {
			break
		}
	}

	if got, want := i, dema.WarmCount(); got != want {
		t.Errorf("unexpected warm count: got=%d want=%d", got, want)
	}
}

func TestTemaWarmCount(t *testing.T) {
	period := 9
	tema := NewTEMA(period, WarmSMA)

	var i int
	for i = 0; i < period*10; i++ {
		tema.Add(float64(i))
		if tema.Warmed() {
			break
		}
	}

	if got, want := i, tema.WarmCount(); got != want {
		t.Errorf("unexpected warm count: got=%d want=%d", got, want)
	}
}
