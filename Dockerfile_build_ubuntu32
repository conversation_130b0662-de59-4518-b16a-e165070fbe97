FROM ioft/i386-ubuntu:xenial

RUN apt-get update && DEBIAN_FRONTEND=noninteractive apt-get install -y \
    python-software-properties \
    software-properties-common \
    wget \
    git \
    mercurial \
    make \
    ruby \
    ruby-dev \
    rubygems \
    autoconf \
    libtool \
    build-essential \
    rpm \
    zip \
    python \
    python-boto

RUN gem install fpm

# setup environment
ENV GO_VERSION  1.16.15
ENV GOARCH      386
ENV GOROOT      /usr/local/go
ENV GOPATH      /root/go
ENV PATH        $GOPATH/bin:$GOROOT/bin:$PATH
ENV PROJECT_DIR /root/influxdb

# install go
RUN wget --no-verbose https://storage.googleapis.com/golang/go${GO_VERSION}.linux-${GOARCH}.tar.gz -O- | tar -C /usr/local/ -zxf-

RUN mkdir -p $PROJECT_DIR

WORKDIR $PROJECT_DIR
VOLUME $PROJECT_DIR

ENTRYPOINT [ "/root/influxdb/build.py" ]
